// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendorgateway.lending.creditcard;

import "api/firefly/enums/enums.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/card_material.proto";
import "api/typesv2/card_type.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/credit_card_category.proto";
import "api/typesv2/date.proto";
import "api/typesv2/document_type.proto";
import "api/typesv2/employment_industry.proto";
import "api/typesv2/employment_type.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/marital_status.proto";
import "api/typesv2/nominee.proto";
import "api/typesv2/transaction_transfer_type.proto";
import "api/vendorgateway/lending/creditcard/enums.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/creditcard";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.creditcard";

service CreditCard {
  // RPC to register credit card customer via CC vendor
  rpc RegisterCustomer (RegisterCustomerRequest) returns (RegisterCustomerResponse);
  // RPC to set preferences for credit card
  rpc SetPreferences (SetPreferencesRequest) returns (SetPreferencesResponse);
  // RPC to request physical card
  rpc RequestPhysicalCard (RequestPhysicalCardRequest) returns (RequestPhysicalCardResponse);
  // RPC to upgrade limit
  rpc UpgradeLimit (UpgradeLimitRequest) returns (UpgradeLimitResponse);
  // RPC to fetch limit
  rpc FetchLimit (FetchLimitRequest) returns (FetchLimitResponse);
  // RPC to fetch preference
  rpc FetchPreference (FetchPreferenceRequest) returns (FetchPreferenceResponse);
  // RPC for file upload
  rpc UploadFile (UploadFileRequest) returns (UploadFileResponse);

  // RPC to get card details
  rpc GetCardList (GetCardListRequest) returns (GetCardListResponse);

  //RPC to fetch card balance
  rpc FetchBalance (FetchBalanceRequest) returns (FetchBalanceResponse);

  //RPC to lock/unlock a card for a user
  rpc ManageLock (ManageLockRequest) returns (ManageLockResponse);

  // RPC to fetch transactions for a customer in a given date range
  rpc FetchTransactions (FetchTransactionsRequest) returns (FetchTransactionsResponse);

  // RPC to set credit limit for a customer
  rpc SetCreditLimit (SetCreditLimitRequest) returns (SetCreditLimitResponse);

  // RPC to fetch due amount for a customer
  rpc FetchDueAmount (FetchDueAmountRequest) returns (FetchDueAmountResponse);

  rpc GenerateCVV (GenerateCVVRqeuest) returns (GenerateCVVResponse) {
    option (rpc.skip_tokenization) = false;
  }

  // RPC to update an existing customer
  rpc UpdateCustomer (UpdateCustomerRequest) returns (UpdateCustomerResponse);

  // RPC to get unmasked card details for a customer
  rpc GetUnmaskedCardDetails (GetUnmaskedCardDetailsRequest) returns (GetUnmaskedCardDetailsResponse) {
    option (rpc.skip_tokenization) = false;
  }

  // RPC to fetch base64 encoded statement for a customer for a month
  rpc FetchEncodedStatement (FetchEncodedStatementRequest) returns (FetchEncodedStatementResponse);

  // RPC to fetch statement as well as balance for a month for a customer
  rpc FetchMonthlyStatement (FetchMonthlyStatementRequest) returns (FetchMonthlyStatementResponse);

  // RPC to replace card of a user
  rpc ReplaceCard (ReplaceCardRequest) returns (ReplaceCardResponse);

  // RPC to fetch transaction status for a given time period
  rpc FetchTransactionStatus (FetchTransactionStatusRequest) returns (FetchTransactionStatusResponse);

  // RPC to set pin for a card
  rpc SetPin (SetPinRequest) returns (SetPinResponse);

  // RPC to fetch paginated unbilled transactions for a time period
  rpc FetchUnbilledTransactions (FetchUnbilledTransactionsRequest) returns (FetchUnbilledTransactionsResponse);

  // RPC to repay the loan amount once the monthly statement is generated for a customer.
  rpc RepayLoanAmount (RepayLoanAmountRequest) returns (RepayLoanAmountResponse);

  // RPC to fetch statement list for a customer for a month
  rpc FetchStatement (FetchStatementRequest) returns (FetchStatementResponse);

  //RPC to update the date on which the statement is generated for a customer per month.
  rpc UpdateStatementDate (UpdateStatementDateRequest) returns (UpdateStatementDateResponse);

  // RPC to mark dispute in transaction
  rpc MarkDispute (MarkDisputeRequest) returns (MarkDisputeResponse);

  // RPC to get all disputes for a customer
  rpc GetAllDisputes (GetAllDisputesRequest) returns (GetAllDisputesResponse);

  // FetchTransactionsForEmiConversion to fetch eligible transactions for EMI conversion
  // this rpc is similar to FetchEligibleTransactionsForEmiConversion but
  // provides additional information about the transactions
  rpc FetchTransactionsForEmiConversion (FetchTransactionsForEmiConversionRequest) returns (FetchTransactionsForEmiConversionResponse);

  // RPC to fetch eligible transactions for EMI conversion
  rpc FetchEligibleTransactionsForEmiConversion (FetchEligibleTransactionsForEmiConversionRequest) returns (FetchEligibleTransactionsForEmiConversionResponse);

  // RPC to fetch loan offers for eligible transaction for EMI conversion
  rpc FetchTransactionLoanOffers (FetchTransactionLoanOffersRequest) returns (FetchTransactionLoanOffersResponse);

  // RPC to create loan
  rpc CreateLoan (CreateLoanRequest) returns (CreateLoanResponse);

  // RPC to fetch loans by status for a customer
  rpc FetchLoanByStatus (FetchLoanByStatusRequest) returns (FetchLoanByStatusResponse);

  // RPC to fetch loan by loan id for a customer
  rpc FetchLoanById (FetchLoanByIdRequest) returns (FetchLoanByIdResponse);

  // RPC to preview pre-close loan
  rpc PreviewPreCloseLoan (PreviewPreCloseLoanRequest) returns (PreviewPreCloseLoanResponse);

  // RPC to preview pre-close loan
  rpc PreCloseLoan (PreCloseLoanRequest) returns (PreCloseLoanResponse);

  // RPC to cancel loan
  rpc CancelLoan (CancelLoanRequest) returns (CancelLoanResponse);

  // RPC to set pin through partnerSDK
  rpc SetPinPartnerSdk (SetPinPartnerSdkRequest) returns (SetPinPartnerSdkResponse);

  rpc RegisterCustomerV2 (RegisterCustomerV2Request) returns (RegisterCustomerV2Response);

  // RPC to register customer through secured card onboarding
  rpc SecuredCardRegisterCustomer (SecuredCardRegisterCustomerRequest) returns (SecuredCardRegisterCustomerResponse);

  // RPC to register non-pre-approved onboarding for unsecured card
  // to be used for realtime BRE eligible user
  // API doc: https://m2p-fintech.stoplight.io/docs/federal-bank/ym50kfq3fjthn-non-pre-approved-onboarding-for-unsecured-card
  rpc NonPreApprovedUnsecuredRegisterCustomer (NonPreApprovedUnsecuredRegisterCustomerRequest) returns (NonPreApprovedUnsecuredRegisterCustomerResponse);

  // RPC to update the existing credit card details.
  rpc UpdateCreditCardDetails (UpdateCreditCardDetailsRequest) returns (UpdateCreditCardDetailsResponse);

  // RPC to reverse a CC fee
  // M2P doc: https://m2p-fintech.stoplight.io/docs/credit/735be7e7c979f-create-a-statement-partner-fee-post
  rpc FeeReversal (FeeReversalRequest) returns (FeeReversalResponse);
}

message FetchTransactionsForEmiConversionRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique customer id
  string customer_id = 2;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 3;
}

message FetchTransactionsForEmiConversionResponse {
  rpc.Status status = 1;
  repeated EligibleTransaction eligible_transactions = 2;
}

message FetchEligibleTransactionsForEmiConversionRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique customer id
  string customer_id = 2;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 3;
}

message FetchEligibleTransactionsForEmiConversionResponse {
  rpc.Status status = 1;
  repeated EligibleTransaction eligible_transactions = 2;
}

message EligibleTransaction {
  google.type.Money amount = 1;
  string external_transaction_id = 2;
  SingleEmi single_emi = 3;
  repeated GroupEmi group_emi = 4;

  message SingleEmi {
    string rule_id = 1;
    google.type.Money min_amount = 2;
    google.type.Money max_amount = 3;
  }

  message GroupEmi {
    string rule_id = 1;
    google.type.Money min_amount = 2;
    google.type.Money max_amount = 3;
  }
}

message FetchTransactionLoanOffersRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique customer id
  string customer_id = 2;
  // Loan Product Id
  string rule_id = 3;
  LoanRequestType loan_request_type = 4;
  repeated TransactionForLoanOffer transactions = 5;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 6;

  message TransactionForLoanOffer {
    google.type.Money loan_amount = 1;
    string external_transaction_id = 2;
  }
}

message FetchTransactionLoanOffersResponse {
  rpc.Status status = 1;
  repeated LoanOffer loan_offers = 2;
  string request_id = 3;

  message LoanOffer {
    AmountInfo amount_info = 1;
    InterestInfo interest_info = 2;
    int64 tenure_in_months = 3;
    Summary summary = 4;
    ProcessingFeeInfo processing_fee_info = 5;
    repeated Amortization amortizations = 6;

    message AmountInfo {
      google.type.Money loan_amount = 1;
      google.type.Money emi_amount = 2;
      google.type.Money discount_amount = 3;
    }

    message InterestInfo {
      double interest_rate = 1;
      google.type.Money total_interest = 2;
      google.type.Money broken_period_interest = 3;
    }

    message ProcessingFeeInfo {
      google.type.Money processing_fee = 1;
      google.type.Money processing_fee_tax = 2;
    }

    message Summary {
      google.type.Money total_principal = 1;
      google.type.Money total_principal_paid = 2;
      google.type.Money total_fee = 3;
      google.type.Money total_tax = 4;
      google.type.Money total_expected_repayment = 5;
    }
  }
}

message Amortization {
  int64 installment_number = 1;
  google.type.Date due_date = 2;
  PrincipalInfo principal_info = 3;
  InterestInfo interest_info = 4;
  FeeInfo fee_info = 5;
  Summary summary = 6;
  google.type.Money tax = 7;

  message PrincipalInfo {
    google.type.Money principal = 1;
    google.type.Money principal_paid = 2;
    google.type.Money principal_outstanding = 3;
    google.type.Money closing_principal_amount = 4;
  }

  message InterestInfo {
    google.type.Money interest = 1;
    google.type.Money interest_paid = 2;
    google.type.Money interest_outstanding = 3;
    google.type.Money interest_waived = 4;
  }

  message FeeInfo {
    google.type.Money fee = 1;
    google.type.Money fee_paid = 2;
    google.type.Money fee_outstanding = 3;
    google.type.Money fee_waived = 4;
  }

  message Summary {
    google.type.Money total_due = 1;
    google.type.Money total_paid = 2;
    google.type.Money total_outstanding = 3;
    google.type.Money total_waived = 4;
  }
}

message CreateLoanRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique customer id
  string customer_id = 2;
  // Unique request id
  string request_id = 3;
  // total number of months
  int64 tenure_in_months = 4;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 5;
}

message CreateLoanResponse {
  rpc.Status status = 1;
  // loan id provided by vendor
  string vendor_loan_id = 2;
}

message FetchLoanByStatusRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique customer id
  string customer_id = 2;
  // status of the loan
  LoanStatus loan_status = 3;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 4;
}

message FetchLoanByStatusResponse {
  rpc.Status status = 1;
  repeated LoanInfo loan_info = 2;
}

message FetchLoanByIdRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique customer id
  string customer_id = 2;
  // Loan ID provided by vendor
  string vendor_loan_id = 3;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 4;
}

message FetchLoanByIdResponse {
  rpc.Status status = 1;
  LoanInfo loan_info = 2;
}

message LoanInfo {
  // Loan ID provided by vendor
  string vendor_loan_id = 1;
  int64 tenure_in_months = 2;
  LoanStatus loan_status = 3;
  google.type.Date disbursed_date = 4;
  AmountInfo amount_info = 5;
  ApplicableActions applicable_actions = 6;
  PrincipalInfo principal_info = 7;
  RepaymentInfo repayment_info = 8;
  InterestInfo interest_info = 9;
  FeeInfo fee_info = 10;
  Summary summary = 11;
  repeated Amortization amortizations = 12;
  repeated Transaction transactions = 13;

  message AmountInfo {
    google.type.Money loan_amount = 1;
    google.type.Money emi_amount = 2;
  }

  message ApplicableActions {
    bool is_loan_cancel_applicable = 1;
    bool is_loan_reschedule_applicable = 2;
    bool is_loan_pre_closure_applicable = 3;
  }

  message PrincipalInfo {
    google.type.Money total_principal = 1;
    google.type.Money principal_overdue = 2;
    google.type.Money principal_outstanding = 3;
  }

  message RepaymentInfo {
    google.type.Money total_expected_repayment = 1;
    int64 number_of_due_repayments = 2;
    int64 number_of_paid_repayments = 3;
  }

  message InterestInfo {
    double interest_rate = 1;
    google.type.Money total_interest = 2;
    google.type.Money interest_outstanding = 3;
    google.type.Money interest_overdue = 4;
    google.type.Money interest_paid = 5;
    google.type.Money interest_waived = 6;
    google.type.Money broken_period_interest = 7;
  }

  message FeeInfo {
    google.type.Money total_fee = 1;
    google.type.Money fee_outstanding = 2;
    google.type.Money fee_overdue = 3;
    google.type.Money total_fee_paid = 4;
    google.type.Money fee_waived = 5;
    google.type.Money processing_fee = 6;
    google.type.Money processing_fee_tax = 7;
  }

  message Summary {
    google.type.Money total_tax = 1;
    google.type.Money total_repayment = 2;
    google.type.Money total_outstanding = 3;
    google.type.Money total_overdue = 4;
    google.type.Money total_waived_off = 5;
  }

  message Transaction {
    string vendor_external_transaction_id = 1;
    google.type.Money transaction_amount = 2;
    google.type.Money amount = 3;
    string description = 4;
    google.protobuf.Timestamp transaction_timestamp = 5;
    string merchant_category_code = 6;
    firefly.enums.TransactionType transaction_type = 7;
    string sub_transaction_type = 8;
    string transaction_origin = 9;
    string merchant_name = 10;
  }
}

message FetchStatementRequest {
  vendorgateway.RequestHeader header = 1;
  string customer_id = 2;
  google.type.Date statement_date = 3;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 4;
}

message FetchStatementResponse {
  rpc.Status status = 1;
  repeated CustomerClosingDetails statements = 2;
}

message RepayLoanAmountRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique customer id
  string entity_id = 2;
  string business_entity_id = 3;
  string business_name = 4;
  google.type.Money amount = 5;
  firefly.enums.TransactionType transaction_type = 6;
  TransactionOrigin transaction_origin = 7;
  string product_id = 8;
  string external_transaction_id = 9;
  string transaction_description = 10;
  string other_party_id = 11;
  string other_party_name = 12;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 13;
}

message RepayLoanAmountResponse {
  rpc.Status status = 1;
  int32 transaction_id = 2;
}

message FetchUnbilledTransactionsRequest {
  vendorgateway.RequestHeader header = 1;
  string customer_id = 2;
  google.type.Date start_date = 3;
  google.type.Date end_date = 4;
  Pagination pagination_info = 5;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 6;
}

message FetchUnbilledTransactionsResponse {
  rpc.Status status = 1;
  repeated Transaction transactions = 2;
  Pagination pagination_info = 3;
}

// Request message for set pin RPC
message SetPinRequest {
  vendorgateway.RequestHeader header = 1;
  string customer_id = 2;
  string pin = 3;
  string kit_number = 4;
  // Due to compliance regulations we cannot have raw expiry, we will be using the
  // expiry token to pass the expiry which will be converted to actual expiry date by tokenizer
  google.type.Date expiry_date = 5 [deprecated = true];
  google.type.Date dob = 6;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 7;
  string expiry_token = 8;
}

message SetPinResponse {
  rpc.Status status = 1;
}

// Request message for fetchTransactionStatus API integration
message FetchTransactionStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string entity_id = 2;
  google.type.Date start_date = 3;
  google.type.Date end_date = 4;
  Pagination pagination_info = 5;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 6;
}

message FetchTransactionStatusResponse {
  rpc.Status status = 1;
  repeated Transaction transactions = 2;
  Pagination pagination_info = 3;
}

// Request message for PreviewPreCloseLoan API integration
message PreviewPreCloseLoanRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique customer id
  string customer_id = 2;
  // loan id provided by vendor
  string vendor_loan_id = 3;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 4;
}

message PreviewPreCloseLoanResponse {
  rpc.Status status = 1;
  PreCloseLoanInfo pre_close_loan_info = 2;
}

message PreCloseLoanInfo {
  google.type.Money total_payable_amount = 1;
  google.type.Money principal = 2;
  google.type.Money interest = 3;
  google.type.Money interest_tax = 4;
  ProcessingFeeInfo processing_fee_info = 5;
  PreClosureFeeInfo pre_closure_fee_info = 6;
  TotalFeeInfo total_fee_info = 7;

  message ProcessingFeeInfo {
    google.type.Money processing_fee = 1;
    google.type.Money processing_fee_tax = 2;
  }

  message PreClosureFeeInfo {
    google.type.Money pre_closure_fee = 1;
    google.type.Money pre_closure_fee_tax = 2;
  }

  message TotalFeeInfo {
    google.type.Money total_fee = 1;
    google.type.Money total_tax = 2;
  }
}

// Request message for PreviewPreCloseLoan API integration
message PreCloseLoanRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique customer id
  string customer_id = 2;
  // loan id provided by vendor
  string vendor_loan_id = 3;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 4;
}

message PreCloseLoanResponse {
  rpc.Status status = 1;
  PreCloseLoanInfo pre_close_loan_info = 2;
}

// Request message for CancelLoan API integration
message CancelLoanRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique customer id
  string customer_id = 2;
  // loan id provided by vendor
  string vendor_loan_id = 3;
  CancelLoanChargesAction charges_action = 4;
  CancelLoanChargesAction interest_action = 5;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 6;
}

message CancelLoanResponse {
  rpc.Status status = 1;
  google.type.Money interest = 2;
  google.type.Money interest_tax = 3;
  google.type.Money fee = 4;
  google.type.Money fee_tax = 5;
  google.type.Money total_payable_amount = 6;
  RefundDetails refund_details = 7;

  message RefundDetails {
    google.type.Money total_refund_amount = 1;
    google.type.Money principal = 2;
    google.type.Money interest = 3;
    google.type.Money interest_tax = 5;
    google.type.Money fee = 6;
    google.type.Money fee_tax = 7;
  }
}


// Request messge for replacing card for a user
message ReplaceCardRequest {
  vendorgateway.RequestHeader header = 1;
  // Customer id
  string customer_id = 2;
  string old_kit_number = 3;
  string new_kit_number = 4;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 5;
}

message ReplaceCardResponse {
  rpc.Status status = 1;
}

// Request message for fetching monthly statement of a customer
message FetchMonthlyStatementRequest {
  vendorgateway.RequestHeader header = 1;
  // Entity id is the unique id of a customer registered for credit card
  string entity_id = 2;
  google.type.Date statement_date = 3;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 4;
  // Index of the statement to be fetched in case there are multiple stmts per month. by default this is ignored
  int32 statement_number = 5;
}

message FetchMonthlyStatementResponse {
  rpc.Status status = 1;
  repeated Transaction transactions = 2;
  CustomerClosingDetails customer_closing_details = 3;
}

message CustomerClosingDetails {
  google.type.Money last_statement_balance = 1;
  google.type.Money current_statement_amount = 2;
  google.type.Money total_credit = 3;
  google.type.Money total_debit = 4;
  google.type.Money total_amount_from_atm = 5;
  google.type.Money purchase = 6;
  google.type.Money min_due_amount = 7;
  google.type.Date statement_date = 8;
  google.type.Date customer_due_date = 9;
  google.type.Date final_due_date = 10;
  int32 reward_points_for_last_statement = 11;
  int32 reward_points_lapsed = 12;
  int32 reward_points_accumulated = 13;
  int32 reward_points_total = 14;
  string statement_file_name = 15;
  google.type.Money after_statement_limit = 16;
}


// Request message for FetchEncodedStatement API
message FetchEncodedStatementRequest {
  vendorgateway.RequestHeader header = 1;
  // EntityId refers to customerId
  string entity_id = 2;
  api.typesv2.Date statement_month = 3;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 4;
}

message FetchEncodedStatementResponse {
  rpc.Status status = 1;
  string encoded_statement = 2;
}

// Request message for UpdateCustomer API integration
message UpdateCustomerRequest {
  vendorgateway.RequestHeader header = 1;
  string customer_id = 2;
  CommunicationInfo communication_info = 3;
  repeated api.typesv2.AddressWithType address_info = 4;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 5;
}

message UpdateCustomerResponse {
  rpc.Status status = 1;
}

// Request message for GenerateCVV API integration
message GenerateCVVRqeuest {
  vendorgateway.RequestHeader header = 1;
  string customer_id = 2;
  string kit_number = 3;
  // Due to compliance regulations we cannot have raw expiry, we will be using the
  // expiry token to pass the expiry which will be converted to actual expiry date by tokenizer
  google.type.Date expiry_date = 4 [deprecated = true];
  google.type.Date dob = 5;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 6;
  string expiry_token = 7;
}

message GenerateCVVResponse {
  rpc.Status status = 1;
  string cvv = 2;
}

// Request message for fetch due amout API
message FetchDueAmountRequest {
  vendorgateway.RequestHeader header = 1;
  string customer_id = 2;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 3;
}

message FetchDueAmountResponse {
  rpc.Status status = 1;
  google.type.Money minimum_due_amount = 2;
  google.type.Money total_due_amount = 3;
  google.type.Money payment_made = 4;
  google.type.Money non_billed_amount = 5;
  google.type.Money interest_accumulated = 6;
  api.typesv2.Date due_date = 7;
  EmiDetails emi_details = 8;
  google.type.Money unpaid_min_due = 9;
  google.type.Money unpaid_total_due = 10;
  google.type.Money total_outstanding_amount = 11;
}

message EmiDetails {
  google.type.Money emi_total_amount = 1;
  google.type.Money principal = 2;
  google.type.Money interest = 3;
  google.type.Money other_charges = 4;
}

// Request message for set credit limit API
message SetCreditLimitRequest {
  vendorgateway.RequestHeader header = 1;
  // unique customer id for a user
  string entity_id = 2;
  google.type.Money amount = 3;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 8;
}

message SetCreditLimitResponse {
  rpc.Status status = 1;
}

// Request message for fetch transactions API
message FetchTransactionsRequest {
  vendorgateway.RequestHeader header = 1;
  string entity_id = 2;
  api.typesv2.Date start_date = 3;
  api.typesv2.Date end_date = 4;
  int32 page_number = 5;
  int32 page_size = 6;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 7;
}

// Response message for fetch transactions API
message FetchTransactionsResponse {
  rpc.Status status = 1;
  repeated Transaction transactions = 2;
  Pagination pagination_info = 3;
}

message Transaction {
  firefly.enums.TransactionType transaction_type = 1;
  // Whether it is a credit transfer or a debit transfer
  api.typesv2.TransactionTransferType transaction_transfer_type = 2;
  google.protobuf.Timestamp transaction_time = 3;
  string transaction_reference_id = 4;
  string business_id = 5;
  BeneficiaryDetails beneficiary_details = 6;
  string transaction_description = 7;
  firefly.enums.TransactionOrigin transaction_origin = 8;
  firefly.enums.TransactionStatus transaction_status = 9;
  string customer_wallet_name = 10;
  string external_transaction_id = 11;
  string customer_retrieval_reference_number = 12;
  string authorization_code = 13;
  string bill_reference_number = 14;
  string bank_transaction_id = 15;
  firefly.enums.CardNetworkType network_type = 16;
  string kit_number = 17;
  string sor_transaction_id = 18;
  string transaction_currency_code = 19;
  string forex_transaction_conversion_details = 20;
  string other_conversion_details = 21;
  string disputed_dto = 22;
  string dispute_reference_number = 23;
  string account_number = 24;
  string acquirer_id = 25;
  string merchant_category_code = 26;
  TransactionMonetaryDetails monetary_details = 27;
  string internal_transaction_id = 28;
  MerchantInformation merchant_information = 29;
  string stan_number = 30;
  AuthorizationStatus authorization_status = 31;
  TransactionSubCategory transaction_sub_category = 32;
}

message MerchantInformation {
  string merchant_id = 1;
  string merchant_name = 2;
  string merchant_location = 3;
  string merchant_terminal_id = 4;
}

message TransactionMonetaryDetails {
  google.type.Money amount = 1;
  google.type.Money available_balance = 2;
  google.type.Money converted_amount = 3;
  string limit_currency_code = 4;
  google.type.Money balance = 5;
  google.type.Money after_transaction_limit = 6;
}

message Pagination {
  bool is_list = 1;
  int32 page_size = 2;
  int32 page_number = 3;
  int32 total_pages = 4;
  int32 total_elements = 5;
}

message BeneficiaryDetails {
  string beneficiary_name = 1;
  string beneficiary_type = 2;
  string beneficiary_id = 3;
  string other_party_name = 4;
  string other_party_id = 5;
  string beneficiary_wallet_name = 6;
}

// Request message for lock/unlock API
message ManageLockRequest {
  vendorgateway.RequestHeader header = 1;
  string customer_id = 2;
  string kit_no = 3;
  LockAction flag = 4;
  string reason = 5;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 6;
}

message ManageLockResponse {
  rpc.Status status = 1;
}

// Request body for FetchBalance API of CC
message FetchBalanceRequest {
  vendorgateway.RequestHeader header = 1;
  string customer_id = 2;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 3;
}

message FetchBalanceResponse {
  rpc.Status status = 1;
  repeated BalanceDetails balance_details = 2;
}

message BalanceDetails {
  google.type.Money balance = 1;
  google.type.Money lien_balance = 2;
  string product_id = 3;
}

//Request body for the GetCardDetails API of M2P
message GetCardListRequest {
  vendorgateway.RequestHeader header = 1;
  string customer_id = 2;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 3;
}

message GetCardListResponse {
  rpc.Status status = 1;
  repeated CardDetails card_details = 2;
}

message GetUnmaskedCardDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  string customer_id = 2;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 3;
}

message GetUnmaskedCardDetailsResponse {
  rpc.Status status = 1;
  repeated CardDetails card_details = 2;
  google.type.Date date_of_birth = 3;
  api.typesv2.common.Name name = 4;
}

message CardDetails {
  // Due to compliance regulations we cannot have raw expiry, we will be using the
  // expiry token to pass the expiry which will be converted to actual expiry date by tokenizer
  api.typesv2.Date expiry_date = 1 [deprecated = true];
  string kit_number = 2;
  api.typesv2.CardMaterial card_material = 3;
  firefly.enums.CardNetworkType network_type = 4;
  string card_number = 5;
  firefly.enums.CardStatus card_status = 6;
  string expiry_token = 7;
}

//Request body for the RegisterCustomer API of M2P
message RegisterCustomerRequest {
  vendorgateway.RequestHeader header = 1;
  // Entity id is the unique customer id for a credit card user
  string entity_id = 2;
  string entity_type = 3;
  string business_type = 4;
  string business_id = 5;
  api.typesv2.common.Name name = 6;
  api.typesv2.Gender gender = 7;
  bool is_NRI_customer = 8;
  bool is_minor = 9;
  bool is_dependant = 10;
  EmploymentDetails employment_details = 11;
  api.typesv2.MaritalStatus marital_status = 12;
  CreditInfo credit_info = 13;
  repeated KitInfo kit_info = 14;
  repeated api.typesv2.AddressWithType address_info = 15;
  repeated CommunicationInfo communication_info = 16;
  repeated KycInfo kyc_info = 17;
  repeated DateInfo date_info = 18;
  repeated api.typesv2.Nominee nominee_info = 19;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 20;
}

message EmploymentDetails {
  api.typesv2.EmploymentIndustry employment_industry = 1;
  api.typesv2.EmploymentType employment_type = 2;
}

message RegisterCustomerResponse {
  rpc.Status status = 1;
  string kit_no = 2;
}

message CreditInfo {
  api.typesv2.CreditCardCategory card_category = 1;
  google.type.Money credit_limit = 2;
  int32 statement_date = 3;
}

message KitInfo {
  string kit_no = 1;
  api.typesv2.CardMaterial card_material = 2;
  api.typesv2.CardType card_type = 3;
  CardRegStatus card_reg_status = 4;
  google.type.Date exp_date = 5;
}

message CommunicationInfo {
  api.typesv2.common.PhoneNumber contact_number = 1;
  string notification = 2;
  string email_id = 3;
  api.typesv2.common.PhoneNumber additional_contact_number = 4;
  string additional_email_id = 5;
}

message KycInfo {
  string kyc_ref_no = 1;
  api.typesv2.DocumentType document_type = 2;
  string document_no = 3;
  google.type.Date document_expiry = 4;
}

message DateInfo {
  DateType date_type = 1;
  google.type.Date date = 2;
}

message SetPreferencesRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique customer id
  string entity_id = 2;
  // Configuration to add disallow config and edit it
  DisallowedRuleConfig disallowed_rule_config = 3;
  // Limit configuration for specific transaction type
  LimitConfig limit_config = 4;
  // Overall limit configuration
  OverallLimitConfig overall_limit_config = 5;
  // Enable or disable International
  api.typesv2.common.BooleanEnum international = 6;
  // Enable or disable contactless
  api.typesv2.common.BooleanEnum contactless = 7;
  // Enable or disable ATM transactions
  api.typesv2.common.BooleanEnum atm = 8;
  // Enable or disable POS transactions
  api.typesv2.common.BooleanEnum pos = 9;
  // Enable or disable ECOM transactions
  api.typesv2.common.BooleanEnum ecom = 10;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 11;
}

message DisallowedRuleConfig {
  CardRuleModify rule_modify = 1;
  repeated TransactionOrigin txn_origin = 2;
  // Merchant category codes
  repeated string mcc_grouping = 3;
  repeated string country_grouping = 4;
}

message LimitConfig {
  TransactionType txn_type = 1;
  google.type.Money daily_limit_value = 2;
  uint32 daily_limit_cnt = 3;
  google.type.Money min_amount = 4;
  google.type.Money max_amount = 5;
  CardUsageLocationType card_usage_location_type = 6;
}

message OverallLimitConfig {
  google.type.Money daily_limit_value = 1;
  uint32 daily_limit_cnt = 2;
}

message SetPreferencesResponse {
  rpc.Status status = 1;
}

message RequestPhysicalCardRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique customer id
  string entity_id = 2;
  string kit_no = 3;
  // Object with Address Array to update customer's card delivery address
  AddressDto address_dto = 4;
  CardMaterial card_material = 5;
  api.typesv2.common.Name card_printing_name = 6;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 7;
}

message AddressDto {
  repeated api.typesv2.AddressWithType address = 1;
}

message RequestPhysicalCardResponse {
  rpc.Status status = 1;
}

message UpgradeLimitRequest {
  vendorgateway.RequestHeader header = 1;
  string entity_id = 2;
  google.type.Money amount = 3;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 4;
}

message UpgradeLimitResponse {
  rpc.Status status = 1;
}

message FetchLimitRequest {
  vendorgateway.RequestHeader header = 1;
  string entity_id = 2;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 3;
}

message FetchLimitResponse {
  rpc.Status status = 1;
  FetchLimitResponseResult result = 2;
}

message FetchLimitResponseResult {
  string entity_id = 1;
  string kit_no = 2;
  // Details on actual, available and used limits
  CardLimitDetails limitDetails = 3;
}

message CardLimitDetails {
  google.type.Money limit_actual = 1;
  google.type.Money limit_available = 2;
  google.type.Money limit_utilized = 3;
}

message FetchPreferenceRequest {
  vendorgateway.RequestHeader header = 1;
  string entity_id = 2;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 3;
}

message FetchPreferenceResponse {
  rpc.Status status = 1;
  FetchPreferenceResponseResult result = 2;
}

message FetchPreferenceResponseResult {
  DisallowedRuleConfig disallowed_rule_config = 1;
  repeated LimitConfig limit_config = 2;
  OverallLimitConfig overall_limit_config = 3;
  // Enable or disable International
  bool international = 6;
  // Enable or disable contactless
  bool contactless = 7;
  // Enable or disable ATM transactions
  bool atm = 8;
  // Enable or disable POS transactions
  bool pos = 9;
  // Enable or disable ECOM transacations
  bool ecom = 10;
}

message UpdateStatementDateRequest {
  vendorgateway.RequestHeader header = 1;
  string entity_id = 2;
  // Statement date refers to the day of the month on which the statement is generated.
  int32 statement_date = 3;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 4;
}

message UpdateStatementDateResponse {
  rpc.Status status = 1;
}

message UploadFileRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Remote path of vendor sftp server where file will be uploaded
  string remote_path = 2;
  // URL where the file is hosted and needs to be downloaded
  string file_url = 3;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 4;
}

message UploadFileResponse {
  rpc.Status status = 1;
}

message MarkDisputeRequest {
  vendorgateway.RequestHeader header = 1;

  string entity_id = 2;
  google.type.Money amount = 3;
  string reason = 4;
  string description = 5;
  // External transaction id
  string ext_txn_id = 6;
  firefly.enums.DisputeType dispute_type = 7;
  string url = 8;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 9;
}

message MarkDisputeResponse {
  rpc.Status status = 1;
  string dispute_ref_id = 2;
}

message GetAllDisputesRequest {
  vendorgateway.RequestHeader header = 1;
  string entity_id = 2;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 3;
}

message Dispute {
  string dispute_ref = 1;
  string entity_id = 2;
  google.type.Money amount = 3;
  string reason = 4;
  string description = 5;
  // External transaction id
  string ext_txn_id = 6;
  firefly.enums.DisputeType dispute_type = 7;
  string url = 8;
  firefly.enums.DisputeState dispute_state = 9;
}

message GetAllDisputesResponse {
  rpc.Status status = 1;
  repeated Dispute disputes = 2;
}

message SetPinPartnerSdkRequest {
  vendorgateway.RequestHeader header = 1;
  string cred_block = 2;
  string device_id = 3;
  api.typesv2.common.Platform platform = 4;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 5;
}

message SetPinPartnerSdkResponse {
  rpc.Status status = 1;
}

message RegisterCustomerV2Request {
  vendorgateway.RequestHeader header = 1;
  // Entity id is the unique customer id for a credit card user
  string entity_id = 2;
  string entity_type = 3;
  string business_type = 4;
  string business_id = 5;
  api.typesv2.common.Name name = 6;
  api.typesv2.Gender gender = 7;
  bool is_NRI_customer = 8;
  bool is_minor = 9;
  bool is_dependant = 10;
  EmploymentDetails employment_details = 11;
  api.typesv2.MaritalStatus marital_status = 12;
  CreditInfo credit_info = 13;
  repeated KitInfo kit_info = 14;
  repeated api.typesv2.AddressWithType address_info = 15;
  repeated CommunicationInfo communication_info = 16;
  repeated KycInfo kyc_info = 17;
  repeated DateInfo date_info = 18;
  repeated api.typesv2.Nominee nominee_info = 19;
  string customer_id = 20;
  google.type.Date card_open_date = 21;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 22;
}

message RegisterCustomerV2Response {
  rpc.Status status = 1;
  string kit_no = 2;
}

message NonPreApprovedUnsecuredRegisterCustomerRequest {
  vendorgateway.RequestHeader header = 1;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 2;
  string entity_id = 3;
  string entity_type = 4;
  string customer_id = 5;
  api.typesv2.common.Name name = 6;
  api.typesv2.Gender gender = 7;
  google.type.Date card_open_date = 8;
  CreditInfo credit_info = 9;
  repeated api.typesv2.AddressWithType address_info = 10;
  repeated CommunicationInfo communication_info = 11;
  repeated KycInfo kyc_info = 12;
  repeated DateInfo date_info = 13;

  // optional field
  bool is_NRI_customer = 14;
  // optional field
  bool is_minor = 15;
  // optional field
  bool is_dependant = 16;
  EmploymentDetails employment_details = 17;
  api.typesv2.MaritalStatus marital_status = 18;
  repeated KitInfo kit_infos = 19;
}

message NonPreApprovedUnsecuredRegisterCustomerResponse {
  rpc.Status status = 1;
  string kit_number = 2;
}

message SecuredCardRegisterCustomerRequest {
  vendorgateway.RequestHeader header = 1;
  // Entity id is the unique customer id for a credit card user
  string entity_id = 2;
  string entity_type = 3;
  string business_type = 4;
  string business_id = 5;
  api.typesv2.common.Name name = 6;
  api.typesv2.Gender gender = 7;
  bool is_NRI_customer = 8;
  bool is_minor = 9;
  bool is_dependant = 10;
  EmploymentDetails employment_details = 11;
  api.typesv2.MaritalStatus marital_status = 12;
  CreditInfo credit_info = 13;
  repeated KitInfo kit_info = 14;
  repeated api.typesv2.AddressWithType address_info = 15;
  repeated CommunicationInfo communication_info = 16;
  repeated KycInfo kyc_info = 17;
  repeated DateInfo date_info = 18;
  repeated api.typesv2.Nominee nominee_info = 19;
  string customer_id = 20;
  google.type.Date card_open_date = 21;
  string fd_number = 22;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 23;
}

message SecuredCardRegisterCustomerResponse {
  enum Status {
    STATUS_UNSPECIFIED = 0;
    // status when the collateral has been closed or the amount FD amount mismatches the guaranteed amount
    // and hence the secured card creation cannot proceed.
    STATUS_SECURED_CARD_COLLATERAL_INVALIDATED = 101;
  }
  rpc.Status status = 1;
  string kit_no = 2;
}

message UpdateCreditCardDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  string kit_number = 2;
  string customer_id = 3;
  google.type.Money updated_card_limit = 4;
  google.type.Date cc_open_date = 5;
  CardStatus card_status = 6;
  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 7;
}

message UpdateCreditCardDetailsResponse {
  rpc.Status status = 1;
}

// M2P doc: https://m2p-fintech.stoplight.io/docs/credit/735be7e7c979f-create-a-statement-partner-fee-post
// NOTE: On the actual M2P API, for a valid transaction ID to be reversed, the reversal will happen & if the API gets hits again for the same transaction ID, the response extTxnId will be null and the reversal will not happen again to the customer
// If the M2P API get hits with a non-existing transaction ID, it will still reverse the amount to the user as on M2P's side there is no such validation check(they said for cases like cashback or rewards), & if the API gets hit again with the same payload, it'll return a 500 response code
message FeeReversalRequest {
  vendorgateway.RequestHeader header = 1 [(validate.rules).message.required = true];
  // External Transaction ID of the transaction to be reversed
  string ext_txn_id = 2 [(validate.rules).string.min_len = 1];
  // Entity ID of the user in M2P database
  string entity_id = 3 [(validate.rules).string.min_len = 1];
  // Fee reversal transaction description
  string description = 4;
  // Amount to be credited back
  google.type.Money amount = 5 [(validate.rules).message.required = true];

  // common header across all vendor request to support multi tenant headers in vg
  CcVendorRequestHeader cc_vendor_request_header = 6;
}

message FeeReversalResponse {
  rpc.Status status = 1;
  // Vendor transaction ID of the reversed transaction
  string vendor_txn_id = 2;
  // Vendor External transaction ID of the reversed transaction
  string vendor_ext_txn_id = 3;
}

// common header across all vendor request to support multi tenant headers in vg
// This header gets populated through the Cc vg interceptor for all outgoing requests
// to Vg from Lending and firefly worker.
message CcVendorRequestHeader {
  CardTenantType card_tenant_type = 1;
}
