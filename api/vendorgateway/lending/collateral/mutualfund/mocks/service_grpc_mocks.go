// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/lending/collateral/mutualfund/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	mutualfund "github.com/epifi/gamma/api/vendorgateway/lending/collateral/mutualfund"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockLoanAgainstMutualFundClient is a mock of LoanAgainstMutualFundClient interface.
type MockLoanAgainstMutualFundClient struct {
	ctrl     *gomock.Controller
	recorder *MockLoanAgainstMutualFundClientMockRecorder
}

// MockLoanAgainstMutualFundClientMockRecorder is the mock recorder for MockLoanAgainstMutualFundClient.
type MockLoanAgainstMutualFundClientMockRecorder struct {
	mock *MockLoanAgainstMutualFundClient
}

// NewMockLoanAgainstMutualFundClient creates a new mock instance.
func NewMockLoanAgainstMutualFundClient(ctrl *gomock.Controller) *MockLoanAgainstMutualFundClient {
	mock := &MockLoanAgainstMutualFundClient{ctrl: ctrl}
	mock.recorder = &MockLoanAgainstMutualFundClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanAgainstMutualFundClient) EXPECT() *MockLoanAgainstMutualFundClientMockRecorder {
	return m.recorder
}

// CheckStatus mocks base method.
func (m *MockLoanAgainstMutualFundClient) CheckStatus(ctx context.Context, in *mutualfund.CheckStatusRequest, opts ...grpc.CallOption) (*mutualfund.CheckStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckStatus", varargs...)
	ret0, _ := ret[0].(*mutualfund.CheckStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStatus indicates an expected call of CheckStatus.
func (mr *MockLoanAgainstMutualFundClientMockRecorder) CheckStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStatus", reflect.TypeOf((*MockLoanAgainstMutualFundClient)(nil).CheckStatus), varargs...)
}

// GetCasDocument mocks base method.
func (m *MockLoanAgainstMutualFundClient) GetCasDocument(ctx context.Context, in *mutualfund.GetCasDocumentRequest, opts ...grpc.CallOption) (*mutualfund.GetCasDocumentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCasDocument", varargs...)
	ret0, _ := ret[0].(*mutualfund.GetCasDocumentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCasDocument indicates an expected call of GetCasDocument.
func (mr *MockLoanAgainstMutualFundClientMockRecorder) GetCasDocument(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCasDocument", reflect.TypeOf((*MockLoanAgainstMutualFundClient)(nil).GetCasDocument), varargs...)
}

// GetTransactionStatus mocks base method.
func (m *MockLoanAgainstMutualFundClient) GetTransactionStatus(ctx context.Context, in *mutualfund.GetTransactionStatusRequest, opts ...grpc.CallOption) (*mutualfund.GetTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionStatus", varargs...)
	ret0, _ := ret[0].(*mutualfund.GetTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionStatus indicates an expected call of GetTransactionStatus.
func (mr *MockLoanAgainstMutualFundClientMockRecorder) GetTransactionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionStatus", reflect.TypeOf((*MockLoanAgainstMutualFundClient)(nil).GetTransactionStatus), varargs...)
}

// InvestorConsent mocks base method.
func (m *MockLoanAgainstMutualFundClient) InvestorConsent(ctx context.Context, in *mutualfund.InvestorConsentRequest, opts ...grpc.CallOption) (*mutualfund.InvestorConsentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InvestorConsent", varargs...)
	ret0, _ := ret[0].(*mutualfund.InvestorConsentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InvestorConsent indicates an expected call of InvestorConsent.
func (mr *MockLoanAgainstMutualFundClientMockRecorder) InvestorConsent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvestorConsent", reflect.TypeOf((*MockLoanAgainstMutualFundClient)(nil).InvestorConsent), varargs...)
}

// InvokeRevokeLien mocks base method.
func (m *MockLoanAgainstMutualFundClient) InvokeRevokeLien(ctx context.Context, in *mutualfund.InvokeRevokeLienRequest, opts ...grpc.CallOption) (*mutualfund.InvokeRevokeLienResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InvokeRevokeLien", varargs...)
	ret0, _ := ret[0].(*mutualfund.InvokeRevokeLienResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InvokeRevokeLien indicates an expected call of InvokeRevokeLien.
func (mr *MockLoanAgainstMutualFundClientMockRecorder) InvokeRevokeLien(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvokeRevokeLien", reflect.TypeOf((*MockLoanAgainstMutualFundClient)(nil).InvokeRevokeLien), varargs...)
}

// SubmitCasSummary mocks base method.
func (m *MockLoanAgainstMutualFundClient) SubmitCasSummary(ctx context.Context, in *mutualfund.SubmitCasSummaryRequest, opts ...grpc.CallOption) (*mutualfund.SubmitCasSummaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitCasSummary", varargs...)
	ret0, _ := ret[0].(*mutualfund.SubmitCasSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitCasSummary indicates an expected call of SubmitCasSummary.
func (mr *MockLoanAgainstMutualFundClientMockRecorder) SubmitCasSummary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitCasSummary", reflect.TypeOf((*MockLoanAgainstMutualFundClient)(nil).SubmitCasSummary), varargs...)
}

// SubmitLien mocks base method.
func (m *MockLoanAgainstMutualFundClient) SubmitLien(ctx context.Context, in *mutualfund.SubmitLienRequest, opts ...grpc.CallOption) (*mutualfund.SubmitLienResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitLien", varargs...)
	ret0, _ := ret[0].(*mutualfund.SubmitLienResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitLien indicates an expected call of SubmitLien.
func (mr *MockLoanAgainstMutualFundClientMockRecorder) SubmitLien(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitLien", reflect.TypeOf((*MockLoanAgainstMutualFundClient)(nil).SubmitLien), varargs...)
}

// ValidateLien mocks base method.
func (m *MockLoanAgainstMutualFundClient) ValidateLien(ctx context.Context, in *mutualfund.ValidateLienRequest, opts ...grpc.CallOption) (*mutualfund.ValidateLienResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValidateLien", varargs...)
	ret0, _ := ret[0].(*mutualfund.ValidateLienResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateLien indicates an expected call of ValidateLien.
func (mr *MockLoanAgainstMutualFundClientMockRecorder) ValidateLien(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateLien", reflect.TypeOf((*MockLoanAgainstMutualFundClient)(nil).ValidateLien), varargs...)
}

// MockLoanAgainstMutualFundServer is a mock of LoanAgainstMutualFundServer interface.
type MockLoanAgainstMutualFundServer struct {
	ctrl     *gomock.Controller
	recorder *MockLoanAgainstMutualFundServerMockRecorder
}

// MockLoanAgainstMutualFundServerMockRecorder is the mock recorder for MockLoanAgainstMutualFundServer.
type MockLoanAgainstMutualFundServerMockRecorder struct {
	mock *MockLoanAgainstMutualFundServer
}

// NewMockLoanAgainstMutualFundServer creates a new mock instance.
func NewMockLoanAgainstMutualFundServer(ctrl *gomock.Controller) *MockLoanAgainstMutualFundServer {
	mock := &MockLoanAgainstMutualFundServer{ctrl: ctrl}
	mock.recorder = &MockLoanAgainstMutualFundServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanAgainstMutualFundServer) EXPECT() *MockLoanAgainstMutualFundServerMockRecorder {
	return m.recorder
}

// CheckStatus mocks base method.
func (m *MockLoanAgainstMutualFundServer) CheckStatus(arg0 context.Context, arg1 *mutualfund.CheckStatusRequest) (*mutualfund.CheckStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckStatus", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.CheckStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckStatus indicates an expected call of CheckStatus.
func (mr *MockLoanAgainstMutualFundServerMockRecorder) CheckStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckStatus", reflect.TypeOf((*MockLoanAgainstMutualFundServer)(nil).CheckStatus), arg0, arg1)
}

// GetCasDocument mocks base method.
func (m *MockLoanAgainstMutualFundServer) GetCasDocument(arg0 context.Context, arg1 *mutualfund.GetCasDocumentRequest) (*mutualfund.GetCasDocumentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCasDocument", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.GetCasDocumentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCasDocument indicates an expected call of GetCasDocument.
func (mr *MockLoanAgainstMutualFundServerMockRecorder) GetCasDocument(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCasDocument", reflect.TypeOf((*MockLoanAgainstMutualFundServer)(nil).GetCasDocument), arg0, arg1)
}

// GetTransactionStatus mocks base method.
func (m *MockLoanAgainstMutualFundServer) GetTransactionStatus(arg0 context.Context, arg1 *mutualfund.GetTransactionStatusRequest) (*mutualfund.GetTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionStatus", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.GetTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionStatus indicates an expected call of GetTransactionStatus.
func (mr *MockLoanAgainstMutualFundServerMockRecorder) GetTransactionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionStatus", reflect.TypeOf((*MockLoanAgainstMutualFundServer)(nil).GetTransactionStatus), arg0, arg1)
}

// InvestorConsent mocks base method.
func (m *MockLoanAgainstMutualFundServer) InvestorConsent(arg0 context.Context, arg1 *mutualfund.InvestorConsentRequest) (*mutualfund.InvestorConsentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InvestorConsent", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.InvestorConsentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InvestorConsent indicates an expected call of InvestorConsent.
func (mr *MockLoanAgainstMutualFundServerMockRecorder) InvestorConsent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvestorConsent", reflect.TypeOf((*MockLoanAgainstMutualFundServer)(nil).InvestorConsent), arg0, arg1)
}

// InvokeRevokeLien mocks base method.
func (m *MockLoanAgainstMutualFundServer) InvokeRevokeLien(arg0 context.Context, arg1 *mutualfund.InvokeRevokeLienRequest) (*mutualfund.InvokeRevokeLienResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InvokeRevokeLien", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.InvokeRevokeLienResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InvokeRevokeLien indicates an expected call of InvokeRevokeLien.
func (mr *MockLoanAgainstMutualFundServerMockRecorder) InvokeRevokeLien(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvokeRevokeLien", reflect.TypeOf((*MockLoanAgainstMutualFundServer)(nil).InvokeRevokeLien), arg0, arg1)
}

// SubmitCasSummary mocks base method.
func (m *MockLoanAgainstMutualFundServer) SubmitCasSummary(arg0 context.Context, arg1 *mutualfund.SubmitCasSummaryRequest) (*mutualfund.SubmitCasSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitCasSummary", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.SubmitCasSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitCasSummary indicates an expected call of SubmitCasSummary.
func (mr *MockLoanAgainstMutualFundServerMockRecorder) SubmitCasSummary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitCasSummary", reflect.TypeOf((*MockLoanAgainstMutualFundServer)(nil).SubmitCasSummary), arg0, arg1)
}

// SubmitLien mocks base method.
func (m *MockLoanAgainstMutualFundServer) SubmitLien(arg0 context.Context, arg1 *mutualfund.SubmitLienRequest) (*mutualfund.SubmitLienResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitLien", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.SubmitLienResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitLien indicates an expected call of SubmitLien.
func (mr *MockLoanAgainstMutualFundServerMockRecorder) SubmitLien(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitLien", reflect.TypeOf((*MockLoanAgainstMutualFundServer)(nil).SubmitLien), arg0, arg1)
}

// ValidateLien mocks base method.
func (m *MockLoanAgainstMutualFundServer) ValidateLien(arg0 context.Context, arg1 *mutualfund.ValidateLienRequest) (*mutualfund.ValidateLienResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateLien", arg0, arg1)
	ret0, _ := ret[0].(*mutualfund.ValidateLienResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateLien indicates an expected call of ValidateLien.
func (mr *MockLoanAgainstMutualFundServerMockRecorder) ValidateLien(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateLien", reflect.TypeOf((*MockLoanAgainstMutualFundServer)(nil).ValidateLien), arg0, arg1)
}

// MockUnsafeLoanAgainstMutualFundServer is a mock of UnsafeLoanAgainstMutualFundServer interface.
type MockUnsafeLoanAgainstMutualFundServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeLoanAgainstMutualFundServerMockRecorder
}

// MockUnsafeLoanAgainstMutualFundServerMockRecorder is the mock recorder for MockUnsafeLoanAgainstMutualFundServer.
type MockUnsafeLoanAgainstMutualFundServerMockRecorder struct {
	mock *MockUnsafeLoanAgainstMutualFundServer
}

// NewMockUnsafeLoanAgainstMutualFundServer creates a new mock instance.
func NewMockUnsafeLoanAgainstMutualFundServer(ctrl *gomock.Controller) *MockUnsafeLoanAgainstMutualFundServer {
	mock := &MockUnsafeLoanAgainstMutualFundServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeLoanAgainstMutualFundServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeLoanAgainstMutualFundServer) EXPECT() *MockUnsafeLoanAgainstMutualFundServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedLoanAgainstMutualFundServer mocks base method.
func (m *MockUnsafeLoanAgainstMutualFundServer) mustEmbedUnimplementedLoanAgainstMutualFundServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedLoanAgainstMutualFundServer")
}

// mustEmbedUnimplementedLoanAgainstMutualFundServer indicates an expected call of mustEmbedUnimplementedLoanAgainstMutualFundServer.
func (mr *MockUnsafeLoanAgainstMutualFundServerMockRecorder) mustEmbedUnimplementedLoanAgainstMutualFundServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedLoanAgainstMutualFundServer", reflect.TypeOf((*MockUnsafeLoanAgainstMutualFundServer)(nil).mustEmbedUnimplementedLoanAgainstMutualFundServer))
}
