// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/aa/analytics/ignosis/service.proto

package ignosis

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	enums "github.com/epifi/gamma/api/connected_account/enums"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	ignosis "github.com/epifi/gamma/api/vendors/aa/analytics/ignosis"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetDetailedAnalysisStatusResponse_JobStatus int32

const (
	GetDetailedAnalysisStatusResponse_JOB_STATUS_UNSPECIFIED GetDetailedAnalysisStatusResponse_JobStatus = 0
	GetDetailedAnalysisStatusResponse_JOB_STATUS_CREATED     GetDetailedAnalysisStatusResponse_JobStatus = 1
	GetDetailedAnalysisStatusResponse_JOB_STATUS_PROCESSING  GetDetailedAnalysisStatusResponse_JobStatus = 2
	GetDetailedAnalysisStatusResponse_JOB_STATUS_COMPLETED   GetDetailedAnalysisStatusResponse_JobStatus = 3
	GetDetailedAnalysisStatusResponse_JOB_STATUS_FAILED      GetDetailedAnalysisStatusResponse_JobStatus = 4
)

// Enum value maps for GetDetailedAnalysisStatusResponse_JobStatus.
var (
	GetDetailedAnalysisStatusResponse_JobStatus_name = map[int32]string{
		0: "JOB_STATUS_UNSPECIFIED",
		1: "JOB_STATUS_CREATED",
		2: "JOB_STATUS_PROCESSING",
		3: "JOB_STATUS_COMPLETED",
		4: "JOB_STATUS_FAILED",
	}
	GetDetailedAnalysisStatusResponse_JobStatus_value = map[string]int32{
		"JOB_STATUS_UNSPECIFIED": 0,
		"JOB_STATUS_CREATED":     1,
		"JOB_STATUS_PROCESSING":  2,
		"JOB_STATUS_COMPLETED":   3,
		"JOB_STATUS_FAILED":      4,
	}
)

func (x GetDetailedAnalysisStatusResponse_JobStatus) Enum() *GetDetailedAnalysisStatusResponse_JobStatus {
	p := new(GetDetailedAnalysisStatusResponse_JobStatus)
	*p = x
	return p
}

func (x GetDetailedAnalysisStatusResponse_JobStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetDetailedAnalysisStatusResponse_JobStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_enumTypes[0].Descriptor()
}

func (GetDetailedAnalysisStatusResponse_JobStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aa_analytics_ignosis_service_proto_enumTypes[0]
}

func (x GetDetailedAnalysisStatusResponse_JobStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetDetailedAnalysisStatusResponse_JobStatus.Descriptor instead.
func (GetDetailedAnalysisStatusResponse_JobStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{3, 0}
}

type GetFastAnalysisRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// The trackingId is an identifier to be passed while making the sync request which will uniquely identify the user
	// across multiple requests (Unique user id of our platform).
	TrackingId string `protobuf:"bytes,2,opt,name=tracking_id,json=trackingId,proto3" json:"tracking_id,omitempty"`
	// data w.r.t. each account
	Data           []*GetFastAnalysisRequest_Data `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	EmploymentType typesv2.EmploymentType         `protobuf:"varint,4,opt,name=employment_type,json=employmentType,proto3,enum=api.typesv2.EmploymentType" json:"employment_type,omitempty"` // optional field
	Employer       string                         `protobuf:"bytes,5,opt,name=employer,proto3" json:"employer,omitempty"`                                                                    // optional field
}

func (x *GetFastAnalysisRequest) Reset() {
	*x = GetFastAnalysisRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFastAnalysisRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFastAnalysisRequest) ProtoMessage() {}

func (x *GetFastAnalysisRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFastAnalysisRequest.ProtoReflect.Descriptor instead.
func (*GetFastAnalysisRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetFastAnalysisRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetFastAnalysisRequest) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

func (x *GetFastAnalysisRequest) GetData() []*GetFastAnalysisRequest_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetFastAnalysisRequest) GetEmploymentType() typesv2.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return typesv2.EmploymentType(0)
}

func (x *GetFastAnalysisRequest) GetEmployer() string {
	if x != nil {
		return x.Employer
	}
	return ""
}

type GetFastAnalysisResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TrackingId string      `protobuf:"bytes,2,opt,name=tracking_id,json=trackingId,proto3" json:"tracking_id,omitempty"`
	// referenceId is a unique identifier for each request for a trackingId
	// that will be generated by vendor for each new sync request
	ReferenceId string `protobuf:"bytes,3,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// Deprecated: Marked as deprecated in api/vendorgateway/aa/analytics/ignosis/service.proto.
	VendorResponse *ignosis.AnalysisResponse `protobuf:"bytes,4,opt,name=vendor_response,json=vendorResponse,proto3" json:"vendor_response,omitempty"`
	RawResponse    []byte                    `protobuf:"bytes,5,opt,name=raw_response,json=rawResponse,proto3" json:"raw_response,omitempty"`
}

func (x *GetFastAnalysisResponse) Reset() {
	*x = GetFastAnalysisResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFastAnalysisResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFastAnalysisResponse) ProtoMessage() {}

func (x *GetFastAnalysisResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFastAnalysisResponse.ProtoReflect.Descriptor instead.
func (*GetFastAnalysisResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetFastAnalysisResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFastAnalysisResponse) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

func (x *GetFastAnalysisResponse) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/vendorgateway/aa/analytics/ignosis/service.proto.
func (x *GetFastAnalysisResponse) GetVendorResponse() *ignosis.AnalysisResponse {
	if x != nil {
		return x.VendorResponse
	}
	return nil
}

func (x *GetFastAnalysisResponse) GetRawResponse() []byte {
	if x != nil {
		return x.RawResponse
	}
	return nil
}

type GetDetailedAnalysisStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header      *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	TrackingId  string                       `protobuf:"bytes,2,opt,name=tracking_id,json=trackingId,proto3" json:"tracking_id,omitempty"`
	ReferenceId string                       `protobuf:"bytes,3,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
}

func (x *GetDetailedAnalysisStatusRequest) Reset() {
	*x = GetDetailedAnalysisStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDetailedAnalysisStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDetailedAnalysisStatusRequest) ProtoMessage() {}

func (x *GetDetailedAnalysisStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDetailedAnalysisStatusRequest.ProtoReflect.Descriptor instead.
func (*GetDetailedAnalysisStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetDetailedAnalysisStatusRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetDetailedAnalysisStatusRequest) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

func (x *GetDetailedAnalysisStatusRequest) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

type GetDetailedAnalysisStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TrackingId  string      `protobuf:"bytes,2,opt,name=tracking_id,json=trackingId,proto3" json:"tracking_id,omitempty"`
	ReferenceId string      `protobuf:"bytes,3,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// Status of Analysis request
	// ● CREATED - Analysis request is initiated and will be processed soon
	// ● PROCESSING - Analysis request is being processed.
	// ● COMPLETED - Analysis request is Completed, you can fetch the updated data using Data Fetch API.
	// ● FAILED - Analysis request has failed
	JobStatus GetDetailedAnalysisStatusResponse_JobStatus `protobuf:"varint,4,opt,name=job_status,json=jobStatus,proto3,enum=vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisStatusResponse_JobStatus" json:"job_status,omitempty"`
}

func (x *GetDetailedAnalysisStatusResponse) Reset() {
	*x = GetDetailedAnalysisStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDetailedAnalysisStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDetailedAnalysisStatusResponse) ProtoMessage() {}

func (x *GetDetailedAnalysisStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDetailedAnalysisStatusResponse.ProtoReflect.Descriptor instead.
func (*GetDetailedAnalysisStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetDetailedAnalysisStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDetailedAnalysisStatusResponse) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

func (x *GetDetailedAnalysisStatusResponse) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *GetDetailedAnalysisStatusResponse) GetJobStatus() GetDetailedAnalysisStatusResponse_JobStatus {
	if x != nil {
		return x.JobStatus
	}
	return GetDetailedAnalysisStatusResponse_JOB_STATUS_UNSPECIFIED
}

type GetDetailedAnalysisRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header      *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	TrackingId  string                       `protobuf:"bytes,2,opt,name=tracking_id,json=trackingId,proto3" json:"tracking_id,omitempty"`
	ReferenceId string                       `protobuf:"bytes,3,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
}

func (x *GetDetailedAnalysisRequest) Reset() {
	*x = GetDetailedAnalysisRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDetailedAnalysisRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDetailedAnalysisRequest) ProtoMessage() {}

func (x *GetDetailedAnalysisRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDetailedAnalysisRequest.ProtoReflect.Descriptor instead.
func (*GetDetailedAnalysisRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetDetailedAnalysisRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetDetailedAnalysisRequest) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

func (x *GetDetailedAnalysisRequest) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

type GetDetailedAnalysisResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TrackingId  string      `protobuf:"bytes,2,opt,name=tracking_id,json=trackingId,proto3" json:"tracking_id,omitempty"`
	ReferenceId string      `protobuf:"bytes,3,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// Deprecated: Marked as deprecated in api/vendorgateway/aa/analytics/ignosis/service.proto.
	Response    *ignosis.AnalysisResponse `protobuf:"bytes,4,opt,name=response,proto3" json:"response,omitempty"`
	RawResponse []byte                    `protobuf:"bytes,5,opt,name=raw_response,json=rawResponse,proto3" json:"raw_response,omitempty"`
}

func (x *GetDetailedAnalysisResponse) Reset() {
	*x = GetDetailedAnalysisResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDetailedAnalysisResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDetailedAnalysisResponse) ProtoMessage() {}

func (x *GetDetailedAnalysisResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDetailedAnalysisResponse.ProtoReflect.Descriptor instead.
func (*GetDetailedAnalysisResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetDetailedAnalysisResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDetailedAnalysisResponse) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

func (x *GetDetailedAnalysisResponse) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/vendorgateway/aa/analytics/ignosis/service.proto.
func (x *GetDetailedAnalysisResponse) GetResponse() *ignosis.AnalysisResponse {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *GetDetailedAnalysisResponse) GetRawResponse() []byte {
	if x != nil {
		return x.RawResponse
	}
	return nil
}

type GetFastAnalysisRequest_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Profile      *GetFastAnalysisRequest_Data_Profile       `protobuf:"bytes,1,opt,name=profile,proto3" json:"profile,omitempty"`
	Summary      *GetFastAnalysisRequest_Data_Summary       `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
	Transactions []*GetFastAnalysisRequest_Data_Transaction `protobuf:"bytes,3,rep,name=transactions,proto3" json:"transactions,omitempty"`
}

func (x *GetFastAnalysisRequest_Data) Reset() {
	*x = GetFastAnalysisRequest_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFastAnalysisRequest_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFastAnalysisRequest_Data) ProtoMessage() {}

func (x *GetFastAnalysisRequest_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFastAnalysisRequest_Data.ProtoReflect.Descriptor instead.
func (*GetFastAnalysisRequest_Data) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GetFastAnalysisRequest_Data) GetProfile() *GetFastAnalysisRequest_Data_Profile {
	if x != nil {
		return x.Profile
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data) GetSummary() *GetFastAnalysisRequest_Data_Summary {
	if x != nil {
		return x.Summary
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data) GetTransactions() []*GetFastAnalysisRequest_Data_Transaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

type GetFastAnalysisRequest_Data_Profile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account *GetFastAnalysisRequest_Data_Profile_Account `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Holders *GetFastAnalysisRequest_Data_Profile_Holders `protobuf:"bytes,2,opt,name=holders,proto3" json:"holders,omitempty"`
}

func (x *GetFastAnalysisRequest_Data_Profile) Reset() {
	*x = GetFastAnalysisRequest_Data_Profile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFastAnalysisRequest_Data_Profile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFastAnalysisRequest_Data_Profile) ProtoMessage() {}

func (x *GetFastAnalysisRequest_Data_Profile) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFastAnalysisRequest_Data_Profile.ProtoReflect.Descriptor instead.
func (*GetFastAnalysisRequest_Data_Profile) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *GetFastAnalysisRequest_Data_Profile) GetAccount() *GetFastAnalysisRequest_Data_Profile_Account {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data_Profile) GetHolders() *GetFastAnalysisRequest_Data_Profile_Holders {
	if x != nil {
		return x.Holders
	}
	return nil
}

type GetFastAnalysisRequest_Data_Summary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionStartDate         *date.Date             `protobuf:"bytes,1,opt,name=transaction_start_date,json=transactionStartDate,proto3" json:"transaction_start_date,omitempty"`
	TransactionEndDate           *date.Date             `protobuf:"bytes,2,opt,name=transaction_end_date,json=transactionEndDate,proto3" json:"transaction_end_date,omitempty"`
	OpeningDate                  *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=opening_date,json=openingDate,proto3" json:"opening_date,omitempty"`
	BalanceDateTime              *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=balance_date_time,json=balanceDateTime,proto3" json:"balance_date_time,omitempty"`
	Branch                       string                 `protobuf:"bytes,5,opt,name=branch,proto3" json:"branch,omitempty"`
	Ifsc                         string                 `protobuf:"bytes,6,opt,name=ifsc,proto3" json:"ifsc,omitempty"`
	MicrCode                     string                 `protobuf:"bytes,7,opt,name=micr_code,json=micrCode,proto3" json:"micr_code,omitempty"`
	MaturityAmount               *money.Money           `protobuf:"bytes,8,opt,name=maturity_amount,json=maturityAmount,proto3" json:"maturity_amount,omitempty"`
	MaturityDate                 *date.Date             `protobuf:"bytes,9,opt,name=maturity_date,json=maturityDate,proto3" json:"maturity_date,omitempty"`
	Description                  string                 `protobuf:"bytes,10,opt,name=description,proto3" json:"description,omitempty"`
	InterestPayout               string                 `protobuf:"bytes,11,opt,name=interest_payout,json=interestPayout,proto3" json:"interest_payout,omitempty"`
	InterestRate                 float64                `protobuf:"fixed64,12,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	PrincipalAmount              *money.Money           `protobuf:"bytes,13,opt,name=principal_amount,json=principalAmount,proto3" json:"principal_amount,omitempty"`
	TenureDays                   string                 `protobuf:"bytes,14,opt,name=tenure_days,json=tenureDays,proto3" json:"tenure_days,omitempty"`
	TenureMonths                 string                 `protobuf:"bytes,15,opt,name=tenure_months,json=tenureMonths,proto3" json:"tenure_months,omitempty"`
	TenureYears                  string                 `protobuf:"bytes,16,opt,name=tenure_years,json=tenureYears,proto3" json:"tenure_years,omitempty"`
	InterestComputation          string                 `protobuf:"bytes,17,opt,name=interest_computation,json=interestComputation,proto3" json:"interest_computation,omitempty"`
	CompoundingFrequency         string                 `protobuf:"bytes,18,opt,name=compounding_frequency,json=compoundingFrequency,proto3" json:"compounding_frequency,omitempty"`
	InterestPeriodicPayoutAmount *money.Money           `protobuf:"bytes,19,opt,name=interest_periodic_payout_amount,json=interestPeriodicPayoutAmount,proto3" json:"interest_periodic_payout_amount,omitempty"`
	InterestOnMaturity           string                 `protobuf:"bytes,20,opt,name=interest_on_maturity,json=interestOnMaturity,proto3" json:"interest_on_maturity,omitempty"`
	CurrentValue                 string                 `protobuf:"bytes,21,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
	RecurringAmount              *money.Money           `protobuf:"bytes,22,opt,name=recurring_amount,json=recurringAmount,proto3" json:"recurring_amount,omitempty"`
	RecurringDepositDay          string                 `protobuf:"bytes,23,opt,name=recurring_deposit_day,json=recurringDepositDay,proto3" json:"recurring_deposit_day,omitempty"`
	CurrentBalance               *money.Money           `protobuf:"bytes,24,opt,name=current_balance,json=currentBalance,proto3" json:"current_balance,omitempty"`
}

func (x *GetFastAnalysisRequest_Data_Summary) Reset() {
	*x = GetFastAnalysisRequest_Data_Summary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFastAnalysisRequest_Data_Summary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFastAnalysisRequest_Data_Summary) ProtoMessage() {}

func (x *GetFastAnalysisRequest_Data_Summary) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFastAnalysisRequest_Data_Summary.ProtoReflect.Descriptor instead.
func (*GetFastAnalysisRequest_Data_Summary) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *GetFastAnalysisRequest_Data_Summary) GetTransactionStartDate() *date.Date {
	if x != nil {
		return x.TransactionStartDate
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data_Summary) GetTransactionEndDate() *date.Date {
	if x != nil {
		return x.TransactionEndDate
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data_Summary) GetOpeningDate() *timestamppb.Timestamp {
	if x != nil {
		return x.OpeningDate
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data_Summary) GetBalanceDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.BalanceDateTime
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data_Summary) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Summary) GetIfsc() string {
	if x != nil {
		return x.Ifsc
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Summary) GetMicrCode() string {
	if x != nil {
		return x.MicrCode
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Summary) GetMaturityAmount() *money.Money {
	if x != nil {
		return x.MaturityAmount
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data_Summary) GetMaturityDate() *date.Date {
	if x != nil {
		return x.MaturityDate
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data_Summary) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Summary) GetInterestPayout() string {
	if x != nil {
		return x.InterestPayout
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Summary) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

func (x *GetFastAnalysisRequest_Data_Summary) GetPrincipalAmount() *money.Money {
	if x != nil {
		return x.PrincipalAmount
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data_Summary) GetTenureDays() string {
	if x != nil {
		return x.TenureDays
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Summary) GetTenureMonths() string {
	if x != nil {
		return x.TenureMonths
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Summary) GetTenureYears() string {
	if x != nil {
		return x.TenureYears
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Summary) GetInterestComputation() string {
	if x != nil {
		return x.InterestComputation
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Summary) GetCompoundingFrequency() string {
	if x != nil {
		return x.CompoundingFrequency
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Summary) GetInterestPeriodicPayoutAmount() *money.Money {
	if x != nil {
		return x.InterestPeriodicPayoutAmount
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data_Summary) GetInterestOnMaturity() string {
	if x != nil {
		return x.InterestOnMaturity
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Summary) GetCurrentValue() string {
	if x != nil {
		return x.CurrentValue
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Summary) GetRecurringAmount() *money.Money {
	if x != nil {
		return x.RecurringAmount
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data_Summary) GetRecurringDepositDay() string {
	if x != nil {
		return x.RecurringDepositDay
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Summary) GetCurrentBalance() *money.Money {
	if x != nil {
		return x.CurrentBalance
	}
	return nil
}

type GetFastAnalysisRequest_Data_Transaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type                 enums.TransactionType  `protobuf:"varint,1,opt,name=type,proto3,enum=connected_account.enums.TransactionType" json:"type,omitempty"`
	Amount               string                 `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Narration            string                 `protobuf:"bytes,3,opt,name=narration,proto3" json:"narration,omitempty"`
	CurrentBalance       string                 `protobuf:"bytes,4,opt,name=current_balance,json=currentBalance,proto3" json:"current_balance,omitempty"`
	TransactionTimestamp *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=transaction_timestamp,json=transactionTimestamp,proto3" json:"transaction_timestamp,omitempty"`
	TxnRefId             string                 `protobuf:"bytes,6,opt,name=txn_ref_id,json=txnRefId,proto3" json:"txn_ref_id,omitempty"`
}

func (x *GetFastAnalysisRequest_Data_Transaction) Reset() {
	*x = GetFastAnalysisRequest_Data_Transaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFastAnalysisRequest_Data_Transaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFastAnalysisRequest_Data_Transaction) ProtoMessage() {}

func (x *GetFastAnalysisRequest_Data_Transaction) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFastAnalysisRequest_Data_Transaction.ProtoReflect.Descriptor instead.
func (*GetFastAnalysisRequest_Data_Transaction) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{0, 0, 2}
}

func (x *GetFastAnalysisRequest_Data_Transaction) GetType() enums.TransactionType {
	if x != nil {
		return x.Type
	}
	return enums.TransactionType(0)
}

func (x *GetFastAnalysisRequest_Data_Transaction) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Transaction) GetNarration() string {
	if x != nil {
		return x.Narration
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Transaction) GetCurrentBalance() string {
	if x != nil {
		return x.CurrentBalance
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Transaction) GetTransactionTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionTimestamp
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data_Transaction) GetTxnRefId() string {
	if x != nil {
		return x.TxnRefId
	}
	return ""
}

type GetFastAnalysisRequest_Data_Profile_Account struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number       string                   `protobuf:"bytes,1,opt,name=number,proto3" json:"number,omitempty"`
	AccType      enums.DepositAccountType `protobuf:"varint,2,opt,name=acc_type,json=accType,proto3,enum=connected_account.enums.DepositAccountType" json:"acc_type,omitempty"`
	FiType       enums.AccInstrumentType  `protobuf:"varint,3,opt,name=fi_type,json=fiType,proto3,enum=connected_account.enums.AccInstrumentType" json:"fi_type,omitempty"`
	Bank         typesv2.Bank             `protobuf:"varint,4,opt,name=bank,proto3,enum=api.typesv2.Bank" json:"bank,omitempty"`
	LinkedAccRef string                   `protobuf:"bytes,5,opt,name=linked_acc_ref,json=linkedAccRef,proto3" json:"linked_acc_ref,omitempty"`
	FipId        string                   `protobuf:"bytes,6,opt,name=fip_id,json=fipId,proto3" json:"fip_id,omitempty"`
}

func (x *GetFastAnalysisRequest_Data_Profile_Account) Reset() {
	*x = GetFastAnalysisRequest_Data_Profile_Account{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFastAnalysisRequest_Data_Profile_Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFastAnalysisRequest_Data_Profile_Account) ProtoMessage() {}

func (x *GetFastAnalysisRequest_Data_Profile_Account) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFastAnalysisRequest_Data_Profile_Account.ProtoReflect.Descriptor instead.
func (*GetFastAnalysisRequest_Data_Profile_Account) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{0, 0, 0, 0}
}

func (x *GetFastAnalysisRequest_Data_Profile_Account) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Profile_Account) GetAccType() enums.DepositAccountType {
	if x != nil {
		return x.AccType
	}
	return enums.DepositAccountType(0)
}

func (x *GetFastAnalysisRequest_Data_Profile_Account) GetFiType() enums.AccInstrumentType {
	if x != nil {
		return x.FiType
	}
	return enums.AccInstrumentType(0)
}

func (x *GetFastAnalysisRequest_Data_Profile_Account) GetBank() typesv2.Bank {
	if x != nil {
		return x.Bank
	}
	return typesv2.Bank(0)
}

func (x *GetFastAnalysisRequest_Data_Profile_Account) GetLinkedAccRef() string {
	if x != nil {
		return x.LinkedAccRef
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Profile_Account) GetFipId() string {
	if x != nil {
		return x.FipId
	}
	return ""
}

type GetFastAnalysisRequest_Data_Profile_Holders struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   enums.HolderType                                      `protobuf:"varint,1,opt,name=type,proto3,enum=connected_account.enums.HolderType" json:"type,omitempty"`
	Holder []*GetFastAnalysisRequest_Data_Profile_Holders_Holder `protobuf:"bytes,2,rep,name=holder,proto3" json:"holder,omitempty"`
}

func (x *GetFastAnalysisRequest_Data_Profile_Holders) Reset() {
	*x = GetFastAnalysisRequest_Data_Profile_Holders{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFastAnalysisRequest_Data_Profile_Holders) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFastAnalysisRequest_Data_Profile_Holders) ProtoMessage() {}

func (x *GetFastAnalysisRequest_Data_Profile_Holders) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFastAnalysisRequest_Data_Profile_Holders.ProtoReflect.Descriptor instead.
func (*GetFastAnalysisRequest_Data_Profile_Holders) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{0, 0, 0, 1}
}

func (x *GetFastAnalysisRequest_Data_Profile_Holders) GetType() enums.HolderType {
	if x != nil {
		return x.Type
	}
	return enums.HolderType(0)
}

func (x *GetFastAnalysisRequest_Data_Profile_Holders) GetHolder() []*GetFastAnalysisRequest_Data_Profile_Holders_Holder {
	if x != nil {
		return x.Holder
	}
	return nil
}

type GetFastAnalysisRequest_Data_Profile_Holders_Holder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    *common.Name        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Mobile  *common.PhoneNumber `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Address string              `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	Email   string              `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Pan     string              `protobuf:"bytes,5,opt,name=pan,proto3" json:"pan,omitempty"`
}

func (x *GetFastAnalysisRequest_Data_Profile_Holders_Holder) Reset() {
	*x = GetFastAnalysisRequest_Data_Profile_Holders_Holder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFastAnalysisRequest_Data_Profile_Holders_Holder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFastAnalysisRequest_Data_Profile_Holders_Holder) ProtoMessage() {}

func (x *GetFastAnalysisRequest_Data_Profile_Holders_Holder) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFastAnalysisRequest_Data_Profile_Holders_Holder.ProtoReflect.Descriptor instead.
func (*GetFastAnalysisRequest_Data_Profile_Holders_Holder) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP(), []int{0, 0, 0, 1, 0}
}

func (x *GetFastAnalysisRequest_Data_Profile_Holders_Holder) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data_Profile_Holders_Holder) GetMobile() *common.PhoneNumber {
	if x != nil {
		return x.Mobile
	}
	return nil
}

func (x *GetFastAnalysisRequest_Data_Profile_Holders_Holder) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Profile_Holders_Holder) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetFastAnalysisRequest_Data_Profile_Holders_Holder) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

var File_api_vendorgateway_aa_analytics_ignosis_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDesc = []byte{
	0x0a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x61, 0x61, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73,
	0x2f, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x61, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x61, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x74, 0x69, 0x63, 0x73, 0x2f, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x2f, 0x70, 0x66, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb6, 0x17, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x46, 0x61, 0x73, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x0b, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x61, 0x61, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e,
	0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x61, 0x73, 0x74, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x44, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x65, 0x72, 0x1a, 0xfa, 0x14, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x61,
	0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x47, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x61, 0x61, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e,
	0x6f, 0x73, 0x69, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x61, 0x73, 0x74, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x12, 0x61, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x47, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x61, 0x61, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e,
	0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x61, 0x73, 0x74, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x07, 0x73, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x12, 0x6f, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x61, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x46, 0x61, 0x73, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0xdd, 0x06, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x12, 0x69, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x61, 0x61, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e,
	0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x61, 0x73, 0x74, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x69, 0x0a, 0x07,
	0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4f, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x61,
	0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x73,
	0x69, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x61, 0x73, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x52, 0x07,
	0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x1a, 0x92, 0x02, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x46, 0x0a, 0x08, 0x61,
	0x63, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x61, 0x63, 0x63, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x07, 0x66, 0x69, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41,
	0x63, 0x63, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x06, 0x66, 0x69, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x62, 0x61, 0x6e, 0x6b,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x04, 0x62, 0x61, 0x6e, 0x6b, 0x12,
	0x24, 0x0a, 0x0e, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x5f, 0x72, 0x65,
	0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x41,
	0x63, 0x63, 0x52, 0x65, 0x66, 0x12, 0x15, 0x0a, 0x06, 0x66, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x70, 0x49, 0x64, 0x1a, 0xe6, 0x02, 0x0a,
	0x07, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x12, 0x37, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x6e, 0x0a, 0x06, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x56, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x61, 0x61, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x69,
	0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x61, 0x73, 0x74, 0x41, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x73, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x1a, 0xb1, 0x01, 0x0a, 0x06, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x06, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x06, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x70, 0x61, 0x6e, 0x1a, 0xbe, 0x09, 0x0a, 0x07, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x12, 0x47, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x43, 0x0a, 0x14, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x12, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x3d, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x12, 0x46,
	0x0a, 0x11, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x66, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x66,
	0x73, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x63, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69, 0x63, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x3b, 0x0a, 0x0f, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x6d, 0x61,
	0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x0d,
	0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x12,
	0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61,
	0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0f, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x64, 0x61,
	0x79, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65,
	0x44, 0x61, 0x79, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x6d,
	0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x65, 0x6e,
	0x75, 0x72, 0x65, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x65, 0x6e,
	0x75, 0x72, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x59, 0x65, 0x61, 0x72, 0x73, 0x12, 0x31, 0x0a, 0x14,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x33, 0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x66,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x59, 0x0a, 0x1f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74,
	0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x69, 0x63, 0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x1c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x69, 0x63, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x30, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x6f, 0x6e, 0x5f, 0x6d,
	0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x4d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3d, 0x0a, 0x10, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x61, 0x79, 0x12, 0x3b, 0x0a, 0x0f, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x99, 0x02, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x6e, 0x61, 0x72, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6e, 0x61, 0x72, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x4f, 0x0a, 0x15, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x1c, 0x0a, 0x0a, 0x74, 0x78, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x78, 0x6e, 0x52, 0x65, 0x66,
	0x49, 0x64, 0x22, 0x82, 0x02, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x46, 0x61, 0x73, 0x74, 0x41, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x5b, 0x0a, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x61, 0x61, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x2e,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x61, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xae, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x28, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0c,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x8a, 0x03, 0x0a, 0x21, 0x47, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x6e, 0x0a, 0x0a, 0x6a, 0x6f, 0x62, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4f, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x61, 0x2e, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x6a, 0x6f,
	0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x8b, 0x01, 0x0a, 0x09, 0x4a, 0x6f, 0x62, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16, 0x4a, 0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x16, 0x0a, 0x12, 0x4a, 0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x4a, 0x4f, 0x42,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49,
	0x4e, 0x47, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x4a, 0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x15,
	0x0a, 0x11, 0x4a, 0x4f, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0x04, 0x22, 0xa8, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x0b, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x22, 0xf9, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x08, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x61, 0x61, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x61, 0x77,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0b, 0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xec, 0x03, 0x0a,
	0x19, 0x49, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x41, 0x61, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x46, 0x61, 0x73, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x12, 0x3a,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61,
	0x61, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e, 0x6f,
	0x73, 0x69, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x61, 0x73, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x61, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x46, 0x61, 0x73, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa8, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x61, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x61, 0x2e, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x12, 0x3e, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x61, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x61, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x7e, 0x0a, 0x3d, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x61, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x74, 0x69, 0x63, 0x73, 0x2e, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x5a, 0x3d, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x61, 0x61, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x2f, 0x69, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescData = file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDesc
)

func file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescData)
	})
	return file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDescData
}

var file_api_vendorgateway_aa_analytics_ignosis_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_api_vendorgateway_aa_analytics_ignosis_service_proto_goTypes = []interface{}{
	(GetDetailedAnalysisStatusResponse_JobStatus)(0),           // 0: vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisStatusResponse.JobStatus
	(*GetFastAnalysisRequest)(nil),                             // 1: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest
	(*GetFastAnalysisResponse)(nil),                            // 2: vendorgateway.aa.analytics.ignosis.GetFastAnalysisResponse
	(*GetDetailedAnalysisStatusRequest)(nil),                   // 3: vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisStatusRequest
	(*GetDetailedAnalysisStatusResponse)(nil),                  // 4: vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisStatusResponse
	(*GetDetailedAnalysisRequest)(nil),                         // 5: vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisRequest
	(*GetDetailedAnalysisResponse)(nil),                        // 6: vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisResponse
	(*GetFastAnalysisRequest_Data)(nil),                        // 7: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data
	(*GetFastAnalysisRequest_Data_Profile)(nil),                // 8: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile
	(*GetFastAnalysisRequest_Data_Summary)(nil),                // 9: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Summary
	(*GetFastAnalysisRequest_Data_Transaction)(nil),            // 10: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Transaction
	(*GetFastAnalysisRequest_Data_Profile_Account)(nil),        // 11: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.Account
	(*GetFastAnalysisRequest_Data_Profile_Holders)(nil),        // 12: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.Holders
	(*GetFastAnalysisRequest_Data_Profile_Holders_Holder)(nil), // 13: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.Holders.Holder
	(*vendorgateway.RequestHeader)(nil),                        // 14: vendorgateway.RequestHeader
	(typesv2.EmploymentType)(0),                                // 15: api.typesv2.EmploymentType
	(*rpc.Status)(nil),                                         // 16: rpc.Status
	(*ignosis.AnalysisResponse)(nil),                           // 17: vendors.aa.analytics.ignosis.AnalysisResponse
	(*date.Date)(nil),                                          // 18: google.type.Date
	(*timestamppb.Timestamp)(nil),                              // 19: google.protobuf.Timestamp
	(*money.Money)(nil),                                        // 20: google.type.Money
	(enums.TransactionType)(0),                                 // 21: connected_account.enums.TransactionType
	(enums.DepositAccountType)(0),                              // 22: connected_account.enums.DepositAccountType
	(enums.AccInstrumentType)(0),                               // 23: connected_account.enums.AccInstrumentType
	(typesv2.Bank)(0),                                          // 24: api.typesv2.Bank
	(enums.HolderType)(0),                                      // 25: connected_account.enums.HolderType
	(*common.Name)(nil),                                        // 26: api.typesv2.common.Name
	(*common.PhoneNumber)(nil),                                 // 27: api.typesv2.common.PhoneNumber
}
var file_api_vendorgateway_aa_analytics_ignosis_service_proto_depIdxs = []int32{
	14, // 0: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.header:type_name -> vendorgateway.RequestHeader
	7,  // 1: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.data:type_name -> vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data
	15, // 2: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.employment_type:type_name -> api.typesv2.EmploymentType
	16, // 3: vendorgateway.aa.analytics.ignosis.GetFastAnalysisResponse.status:type_name -> rpc.Status
	17, // 4: vendorgateway.aa.analytics.ignosis.GetFastAnalysisResponse.vendor_response:type_name -> vendors.aa.analytics.ignosis.AnalysisResponse
	14, // 5: vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisStatusRequest.header:type_name -> vendorgateway.RequestHeader
	16, // 6: vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisStatusResponse.status:type_name -> rpc.Status
	0,  // 7: vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisStatusResponse.job_status:type_name -> vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisStatusResponse.JobStatus
	14, // 8: vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisRequest.header:type_name -> vendorgateway.RequestHeader
	16, // 9: vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisResponse.status:type_name -> rpc.Status
	17, // 10: vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisResponse.response:type_name -> vendors.aa.analytics.ignosis.AnalysisResponse
	8,  // 11: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.profile:type_name -> vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile
	9,  // 12: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.summary:type_name -> vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Summary
	10, // 13: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.transactions:type_name -> vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Transaction
	11, // 14: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.account:type_name -> vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.Account
	12, // 15: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.holders:type_name -> vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.Holders
	18, // 16: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Summary.transaction_start_date:type_name -> google.type.Date
	18, // 17: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Summary.transaction_end_date:type_name -> google.type.Date
	19, // 18: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Summary.opening_date:type_name -> google.protobuf.Timestamp
	19, // 19: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Summary.balance_date_time:type_name -> google.protobuf.Timestamp
	20, // 20: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Summary.maturity_amount:type_name -> google.type.Money
	18, // 21: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Summary.maturity_date:type_name -> google.type.Date
	20, // 22: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Summary.principal_amount:type_name -> google.type.Money
	20, // 23: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Summary.interest_periodic_payout_amount:type_name -> google.type.Money
	20, // 24: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Summary.recurring_amount:type_name -> google.type.Money
	20, // 25: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Summary.current_balance:type_name -> google.type.Money
	21, // 26: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Transaction.type:type_name -> connected_account.enums.TransactionType
	19, // 27: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Transaction.transaction_timestamp:type_name -> google.protobuf.Timestamp
	22, // 28: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.Account.acc_type:type_name -> connected_account.enums.DepositAccountType
	23, // 29: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.Account.fi_type:type_name -> connected_account.enums.AccInstrumentType
	24, // 30: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.Account.bank:type_name -> api.typesv2.Bank
	25, // 31: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.Holders.type:type_name -> connected_account.enums.HolderType
	13, // 32: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.Holders.holder:type_name -> vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.Holders.Holder
	26, // 33: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.Holders.Holder.name:type_name -> api.typesv2.common.Name
	27, // 34: vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest.Data.Profile.Holders.Holder.mobile:type_name -> api.typesv2.common.PhoneNumber
	1,  // 35: vendorgateway.aa.analytics.ignosis.IgnosisAaAnalyticsService.GetFastAnalysis:input_type -> vendorgateway.aa.analytics.ignosis.GetFastAnalysisRequest
	3,  // 36: vendorgateway.aa.analytics.ignosis.IgnosisAaAnalyticsService.GetDetailedAnalysisStatus:input_type -> vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisStatusRequest
	5,  // 37: vendorgateway.aa.analytics.ignosis.IgnosisAaAnalyticsService.GetDetailedAnalysis:input_type -> vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisRequest
	2,  // 38: vendorgateway.aa.analytics.ignosis.IgnosisAaAnalyticsService.GetFastAnalysis:output_type -> vendorgateway.aa.analytics.ignosis.GetFastAnalysisResponse
	4,  // 39: vendorgateway.aa.analytics.ignosis.IgnosisAaAnalyticsService.GetDetailedAnalysisStatus:output_type -> vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisStatusResponse
	6,  // 40: vendorgateway.aa.analytics.ignosis.IgnosisAaAnalyticsService.GetDetailedAnalysis:output_type -> vendorgateway.aa.analytics.ignosis.GetDetailedAnalysisResponse
	38, // [38:41] is the sub-list for method output_type
	35, // [35:38] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_aa_analytics_ignosis_service_proto_init() }
func file_api_vendorgateway_aa_analytics_ignosis_service_proto_init() {
	if File_api_vendorgateway_aa_analytics_ignosis_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFastAnalysisRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFastAnalysisResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDetailedAnalysisStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDetailedAnalysisStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDetailedAnalysisRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDetailedAnalysisResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFastAnalysisRequest_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFastAnalysisRequest_Data_Profile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFastAnalysisRequest_Data_Summary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFastAnalysisRequest_Data_Transaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFastAnalysisRequest_Data_Profile_Account); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFastAnalysisRequest_Data_Profile_Holders); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFastAnalysisRequest_Data_Profile_Holders_Holder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_aa_analytics_ignosis_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_aa_analytics_ignosis_service_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_aa_analytics_ignosis_service_proto_enumTypes,
		MessageInfos:      file_api_vendorgateway_aa_analytics_ignosis_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_aa_analytics_ignosis_service_proto = out.File
	file_api_vendorgateway_aa_analytics_ignosis_service_proto_rawDesc = nil
	file_api_vendorgateway_aa_analytics_ignosis_service_proto_goTypes = nil
	file_api_vendorgateway_aa_analytics_ignosis_service_proto_depIdxs = nil
}
