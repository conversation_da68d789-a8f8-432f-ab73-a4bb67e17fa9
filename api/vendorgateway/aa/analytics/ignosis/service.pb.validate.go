// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/aa/analytics/ignosis/service.proto

package ignosis

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/connected_account/enums"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.TransactionType(0)

	_ = typesv2.EmploymentType(0)
)

// Validate checks the field values on GetFastAnalysisRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFastAnalysisRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFastAnalysisRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFastAnalysisRequestMultiError, or nil if none found.
func (m *GetFastAnalysisRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFastAnalysisRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetTrackingId()) < 1 {
		err := GetFastAnalysisRequestValidationError{
			field:  "TrackingId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetData()) < 1 {
		err := GetFastAnalysisRequestValidationError{
			field:  "Data",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFastAnalysisRequestValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFastAnalysisRequestValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFastAnalysisRequestValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for EmploymentType

	// no validation rules for Employer

	if len(errors) > 0 {
		return GetFastAnalysisRequestMultiError(errors)
	}

	return nil
}

// GetFastAnalysisRequestMultiError is an error wrapping multiple validation
// errors returned by GetFastAnalysisRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFastAnalysisRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFastAnalysisRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFastAnalysisRequestMultiError) AllErrors() []error { return m }

// GetFastAnalysisRequestValidationError is the validation error returned by
// GetFastAnalysisRequest.Validate if the designated constraints aren't met.
type GetFastAnalysisRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFastAnalysisRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFastAnalysisRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFastAnalysisRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFastAnalysisRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFastAnalysisRequestValidationError) ErrorName() string {
	return "GetFastAnalysisRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFastAnalysisRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFastAnalysisRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFastAnalysisRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFastAnalysisRequestValidationError{}

// Validate checks the field values on GetFastAnalysisResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFastAnalysisResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFastAnalysisResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFastAnalysisResponseMultiError, or nil if none found.
func (m *GetFastAnalysisResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFastAnalysisResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TrackingId

	// no validation rules for ReferenceId

	if all {
		switch v := interface{}(m.GetVendorResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisResponseValidationError{
					field:  "VendorResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisResponseValidationError{
					field:  "VendorResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisResponseValidationError{
				field:  "VendorResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawResponse

	if len(errors) > 0 {
		return GetFastAnalysisResponseMultiError(errors)
	}

	return nil
}

// GetFastAnalysisResponseMultiError is an error wrapping multiple validation
// errors returned by GetFastAnalysisResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFastAnalysisResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFastAnalysisResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFastAnalysisResponseMultiError) AllErrors() []error { return m }

// GetFastAnalysisResponseValidationError is the validation error returned by
// GetFastAnalysisResponse.Validate if the designated constraints aren't met.
type GetFastAnalysisResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFastAnalysisResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFastAnalysisResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFastAnalysisResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFastAnalysisResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFastAnalysisResponseValidationError) ErrorName() string {
	return "GetFastAnalysisResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFastAnalysisResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFastAnalysisResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFastAnalysisResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFastAnalysisResponseValidationError{}

// Validate checks the field values on GetDetailedAnalysisStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetDetailedAnalysisStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDetailedAnalysisStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetDetailedAnalysisStatusRequestMultiError, or nil if none found.
func (m *GetDetailedAnalysisStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDetailedAnalysisStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailedAnalysisStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailedAnalysisStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailedAnalysisStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetTrackingId()) < 1 {
		err := GetDetailedAnalysisStatusRequestValidationError{
			field:  "TrackingId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetReferenceId()) < 1 {
		err := GetDetailedAnalysisStatusRequestValidationError{
			field:  "ReferenceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetDetailedAnalysisStatusRequestMultiError(errors)
	}

	return nil
}

// GetDetailedAnalysisStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetDetailedAnalysisStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDetailedAnalysisStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDetailedAnalysisStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDetailedAnalysisStatusRequestMultiError) AllErrors() []error { return m }

// GetDetailedAnalysisStatusRequestValidationError is the validation error
// returned by GetDetailedAnalysisStatusRequest.Validate if the designated
// constraints aren't met.
type GetDetailedAnalysisStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDetailedAnalysisStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDetailedAnalysisStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDetailedAnalysisStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDetailedAnalysisStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDetailedAnalysisStatusRequestValidationError) ErrorName() string {
	return "GetDetailedAnalysisStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDetailedAnalysisStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDetailedAnalysisStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDetailedAnalysisStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDetailedAnalysisStatusRequestValidationError{}

// Validate checks the field values on GetDetailedAnalysisStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetDetailedAnalysisStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDetailedAnalysisStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetDetailedAnalysisStatusResponseMultiError, or nil if none found.
func (m *GetDetailedAnalysisStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDetailedAnalysisStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailedAnalysisStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailedAnalysisStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailedAnalysisStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TrackingId

	// no validation rules for ReferenceId

	// no validation rules for JobStatus

	if len(errors) > 0 {
		return GetDetailedAnalysisStatusResponseMultiError(errors)
	}

	return nil
}

// GetDetailedAnalysisStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetDetailedAnalysisStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDetailedAnalysisStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDetailedAnalysisStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDetailedAnalysisStatusResponseMultiError) AllErrors() []error { return m }

// GetDetailedAnalysisStatusResponseValidationError is the validation error
// returned by GetDetailedAnalysisStatusResponse.Validate if the designated
// constraints aren't met.
type GetDetailedAnalysisStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDetailedAnalysisStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDetailedAnalysisStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDetailedAnalysisStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDetailedAnalysisStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDetailedAnalysisStatusResponseValidationError) ErrorName() string {
	return "GetDetailedAnalysisStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDetailedAnalysisStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDetailedAnalysisStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDetailedAnalysisStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDetailedAnalysisStatusResponseValidationError{}

// Validate checks the field values on GetDetailedAnalysisRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDetailedAnalysisRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDetailedAnalysisRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDetailedAnalysisRequestMultiError, or nil if none found.
func (m *GetDetailedAnalysisRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDetailedAnalysisRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailedAnalysisRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailedAnalysisRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailedAnalysisRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetTrackingId()) < 1 {
		err := GetDetailedAnalysisRequestValidationError{
			field:  "TrackingId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetReferenceId()) < 1 {
		err := GetDetailedAnalysisRequestValidationError{
			field:  "ReferenceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetDetailedAnalysisRequestMultiError(errors)
	}

	return nil
}

// GetDetailedAnalysisRequestMultiError is an error wrapping multiple
// validation errors returned by GetDetailedAnalysisRequest.ValidateAll() if
// the designated constraints aren't met.
type GetDetailedAnalysisRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDetailedAnalysisRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDetailedAnalysisRequestMultiError) AllErrors() []error { return m }

// GetDetailedAnalysisRequestValidationError is the validation error returned
// by GetDetailedAnalysisRequest.Validate if the designated constraints aren't met.
type GetDetailedAnalysisRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDetailedAnalysisRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDetailedAnalysisRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDetailedAnalysisRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDetailedAnalysisRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDetailedAnalysisRequestValidationError) ErrorName() string {
	return "GetDetailedAnalysisRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDetailedAnalysisRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDetailedAnalysisRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDetailedAnalysisRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDetailedAnalysisRequestValidationError{}

// Validate checks the field values on GetDetailedAnalysisResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDetailedAnalysisResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDetailedAnalysisResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDetailedAnalysisResponseMultiError, or nil if none found.
func (m *GetDetailedAnalysisResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDetailedAnalysisResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailedAnalysisResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailedAnalysisResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailedAnalysisResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TrackingId

	// no validation rules for ReferenceId

	if all {
		switch v := interface{}(m.GetResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailedAnalysisResponseValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailedAnalysisResponseValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailedAnalysisResponseValidationError{
				field:  "Response",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawResponse

	if len(errors) > 0 {
		return GetDetailedAnalysisResponseMultiError(errors)
	}

	return nil
}

// GetDetailedAnalysisResponseMultiError is an error wrapping multiple
// validation errors returned by GetDetailedAnalysisResponse.ValidateAll() if
// the designated constraints aren't met.
type GetDetailedAnalysisResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDetailedAnalysisResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDetailedAnalysisResponseMultiError) AllErrors() []error { return m }

// GetDetailedAnalysisResponseValidationError is the validation error returned
// by GetDetailedAnalysisResponse.Validate if the designated constraints
// aren't met.
type GetDetailedAnalysisResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDetailedAnalysisResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDetailedAnalysisResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDetailedAnalysisResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDetailedAnalysisResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDetailedAnalysisResponseValidationError) ErrorName() string {
	return "GetDetailedAnalysisResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDetailedAnalysisResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDetailedAnalysisResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDetailedAnalysisResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDetailedAnalysisResponseValidationError{}

// Validate checks the field values on GetFastAnalysisRequest_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFastAnalysisRequest_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFastAnalysisRequest_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFastAnalysisRequest_DataMultiError, or nil if none found.
func (m *GetFastAnalysisRequest_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFastAnalysisRequest_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProfile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_DataValidationError{
					field:  "Profile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_DataValidationError{
					field:  "Profile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProfile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_DataValidationError{
				field:  "Profile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_DataValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_DataValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_DataValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFastAnalysisRequest_DataValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFastAnalysisRequest_DataValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFastAnalysisRequest_DataValidationError{
					field:  fmt.Sprintf("Transactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFastAnalysisRequest_DataMultiError(errors)
	}

	return nil
}

// GetFastAnalysisRequest_DataMultiError is an error wrapping multiple
// validation errors returned by GetFastAnalysisRequest_Data.ValidateAll() if
// the designated constraints aren't met.
type GetFastAnalysisRequest_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFastAnalysisRequest_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFastAnalysisRequest_DataMultiError) AllErrors() []error { return m }

// GetFastAnalysisRequest_DataValidationError is the validation error returned
// by GetFastAnalysisRequest_Data.Validate if the designated constraints
// aren't met.
type GetFastAnalysisRequest_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFastAnalysisRequest_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFastAnalysisRequest_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFastAnalysisRequest_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFastAnalysisRequest_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFastAnalysisRequest_DataValidationError) ErrorName() string {
	return "GetFastAnalysisRequest_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetFastAnalysisRequest_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFastAnalysisRequest_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFastAnalysisRequest_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFastAnalysisRequest_DataValidationError{}

// Validate checks the field values on GetFastAnalysisRequest_Data_Profile with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFastAnalysisRequest_Data_Profile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFastAnalysisRequest_Data_Profile
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFastAnalysisRequest_Data_ProfileMultiError, or nil if none found.
func (m *GetFastAnalysisRequest_Data_Profile) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFastAnalysisRequest_Data_Profile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_ProfileValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_ProfileValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_ProfileValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHolders()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_ProfileValidationError{
					field:  "Holders",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_ProfileValidationError{
					field:  "Holders",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHolders()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_ProfileValidationError{
				field:  "Holders",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFastAnalysisRequest_Data_ProfileMultiError(errors)
	}

	return nil
}

// GetFastAnalysisRequest_Data_ProfileMultiError is an error wrapping multiple
// validation errors returned by
// GetFastAnalysisRequest_Data_Profile.ValidateAll() if the designated
// constraints aren't met.
type GetFastAnalysisRequest_Data_ProfileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFastAnalysisRequest_Data_ProfileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFastAnalysisRequest_Data_ProfileMultiError) AllErrors() []error { return m }

// GetFastAnalysisRequest_Data_ProfileValidationError is the validation error
// returned by GetFastAnalysisRequest_Data_Profile.Validate if the designated
// constraints aren't met.
type GetFastAnalysisRequest_Data_ProfileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFastAnalysisRequest_Data_ProfileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFastAnalysisRequest_Data_ProfileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFastAnalysisRequest_Data_ProfileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFastAnalysisRequest_Data_ProfileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFastAnalysisRequest_Data_ProfileValidationError) ErrorName() string {
	return "GetFastAnalysisRequest_Data_ProfileValidationError"
}

// Error satisfies the builtin error interface
func (e GetFastAnalysisRequest_Data_ProfileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFastAnalysisRequest_Data_Profile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFastAnalysisRequest_Data_ProfileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFastAnalysisRequest_Data_ProfileValidationError{}

// Validate checks the field values on GetFastAnalysisRequest_Data_Summary with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFastAnalysisRequest_Data_Summary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFastAnalysisRequest_Data_Summary
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFastAnalysisRequest_Data_SummaryMultiError, or nil if none found.
func (m *GetFastAnalysisRequest_Data_Summary) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFastAnalysisRequest_Data_Summary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTransactionStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "TransactionStartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "TransactionStartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_SummaryValidationError{
				field:  "TransactionStartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTransactionEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "TransactionEndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "TransactionEndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_SummaryValidationError{
				field:  "TransactionEndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOpeningDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "OpeningDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "OpeningDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOpeningDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_SummaryValidationError{
				field:  "OpeningDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBalanceDateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "BalanceDateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "BalanceDateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBalanceDateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_SummaryValidationError{
				field:  "BalanceDateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Branch

	// no validation rules for Ifsc

	// no validation rules for MicrCode

	if all {
		switch v := interface{}(m.GetMaturityAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "MaturityAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "MaturityAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaturityAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_SummaryValidationError{
				field:  "MaturityAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaturityDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "MaturityDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "MaturityDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaturityDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_SummaryValidationError{
				field:  "MaturityDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Description

	// no validation rules for InterestPayout

	// no validation rules for InterestRate

	if all {
		switch v := interface{}(m.GetPrincipalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "PrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "PrincipalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_SummaryValidationError{
				field:  "PrincipalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TenureDays

	// no validation rules for TenureMonths

	// no validation rules for TenureYears

	// no validation rules for InterestComputation

	// no validation rules for CompoundingFrequency

	if all {
		switch v := interface{}(m.GetInterestPeriodicPayoutAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "InterestPeriodicPayoutAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "InterestPeriodicPayoutAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestPeriodicPayoutAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_SummaryValidationError{
				field:  "InterestPeriodicPayoutAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InterestOnMaturity

	// no validation rules for CurrentValue

	if all {
		switch v := interface{}(m.GetRecurringAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "RecurringAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "RecurringAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_SummaryValidationError{
				field:  "RecurringAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecurringDepositDay

	if all {
		switch v := interface{}(m.GetCurrentBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "CurrentBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_SummaryValidationError{
					field:  "CurrentBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_SummaryValidationError{
				field:  "CurrentBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFastAnalysisRequest_Data_SummaryMultiError(errors)
	}

	return nil
}

// GetFastAnalysisRequest_Data_SummaryMultiError is an error wrapping multiple
// validation errors returned by
// GetFastAnalysisRequest_Data_Summary.ValidateAll() if the designated
// constraints aren't met.
type GetFastAnalysisRequest_Data_SummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFastAnalysisRequest_Data_SummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFastAnalysisRequest_Data_SummaryMultiError) AllErrors() []error { return m }

// GetFastAnalysisRequest_Data_SummaryValidationError is the validation error
// returned by GetFastAnalysisRequest_Data_Summary.Validate if the designated
// constraints aren't met.
type GetFastAnalysisRequest_Data_SummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFastAnalysisRequest_Data_SummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFastAnalysisRequest_Data_SummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFastAnalysisRequest_Data_SummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFastAnalysisRequest_Data_SummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFastAnalysisRequest_Data_SummaryValidationError) ErrorName() string {
	return "GetFastAnalysisRequest_Data_SummaryValidationError"
}

// Error satisfies the builtin error interface
func (e GetFastAnalysisRequest_Data_SummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFastAnalysisRequest_Data_Summary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFastAnalysisRequest_Data_SummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFastAnalysisRequest_Data_SummaryValidationError{}

// Validate checks the field values on GetFastAnalysisRequest_Data_Transaction
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetFastAnalysisRequest_Data_Transaction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFastAnalysisRequest_Data_Transaction with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetFastAnalysisRequest_Data_TransactionMultiError, or nil if none found.
func (m *GetFastAnalysisRequest_Data_Transaction) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFastAnalysisRequest_Data_Transaction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Amount

	// no validation rules for Narration

	// no validation rules for CurrentBalance

	if all {
		switch v := interface{}(m.GetTransactionTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_TransactionValidationError{
					field:  "TransactionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_TransactionValidationError{
					field:  "TransactionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_TransactionValidationError{
				field:  "TransactionTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TxnRefId

	if len(errors) > 0 {
		return GetFastAnalysisRequest_Data_TransactionMultiError(errors)
	}

	return nil
}

// GetFastAnalysisRequest_Data_TransactionMultiError is an error wrapping
// multiple validation errors returned by
// GetFastAnalysisRequest_Data_Transaction.ValidateAll() if the designated
// constraints aren't met.
type GetFastAnalysisRequest_Data_TransactionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFastAnalysisRequest_Data_TransactionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFastAnalysisRequest_Data_TransactionMultiError) AllErrors() []error { return m }

// GetFastAnalysisRequest_Data_TransactionValidationError is the validation
// error returned by GetFastAnalysisRequest_Data_Transaction.Validate if the
// designated constraints aren't met.
type GetFastAnalysisRequest_Data_TransactionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFastAnalysisRequest_Data_TransactionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFastAnalysisRequest_Data_TransactionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFastAnalysisRequest_Data_TransactionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFastAnalysisRequest_Data_TransactionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFastAnalysisRequest_Data_TransactionValidationError) ErrorName() string {
	return "GetFastAnalysisRequest_Data_TransactionValidationError"
}

// Error satisfies the builtin error interface
func (e GetFastAnalysisRequest_Data_TransactionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFastAnalysisRequest_Data_Transaction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFastAnalysisRequest_Data_TransactionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFastAnalysisRequest_Data_TransactionValidationError{}

// Validate checks the field values on
// GetFastAnalysisRequest_Data_Profile_Account with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFastAnalysisRequest_Data_Profile_Account) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFastAnalysisRequest_Data_Profile_Account with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetFastAnalysisRequest_Data_Profile_AccountMultiError, or nil if none found.
func (m *GetFastAnalysisRequest_Data_Profile_Account) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFastAnalysisRequest_Data_Profile_Account) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Number

	// no validation rules for AccType

	// no validation rules for FiType

	// no validation rules for Bank

	// no validation rules for LinkedAccRef

	// no validation rules for FipId

	if len(errors) > 0 {
		return GetFastAnalysisRequest_Data_Profile_AccountMultiError(errors)
	}

	return nil
}

// GetFastAnalysisRequest_Data_Profile_AccountMultiError is an error wrapping
// multiple validation errors returned by
// GetFastAnalysisRequest_Data_Profile_Account.ValidateAll() if the designated
// constraints aren't met.
type GetFastAnalysisRequest_Data_Profile_AccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFastAnalysisRequest_Data_Profile_AccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFastAnalysisRequest_Data_Profile_AccountMultiError) AllErrors() []error { return m }

// GetFastAnalysisRequest_Data_Profile_AccountValidationError is the validation
// error returned by GetFastAnalysisRequest_Data_Profile_Account.Validate if
// the designated constraints aren't met.
type GetFastAnalysisRequest_Data_Profile_AccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFastAnalysisRequest_Data_Profile_AccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFastAnalysisRequest_Data_Profile_AccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFastAnalysisRequest_Data_Profile_AccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFastAnalysisRequest_Data_Profile_AccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFastAnalysisRequest_Data_Profile_AccountValidationError) ErrorName() string {
	return "GetFastAnalysisRequest_Data_Profile_AccountValidationError"
}

// Error satisfies the builtin error interface
func (e GetFastAnalysisRequest_Data_Profile_AccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFastAnalysisRequest_Data_Profile_Account.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFastAnalysisRequest_Data_Profile_AccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFastAnalysisRequest_Data_Profile_AccountValidationError{}

// Validate checks the field values on
// GetFastAnalysisRequest_Data_Profile_Holders with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFastAnalysisRequest_Data_Profile_Holders) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFastAnalysisRequest_Data_Profile_Holders with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetFastAnalysisRequest_Data_Profile_HoldersMultiError, or nil if none found.
func (m *GetFastAnalysisRequest_Data_Profile_Holders) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFastAnalysisRequest_Data_Profile_Holders) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	for idx, item := range m.GetHolder() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFastAnalysisRequest_Data_Profile_HoldersValidationError{
						field:  fmt.Sprintf("Holder[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFastAnalysisRequest_Data_Profile_HoldersValidationError{
						field:  fmt.Sprintf("Holder[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFastAnalysisRequest_Data_Profile_HoldersValidationError{
					field:  fmt.Sprintf("Holder[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFastAnalysisRequest_Data_Profile_HoldersMultiError(errors)
	}

	return nil
}

// GetFastAnalysisRequest_Data_Profile_HoldersMultiError is an error wrapping
// multiple validation errors returned by
// GetFastAnalysisRequest_Data_Profile_Holders.ValidateAll() if the designated
// constraints aren't met.
type GetFastAnalysisRequest_Data_Profile_HoldersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFastAnalysisRequest_Data_Profile_HoldersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFastAnalysisRequest_Data_Profile_HoldersMultiError) AllErrors() []error { return m }

// GetFastAnalysisRequest_Data_Profile_HoldersValidationError is the validation
// error returned by GetFastAnalysisRequest_Data_Profile_Holders.Validate if
// the designated constraints aren't met.
type GetFastAnalysisRequest_Data_Profile_HoldersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFastAnalysisRequest_Data_Profile_HoldersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFastAnalysisRequest_Data_Profile_HoldersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFastAnalysisRequest_Data_Profile_HoldersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFastAnalysisRequest_Data_Profile_HoldersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFastAnalysisRequest_Data_Profile_HoldersValidationError) ErrorName() string {
	return "GetFastAnalysisRequest_Data_Profile_HoldersValidationError"
}

// Error satisfies the builtin error interface
func (e GetFastAnalysisRequest_Data_Profile_HoldersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFastAnalysisRequest_Data_Profile_Holders.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFastAnalysisRequest_Data_Profile_HoldersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFastAnalysisRequest_Data_Profile_HoldersValidationError{}

// Validate checks the field values on
// GetFastAnalysisRequest_Data_Profile_Holders_Holder with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFastAnalysisRequest_Data_Profile_Holders_Holder) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFastAnalysisRequest_Data_Profile_Holders_Holder with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetFastAnalysisRequest_Data_Profile_Holders_HolderMultiError, or nil if
// none found.
func (m *GetFastAnalysisRequest_Data_Profile_Holders_Holder) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFastAnalysisRequest_Data_Profile_Holders_Holder) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMobile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError{
					field:  "Mobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError{
					field:  "Mobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMobile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError{
				field:  "Mobile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Address

	// no validation rules for Email

	// no validation rules for Pan

	if len(errors) > 0 {
		return GetFastAnalysisRequest_Data_Profile_Holders_HolderMultiError(errors)
	}

	return nil
}

// GetFastAnalysisRequest_Data_Profile_Holders_HolderMultiError is an error
// wrapping multiple validation errors returned by
// GetFastAnalysisRequest_Data_Profile_Holders_Holder.ValidateAll() if the
// designated constraints aren't met.
type GetFastAnalysisRequest_Data_Profile_Holders_HolderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFastAnalysisRequest_Data_Profile_Holders_HolderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFastAnalysisRequest_Data_Profile_Holders_HolderMultiError) AllErrors() []error { return m }

// GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError is the
// validation error returned by
// GetFastAnalysisRequest_Data_Profile_Holders_Holder.Validate if the
// designated constraints aren't met.
type GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError) ErrorName() string {
	return "GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError"
}

// Error satisfies the builtin error interface
func (e GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFastAnalysisRequest_Data_Profile_Holders_Holder.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFastAnalysisRequest_Data_Profile_Holders_HolderValidationError{}
