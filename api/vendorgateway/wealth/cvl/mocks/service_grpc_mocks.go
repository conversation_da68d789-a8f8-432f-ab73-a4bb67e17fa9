// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/wealth/cvl/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	cvl "github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

// MockCvlClient is a mock of CvlClient interface.
type MockCvlClient struct {
	ctrl     *gomock.Controller
	recorder *MockCvlClientMockRecorder
}

// MockCvlClientMockRecorder is the mock recorder for MockCvlClient.
type MockCvlClientMockRecorder struct {
	mock *MockCvlClient
}

// NewMockCvlClient creates a new mock instance.
func NewMockCvlClient(ctrl *gomock.Controller) *MockCvlClient {
	mock := &MockCvlClient{ctrl: ctrl}
	mock.recorder = &MockCvlClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCvlClient) EXPECT() *MockCvlClientMockRecorder {
	return m.recorder
}

// DownloadDir mocks base method.
func (m *MockCvlClient) DownloadDir(ctx context.Context, in *cvl.DownloadDirRequest, opts ...grpc.CallOption) (*cvl.DownloadDirResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DownloadDir", varargs...)
	ret0, _ := ret[0].(*cvl.DownloadDirResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadDir indicates an expected call of DownloadDir.
func (mr *MockCvlClientMockRecorder) DownloadDir(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadDir", reflect.TypeOf((*MockCvlClient)(nil).DownloadDir), varargs...)
}

// DownloadFile mocks base method.
func (m *MockCvlClient) DownloadFile(ctx context.Context, in *cvl.DownloadFileRequest, opts ...grpc.CallOption) (*cvl.DownloadFileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DownloadFile", varargs...)
	ret0, _ := ret[0].(*cvl.DownloadFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadFile indicates an expected call of DownloadFile.
func (mr *MockCvlClientMockRecorder) DownloadFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadFile", reflect.TypeOf((*MockCvlClient)(nil).DownloadFile), varargs...)
}

// DownloadFileWithStream mocks base method.
func (m *MockCvlClient) DownloadFileWithStream(ctx context.Context, in *cvl.DownloadFileWithStreamRequest, opts ...grpc.CallOption) (cvl.Cvl_DownloadFileWithStreamClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DownloadFileWithStream", varargs...)
	ret0, _ := ret[0].(cvl.Cvl_DownloadFileWithStreamClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadFileWithStream indicates an expected call of DownloadFileWithStream.
func (mr *MockCvlClientMockRecorder) DownloadFileWithStream(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadFileWithStream", reflect.TypeOf((*MockCvlClient)(nil).DownloadFileWithStream), varargs...)
}

// GetPanStatus mocks base method.
func (m *MockCvlClient) GetPanStatus(ctx context.Context, in *cvl.GetPanStatusRequest, opts ...grpc.CallOption) (*cvl.GetPanStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPanStatus", varargs...)
	ret0, _ := ret[0].(*cvl.GetPanStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPanStatus indicates an expected call of GetPanStatus.
func (mr *MockCvlClientMockRecorder) GetPanStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPanStatus", reflect.TypeOf((*MockCvlClient)(nil).GetPanStatus), varargs...)
}

// GetPassword mocks base method.
func (m *MockCvlClient) GetPassword(ctx context.Context, in *cvl.GetPasswordRequest, opts ...grpc.CallOption) (*cvl.GetPasswordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPassword", varargs...)
	ret0, _ := ret[0].(*cvl.GetPasswordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPassword indicates an expected call of GetPassword.
func (mr *MockCvlClientMockRecorder) GetPassword(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPassword", reflect.TypeOf((*MockCvlClient)(nil).GetPassword), varargs...)
}

// InsertUpdateKycRecord mocks base method.
func (m *MockCvlClient) InsertUpdateKycRecord(ctx context.Context, in *cvl.InsertUpdateKycRecordRequest, opts ...grpc.CallOption) (*cvl.InsertUpdateKycRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InsertUpdateKycRecord", varargs...)
	ret0, _ := ret[0].(*cvl.InsertUpdateKycRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertUpdateKycRecord indicates an expected call of InsertUpdateKycRecord.
func (mr *MockCvlClientMockRecorder) InsertUpdateKycRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertUpdateKycRecord", reflect.TypeOf((*MockCvlClient)(nil).InsertUpdateKycRecord), varargs...)
}

// ListDirFiles mocks base method.
func (m *MockCvlClient) ListDirFiles(ctx context.Context, in *cvl.ListDirFilesRequest, opts ...grpc.CallOption) (*cvl.ListDirFilesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListDirFiles", varargs...)
	ret0, _ := ret[0].(*cvl.ListDirFilesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListDirFiles indicates an expected call of ListDirFiles.
func (mr *MockCvlClientMockRecorder) ListDirFiles(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListDirFiles", reflect.TypeOf((*MockCvlClient)(nil).ListDirFiles), varargs...)
}

// PanDetailsFetch mocks base method.
func (m *MockCvlClient) PanDetailsFetch(ctx context.Context, in *cvl.PanDetailsFetchRequest, opts ...grpc.CallOption) (*cvl.PanDetailsFetchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PanDetailsFetch", varargs...)
	ret0, _ := ret[0].(*cvl.PanDetailsFetchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PanDetailsFetch indicates an expected call of PanDetailsFetch.
func (mr *MockCvlClientMockRecorder) PanDetailsFetch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PanDetailsFetch", reflect.TypeOf((*MockCvlClient)(nil).PanDetailsFetch), varargs...)
}

// UploadFile mocks base method.
func (m *MockCvlClient) UploadFile(ctx context.Context, in *cvl.UploadFileRequest, opts ...grpc.CallOption) (*cvl.UploadFileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UploadFile", varargs...)
	ret0, _ := ret[0].(*cvl.UploadFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadFile indicates an expected call of UploadFile.
func (mr *MockCvlClientMockRecorder) UploadFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadFile", reflect.TypeOf((*MockCvlClient)(nil).UploadFile), varargs...)
}

// MockCvl_DownloadFileWithStreamClient is a mock of Cvl_DownloadFileWithStreamClient interface.
type MockCvl_DownloadFileWithStreamClient struct {
	ctrl     *gomock.Controller
	recorder *MockCvl_DownloadFileWithStreamClientMockRecorder
}

// MockCvl_DownloadFileWithStreamClientMockRecorder is the mock recorder for MockCvl_DownloadFileWithStreamClient.
type MockCvl_DownloadFileWithStreamClientMockRecorder struct {
	mock *MockCvl_DownloadFileWithStreamClient
}

// NewMockCvl_DownloadFileWithStreamClient creates a new mock instance.
func NewMockCvl_DownloadFileWithStreamClient(ctrl *gomock.Controller) *MockCvl_DownloadFileWithStreamClient {
	mock := &MockCvl_DownloadFileWithStreamClient{ctrl: ctrl}
	mock.recorder = &MockCvl_DownloadFileWithStreamClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCvl_DownloadFileWithStreamClient) EXPECT() *MockCvl_DownloadFileWithStreamClientMockRecorder {
	return m.recorder
}

// CloseSend mocks base method.
func (m *MockCvl_DownloadFileWithStreamClient) CloseSend() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseSend")
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseSend indicates an expected call of CloseSend.
func (mr *MockCvl_DownloadFileWithStreamClientMockRecorder) CloseSend() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseSend", reflect.TypeOf((*MockCvl_DownloadFileWithStreamClient)(nil).CloseSend))
}

// Context mocks base method.
func (m *MockCvl_DownloadFileWithStreamClient) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockCvl_DownloadFileWithStreamClientMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockCvl_DownloadFileWithStreamClient)(nil).Context))
}

// Header mocks base method.
func (m *MockCvl_DownloadFileWithStreamClient) Header() (metadata.MD, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Header")
	ret0, _ := ret[0].(metadata.MD)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Header indicates an expected call of Header.
func (mr *MockCvl_DownloadFileWithStreamClientMockRecorder) Header() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Header", reflect.TypeOf((*MockCvl_DownloadFileWithStreamClient)(nil).Header))
}

// Recv mocks base method.
func (m *MockCvl_DownloadFileWithStreamClient) Recv() (*cvl.DownloadFileWithStreamResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Recv")
	ret0, _ := ret[0].(*cvl.DownloadFileWithStreamResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Recv indicates an expected call of Recv.
func (mr *MockCvl_DownloadFileWithStreamClientMockRecorder) Recv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Recv", reflect.TypeOf((*MockCvl_DownloadFileWithStreamClient)(nil).Recv))
}

// RecvMsg mocks base method.
func (m_2 *MockCvl_DownloadFileWithStreamClient) RecvMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "RecvMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockCvl_DownloadFileWithStreamClientMockRecorder) RecvMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockCvl_DownloadFileWithStreamClient)(nil).RecvMsg), m)
}

// SendMsg mocks base method.
func (m_2 *MockCvl_DownloadFileWithStreamClient) SendMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "SendMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockCvl_DownloadFileWithStreamClientMockRecorder) SendMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockCvl_DownloadFileWithStreamClient)(nil).SendMsg), m)
}

// Trailer mocks base method.
func (m *MockCvl_DownloadFileWithStreamClient) Trailer() metadata.MD {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Trailer")
	ret0, _ := ret[0].(metadata.MD)
	return ret0
}

// Trailer indicates an expected call of Trailer.
func (mr *MockCvl_DownloadFileWithStreamClientMockRecorder) Trailer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Trailer", reflect.TypeOf((*MockCvl_DownloadFileWithStreamClient)(nil).Trailer))
}

// MockCvlServer is a mock of CvlServer interface.
type MockCvlServer struct {
	ctrl     *gomock.Controller
	recorder *MockCvlServerMockRecorder
}

// MockCvlServerMockRecorder is the mock recorder for MockCvlServer.
type MockCvlServerMockRecorder struct {
	mock *MockCvlServer
}

// NewMockCvlServer creates a new mock instance.
func NewMockCvlServer(ctrl *gomock.Controller) *MockCvlServer {
	mock := &MockCvlServer{ctrl: ctrl}
	mock.recorder = &MockCvlServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCvlServer) EXPECT() *MockCvlServerMockRecorder {
	return m.recorder
}

// DownloadDir mocks base method.
func (m *MockCvlServer) DownloadDir(arg0 context.Context, arg1 *cvl.DownloadDirRequest) (*cvl.DownloadDirResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadDir", arg0, arg1)
	ret0, _ := ret[0].(*cvl.DownloadDirResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadDir indicates an expected call of DownloadDir.
func (mr *MockCvlServerMockRecorder) DownloadDir(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadDir", reflect.TypeOf((*MockCvlServer)(nil).DownloadDir), arg0, arg1)
}

// DownloadFile mocks base method.
func (m *MockCvlServer) DownloadFile(arg0 context.Context, arg1 *cvl.DownloadFileRequest) (*cvl.DownloadFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadFile", arg0, arg1)
	ret0, _ := ret[0].(*cvl.DownloadFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadFile indicates an expected call of DownloadFile.
func (mr *MockCvlServerMockRecorder) DownloadFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadFile", reflect.TypeOf((*MockCvlServer)(nil).DownloadFile), arg0, arg1)
}

// DownloadFileWithStream mocks base method.
func (m *MockCvlServer) DownloadFileWithStream(arg0 *cvl.DownloadFileWithStreamRequest, arg1 cvl.Cvl_DownloadFileWithStreamServer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadFileWithStream", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DownloadFileWithStream indicates an expected call of DownloadFileWithStream.
func (mr *MockCvlServerMockRecorder) DownloadFileWithStream(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadFileWithStream", reflect.TypeOf((*MockCvlServer)(nil).DownloadFileWithStream), arg0, arg1)
}

// GetPanStatus mocks base method.
func (m *MockCvlServer) GetPanStatus(arg0 context.Context, arg1 *cvl.GetPanStatusRequest) (*cvl.GetPanStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPanStatus", arg0, arg1)
	ret0, _ := ret[0].(*cvl.GetPanStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPanStatus indicates an expected call of GetPanStatus.
func (mr *MockCvlServerMockRecorder) GetPanStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPanStatus", reflect.TypeOf((*MockCvlServer)(nil).GetPanStatus), arg0, arg1)
}

// GetPassword mocks base method.
func (m *MockCvlServer) GetPassword(arg0 context.Context, arg1 *cvl.GetPasswordRequest) (*cvl.GetPasswordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPassword", arg0, arg1)
	ret0, _ := ret[0].(*cvl.GetPasswordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPassword indicates an expected call of GetPassword.
func (mr *MockCvlServerMockRecorder) GetPassword(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPassword", reflect.TypeOf((*MockCvlServer)(nil).GetPassword), arg0, arg1)
}

// InsertUpdateKycRecord mocks base method.
func (m *MockCvlServer) InsertUpdateKycRecord(arg0 context.Context, arg1 *cvl.InsertUpdateKycRecordRequest) (*cvl.InsertUpdateKycRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertUpdateKycRecord", arg0, arg1)
	ret0, _ := ret[0].(*cvl.InsertUpdateKycRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertUpdateKycRecord indicates an expected call of InsertUpdateKycRecord.
func (mr *MockCvlServerMockRecorder) InsertUpdateKycRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertUpdateKycRecord", reflect.TypeOf((*MockCvlServer)(nil).InsertUpdateKycRecord), arg0, arg1)
}

// ListDirFiles mocks base method.
func (m *MockCvlServer) ListDirFiles(arg0 context.Context, arg1 *cvl.ListDirFilesRequest) (*cvl.ListDirFilesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListDirFiles", arg0, arg1)
	ret0, _ := ret[0].(*cvl.ListDirFilesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListDirFiles indicates an expected call of ListDirFiles.
func (mr *MockCvlServerMockRecorder) ListDirFiles(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListDirFiles", reflect.TypeOf((*MockCvlServer)(nil).ListDirFiles), arg0, arg1)
}

// PanDetailsFetch mocks base method.
func (m *MockCvlServer) PanDetailsFetch(arg0 context.Context, arg1 *cvl.PanDetailsFetchRequest) (*cvl.PanDetailsFetchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PanDetailsFetch", arg0, arg1)
	ret0, _ := ret[0].(*cvl.PanDetailsFetchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PanDetailsFetch indicates an expected call of PanDetailsFetch.
func (mr *MockCvlServerMockRecorder) PanDetailsFetch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PanDetailsFetch", reflect.TypeOf((*MockCvlServer)(nil).PanDetailsFetch), arg0, arg1)
}

// UploadFile mocks base method.
func (m *MockCvlServer) UploadFile(arg0 context.Context, arg1 *cvl.UploadFileRequest) (*cvl.UploadFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadFile", arg0, arg1)
	ret0, _ := ret[0].(*cvl.UploadFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadFile indicates an expected call of UploadFile.
func (mr *MockCvlServerMockRecorder) UploadFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadFile", reflect.TypeOf((*MockCvlServer)(nil).UploadFile), arg0, arg1)
}

// MockUnsafeCvlServer is a mock of UnsafeCvlServer interface.
type MockUnsafeCvlServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCvlServerMockRecorder
}

// MockUnsafeCvlServerMockRecorder is the mock recorder for MockUnsafeCvlServer.
type MockUnsafeCvlServerMockRecorder struct {
	mock *MockUnsafeCvlServer
}

// NewMockUnsafeCvlServer creates a new mock instance.
func NewMockUnsafeCvlServer(ctrl *gomock.Controller) *MockUnsafeCvlServer {
	mock := &MockUnsafeCvlServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCvlServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCvlServer) EXPECT() *MockUnsafeCvlServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCvlServer mocks base method.
func (m *MockUnsafeCvlServer) mustEmbedUnimplementedCvlServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCvlServer")
}

// mustEmbedUnimplementedCvlServer indicates an expected call of mustEmbedUnimplementedCvlServer.
func (mr *MockUnsafeCvlServerMockRecorder) mustEmbedUnimplementedCvlServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCvlServer", reflect.TypeOf((*MockUnsafeCvlServer)(nil).mustEmbedUnimplementedCvlServer))
}

// MockCvl_DownloadFileWithStreamServer is a mock of Cvl_DownloadFileWithStreamServer interface.
type MockCvl_DownloadFileWithStreamServer struct {
	ctrl     *gomock.Controller
	recorder *MockCvl_DownloadFileWithStreamServerMockRecorder
}

// MockCvl_DownloadFileWithStreamServerMockRecorder is the mock recorder for MockCvl_DownloadFileWithStreamServer.
type MockCvl_DownloadFileWithStreamServerMockRecorder struct {
	mock *MockCvl_DownloadFileWithStreamServer
}

// NewMockCvl_DownloadFileWithStreamServer creates a new mock instance.
func NewMockCvl_DownloadFileWithStreamServer(ctrl *gomock.Controller) *MockCvl_DownloadFileWithStreamServer {
	mock := &MockCvl_DownloadFileWithStreamServer{ctrl: ctrl}
	mock.recorder = &MockCvl_DownloadFileWithStreamServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCvl_DownloadFileWithStreamServer) EXPECT() *MockCvl_DownloadFileWithStreamServerMockRecorder {
	return m.recorder
}

// Context mocks base method.
func (m *MockCvl_DownloadFileWithStreamServer) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockCvl_DownloadFileWithStreamServerMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockCvl_DownloadFileWithStreamServer)(nil).Context))
}

// RecvMsg mocks base method.
func (m_2 *MockCvl_DownloadFileWithStreamServer) RecvMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "RecvMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockCvl_DownloadFileWithStreamServerMockRecorder) RecvMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockCvl_DownloadFileWithStreamServer)(nil).RecvMsg), m)
}

// Send mocks base method.
func (m *MockCvl_DownloadFileWithStreamServer) Send(arg0 *cvl.DownloadFileWithStreamResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send indicates an expected call of Send.
func (mr *MockCvl_DownloadFileWithStreamServerMockRecorder) Send(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockCvl_DownloadFileWithStreamServer)(nil).Send), arg0)
}

// SendHeader mocks base method.
func (m *MockCvl_DownloadFileWithStreamServer) SendHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendHeader indicates an expected call of SendHeader.
func (mr *MockCvl_DownloadFileWithStreamServerMockRecorder) SendHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendHeader", reflect.TypeOf((*MockCvl_DownloadFileWithStreamServer)(nil).SendHeader), arg0)
}

// SendMsg mocks base method.
func (m_2 *MockCvl_DownloadFileWithStreamServer) SendMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "SendMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockCvl_DownloadFileWithStreamServerMockRecorder) SendMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockCvl_DownloadFileWithStreamServer)(nil).SendMsg), m)
}

// SetHeader mocks base method.
func (m *MockCvl_DownloadFileWithStreamServer) SetHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetHeader indicates an expected call of SetHeader.
func (mr *MockCvl_DownloadFileWithStreamServerMockRecorder) SetHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeader", reflect.TypeOf((*MockCvl_DownloadFileWithStreamServer)(nil).SetHeader), arg0)
}

// SetTrailer mocks base method.
func (m *MockCvl_DownloadFileWithStreamServer) SetTrailer(arg0 metadata.MD) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetTrailer", arg0)
}

// SetTrailer indicates an expected call of SetTrailer.
func (mr *MockCvl_DownloadFileWithStreamServerMockRecorder) SetTrailer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTrailer", reflect.TypeOf((*MockCvl_DownloadFileWithStreamServer)(nil).SetTrailer), arg0)
}
