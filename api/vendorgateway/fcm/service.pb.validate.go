// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/fcm/service.proto

package fcm

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	comms "github.com/epifi/gamma/api/comms"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = comms.CommsMessageSubStatus(0)
)

// Validate checks the field values on SendMessageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendMessageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendMessageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendMessageRequestMultiError, or nil if none found.
func (m *SendMessageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendMessageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendMessageRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendMessageRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendMessageRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNotificationMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendMessageRequestValidationError{
					field:  "NotificationMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendMessageRequestValidationError{
					field:  "NotificationMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotificationMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendMessageRequestValidationError{
				field:  "NotificationMessage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeviceToken

	// no validation rules for AnalyticsLabel

	if len(errors) > 0 {
		return SendMessageRequestMultiError(errors)
	}

	return nil
}

// SendMessageRequestMultiError is an error wrapping multiple validation errors
// returned by SendMessageRequest.ValidateAll() if the designated constraints
// aren't met.
type SendMessageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendMessageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendMessageRequestMultiError) AllErrors() []error { return m }

// SendMessageRequestValidationError is the validation error returned by
// SendMessageRequest.Validate if the designated constraints aren't met.
type SendMessageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendMessageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendMessageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendMessageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendMessageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendMessageRequestValidationError) ErrorName() string {
	return "SendMessageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendMessageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendMessageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendMessageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendMessageRequestValidationError{}

// Validate checks the field values on SendMessageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendMessageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendMessageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendMessageResponseMultiError, or nil if none found.
func (m *SendMessageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SendMessageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendMessageResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendMessageResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendMessageResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NotificationId

	// no validation rules for SubStatus

	if len(errors) > 0 {
		return SendMessageResponseMultiError(errors)
	}

	return nil
}

// SendMessageResponseMultiError is an error wrapping multiple validation
// errors returned by SendMessageResponse.ValidateAll() if the designated
// constraints aren't met.
type SendMessageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendMessageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendMessageResponseMultiError) AllErrors() []error { return m }

// SendMessageResponseValidationError is the validation error returned by
// SendMessageResponse.Validate if the designated constraints aren't met.
type SendMessageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendMessageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendMessageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendMessageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendMessageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendMessageResponseValidationError) ErrorName() string {
	return "SendMessageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SendMessageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendMessageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendMessageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendMessageResponseValidationError{}
