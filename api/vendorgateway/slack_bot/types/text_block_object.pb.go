// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/slack_bot/types/text_block_object.proto

package types

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TextBlockObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Text     string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Emoji    bool   `protobuf:"varint,3,opt,name=emoji,proto3" json:"emoji,omitempty"`
	Verbatim bool   `protobuf:"varint,4,opt,name=verbatim,proto3" json:"verbatim,omitempty"`
}

func (x *TextBlockObject) Reset() {
	*x = TextBlockObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_slack_bot_types_text_block_object_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextBlockObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextBlockObject) ProtoMessage() {}

func (x *TextBlockObject) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_slack_bot_types_text_block_object_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextBlockObject.ProtoReflect.Descriptor instead.
func (*TextBlockObject) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_slack_bot_types_text_block_object_proto_rawDescGZIP(), []int{0}
}

func (x *TextBlockObject) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TextBlockObject) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *TextBlockObject) GetEmoji() bool {
	if x != nil {
		return x.Emoji
	}
	return false
}

func (x *TextBlockObject) GetVerbatim() bool {
	if x != nil {
		return x.Verbatim
	}
	return false
}

var File_api_vendorgateway_slack_bot_types_text_block_object_proto protoreflect.FileDescriptor

var file_api_vendorgateway_slack_bot_types_text_block_object_proto_rawDesc = []byte{
	0x0a, 0x39, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b,
	0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x6b, 0x0a, 0x0f, 0x54, 0x65,
	0x78, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x6f, 0x6a, 0x69, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x65, 0x6d, 0x6f, 0x6a, 0x69, 0x12, 0x1a, 0x0a, 0x08, 0x76,
	0x65, 0x72, 0x62, 0x61, 0x74, 0x69, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x76,
	0x65, 0x72, 0x62, 0x61, 0x74, 0x69, 0x6d, 0x42, 0x74, 0x0a, 0x38, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x73, 0x6c,
	0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_slack_bot_types_text_block_object_proto_rawDescOnce sync.Once
	file_api_vendorgateway_slack_bot_types_text_block_object_proto_rawDescData = file_api_vendorgateway_slack_bot_types_text_block_object_proto_rawDesc
)

func file_api_vendorgateway_slack_bot_types_text_block_object_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_slack_bot_types_text_block_object_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_slack_bot_types_text_block_object_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_slack_bot_types_text_block_object_proto_rawDescData)
	})
	return file_api_vendorgateway_slack_bot_types_text_block_object_proto_rawDescData
}

var file_api_vendorgateway_slack_bot_types_text_block_object_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_vendorgateway_slack_bot_types_text_block_object_proto_goTypes = []interface{}{
	(*TextBlockObject)(nil), // 0: vendorgateway.slack_bot.types.TextBlockObject
}
var file_api_vendorgateway_slack_bot_types_text_block_object_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_slack_bot_types_text_block_object_proto_init() }
func file_api_vendorgateway_slack_bot_types_text_block_object_proto_init() {
	if File_api_vendorgateway_slack_bot_types_text_block_object_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_slack_bot_types_text_block_object_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextBlockObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_slack_bot_types_text_block_object_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_slack_bot_types_text_block_object_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_slack_bot_types_text_block_object_proto_depIdxs,
		MessageInfos:      file_api_vendorgateway_slack_bot_types_text_block_object_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_slack_bot_types_text_block_object_proto = out.File
	file_api_vendorgateway_slack_bot_types_text_block_object_proto_rawDesc = nil
	file_api_vendorgateway_slack_bot_types_text_block_object_proto_goTypes = nil
	file_api_vendorgateway_slack_bot_types_text_block_object_proto_depIdxs = nil
}
