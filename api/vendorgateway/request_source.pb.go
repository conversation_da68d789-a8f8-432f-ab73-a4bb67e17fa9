// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/request_source.proto

package vendorgateway

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// A vendor api may require different handling based on the source of the request.
// This enum can be propagated from the origin of the request and the implementation
// can use this information to process the vendor API call differently.
// Ex: Federal B2c fund transfer API will need to use different credentials based on the request source,
//
//	REQUEST_SOURCE_UNSPECIFIED will correspond to addition of default credentials in this case
type RequestSource int32

const (
	RequestSource_REQUEST_SOURCE_UNSPECIFIED      RequestSource = 0
	RequestSource_REQUEST_SOURCE_LOANS            RequestSource = 1
	RequestSource_REQUEST_SOURCE_BILLPAY_RECHARGE RequestSource = 2
)

// Enum value maps for RequestSource.
var (
	RequestSource_name = map[int32]string{
		0: "REQUEST_SOURCE_UNSPECIFIED",
		1: "REQUEST_SOURCE_LOANS",
		2: "REQUEST_SOURCE_BILLPAY_RECHARGE",
	}
	RequestSource_value = map[string]int32{
		"REQUEST_SOURCE_UNSPECIFIED":      0,
		"REQUEST_SOURCE_LOANS":            1,
		"REQUEST_SOURCE_BILLPAY_RECHARGE": 2,
	}
)

func (x RequestSource) Enum() *RequestSource {
	p := new(RequestSource)
	*p = x
	return p
}

func (x RequestSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RequestSource) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_request_source_proto_enumTypes[0].Descriptor()
}

func (RequestSource) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_request_source_proto_enumTypes[0]
}

func (x RequestSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RequestSource.Descriptor instead.
func (RequestSource) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_request_source_proto_rawDescGZIP(), []int{0}
}

var File_api_vendorgateway_request_source_proto protoreflect.FileDescriptor

var file_api_vendorgateway_request_source_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2a, 0x6e, 0x0a, 0x0d, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x1a,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x53, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x42, 0x49, 0x4c, 0x4c, 0x50, 0x41, 0x59,
	0x5f, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x02, 0x42, 0x54, 0x0a, 0x28, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_request_source_proto_rawDescOnce sync.Once
	file_api_vendorgateway_request_source_proto_rawDescData = file_api_vendorgateway_request_source_proto_rawDesc
)

func file_api_vendorgateway_request_source_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_request_source_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_request_source_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_request_source_proto_rawDescData)
	})
	return file_api_vendorgateway_request_source_proto_rawDescData
}

var file_api_vendorgateway_request_source_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_vendorgateway_request_source_proto_goTypes = []interface{}{
	(RequestSource)(0), // 0: api.vendorgateway.RequestSource
}
var file_api_vendorgateway_request_source_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_request_source_proto_init() }
func file_api_vendorgateway_request_source_proto_init() {
	if File_api_vendorgateway_request_source_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_request_source_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_request_source_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_request_source_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_request_source_proto_enumTypes,
	}.Build()
	File_api_vendorgateway_request_source_proto = out.File
	file_api_vendorgateway_request_source_proto_rawDesc = nil
	file_api_vendorgateway_request_source_proto_goTypes = nil
	file_api_vendorgateway_request_source_proto_depIdxs = nil
}
