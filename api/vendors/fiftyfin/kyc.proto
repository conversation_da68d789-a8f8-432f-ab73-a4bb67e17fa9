syntax = "proto3";

package vendors.fiftyfin;

import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/vendors/fiftyfin";
option java_package = "com.github.epifi.gamma.api.vendors.fiftyfin";

message InitiateKycProcessRequest {
  bool send_on_email = 1 [json_name = 'send_on_email'];
  string user_id = 2 [json_name = 'user_id'];
  string customer_identifier_type = 3 [json_name = 'customer_identifier_type'];
  bool is_kyc_image_uploaded = 4 [json_name = 'is_kyc_image_uploaded'];
  bool restart_kyc = 5 [json_name = 'restart_kyc'];
}

message InitiateKycProcessResponse {
  int32 code = 1 [json_name = 'code'];
  string detail = 2 [json_name = 'detail'];
  Data data = 3 [json_name = 'data'];

  message Data {
    string workflow_name = 1 [json_name = 'workflow_name'];
    bool auto_approved = 2 [json_name = 'auto_approved'];
    string id = 4 [json_name = 'id'];
    string customer_identifier = 5 [json_name = 'customer_identifier'];
    string reference_id = 6 [json_name = 'reference_id'];
    string transaction_id = 7 [json_name = 'transaction_id'];
    int32 expire_in_days = 8 [json_name = 'expire_in_days'];
    bool reminder_registered = 9 [json_name = 'reminder_registered'];
    string status = 10 [json_name = "status"];
    string template_id = 11 [json_name = "template_id"];
    bool is_kyc_image_uploaded = 12 [json_name = "is_kyc_image_uploaded"];
    bool is_ckyc_verified = 13 [json_name = "is_ckyc_verified"];
    string kyc_url = 14 [json_name = "kyc_url"];
    repeated string entities = 15 [json_name = "entities"];
    string event = 16 [json_name = "event"];
    Payload payload = 17 [json_name = "payload"];
    AccessToken token = 18 [json_name = "access_token"];
  }

  message AccessToken {
    string id = 1 [json_name = "id"];
    string created_at = 2 [json_name = "created_at"];
    string entity_id = 3 [json_name = "entity_id"];
    string valid_till = 4 [json_name = "valid_till"];
  }

  message Payload {
    KycRequest kyc_request = 1 [json_name = 'kyc_request'];
  }

  message KycRequest {
    string transaction_id = 1 [json_name = 'transaction_id'];
    string reference_id = 2 [json_name = 'reference_id'];
    string terminal_status = 3 [json_name = 'terminal_status'];
    string id = 4 [json_name = 'id'];
    string status = 8 [json_name = 'status'];
  }
}

message AddExtraKycDetailsRequest {
  string user_id = 1 [json_name = 'user_id'];
  repeated google.protobuf.Struct extra_details = 2 [json_name = 'extra_details'];
}

message AddExtraKycDetailsResponse {
  int32 code = 1 [json_name = 'code'];
  string detail = 2 [json_name = 'detail'];
  bool data = 3 [json_name = 'data'];
}


