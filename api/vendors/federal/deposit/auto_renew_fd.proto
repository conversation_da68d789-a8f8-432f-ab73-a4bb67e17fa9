// proapi/vendorgateway/openbanking/deposit/service.prototolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendors.federal.deposit;

option go_package = "github.com/epifi/gamma/api/vendors/federal/deposit";
option java_package = "com.github.epifi.gamma.api.vendors.federal.deposit";

// Represents request message to renew FD deposit accounts.
message AutoRenewFdRequest {
  // Call back URL. API will send the response back to this URL, provided by the client. (mandatory)
  string callback_url = 1 [json_name = "RespUrl"];

  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include sender_id, access code and sender code
  // (mandatory)
  string sender_code = 2 [json_name = "SenderCode"];
  string service_access_id = 3 [json_name = "ServiceAccessId"];
  string service_access_code = 4 [json_name = "ServiceAccessCode"];

  // Device ID of the client that has initiated the request (mandatory)
  string device_id = 5 [json_name = "DeviceId"];

  // Device token that is issued by the federal bank at the time of device registration (mandatory)
  string device_token = 6 [json_name = "DeviceToken"];

  // Client should pass unique value to each call, to identify the request (mandatory)
  string request_id = 7 [json_name = "RequestId"];

  // Customer identification information (mandatory)
  string customer_id = 8 [json_name = "CustomerId"];

  // Customer First Name. Alphanumeric with spaces are allowed (mandatory)
  string first_name = 9 [json_name = "FirstName"];

  // Customer Last Name. Alphanumeric with spaces are allowed (mandatory)
  string last_name = 10 [json_name = "LastName"];

  // (mandatory)
  string salutation = 11 [json_name = "Salutation"];

  // Currency - ex: INR (mandatory)
  string currency = 12 [json_name = "Currency"];

  // Cred block contains the user credentials in encrypted form.
  string cred_block = 13 [json_name = "CredBlock"];

  // Scheme Code. Within a scheme type, Accounts may classified into different configurable scheme codes. (mandatory)
  // For FDs - FDMI, FDQI, FDCC, FDFTS
  // For SDs - FFSDR
  // For RDs - FSFRI
  string renewal_scheme_type = 14 [json_name = "RenewalSchmType"];

  // the renewal term for which the customer wants to renew deposit account. (mandatory)
  RenewalTerm renewal_term = 15 [json_name = "RenewalTerm"];

  // The details of the account to which the amount is to be transferred on maturity. (mandatory)
  // repay account and operative account should belong to same customer.
  string repay_account_num = 16 [json_name = "RepayAccountNum"];

  // Type of renewal that must be carried out for the deposit account. (optional)
  //F – Fixed Amount
  //I – Interest Only
  //M – Maturity only
  //P – Principle only
  //E – Principle + Extra
  // Relevant only if auto_renewal_flag is Y
  string renewal_option = 17 [json_name = "RenewalOption"];

  // In case of Fixed Amount renewal_option, renewal_amount will be debited from this account. (optional)
  // Relevant only if auto_renewal_flag is Y
  string renewal_account_num = 18 [json_name = "RenewalAccountNum"];

  // Flag to indicate, if deposit account must be automatically closed at the end of deposit period. Y- Yes, N- No.
  // Value should be (Y/N/y/n) (optional)
  string auto_close_on_maturity = 19 [json_name = "AutoCloseOnMaturity"];

  // Y- Yes, N- No.Value should be (Y/N/y/n) (optional)
  // If set to Y, renewal_option attribute will help in identifying the type of renewal to be done at end of deposit term.
  string auto_renewal_flg = 20 [json_name = "AutoRenewalflg"];

  // mobile number of customer (mandatory)
  string mobile_number = 21 [json_name = "MobileNumber"];

  // email of customer (mandatory)
  string email = 22 [json_name = "Email"];

  // Represents the duration for which the customer wants to renew deposit account.
  message RenewalTerm {
    // No of days wish to deposit Eg: - "Days": "0", Numeric values are allowed.
    string days = 1 [json_name = "Days"];

    // No of Months wish to deposit Eg: - "Months": "36".Numeric values are allowed.
    string months = 2 [json_name = "Months"];
  }
}

// Represents the acknowledgement message received from Federal.
// The below acknowledge response type is received for AutoRenewFdRequest.
message AutoRenewFdAckResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  string request_id = 2 [json_name = "RequestId"];
  string device_token = 3 [json_name = "DeviceToken"];
  string tran_time_stamp = 4 [json_name = "TranTimeStamp"];
  string response = 5 [json_name = "Response"];
  string reason = 6 [json_name = "Reason"];
}

// Represents the error received from Federal for AutoRenewFd APIs.
message AutoRenewFdError {
  // error_code defined at Federal end.
  string error_code = 1 [json_name = "ErrorCode"];

  // the reason for the error.
  string reason = 2 [json_name = "Reason"];
}
