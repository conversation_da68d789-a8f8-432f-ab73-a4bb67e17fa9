// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendors.federal.payment;

option go_package = "github.com/epifi/gamma/api/vendors/federal/payment";
option java_package = "com.github.epifi.gamma.api.vendors.federal.payment";

// Remitter Details
// Bank Account from which the monies are transferred
message Remitter {
  // Remitter’s Name
  string name = 1 [json_name = "Name"];
  // Remitter’s Account Number. Amount will be debited from this Account.
  string account_number = 2 [json_name = "AccountNumber"];
  // Conditional Mandatory (This is Mandatory incase of Acc_Val_Req is’ N’ in database for sender).
  string account_type = 3 [json_name = "Acctype"];
  // Remit<PERSON>’s mobile. Notifications will be sent to this mobile.
  string mobile = 4 [json_name = "Mobile"];
  // Remitter’s e-mail. Notifications will be sent on this Id.
  string email = 5 [json_name = "Email"];
}

// Beneficiary Details
// Bank Account to which the monies are transferred
message Beneficiary {
  // Beneficiary Name
  string name = 1 [json_name = "Name"];
  // Beneficiary's Acccount Number
  string account_number = 2 [json_name = "AccountNumber"];
  //    IFSC of the Beneficiary’s home branch
  string ifsc = 3 [json_name = "Ifsc"];
  // Beneficiary’s mobile
  string mobile = 4 [json_name = "Mobile"];
  // Beneficiary’s e-mail
  string email = 5 [json_name = "Email"];
}

message IMPSRequest {

  // Call back URL. API will send the response back to this URL, provided by the client.
  string callback_url = 1 [json_name = "RespUrl"];

  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 2 [json_name = "SenderCode"];
  string access_id = 3 [json_name = "ServiceAccessId"];
  string access_code = 4 [json_name = "ServiceAccessCode"];

  // Device ID of the client that has initiated the request
  string device_id = 5 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 6 [json_name = "DeviceToken"];
  // Encrypted PIN block that is required to authorise the transaction
  // All transaction requests except for low risk ones need Secure PIN for authorisation
  string secure_pin = 7 [json_name = "CredBlock"];
  // Client should pass unique value to each call, to identify the request
  // TODO(pruthvi): Confirm the format of this field
  string request_id = 8 [json_name = "RequestId"];
  // Date of Transaction in “DD-MM-YYYY” format E.g. “23-03-2020”
  string transaction_date = 9 [json_name = "TranDate"];

  // Remitter details
  Remitter remitter = 10 [json_name = "RemitterDetails"];

  // Beneficiary details
  Beneficiary beneficiary = 11 [json_name = "BeneficiaryDetails"];
  //  Amount to be Transferred in xxx.yy Format E.g 123.45 INR
  string amount = 12 [json_name = "Amount"];
  // Description of the Transaction
  string remarks = 13 [json_name = "Remarks"];
}

message IMPSResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 3 [json_name = "DeviceToken"];
  // Transaction Timestamp
  string trans_timestamp = 4 [json_name = "TranTimeStamp"];
  // Response Code for the Transaction
  string response_code = 5 [json_name = "Response"];
  // Response Description for the Transaction
  string response_desc = 6 [json_name = "Reason"];
}

message NEFTRequest {

  // Call back URL. API will send the response back to this URL, provided by the client.
  string callback_url = 1 [json_name = "RespUrl"];

  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 2 [json_name = "SenderCode"];
  string access_id = 3 [json_name = "ServiceAccessId"];
  string access_code = 4 [json_name = "ServiceAccessCode"];

  // Device ID of the client that has initiated the request
  string device_id = 5 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 6 [json_name = "DeviceToken"];
  // Encrypted PIN block that is required to authorise the transaction
  // All transaction requests except for low risk ones need Secure PIN for authorisation
  string secure_pin = 7 [json_name = "CredBlock"];
  // Client should pass unique value to each call, to identify the request
  // TODO(raunak): Confirm the format of this field
  string request_id = 8 [json_name = "RequestId"];
  // Date of Transaction in “DD-MM-YYYY” format E.g. “23-03-2020”
  string transaction_date = 9 [json_name = "TranDate"];

  // Account Number to which the fund needs to be Credited, in case the Fund transfer fails at the Credit Leg.
  string debit_account_number = 10 [json_name = "DebitAccountNumber"];

  //Flag indicating to use the Debit Account, mentioned in DebitAccountNumber
  string debit_suspense_account = 11 [json_name = "DebitSuspenseAccount"];
  // Remitter Details
  // Bank Account from which the monies are transferred
  message NEFTRemitter {
    // Remitter’s Name
    string name = 1 [json_name = "Name"];
    // Remitter’s Account Number. Amount will be debited from this Account.
    string account_number = 2 [json_name = "AccNumber"];
    // Conditional Mandatory (This is Mandatory incase of Acc_Val_Req is’ N’ in database for sender).
    string account_type = 3 [json_name = "Acctype"];
    // Remitter’s mobile. Notifications will be sent to this mobile.
    string mobile = 4 [json_name = "Mobile"];
    // Remitter’s e-mail. Notifications will be sent on this Id.
    string email = 5 [json_name = "Email"];
  }
  // Remitter details
  NEFTRemitter remitter = 12 [json_name = "RemitterDetails"];
  // Beneficiary Details
  // Bank Account to which the monies are transferred
  message NEFTBeneficiary {
    // Beneficiary Name
    string name = 1 [json_name = "Name"];
    // Beneficiary's Acccount Number
    string account_number = 2 [json_name = "AccNumber"];
    //    IFSC of the Beneficiary’s home branch
    string ifsc = 3 [json_name = "Ifsc"];
    // Beneficiary’s mobile
    string mobile = 4 [json_name = "Mobile"];
    // Beneficiary’s e-mail
    string email = 5 [json_name = "Email"];
  }
  // Beneficiary details
  NEFTBeneficiary beneficiary = 13 [json_name = "BeneficiaryDetails"];
  //  Amount to be Transferred in xxx.yy Format E.g 123.45 INR
  string amount = 14 [json_name = "Amount"];
  // Description of the Transaction
  string remarks = 15 [json_name = "Remarks"];
  //It is always 'N' / 'n' in NEFT Message,
  string alternative_payments = 16 [json_name = "AlternativePayments"];
  //Y or y – process payment after neft timings pass N or n
  string postpone = 17 [json_name = "Postpone"];
}

message NEFTResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 3 [json_name = "DeviceToken"];
  // Transaction Timestamp
  string trans_timestamp = 4 [json_name = "TranTimeStamp"];
  // Response Code for the Transaction
  string response_code = 5 [json_name = "Response"];
  // Response Description for the Transaction
  string response_desc = 6 [json_name = "Reason"];

}

message RTGSRequest {

  // Call back URL. API will send the response back to this URL, provided by the client.
  string callback_url = 1 [json_name = "RespUrl"];

  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 2 [json_name = "SenderCode"];
  string access_id = 3 [json_name = "ServiceAccessId"];
  string access_code = 4 [json_name = "ServiceAccessCode"];

  // Device ID of the client that has initiated the request
  string device_id = 5 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 6 [json_name = "DeviceToken"];
  // Encrypted PIN block that is required to authorise the transaction
  // All transaction requests except for low risk ones need Secure PIN for authorisation
  string secure_pin = 7 [json_name = "CredBlock"];
  // Client should pass unique value to each call, to identify the request
  string request_id = 8 [json_name = "RequestId"];
  // Date of Transaction in “DD-MM-YYYY” format E.g. “23-03-2020”
  string transaction_date = 9 [json_name = "TranDate"];

  // Account Number to which the fund needs to be Credited, in case the Fund transfer fails at the Credit Leg.
  string debit_account_number = 10 [json_name = "DebitAccountNumber"];

  //Flag indicating to use the Debit Account, mentioned in DebitAccountNumber
  string debit_suspense_account = 11 [json_name = "DebitSuspenseAccount"];
  // Remitter Details
  // Bank Account from which the monies are transferred
  message RtgsRemitter {
    // Remitter’s Name
    string name = 1 [json_name = "Name"];
    // Remitter’s Account Number. Amount will be debited from this Account.
    string account_number = 2 [json_name = "AccNumber"];
    // Conditional Mandatory (This is Mandatory incase of Acc_Val_Req is’ N’ in database for sender).
    string account_type = 3 [json_name = "Acctype"];
    // Remitter’s mobile. Notifications will be sent to this mobile.
    string mobile = 4 [json_name = "Mobile"];
    // Remitter’s e-mail. Notifications will be sent on this Id.
    string email = 5 [json_name = "Email"];
  }
  // Remitter details
  RtgsRemitter remitter = 12 [json_name = "RemitterDetails"];

  // Beneficiary Details
  // Bank Account to which the monies are transferred
  message RtgsBeneficiary {
    // Beneficiary Name
    string name = 1 [json_name = "Name"];
    // Beneficiary's Acccount Number
    string account_number = 2 [json_name = "AccNumber"];
    //    IFSC of the Beneficiary’s home branch
    string ifsc = 3 [json_name = "Ifsc"];
    // Beneficiary’s mobile
    string mobile = 4 [json_name = "Mobile"];
    // Beneficiary’s e-mail
    string email = 5 [json_name = "Email"];
  }

  // Beneficiary details
  RtgsBeneficiary beneficiary = 13 [json_name = "BeneficiaryDetails"];
  //  Amount to be Transferred in xxx.yy Format E.g 123.45 INR
  string amount = 14 [json_name = "Amount"];
  // Description of the Transaction
  string remarks = 15 [json_name = "Remarks"];
  // Not used. Pass ‘N’
  string alternative_payments = 16 [json_name = "AlternativePayments"];

}

message RTGSResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 3 [json_name = "DeviceToken"];
  // Transaction Timestamp
  string trans_timestamp = 4 [json_name = "TranTimeStamp"];
  // Response Code for the Transaction
  string response_code = 5 [json_name = "Response"];
  // Response Description for the Transaction
  string response_desc = 6 [json_name = "Reason"];

}

message IntraBankRequest {

  // Call back URL. API will send the response back to this URL, provided by the client.
  string callback_url = 1 [json_name = "RespUrl"];

  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 2 [json_name = "SenderCode"];
  string access_id = 3 [json_name = "ServiceAccessId"];
  string access_code = 4 [json_name = "ServiceAccessCode"];

  // Device ID of the client that has initiated the request
  string device_id = 5 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 6 [json_name = "DeviceToken"];
  // Encrypted PIN block that is required to authorise the transaction
  // All transaction requests except for low risk ones need Secure PIN for authorisation
  string secure_pin = 7 [json_name = "CredBlock"];
  // Client should pass unique value to each call, to identify the request
  string request_id = 8 [json_name = "RequestId"];
  // Date of Transaction in “DD-MM-YYYY” format E.g. “23-03-2020”
  string transaction_date = 9 [json_name = "TranDate"];

  // Alphanumeric. Client should pass unique value to each call.
  //API will validate the uniqueness against the Client and Channel
  string cust_ref_no = 10 [json_name = "Cust_Ref_No"];

  // Remitter details
  Remitter remitter = 12 [json_name = "RemitterDetails"];

  // Beneficiary details
  Beneficiary beneficiary = 13 [json_name = "BeneficiaryDetails"];
  //  Amount to be Transferred in xxx.yy Format E.g 123.45 INR
  string amount = 14 [json_name = "Amount"];
  // Description of the Transaction
  string remarks = 15 [json_name = "Remarks"];
  // Not used. Pass ‘N’
  string alternative_payments = 16 [json_name = "AlternativePayments"];
  // Alphanumeric values are allowed.
  string instrument_number = 17 [json_name = "Instrument_Number"];
  // Type of instrument. Alphanumeric with spaces are allowed
  string instrument_type = 18 [json_name = "Instrument_Type"];
  // Instrument Date. Should be in this format 'YYYYMMDD'
  string instrument_date = 19 [json_name = "Instrument_Date"];
}

message IntraBankResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];
  // Transaction Timestamp
  string trans_timestamp = 3 [json_name = "TranTimeStamp"];
  // Response Code for the Transaction
  string response_code = 4 [json_name = "Response"];
  // Response Description for the Transaction
  string response_desc = 5 [json_name = "Reason"];
}

message TransactionStatusRequest {
  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 1 [json_name = "SenderCode"];
  string access_id = 2 [json_name = "ServiceAccessId"];
  string access_code = 3 [json_name = "ServiceAccessCode"];

  // Device ID of the client that has initiated the request
  string device_id = 4 [json_name = "DeviceId"];

  // TODO(pruthvi): Not sure what this field represents
  string user_id = 5 [json_name = "UserProfileId"];

  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 6 [json_name = "DeviceToken"];

  // Client should pass unique value to each call, to identify the request
  // TODO(pruthvi): Confirm the format of this field
  string request_id = 7 [json_name = "RequestId"];

  // Determine the service for which the status is being enquired
  // Can be one of OB_NEFT, OB_FUNDTRANSFER, OB_IMPS, OB_RTGS
  string api_id = 8 [json_name = "ApiId"];

  // Original ReferenceId i.e., ReferenceId of the transaction about which the status is sought.
  // API will return the status of the transaction identified by the this reference Id.
  string original_request_id = 9 [json_name = "Original_RequestId"];

  // Mobile number registered with the account
  string mobile_number = 10 [json_name = "Mobile_Number"];

}

message TransactionStatusResponse {
  reserved 4;
  reserved 7;
  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];
  // Original ReferenceId i.e., ReferenceId of the transaction about which the status is sought.
  // API will return the status of the transaction identified by the this reference Id.
  string original_request_id = 3 [json_name = "Original_RequestId"];
  // Response Code for the Transaction
  string response_code = 5 [json_name = "ResponseCode"];
  // Response Description for the Transaction
  string response_desc = 6 [json_name = "ResponseReason"];
  // device token
  string device_token = 8 [json_name = "DeviceToken"];
  // High level response codes depicting the stage of the transaction. Can take these
  // PROCESSED, SUCCESS, FAILURE, SUSPECT values
  string response_action = 9 [json_name = "ResponseAction"];

  message OriginalTransactionDetails {
    // creation time of the transaction
    string created_at = 1 [json_name = "CreatedTime"];
    // unique identifier for a transaction
    // it may be empty and non empty depending on the status of transaction
    string utr = 2 [json_name = "Utr"];
    // UTR22 is the new field in which Federal is sending us the new-format of UTRs
    // UTR number stands for Unique Transaction Reference number
    // UTR No (Unique transaction reference number) is generally used for
    // reference of a particular NEFT /RTGS transaction.
    //
    // IMPS reference number in case of IMPS
    // Bank reference number in case of Intra fund transfer
    string utr_twenty_two = 15 [json_name = "Utr22"];
    // account number of the remitter
    string remitter_account_number = 3 [json_name = "RemitterAccountNumber"];
    // account number of the beneficiary
    string beneficiary_account_number = 4 [json_name = "BeneficiaryAccountNumber"];
    // amount for the transaction
    string amount = 5 [json_name = "TranAmount"];
    // Response Code for the original Transaction
    string response_code = 6 [json_name = "ResponseCode"];
    // Response Description for the original Transaction
    string response_desc = 7 [json_name = "ResponseReason"];
    // High level response codes depicting the stage of the original Transaction. Can take these
    // PROCESSED, SUCCESS, FAILURE, SUSPECT values
    string response_action = 8 [json_name = "ResponseAction"];
    // unique identifier for a add fund transaction via upi
    // it may be empty and non empty depending on the status of transaction and api response
    string upi_transaction_id = 9 [json_name = "UpiTransactionId"];
    // account number of the remitter
    string pool_account = 10 [json_name = "PoolAccount"];
    // account number of the beneficiary
    string customer_account = 11 [json_name = "CustomerAccount"];
    // upi customer ref id
    string upi_cust_ref_id = 12 [json_name = "UpiCustRefId"];
    // reversal_transaction_id: unique identifier for the refund txn. E.g. Reversal of first
    // leg due to 2nd leg failure
    // it may be empty and non empty depending on the status of transaction and api response
    string reversal_transaction_id = 13 [json_name = "UdirTransactionId"];
    // Customer reference ID of the reversal transaction i.e. RRN.
	  // e.g. Reversal of first leg due to 2nd leg failure
    string reversal_cust_ref_id = 14 [json_name = "UdirCustRefId"];
  }

  // details for the original transaction
  OriginalTransactionDetails original_transaction_details = 10 [json_name = "Original_Transaction_Details"];
}

message GetRemitterDetailsRequest {
  // sender id of the client.
  string sender_id = 1 [json_name = "SenderId"];
  // sender code given to client by federal
  string sender_code = 2 [json_name = "SenderCode"];
  // sender access code given to client by federal
  string service_access_code = 3 [json_name = "ServiceAccessCode"];
  // request id is unique in form of NEORI<DD><MM><YYYY><XXXXX>(5 DIGIT UNIQUE)
  // Client should pass unique value to each call, to identify the request
  string request_id = 4 [json_name = "RequestId"];
  // trans_id is CBS-ID of the transction, which is generated from vendor for every transaction. ( on-app and off app)
  string tran_id = 5 [json_name = "Tran_id"];
  // date format is in DD-MM-YYYY
  string date = 6 [json_name = "Date"];
}

message GetRemitterDetailsResponse {
  // Sender code that is passed in the request
  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];
  // response code sent by Federal.
  // for successful response, federal sent "RE0000" as response code.
  string response = 3 [json_name = "Response"];
  // account number of the remitter.
  string remitter_account = 4 [json_name = "Remitter_Account"];
  // bank ifsc code of the remitter's bank.
  string remitter_ifsc = 5 [json_name = "Remitter_IFSC"];
  // remitter name who initiated the transaction. It is verified name register while opening bank account.
  // No constraints on length of remitter_name.
  string remitter_name = 6 [json_name = "Remitter_Name"];
  // failure response reason, example: "Field is required" when required field is not passed in request.
  // On successful response, reason field is nil.
  string reason = 7 [json_name = "Reason"];
}

message GetCsisStatusRequest {
  // sender id of the client.
  string sender_id = 1 [json_name = "SenderId"];
  // sender code given to client by federal
  string sender_code = 2 [json_name = "SenderCode"];
  // sender access code given to client by federal
  string service_access_code = 3 [json_name = "ServiceAccessCode"];
  // request id is randomly generated unique string
  string request_id = 4 [json_name = "RequestId"];
  // constant string shared by vendor
  string bank_id = 5 [json_name = "BankId"];
}

message GetCsisStatusResponse {
  // Sender code that is passed in the request
  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];
  // response code sent by Federal.
  // for successful response, federal sent "CSIS000" as response code.
  string response = 3 [json_name = "Response"];
  // reason for failure
  string reason = 4 [json_name = "Reason"];
  // start date in format YYYY-MM-DD
  string start_date = 5 [json_name = "Stat_Date"];
  // csis status from vendor. It could be below values
  // O - Uniser for the given Stat_Date- Normal bank working status, that is other than CSIS time. All API services will be available in this mode.
  // F - CSIS switch for the given Stat_Date - Bank Switched to CSIS mode.
  // S - SAF replay - services will start switching back to the Normal working status from CSIS mode.
  string csis_status = 6 [json_name = "CDSI_Status"];
}

message GetRemitterDetailsV1Request {
  // sender code given to client by federal
  string sender_code = 1 [json_name = "user"];
  // request id is unique in form of NEORI<DD><MM><YYYY><XXXXX>(5 DIGIT UNIQUE)
  // Client should pass unique value to each call, to identify the request
  string request_id = 2 [json_name = "requestID"];
  // trans_id is CBS-ID of the transction, which is generated from vendor for every transaction. ( on-app and off app)
  string tran_id = 3 [json_name = "transactionid"];
  // date format is in DD-MM-YYYY
  string date = 4 [json_name = "transactiondate"];
  // Payment protocol used for transfer (for NEFT/RTGS fund transfer is used as protocol at federal end.). Values map are as below:
  // UPI -> `UPI`
  // IMPS -> `IMPS`
  // INTRA -> `FT`
  string payment_protocol = 5 [json_name = "type"];
}

message GetRemitterDetailsV1Response {
  // status response for API. Here success/fail means api call passed or fail. More details on failure could be fetch from error code.
  // Values could be: S/F
  string api_status = 1 [json_name = "status"];
  // Reason of failure in text.
  // In case of api_status is S only "S" is returned.
  // In case of api_status F, it could be "No data found", "Failure" etc.
  string message = 2 [json_name = "message"];
  // VPA used for transaction in case of payment protocol of transaction is UPI.
  // Blank in other case.
  string payer_vpa = 3 [json_name = "payerVid"];
  // ifsc code of bank. It will be available in case of bank account transfer protocol used (IMPS/INTRA)
  // NA in case of UPI transaction.
  string ifsc_code = 4 [json_name = "ifsc"];
  // account number associated for transaction. Present in case of bank account transfer protocol.
  string account_number = 5 [json_name = "accountNum"];
  // mobile number TODO(vivek): Confirm with federal this mobile number is of payer or payee.
  string mobile_num = 6 [json_name = "mobileNum"];
  // Remitter name in case of bank account transfer protocol (INTRA/IMPS).
  // NA in case of UPI protocol. TODO: Confirm for UPI after production.
  string remitter_customer_name = 7 [json_name = "custName"];
  // Transaction amount Rupees in upto two decimal place. ex: ₹ "89.76"
  string txn_amount = 8 [json_name = "txnAmount"];
  // Available in case of UPI transaction. NA for other case.
  // Payee transaction cbs reference number
  string payee_approval_num = 9 [json_name = "payeeApprovalNum"];
  // particular for transaction.
  // It is sometime request-id used for transaction in case of UPI transaction.
  // TODO(vivek): Document in case of bank transfer protocol.
  string txn_particular = 10 [json_name = "txnParticular"];
  // VPA of payee in case of UPI transaction. NA in case of bank transfer.
  string payee_vpa = 11 [json_name = "payeeVirId"];
  // payee name. "NA" for not applicable
  string payee_name = 12 [json_name = "payeeName"];
  // purpose code. TODO: add more details after federal clarification.
  string purpose_code = 13 [json_name = "purposeCode"];
  // error code against error. Each error code indicate different error scenarios.
  // 00: Success
  // 01: record not found
  // 02: all other errors like invalid request will be mapped to error code 02
  string error_code = 14 [json_name = "errCode"];
  // error string, ex- Error1 (in-case of no data found)
  // added this temp fix for not getting error codes on failure due to issue on federals end
  // todo(pay-team/yuvraj): revert this once we start getting error codes on failures
  string error = 15 [json_name = "error"];
  // federal sending account number in this field as temp fix for the issue- not getting account number in accountNum field
  // todo(pay-team/yuvraj): revert this once we start getting account number in accountNum field
  string reserve_free_text1 = 16 [json_name = "reservefreetext1"];
}

message GetBeneficiaryNameDetailsRequest {
  string sender_id = 1 [json_name = "SenderId"];
  string access_code = 2 [json_name = "AccessCode"];
  string sender_cd = 3 [json_name = "SenderCd"];
  string reference_id = 4 [json_name = "ReferenceId"];
  string initiation_mode = 5 [json_name = "InitiationMode"];
  string remitter_type = 6 [json_name = "RemitterType"];
  string remitter_name = 7 [json_name = "RemitterName"];
  string payment_mode = 8 [json_name = "PaymentMode"];

  message ReqBeneDetails {
    string beneficiary_acc_num = 1 [json_name = "BeneficiaryAccNum"];
    string beneficiary_ifsc = 2 [json_name = "BeneficiaryIFSC"];
  }

  ReqBeneDetails req_bene_details = 9 [json_name = "ReqBeneDetails"];
}

message GetBeneficiaryNameDetailsResponse {
  string sender_cd = 1 [json_name = "SenderCd"];
  string reference_id = 2 [json_name = "ReferenceId"];
  string response_code = 3 [json_name = "ResponseCode"];
  string response_reason = 4 [json_name = "ResponseReason"];
  string response_status = 5 [json_name = "ResponseStatus"];
  string beneficiary_name = 6 [json_name = "BeneficiaryName"];
}
