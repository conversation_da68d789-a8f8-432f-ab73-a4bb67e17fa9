// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/federal/update_nominee.proto

package federal

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UpdateNomineeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateNomineeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNomineeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateNomineeRequestMultiError, or nil if none found.
func (m *UpdateNomineeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNomineeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReqType

	// no validation rules for Foracid

	// no validation rules for RequestId

	// no validation rules for ServiceReqId

	// no validation rules for EkycCrrn

	// no validation rules for NomineeName

	// no validation rules for NomineeRegNo

	// no validation rules for NomineeRelType

	// no validation rules for NomineeMinorFlag

	// no validation rules for NomineeDob

	// no validation rules for NomineeAddrLine1

	// no validation rules for NomineeAddrLine2

	// no validation rules for NomineeAddrLine3

	// no validation rules for NomineeCity

	// no validation rules for NomineeState

	// no validation rules for NomineeCountry

	// no validation rules for NomineePostalCode

	// no validation rules for GuardianCode

	// no validation rules for GuardianName

	// no validation rules for Channel

	// no validation rules for ReserveFreeText1

	// no validation rules for ReserveFreeText2

	// no validation rules for ReserveFreeText3

	// no validation rules for ReserveFreeText4

	// no validation rules for ReserveFreeText5

	// no validation rules for ReserveFreeText6

	// no validation rules for ReserveFreeText7

	// no validation rules for ReserveFreeTextt8

	// no validation rules for ReserveFreeText9

	// no validation rules for ReserveFreeText10

	if len(errors) > 0 {
		return UpdateNomineeRequestMultiError(errors)
	}

	return nil
}

// UpdateNomineeRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateNomineeRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateNomineeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNomineeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNomineeRequestMultiError) AllErrors() []error { return m }

// UpdateNomineeRequestValidationError is the validation error returned by
// UpdateNomineeRequest.Validate if the designated constraints aren't met.
type UpdateNomineeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNomineeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNomineeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNomineeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNomineeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNomineeRequestValidationError) ErrorName() string {
	return "UpdateNomineeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNomineeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNomineeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNomineeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNomineeRequestValidationError{}

// Validate checks the field values on UpdateNomineeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateNomineeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNomineeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateNomineeResponseMultiError, or nil if none found.
func (m *UpdateNomineeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNomineeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for Status

	// no validation rules for Message

	// no validation rules for CbsResponse

	// no validation rules for CbsStatus

	if len(errors) > 0 {
		return UpdateNomineeResponseMultiError(errors)
	}

	return nil
}

// UpdateNomineeResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateNomineeResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateNomineeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNomineeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNomineeResponseMultiError) AllErrors() []error { return m }

// UpdateNomineeResponseValidationError is the validation error returned by
// UpdateNomineeResponse.Validate if the designated constraints aren't met.
type UpdateNomineeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNomineeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNomineeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNomineeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNomineeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNomineeResponseValidationError) ErrorName() string {
	return "UpdateNomineeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNomineeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNomineeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNomineeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNomineeResponseValidationError{}
