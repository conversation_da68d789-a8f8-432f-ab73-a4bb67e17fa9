// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/lenden/select_offer.proto

package lenden

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SelectOfferRequestPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SelectOfferRequestPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SelectOfferRequestPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SelectOfferRequestPayloadMultiError, or nil if none found.
func (m *SelectOfferRequestPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *SelectOfferRequestPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanId

	// no validation rules for UserId

	// no validation rules for SelectedOffer

	// no validation rules for SelectedAmount

	// no validation rules for Tenure

	// no validation rules for ProductId

	if len(errors) > 0 {
		return SelectOfferRequestPayloadMultiError(errors)
	}

	return nil
}

// SelectOfferRequestPayloadMultiError is an error wrapping multiple validation
// errors returned by SelectOfferRequestPayload.ValidateAll() if the
// designated constraints aren't met.
type SelectOfferRequestPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SelectOfferRequestPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SelectOfferRequestPayloadMultiError) AllErrors() []error { return m }

// SelectOfferRequestPayloadValidationError is the validation error returned by
// SelectOfferRequestPayload.Validate if the designated constraints aren't met.
type SelectOfferRequestPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SelectOfferRequestPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SelectOfferRequestPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SelectOfferRequestPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SelectOfferRequestPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SelectOfferRequestPayloadValidationError) ErrorName() string {
	return "SelectOfferRequestPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e SelectOfferRequestPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSelectOfferRequestPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SelectOfferRequestPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SelectOfferRequestPayloadValidationError{}

// Validate checks the field values on SelectOfferRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SelectOfferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SelectOfferRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SelectOfferRequestMultiError, or nil if none found.
func (m *SelectOfferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SelectOfferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SelectOfferRequestValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SelectOfferRequestValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SelectOfferRequestValidationError{
				field:  "Params",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SelectOfferRequestValidationError{
					field:  "Fields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SelectOfferRequestValidationError{
					field:  "Fields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SelectOfferRequestValidationError{
				field:  "Fields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetJson()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SelectOfferRequestValidationError{
					field:  "Json",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SelectOfferRequestValidationError{
					field:  "Json",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJson()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SelectOfferRequestValidationError{
				field:  "Json",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SelectOfferRequestValidationError{
					field:  "Attributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SelectOfferRequestValidationError{
					field:  "Attributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SelectOfferRequestValidationError{
				field:  "Attributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApiCode

	if len(errors) > 0 {
		return SelectOfferRequestMultiError(errors)
	}

	return nil
}

// SelectOfferRequestMultiError is an error wrapping multiple validation errors
// returned by SelectOfferRequest.ValidateAll() if the designated constraints
// aren't met.
type SelectOfferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SelectOfferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SelectOfferRequestMultiError) AllErrors() []error { return m }

// SelectOfferRequestValidationError is the validation error returned by
// SelectOfferRequest.Validate if the designated constraints aren't met.
type SelectOfferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SelectOfferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SelectOfferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SelectOfferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SelectOfferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SelectOfferRequestValidationError) ErrorName() string {
	return "SelectOfferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SelectOfferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSelectOfferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SelectOfferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SelectOfferRequestValidationError{}

// Validate checks the field values on SelectOfferResponseWrapper with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SelectOfferResponseWrapper) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SelectOfferResponseWrapper with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SelectOfferResponseWrapperMultiError, or nil if none found.
func (m *SelectOfferResponseWrapper) ValidateAll() error {
	return m.validate(true)
}

func (m *SelectOfferResponseWrapper) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SelectOfferResponseWrapperValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SelectOfferResponseWrapperValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SelectOfferResponseWrapperValidationError{
				field:  "Response",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SelectOfferResponseWrapperMultiError(errors)
	}

	return nil
}

// SelectOfferResponseWrapperMultiError is an error wrapping multiple
// validation errors returned by SelectOfferResponseWrapper.ValidateAll() if
// the designated constraints aren't met.
type SelectOfferResponseWrapperMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SelectOfferResponseWrapperMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SelectOfferResponseWrapperMultiError) AllErrors() []error { return m }

// SelectOfferResponseWrapperValidationError is the validation error returned
// by SelectOfferResponseWrapper.Validate if the designated constraints aren't met.
type SelectOfferResponseWrapperValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SelectOfferResponseWrapperValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SelectOfferResponseWrapperValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SelectOfferResponseWrapperValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SelectOfferResponseWrapperValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SelectOfferResponseWrapperValidationError) ErrorName() string {
	return "SelectOfferResponseWrapperValidationError"
}

// Error satisfies the builtin error interface
func (e SelectOfferResponseWrapperValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSelectOfferResponseWrapper.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SelectOfferResponseWrapperValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SelectOfferResponseWrapperValidationError{}

// Validate checks the field values on SelectOfferResponseWrapper_ResponseData
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SelectOfferResponseWrapper_ResponseData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SelectOfferResponseWrapper_ResponseData with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// SelectOfferResponseWrapper_ResponseDataMultiError, or nil if none found.
func (m *SelectOfferResponseWrapper_ResponseData) ValidateAll() error {
	return m.validate(true)
}

func (m *SelectOfferResponseWrapper_ResponseData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TraceId

	// no validation rules for MessageCode

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetResponseData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SelectOfferResponseWrapper_ResponseDataValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SelectOfferResponseWrapper_ResponseDataValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SelectOfferResponseWrapper_ResponseDataValidationError{
				field:  "ResponseData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SelectOfferResponseWrapper_ResponseDataMultiError(errors)
	}

	return nil
}

// SelectOfferResponseWrapper_ResponseDataMultiError is an error wrapping
// multiple validation errors returned by
// SelectOfferResponseWrapper_ResponseData.ValidateAll() if the designated
// constraints aren't met.
type SelectOfferResponseWrapper_ResponseDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SelectOfferResponseWrapper_ResponseDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SelectOfferResponseWrapper_ResponseDataMultiError) AllErrors() []error { return m }

// SelectOfferResponseWrapper_ResponseDataValidationError is the validation
// error returned by SelectOfferResponseWrapper_ResponseData.Validate if the
// designated constraints aren't met.
type SelectOfferResponseWrapper_ResponseDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SelectOfferResponseWrapper_ResponseDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SelectOfferResponseWrapper_ResponseDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SelectOfferResponseWrapper_ResponseDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SelectOfferResponseWrapper_ResponseDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SelectOfferResponseWrapper_ResponseDataValidationError) ErrorName() string {
	return "SelectOfferResponseWrapper_ResponseDataValidationError"
}

// Error satisfies the builtin error interface
func (e SelectOfferResponseWrapper_ResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSelectOfferResponseWrapper_ResponseData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SelectOfferResponseWrapper_ResponseDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SelectOfferResponseWrapper_ResponseDataValidationError{}

// Validate checks the field values on
// SelectOfferResponseWrapper_SelectOfferResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SelectOfferResponseWrapper_SelectOfferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SelectOfferResponseWrapper_SelectOfferResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// SelectOfferResponseWrapper_SelectOfferResponseMultiError, or nil if none found.
func (m *SelectOfferResponseWrapper_SelectOfferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SelectOfferResponseWrapper_SelectOfferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Amount

	// no validation rules for IsDuplicate

	// no validation rules for OfferCode

	// no validation rules for Tenure

	// no validation rules for SelectedOffer

	if len(errors) > 0 {
		return SelectOfferResponseWrapper_SelectOfferResponseMultiError(errors)
	}

	return nil
}

// SelectOfferResponseWrapper_SelectOfferResponseMultiError is an error
// wrapping multiple validation errors returned by
// SelectOfferResponseWrapper_SelectOfferResponse.ValidateAll() if the
// designated constraints aren't met.
type SelectOfferResponseWrapper_SelectOfferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SelectOfferResponseWrapper_SelectOfferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SelectOfferResponseWrapper_SelectOfferResponseMultiError) AllErrors() []error { return m }

// SelectOfferResponseWrapper_SelectOfferResponseValidationError is the
// validation error returned by
// SelectOfferResponseWrapper_SelectOfferResponse.Validate if the designated
// constraints aren't met.
type SelectOfferResponseWrapper_SelectOfferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SelectOfferResponseWrapper_SelectOfferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SelectOfferResponseWrapper_SelectOfferResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e SelectOfferResponseWrapper_SelectOfferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SelectOfferResponseWrapper_SelectOfferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SelectOfferResponseWrapper_SelectOfferResponseValidationError) ErrorName() string {
	return "SelectOfferResponseWrapper_SelectOfferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SelectOfferResponseWrapper_SelectOfferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSelectOfferResponseWrapper_SelectOfferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SelectOfferResponseWrapper_SelectOfferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SelectOfferResponseWrapper_SelectOfferResponseValidationError{}
