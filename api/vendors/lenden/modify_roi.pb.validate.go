// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/lenden/modify_roi.proto

package lenden

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ModifyRoiRequestPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyRoiRequestPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyRoiRequestPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyRoiRequestPayloadMultiError, or nil if none found.
func (m *ModifyRoiRequestPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyRoiRequestPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanId

	// no validation rules for ProductId

	// no validation rules for InterestRate

	if all {
		switch v := interface{}(m.GetConsentData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRoiRequestPayloadValidationError{
					field:  "ConsentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRoiRequestPayloadValidationError{
					field:  "ConsentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRoiRequestPayloadValidationError{
				field:  "ConsentData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserId

	if len(errors) > 0 {
		return ModifyRoiRequestPayloadMultiError(errors)
	}

	return nil
}

// ModifyRoiRequestPayloadMultiError is an error wrapping multiple validation
// errors returned by ModifyRoiRequestPayload.ValidateAll() if the designated
// constraints aren't met.
type ModifyRoiRequestPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyRoiRequestPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyRoiRequestPayloadMultiError) AllErrors() []error { return m }

// ModifyRoiRequestPayloadValidationError is the validation error returned by
// ModifyRoiRequestPayload.Validate if the designated constraints aren't met.
type ModifyRoiRequestPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyRoiRequestPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyRoiRequestPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyRoiRequestPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyRoiRequestPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyRoiRequestPayloadValidationError) ErrorName() string {
	return "ModifyRoiRequestPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyRoiRequestPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyRoiRequestPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyRoiRequestPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyRoiRequestPayloadValidationError{}

// Validate checks the field values on ModifyRoiRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ModifyRoiRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyRoiRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyRoiRequestMultiError, or nil if none found.
func (m *ModifyRoiRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyRoiRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRoiRequestValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRoiRequestValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRoiRequestValidationError{
				field:  "Params",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRoiRequestValidationError{
					field:  "Fields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRoiRequestValidationError{
					field:  "Fields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRoiRequestValidationError{
				field:  "Fields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetJson()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRoiRequestValidationError{
					field:  "Json",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRoiRequestValidationError{
					field:  "Json",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJson()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRoiRequestValidationError{
				field:  "Json",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRoiRequestValidationError{
					field:  "Attributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRoiRequestValidationError{
					field:  "Attributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRoiRequestValidationError{
				field:  "Attributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApiCode

	if len(errors) > 0 {
		return ModifyRoiRequestMultiError(errors)
	}

	return nil
}

// ModifyRoiRequestMultiError is an error wrapping multiple validation errors
// returned by ModifyRoiRequest.ValidateAll() if the designated constraints
// aren't met.
type ModifyRoiRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyRoiRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyRoiRequestMultiError) AllErrors() []error { return m }

// ModifyRoiRequestValidationError is the validation error returned by
// ModifyRoiRequest.Validate if the designated constraints aren't met.
type ModifyRoiRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyRoiRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyRoiRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyRoiRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyRoiRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyRoiRequestValidationError) ErrorName() string { return "ModifyRoiRequestValidationError" }

// Error satisfies the builtin error interface
func (e ModifyRoiRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyRoiRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyRoiRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyRoiRequestValidationError{}

// Validate checks the field values on ModifyRoiResponseWrapper with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyRoiResponseWrapper) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyRoiResponseWrapper with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyRoiResponseWrapperMultiError, or nil if none found.
func (m *ModifyRoiResponseWrapper) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyRoiResponseWrapper) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRoiResponseWrapperValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRoiResponseWrapperValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRoiResponseWrapperValidationError{
				field:  "Response",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ModifyRoiResponseWrapperMultiError(errors)
	}

	return nil
}

// ModifyRoiResponseWrapperMultiError is an error wrapping multiple validation
// errors returned by ModifyRoiResponseWrapper.ValidateAll() if the designated
// constraints aren't met.
type ModifyRoiResponseWrapperMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyRoiResponseWrapperMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyRoiResponseWrapperMultiError) AllErrors() []error { return m }

// ModifyRoiResponseWrapperValidationError is the validation error returned by
// ModifyRoiResponseWrapper.Validate if the designated constraints aren't met.
type ModifyRoiResponseWrapperValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyRoiResponseWrapperValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyRoiResponseWrapperValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyRoiResponseWrapperValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyRoiResponseWrapperValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyRoiResponseWrapperValidationError) ErrorName() string {
	return "ModifyRoiResponseWrapperValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyRoiResponseWrapperValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyRoiResponseWrapper.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyRoiResponseWrapperValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyRoiResponseWrapperValidationError{}

// Validate checks the field values on Documents with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Documents) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Documents with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DocumentsMultiError, or nil
// if none found.
func (m *Documents) ValidateAll() error {
	return m.validate(true)
}

func (m *Documents) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Kfs

	// no validation rules for LoanAgreement

	if len(errors) > 0 {
		return DocumentsMultiError(errors)
	}

	return nil
}

// DocumentsMultiError is an error wrapping multiple validation errors returned
// by Documents.ValidateAll() if the designated constraints aren't met.
type DocumentsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DocumentsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DocumentsMultiError) AllErrors() []error { return m }

// DocumentsValidationError is the validation error returned by
// Documents.Validate if the designated constraints aren't met.
type DocumentsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DocumentsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DocumentsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DocumentsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DocumentsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DocumentsValidationError) ErrorName() string { return "DocumentsValidationError" }

// Error satisfies the builtin error interface
func (e DocumentsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDocuments.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DocumentsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DocumentsValidationError{}

// Validate checks the field values on ModifyRoiResponseWrapper_ResponseData
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ModifyRoiResponseWrapper_ResponseData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyRoiResponseWrapper_ResponseData
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ModifyRoiResponseWrapper_ResponseDataMultiError, or nil if none found.
func (m *ModifyRoiResponseWrapper_ResponseData) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyRoiResponseWrapper_ResponseData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TraceId

	// no validation rules for MessageCode

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetResponseData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRoiResponseWrapper_ResponseDataValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRoiResponseWrapper_ResponseDataValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRoiResponseWrapper_ResponseDataValidationError{
				field:  "ResponseData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ModifyRoiResponseWrapper_ResponseDataMultiError(errors)
	}

	return nil
}

// ModifyRoiResponseWrapper_ResponseDataMultiError is an error wrapping
// multiple validation errors returned by
// ModifyRoiResponseWrapper_ResponseData.ValidateAll() if the designated
// constraints aren't met.
type ModifyRoiResponseWrapper_ResponseDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyRoiResponseWrapper_ResponseDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyRoiResponseWrapper_ResponseDataMultiError) AllErrors() []error { return m }

// ModifyRoiResponseWrapper_ResponseDataValidationError is the validation error
// returned by ModifyRoiResponseWrapper_ResponseData.Validate if the
// designated constraints aren't met.
type ModifyRoiResponseWrapper_ResponseDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyRoiResponseWrapper_ResponseDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyRoiResponseWrapper_ResponseDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyRoiResponseWrapper_ResponseDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyRoiResponseWrapper_ResponseDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyRoiResponseWrapper_ResponseDataValidationError) ErrorName() string {
	return "ModifyRoiResponseWrapper_ResponseDataValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyRoiResponseWrapper_ResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyRoiResponseWrapper_ResponseData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyRoiResponseWrapper_ResponseDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyRoiResponseWrapper_ResponseDataValidationError{}

// Validate checks the field values on
// ModifyRoiResponseWrapper_ModifyRoiResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ModifyRoiResponseWrapper_ModifyRoiResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ModifyRoiResponseWrapper_ModifyRoiResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ModifyRoiResponseWrapper_ModifyRoiResponseMultiError, or nil if none found.
func (m *ModifyRoiResponseWrapper_ModifyRoiResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyRoiResponseWrapper_ModifyRoiResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InstallmentAmount

	if all {
		switch v := interface{}(m.GetDocuments()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRoiResponseWrapper_ModifyRoiResponseValidationError{
					field:  "Documents",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRoiResponseWrapper_ModifyRoiResponseValidationError{
					field:  "Documents",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDocuments()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRoiResponseWrapper_ModifyRoiResponseValidationError{
				field:  "Documents",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ModifyRoiResponseWrapper_ModifyRoiResponseMultiError(errors)
	}

	return nil
}

// ModifyRoiResponseWrapper_ModifyRoiResponseMultiError is an error wrapping
// multiple validation errors returned by
// ModifyRoiResponseWrapper_ModifyRoiResponse.ValidateAll() if the designated
// constraints aren't met.
type ModifyRoiResponseWrapper_ModifyRoiResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyRoiResponseWrapper_ModifyRoiResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyRoiResponseWrapper_ModifyRoiResponseMultiError) AllErrors() []error { return m }

// ModifyRoiResponseWrapper_ModifyRoiResponseValidationError is the validation
// error returned by ModifyRoiResponseWrapper_ModifyRoiResponse.Validate if
// the designated constraints aren't met.
type ModifyRoiResponseWrapper_ModifyRoiResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyRoiResponseWrapper_ModifyRoiResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyRoiResponseWrapper_ModifyRoiResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyRoiResponseWrapper_ModifyRoiResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyRoiResponseWrapper_ModifyRoiResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyRoiResponseWrapper_ModifyRoiResponseValidationError) ErrorName() string {
	return "ModifyRoiResponseWrapper_ModifyRoiResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyRoiResponseWrapper_ModifyRoiResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyRoiResponseWrapper_ModifyRoiResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyRoiResponseWrapper_ModifyRoiResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyRoiResponseWrapper_ModifyRoiResponseValidationError{}
