// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/lenden/select_offer.proto

package lenden

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SelectOfferRequestPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanId         string `protobuf:"bytes,1,opt,name=loan_id,proto3" json:"loan_id,omitempty"`
	UserId         string `protobuf:"bytes,2,opt,name=user_id,proto3" json:"user_id,omitempty"`
	SelectedOffer  string `protobuf:"bytes,3,opt,name=selected_offer,proto3" json:"selected_offer,omitempty"`
	SelectedAmount int64  `protobuf:"varint,4,opt,name=selected_amount,proto3" json:"selected_amount,omitempty"`
	Tenure         int32  `protobuf:"varint,5,opt,name=tenure,proto3" json:"tenure,omitempty"`
	ProductId      string `protobuf:"bytes,6,opt,name=product_id,proto3" json:"product_id,omitempty"`
}

func (x *SelectOfferRequestPayload) Reset() {
	*x = SelectOfferRequestPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_select_offer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectOfferRequestPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectOfferRequestPayload) ProtoMessage() {}

func (x *SelectOfferRequestPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_select_offer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectOfferRequestPayload.ProtoReflect.Descriptor instead.
func (*SelectOfferRequestPayload) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_select_offer_proto_rawDescGZIP(), []int{0}
}

func (x *SelectOfferRequestPayload) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *SelectOfferRequestPayload) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SelectOfferRequestPayload) GetSelectedOffer() string {
	if x != nil {
		return x.SelectedOffer
	}
	return ""
}

func (x *SelectOfferRequestPayload) GetSelectedAmount() int64 {
	if x != nil {
		return x.SelectedAmount
	}
	return 0
}

func (x *SelectOfferRequestPayload) GetTenure() int32 {
	if x != nil {
		return x.Tenure
	}
	return 0
}

func (x *SelectOfferRequestPayload) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

type SelectOfferRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Params     *Params                    `protobuf:"bytes,1,opt,name=params,proto3" json:"params,omitempty"`
	Fields     *Fields                    `protobuf:"bytes,2,opt,name=fields,proto3" json:"fields,omitempty"`
	Json       *SelectOfferRequestPayload `protobuf:"bytes,3,opt,name=json,proto3" json:"json,omitempty"`
	Attributes *Attributes                `protobuf:"bytes,4,opt,name=attributes,proto3" json:"attributes,omitempty"`
	ApiCode    string                     `protobuf:"bytes,5,opt,name=api_code,proto3" json:"api_code,omitempty"`
}

func (x *SelectOfferRequest) Reset() {
	*x = SelectOfferRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_select_offer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectOfferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectOfferRequest) ProtoMessage() {}

func (x *SelectOfferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_select_offer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectOfferRequest.ProtoReflect.Descriptor instead.
func (*SelectOfferRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_select_offer_proto_rawDescGZIP(), []int{1}
}

func (x *SelectOfferRequest) GetParams() *Params {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *SelectOfferRequest) GetFields() *Fields {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *SelectOfferRequest) GetJson() *SelectOfferRequestPayload {
	if x != nil {
		return x.Json
	}
	return nil
}

func (x *SelectOfferRequest) GetAttributes() *Attributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *SelectOfferRequest) GetApiCode() string {
	if x != nil {
		return x.ApiCode
	}
	return ""
}

type SelectOfferResponseWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message  string                                   `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Response *SelectOfferResponseWrapper_ResponseData `protobuf:"bytes,2,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *SelectOfferResponseWrapper) Reset() {
	*x = SelectOfferResponseWrapper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_select_offer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectOfferResponseWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectOfferResponseWrapper) ProtoMessage() {}

func (x *SelectOfferResponseWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_select_offer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectOfferResponseWrapper.ProtoReflect.Descriptor instead.
func (*SelectOfferResponseWrapper) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_select_offer_proto_rawDescGZIP(), []int{2}
}

func (x *SelectOfferResponseWrapper) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SelectOfferResponseWrapper) GetResponse() *SelectOfferResponseWrapper_ResponseData {
	if x != nil {
		return x.Response
	}
	return nil
}

type SelectOfferResponseWrapper_ResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraceId      string                                          `protobuf:"bytes,1,opt,name=trace_id,proto3" json:"trace_id,omitempty"`
	MessageCode  string                                          `protobuf:"bytes,2,opt,name=message_code,proto3" json:"message_code,omitempty"`
	Message      string                                          `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	ResponseData *SelectOfferResponseWrapper_SelectOfferResponse `protobuf:"bytes,4,opt,name=response_data,proto3" json:"response_data,omitempty"`
}

func (x *SelectOfferResponseWrapper_ResponseData) Reset() {
	*x = SelectOfferResponseWrapper_ResponseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_select_offer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectOfferResponseWrapper_ResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectOfferResponseWrapper_ResponseData) ProtoMessage() {}

func (x *SelectOfferResponseWrapper_ResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_select_offer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectOfferResponseWrapper_ResponseData.ProtoReflect.Descriptor instead.
func (*SelectOfferResponseWrapper_ResponseData) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_select_offer_proto_rawDescGZIP(), []int{2, 0}
}

func (x *SelectOfferResponseWrapper_ResponseData) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *SelectOfferResponseWrapper_ResponseData) GetMessageCode() string {
	if x != nil {
		return x.MessageCode
	}
	return ""
}

func (x *SelectOfferResponseWrapper_ResponseData) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SelectOfferResponseWrapper_ResponseData) GetResponseData() *SelectOfferResponseWrapper_SelectOfferResponse {
	if x != nil {
		return x.ResponseData
	}
	return nil
}

type SelectOfferResponseWrapper_SelectOfferResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount        float64 `protobuf:"fixed64,1,opt,name=amount,proto3" json:"amount,omitempty"`
	IsDuplicate   bool    `protobuf:"varint,2,opt,name=is_duplicate,proto3" json:"is_duplicate,omitempty"`
	OfferCode     string  `protobuf:"bytes,3,opt,name=offer_code,proto3" json:"offer_code,omitempty"`
	Tenure        int32   `protobuf:"varint,4,opt,name=tenure,proto3" json:"tenure,omitempty"`
	SelectedOffer string  `protobuf:"bytes,5,opt,name=selected_offer,proto3" json:"selected_offer,omitempty"`
}

func (x *SelectOfferResponseWrapper_SelectOfferResponse) Reset() {
	*x = SelectOfferResponseWrapper_SelectOfferResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_lenden_select_offer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectOfferResponseWrapper_SelectOfferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectOfferResponseWrapper_SelectOfferResponse) ProtoMessage() {}

func (x *SelectOfferResponseWrapper_SelectOfferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_lenden_select_offer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectOfferResponseWrapper_SelectOfferResponse.ProtoReflect.Descriptor instead.
func (*SelectOfferResponseWrapper_SelectOfferResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_lenden_select_offer_proto_rawDescGZIP(), []int{2, 1}
}

func (x *SelectOfferResponseWrapper_SelectOfferResponse) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *SelectOfferResponseWrapper_SelectOfferResponse) GetIsDuplicate() bool {
	if x != nil {
		return x.IsDuplicate
	}
	return false
}

func (x *SelectOfferResponseWrapper_SelectOfferResponse) GetOfferCode() string {
	if x != nil {
		return x.OfferCode
	}
	return ""
}

func (x *SelectOfferResponseWrapper_SelectOfferResponse) GetTenure() int32 {
	if x != nil {
		return x.Tenure
	}
	return 0
}

func (x *SelectOfferResponseWrapper_SelectOfferResponse) GetSelectedOffer() string {
	if x != nil {
		return x.SelectedOffer
	}
	return ""
}

var File_api_vendors_lenden_select_offer_proto protoreflect.FileDescriptor

var file_api_vendors_lenden_select_offer_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x2f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd9, 0x01, 0x0a, 0x19, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x65,
	0x6e, 0x75, 0x72, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x22, 0x8b, 0x02, 0x0a, 0x12, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x06, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x73, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x3d, 0x0a, 0x04, 0x6a,
	0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x52, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x0a, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x90, 0x04, 0x0a, 0x1a, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65,
	0x72, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x53, 0x0a, 0x08, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x1a, 0xce, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x12, 0x22, 0x0a,
	0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x64, 0x0a, 0x0d, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x65, 0x6e, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x2e, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x1a, 0xb1, 0x01, 0x0a, 0x13, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x5f, 0x64, 0x75, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_lenden_select_offer_proto_rawDescOnce sync.Once
	file_api_vendors_lenden_select_offer_proto_rawDescData = file_api_vendors_lenden_select_offer_proto_rawDesc
)

func file_api_vendors_lenden_select_offer_proto_rawDescGZIP() []byte {
	file_api_vendors_lenden_select_offer_proto_rawDescOnce.Do(func() {
		file_api_vendors_lenden_select_offer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_lenden_select_offer_proto_rawDescData)
	})
	return file_api_vendors_lenden_select_offer_proto_rawDescData
}

var file_api_vendors_lenden_select_offer_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_vendors_lenden_select_offer_proto_goTypes = []interface{}{
	(*SelectOfferRequestPayload)(nil),                      // 0: vendors.lenden.SelectOfferRequestPayload
	(*SelectOfferRequest)(nil),                             // 1: vendors.lenden.SelectOfferRequest
	(*SelectOfferResponseWrapper)(nil),                     // 2: vendors.lenden.SelectOfferResponseWrapper
	(*SelectOfferResponseWrapper_ResponseData)(nil),        // 3: vendors.lenden.SelectOfferResponseWrapper.ResponseData
	(*SelectOfferResponseWrapper_SelectOfferResponse)(nil), // 4: vendors.lenden.SelectOfferResponseWrapper.SelectOfferResponse
	(*Params)(nil),     // 5: vendors.lenden.Params
	(*Fields)(nil),     // 6: vendors.lenden.Fields
	(*Attributes)(nil), // 7: vendors.lenden.Attributes
}
var file_api_vendors_lenden_select_offer_proto_depIdxs = []int32{
	5, // 0: vendors.lenden.SelectOfferRequest.params:type_name -> vendors.lenden.Params
	6, // 1: vendors.lenden.SelectOfferRequest.fields:type_name -> vendors.lenden.Fields
	0, // 2: vendors.lenden.SelectOfferRequest.json:type_name -> vendors.lenden.SelectOfferRequestPayload
	7, // 3: vendors.lenden.SelectOfferRequest.attributes:type_name -> vendors.lenden.Attributes
	3, // 4: vendors.lenden.SelectOfferResponseWrapper.response:type_name -> vendors.lenden.SelectOfferResponseWrapper.ResponseData
	4, // 5: vendors.lenden.SelectOfferResponseWrapper.ResponseData.response_data:type_name -> vendors.lenden.SelectOfferResponseWrapper.SelectOfferResponse
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_vendors_lenden_select_offer_proto_init() }
func file_api_vendors_lenden_select_offer_proto_init() {
	if File_api_vendors_lenden_select_offer_proto != nil {
		return
	}
	file_api_vendors_lenden_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_lenden_select_offer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectOfferRequestPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_lenden_select_offer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectOfferRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_lenden_select_offer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectOfferResponseWrapper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_lenden_select_offer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectOfferResponseWrapper_ResponseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_lenden_select_offer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectOfferResponseWrapper_SelectOfferResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_lenden_select_offer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_lenden_select_offer_proto_goTypes,
		DependencyIndexes: file_api_vendors_lenden_select_offer_proto_depIdxs,
		MessageInfos:      file_api_vendors_lenden_select_offer_proto_msgTypes,
	}.Build()
	File_api_vendors_lenden_select_offer_proto = out.File
	file_api_vendors_lenden_select_offer_proto_rawDesc = nil
	file_api_vendors_lenden_select_offer_proto_goTypes = nil
	file_api_vendors_lenden_select_offer_proto_depIdxs = nil
}
