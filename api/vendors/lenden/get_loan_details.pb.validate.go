// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/lenden/get_loan_details.proto

package lenden

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetLoanDetailsRequestPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDetailsRequestPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDetailsRequestPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDetailsRequestPayloadMultiError, or nil if none found.
func (m *GetLoanDetailsRequestPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDetailsRequestPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProductId

	// no validation rules for LoanId

	// no validation rules for OriginSystem

	if len(errors) > 0 {
		return GetLoanDetailsRequestPayloadMultiError(errors)
	}

	return nil
}

// GetLoanDetailsRequestPayloadMultiError is an error wrapping multiple
// validation errors returned by GetLoanDetailsRequestPayload.ValidateAll() if
// the designated constraints aren't met.
type GetLoanDetailsRequestPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDetailsRequestPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDetailsRequestPayloadMultiError) AllErrors() []error { return m }

// GetLoanDetailsRequestPayloadValidationError is the validation error returned
// by GetLoanDetailsRequestPayload.Validate if the designated constraints
// aren't met.
type GetLoanDetailsRequestPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDetailsRequestPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDetailsRequestPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDetailsRequestPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDetailsRequestPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDetailsRequestPayloadValidationError) ErrorName() string {
	return "GetLoanDetailsRequestPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDetailsRequestPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDetailsRequestPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDetailsRequestPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDetailsRequestPayloadValidationError{}

// Validate checks the field values on GetLoanDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDetailsRequestMultiError, or nil if none found.
func (m *GetLoanDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsRequestValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsRequestValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsRequestValidationError{
				field:  "Params",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsRequestValidationError{
					field:  "Fields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsRequestValidationError{
					field:  "Fields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsRequestValidationError{
				field:  "Fields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetJson()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsRequestValidationError{
					field:  "Json",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsRequestValidationError{
					field:  "Json",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetJson()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsRequestValidationError{
				field:  "Json",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsRequestValidationError{
					field:  "Attributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsRequestValidationError{
					field:  "Attributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsRequestValidationError{
				field:  "Attributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApiCode

	if len(errors) > 0 {
		return GetLoanDetailsRequestMultiError(errors)
	}

	return nil
}

// GetLoanDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetLoanDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDetailsRequestMultiError) AllErrors() []error { return m }

// GetLoanDetailsRequestValidationError is the validation error returned by
// GetLoanDetailsRequest.Validate if the designated constraints aren't met.
type GetLoanDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDetailsRequestValidationError) ErrorName() string {
	return "GetLoanDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDetailsRequestValidationError{}

// Validate checks the field values on GetLoanDetailsResponseWrapper with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDetailsResponseWrapper) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDetailsResponseWrapper with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLoanDetailsResponseWrapperMultiError, or nil if none found.
func (m *GetLoanDetailsResponseWrapper) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDetailsResponseWrapper) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponseWrapperValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponseWrapperValidationError{
					field:  "Response",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponseWrapperValidationError{
				field:  "Response",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDetailsResponseWrapperMultiError(errors)
	}

	return nil
}

// GetLoanDetailsResponseWrapperMultiError is an error wrapping multiple
// validation errors returned by GetLoanDetailsResponseWrapper.ValidateAll()
// if the designated constraints aren't met.
type GetLoanDetailsResponseWrapperMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDetailsResponseWrapperMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDetailsResponseWrapperMultiError) AllErrors() []error { return m }

// GetLoanDetailsResponseWrapperValidationError is the validation error
// returned by GetLoanDetailsResponseWrapper.Validate if the designated
// constraints aren't met.
type GetLoanDetailsResponseWrapperValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDetailsResponseWrapperValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDetailsResponseWrapperValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDetailsResponseWrapperValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDetailsResponseWrapperValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDetailsResponseWrapperValidationError) ErrorName() string {
	return "GetLoanDetailsResponseWrapperValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDetailsResponseWrapperValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDetailsResponseWrapper.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDetailsResponseWrapperValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDetailsResponseWrapperValidationError{}

// Validate checks the field values on LoanDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoanDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoanDetailsMultiError, or
// nil if none found.
func (m *LoanDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for UserId

	// no validation rules for ProductId

	// no validation rules for PartnerLoanId

	if all {
		switch v := interface{}(m.GetTenure()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "Tenure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "Tenure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTenure()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanDetailsValidationError{
				field:  "Tenure",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "Interest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanDetailsValidationError{
					field:  "Interest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanDetailsValidationError{
				field:  "Interest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SanctionedAmount

	// no validation rules for DisbursedAmount

	// no validation rules for ProcessingFee

	// no validation rules for InterestSum

	// no validation rules for PaidPrincipalSum

	// no validation rules for PrincipalOutstanding

	// no validation rules for PaidInterestSum

	// no validation rules for InterestOutstanding

	// no validation rules for RepaymentReceived

	// no validation rules for RepaymentProcessed

	// no validation rules for RepaymentInProcess

	// no validation rules for PaidDelayInterestSum

	// no validation rules for DelayInterestDue

	// no validation rules for PaidOtherChargesSum

	// no validation rules for OtherChargesDue

	// no validation rules for LoanAmortisationType

	// no validation rules for FirstDueDate

	// no validation rules for NextDueDate

	// no validation rules for LastDueDate

	// no validation rules for LoanStatus

	// no validation rules for RepaymentStatus

	// no validation rules for LiquidationDate

	// no validation rules for PrincipalDueTillDate

	// no validation rules for InterestDueTillDate

	// no validation rules for PreviouslyPaidOn

	// no validation rules for InstallmentAmount

	// no validation rules for PaidInstallmentCount

	// no validation rules for EnablePaymentGateway

	if len(errors) > 0 {
		return LoanDetailsMultiError(errors)
	}

	return nil
}

// LoanDetailsMultiError is an error wrapping multiple validation errors
// returned by LoanDetails.ValidateAll() if the designated constraints aren't met.
type LoanDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanDetailsMultiError) AllErrors() []error { return m }

// LoanDetailsValidationError is the validation error returned by
// LoanDetails.Validate if the designated constraints aren't met.
type LoanDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanDetailsValidationError) ErrorName() string { return "LoanDetailsValidationError" }

// Error satisfies the builtin error interface
func (e LoanDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanDetailsValidationError{}

// Validate checks the field values on TenureDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TenureDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TenureDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TenureDetailsMultiError, or
// nil if none found.
func (m *TenureDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *TenureDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Value

	if len(errors) > 0 {
		return TenureDetailsMultiError(errors)
	}

	return nil
}

// TenureDetailsMultiError is an error wrapping multiple validation errors
// returned by TenureDetails.ValidateAll() if the designated constraints
// aren't met.
type TenureDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TenureDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TenureDetailsMultiError) AllErrors() []error { return m }

// TenureDetailsValidationError is the validation error returned by
// TenureDetails.Validate if the designated constraints aren't met.
type TenureDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TenureDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TenureDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TenureDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TenureDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TenureDetailsValidationError) ErrorName() string { return "TenureDetailsValidationError" }

// Error satisfies the builtin error interface
func (e TenureDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTenureDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TenureDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TenureDetailsValidationError{}

// Validate checks the field values on InterestDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InterestDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InterestDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InterestDetailsMultiError, or nil if none found.
func (m *InterestDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *InterestDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Value

	if len(errors) > 0 {
		return InterestDetailsMultiError(errors)
	}

	return nil
}

// InterestDetailsMultiError is an error wrapping multiple validation errors
// returned by InterestDetails.ValidateAll() if the designated constraints
// aren't met.
type InterestDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InterestDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InterestDetailsMultiError) AllErrors() []error { return m }

// InterestDetailsValidationError is the validation error returned by
// InterestDetails.Validate if the designated constraints aren't met.
type InterestDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InterestDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InterestDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InterestDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InterestDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InterestDetailsValidationError) ErrorName() string { return "InterestDetailsValidationError" }

// Error satisfies the builtin error interface
func (e InterestDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInterestDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InterestDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InterestDetailsValidationError{}

// Validate checks the field values on
// GetLoanDetailsResponseWrapper_ResponseData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLoanDetailsResponseWrapper_ResponseData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanDetailsResponseWrapper_ResponseData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetLoanDetailsResponseWrapper_ResponseDataMultiError, or nil if none found.
func (m *GetLoanDetailsResponseWrapper_ResponseData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDetailsResponseWrapper_ResponseData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TraceId

	// no validation rules for MessageCode

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetResponseData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponseWrapper_ResponseDataValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponseWrapper_ResponseDataValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponseWrapper_ResponseDataValidationError{
				field:  "ResponseData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDetailsResponseWrapper_ResponseDataMultiError(errors)
	}

	return nil
}

// GetLoanDetailsResponseWrapper_ResponseDataMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanDetailsResponseWrapper_ResponseData.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDetailsResponseWrapper_ResponseDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDetailsResponseWrapper_ResponseDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDetailsResponseWrapper_ResponseDataMultiError) AllErrors() []error { return m }

// GetLoanDetailsResponseWrapper_ResponseDataValidationError is the validation
// error returned by GetLoanDetailsResponseWrapper_ResponseData.Validate if
// the designated constraints aren't met.
type GetLoanDetailsResponseWrapper_ResponseDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDetailsResponseWrapper_ResponseDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDetailsResponseWrapper_ResponseDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDetailsResponseWrapper_ResponseDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDetailsResponseWrapper_ResponseDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDetailsResponseWrapper_ResponseDataValidationError) ErrorName() string {
	return "GetLoanDetailsResponseWrapper_ResponseDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDetailsResponseWrapper_ResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDetailsResponseWrapper_ResponseData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDetailsResponseWrapper_ResponseDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDetailsResponseWrapper_ResponseDataValidationError{}

// Validate checks the field values on
// GetLoanDetailsResponseWrapper_GetLoanDetailsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDetailsResponseWrapper_GetLoanDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanDetailsResponseWrapper_GetLoanDetailsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetLoanDetailsResponseWrapper_GetLoanDetailsResponseMultiError, or nil if
// none found.
func (m *GetLoanDetailsResponseWrapper_GetLoanDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDetailsResponseWrapper_GetLoanDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLoanDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError{
					field:  "LoanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError{
					field:  "LoanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError{
				field:  "LoanDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDetailsResponseWrapper_GetLoanDetailsResponseMultiError(errors)
	}

	return nil
}

// GetLoanDetailsResponseWrapper_GetLoanDetailsResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetLoanDetailsResponseWrapper_GetLoanDetailsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetLoanDetailsResponseWrapper_GetLoanDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDetailsResponseWrapper_GetLoanDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDetailsResponseWrapper_GetLoanDetailsResponseMultiError) AllErrors() []error { return m }

// GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError is the
// validation error returned by
// GetLoanDetailsResponseWrapper_GetLoanDetailsResponse.Validate if the
// designated constraints aren't met.
type GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError) ErrorName() string {
	return "GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDetailsResponseWrapper_GetLoanDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDetailsResponseWrapper_GetLoanDetailsResponseValidationError{}
