// Deprecated: Use merchantnamecategoriser.proto instead
syntax = "proto3";

package vendors.inhouse;

option go_package = "github.com/epifi/gamma/api/vendors/inhouse";
option java_package = "com.github.epifi.gamma.api.vendors.inhouse";

message EmployerNameCategoriserRequest {
  // unique id against which each request is made
  string request_id = 1 [json_name = 'request_id'];
  // name of the employer whose name category is to be checked
  string name = 2 [json_name = 'name'];
}

message EmployerNameCategoriserResponse {
  // probability that the given name is of an employer, values between 0 and 1
  float prob = 2 [json_name = 'prob'];
  // final decision that if the given name is an employer values: 0(fail), 1(pass)
  int32 decision = 3 [json_name = 'decision'];
  // version of the model used for this prediction
  string model_version = 4 [json_name = 'model_version'];
}

