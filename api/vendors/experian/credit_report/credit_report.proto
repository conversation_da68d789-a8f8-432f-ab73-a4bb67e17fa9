// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendors.experian.credit_report;

option go_package = "github.com/epifi/gamma/api/vendors/experian/credit_report";
option java_package = "com.github.epifi.gamma.api.vendors.experian.credit_report";

// Experian API doc: https://drive.google.com/drive/folders/1lUayGYibO5UhHKZ2DviTRvXVIpzeoypg?usp=sharing
message CheckReportPresenceRequest {
}

message CheckReportPresenceResponse {
  string error_string = 1 [json_name = "errorString"];
  string stage_one_id = 2 [json_name = "stageOneId_"];
  string stage_two_id = 3 [json_name = "stageTwoId_"];
  string show_html_report_for_credit_report = 4 [json_name = "showHtmlReportForCreditReport"];
  int64 new_user_id = 5 [json_name = "newUserId"];
  string subscription_msg = 6 [json_name = "subscriptionMsg"];
}


message FetchReportRequest {
}

message FetchReportResponse {
  string error_string = 1 [json_name = "errorString"];
  string stage_one_id = 2 [json_name = "stageOneId_"];
  string stage_two_id = 3 [json_name = "stageTwoId_"];
  string show_html_report_for_credit_report = 4 [json_name = "showHtmlReportForCreditReport"];
  int64 new_user_id = 5 [json_name = "newUserId"];
  string subscription_msg = 6 [json_name = "subscriptionMsg"];
}

message FetchReportForExistingUserRequest {
}

message FetchReportForExistingUserResponse {
  int64 stg_one_hit_id = 1 [json_name = "stgOneHitId"];
  string show_html_report_for_credit_report = 2 [json_name = "showHtmlReportForCreditReport"];
  int64 user_id = 3 [json_name = "userId"];
  string error_string = 4 [json_name = "errorString"];
}

message ExtendSubscriptionRequest {
}

message ExtendSubscriptionResponse {
  int64 stg_one_hit_id = 1 [json_name = "stg1HitId"];
  string consent_status = 2 [json_name = "consentStatus"];
  string error_string = 3 [json_name = "errorString"];
}

message AccessTokenResponse {
  string issued_at = 1 [json_name = "issued_at"];
  string expires_in = 2 [json_name = "expires_in"];
  string token_type = 3 [json_name = "token_type"];
  string access_token = 4 [json_name = "access_token"];
  string refresh_token = 5 [json_name = "refresh_token"];
}
