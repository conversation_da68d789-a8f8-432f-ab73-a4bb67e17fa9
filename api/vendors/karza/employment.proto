//go:generate gen_sql -types=EPFGetPassbookResponseV2_Result
syntax = "proto3";

package vendors.karza;


option go_package = "github.com/epifi/gamma/api/vendors/karza";
option java_package = "com.github.epifi.gamma.api.vendors.karza";

message CompanyMasterLLPDataResponse {
  CompanyMasterLLPDataResult result = 1 [json_name = "result"];
  string request_id = 2 [json_name = "request_id"];
  string status_code = 3 [json_name = "status-code"];
  string status = 4 [json_name = "status"];
  string error = 5 [json_name = "error"];
}

message CompanyMasterLLPDataResult {
  string cin = 1 [json_name = "cin"];
  string company_name = 2 [json_name = "Company_Name"];
  string roc_code = 3 [json_name = "ROC_Code"];
  string registration_number = 4 [json_name = "Registration_Number"];
  string company_category = 5 [json_name = "Company_Category"];
  string company_subCategory = 6 [json_name = "Company_SubCategory"];
  string class_of_company = 7 [json_name = "Class_of_Company"];
  string authorised_capital = 8 [json_name = "Authorised_Capital(Rs)"];
  string paid_up_capital = 9 [json_name = "Paid_up_Capital(Rs)"];
  string number_of_members = 10 [json_name = "Number_of_Members"];
  string date_of_incorporation = 11 [json_name = "Date_of_Incorporation"];
  string registered_Address = 12 [json_name = "Registered_Address"];
  string alternative_address = 13 [json_name = "alternative_address"];
  string email_id = 14 [json_name = "Email_Id"];
  string whether_listed_or_not = 15 [json_name = "Whether_Listed_or_not"];
  string suspended_at_stock_exchange = 16 [json_name = "Suspended_at_stock_exchange"];
  string date_of_last_agm = 17 [json_name = "Date_of_last_AGM"];
  string date_of_balance_sheet = 18 [json_name = "Date_of_Balance_Sheet"];
  string company_status = 19 [json_name = "Company_Status"];
}

message CompanyMasterLLPDataRequest {
  string cin = 1 [json_name = "cin"];
  string consent = 2 [json_name = "consent"];
}


message SearchGSTINByPANResponse {
  int32 status_code = 1 [json_name = "statusCode"];
  string request_id = 2 [json_name = "request_id"];
  repeated SearchGSTINByPANResult result = 3 [json_name = "result"];
}

message SearchGSTINByPANResult {
  string email_id = 1 [json_name = "emailId"];
  string application_status = 2 [json_name = "applicationStatus"];
  string mobile_number = 3 [json_name = "mobNum"];
  string pan = 4 [json_name = "pan"];
  string gstin_ref_id = 5 [json_name = "gstinRefId"];
  string reg_type = 6 [json_name = "regType"];
  string auth_status = 7 [json_name = "authStatus"];
  string gstin_id = 8 [json_name = "gstinId"];
  string registration_name = 9 [json_name = "registrationName"];
  string tin_number = 10 [json_name = "tinNumber"];
}

message SearchGSTINByPANRequest {
  string consent = 1 [json_name = "consent"];
  string pan = 2 [json_name = "pan"];
}

message GetForm16QuarterlyRequest {
  string company_id = 1 [json_name = "entityId"];
  string tan = 2 [json_name = "tan"];
  string pan = 3 [json_name = "pan"];
  string fiscal_year = 4 [json_name = "fiscalYear"];
  string quarter = 5 [json_name = "quarter"];
  string employment_type = 6 [json_name = "employmentType"];
  string consent = 7 [json_name = "consent"];
}

message GetForm16QuarterlyResponse {
  string request_id = 1 [json_name = "requestId"];
  int64 status_code = 2 [json_name = "statusCode"];
  Form16QuarterlyResults quarterly_results = 3 [json_name = "result"];
}

message Form16QuarterlyResults {
  QuarterlyRecordCounts quarterly_record_counts = 1 [json_name = "quarterlyRecordsCount"];
  string employment_type = 2 [json_name = "typeOfEmployment"];
}

message QuarterlyRecordCounts {
  int64 q1 = 1 [json_name = "q1"];
  int64 q2 = 2 [json_name = "q2"];
  int64 q3 = 3 [json_name = "q3"];
  int64 q4 = 4 [json_name = "q4"];
}

message GetEmployerDetailsByGSTINRequest {
  string consent = 1 [json_name = "consent"];
  string gstin = 2 [json_name = "gstin"];
}

message GetEmployerDetailsByGSTINResponse {
  int32 status_code = 1 [json_name = "statusCode"];
  string request_id = 2 [json_name = "requestId"];
  GSTDetails gst_details = 3 [json_name = "result"];
}

message GSTDetails {
  repeated string member_names = 1 [json_name = "mbr"];
  string can_flag = 2 [json_name = "canFlag"];
  string trade_name = 3 [json_name = "tradeNam"];
  string last_updated = 4 [json_name = "lstupdt"];
  string registration_date = 5 [json_name = "rgdt"];
  string state_jurisdiction_code = 6 [json_name = "stjCd"];
  string state_jurisdiction = 7 [json_name = "stj"];
  string central_jurisdiction_code = 8 [json_name = "ctjCd"];
  string taxpayer_type = 9 [json_name = "dty"];
  string cancellation_date = 10 [json_name = "cxdt"];
  string constitution_of_business = 11 [json_name = "ctb"];
  string status = 12 [json_name = "sts"];
  string given_gstin = 13 [json_name = "gstin"];
  string legal_name = 14 [json_name = "lgnm"];
  repeated string nature_of_business = 15 [json_name = "nba"];
  string central_jurisdiction = 16 [json_name = "ctj"];
}

message UANLookupByPanRequest {
  // Flag to indicate whether to run pan flow i.e the flow on the EmploymentVerificationAdvanced api by karza which indicates
  // if we want to fetch details with the help of pan number
  bool run_pan_flow = 1 [json_name = "runPanFlow"];
  // Contains the pan number for which details are to be fetched
  string pan = 2 [json_name = "pan"];
}

message UANLookupByPanResponse {
  string status = 1 [json_name = "status-code"];
  UANResult result = 2 [json_name = "result"];
}

message UANResult {
  message UANDetails {
    string uan = 1 [json_name = "uan"];
    repeated Employer employer = 2 [json_name = "employer"];
  }
  repeated UANDetails uan = 1 [json_name = "uan"];
  PersonalInfo personal_info = 2 [json_name = "personalInfo"];
  LookupSummary summary = 3 [json_name = "summary"];
  NameLookup name_lookup = 4 [json_name = "nameLookup"];
}

message NameLookup {
  string organization_name = 1 [json_name = "organizationName"];
  message EpfHistory {
    uint32 total_amount = 1 [json_name = "totalAmount"];
    string formatted_wageMonth = 3 [json_name = "formatted_wage_month"];
    string wage_month = 4 [json_name = "wageMonth"];
  }
  repeated EpfHistory epf_history = 2 [json_name = "epfHistory"];
  message EstInfo {
    string address = 1 [json_name = "address"];
    string contact_no = 2 [json_name = "contactNo"];
    string email_id = 3 [json_name = "emailId"];
    string est_id = 4 [json_name = "estId"];
  }
  repeated EstInfo est_info = 3 [json_name = "estInfo"];
  bool is_name_exact = 4 [json_name = "isNameExact"];
  bool is_employed = 5 [json_name = "isEmployed"];
  bool is_recent = 6 [json_name = "isRecent"];
  bool is_name_unique = 7 [json_name = "isNameUnique"];
  string employee_name = 8 [json_name = "employeeName"];
}

message Employer {
  string name = 1 [json_name = "name"];
  string member_id = 2 [json_name = "memberId"];
  string settled = 3 [json_name = "settled"];
  string date_of_exit = 4 [json_name = "dateOfExit"];
  string date_of_joining = 5 [json_name = "dateOfJoining"];
  string last_month_year = 6 [json_name = "lastMonthYear"];
  string start_month_year = 7 [json_name = "startMonthYear"];
  uint32 employment_period = 8 [json_name = "employmentPeriod"];
  bool is_name_unique = 9 [json_name = "isNameUnique"];
  string match_name = 10 [json_name = "matchName"];
  string last_month = 11 [json_name = "lastMonth"];
  bool is_recent = 12 [json_name = "isRecent"];
  bool is_name_exact = 13 [json_name = "isNameExact"];
  bool is_employed = 14 [json_name = "isEmployed"];
  float name_confidence = 15 [json_name = "nameConfidence"];
  float emplr_score = 16 [json_name = "emplrScore"];
  bool uan_name_match = 17 [json_name = "uanNameMatch"];
}

message PersonalInfo {
  string uan = 1 [json_name = "uan"];
  string aadhaar_number = 2 [json_name = "aadhaarNumber"];
  string name = 3 [json_name = "name"];
  string date_of_birth = 4 [json_name = "dateOfBirth"];
  string gender = 5 [json_name = "gender"];
  string father_husband_name = 6 [json_name = "fatherHusbandName"];
  string relation = 7 [json_name = "relation"];
  string nationality = 8 [json_name = "nationality"];
  string marital_status = 9 [json_name = "maritalStatus"];
  string qualification = 10 [json_name = "qualification"];
  string mobile_number = 11 [json_name = "mobileNumber"];
  string email_id = 12 [json_name = "emailId"];
  string pan = 13 [json_name = "pan"];
  string passport = 14 [json_name = "passport"];
}

message LookupSummary {
  message NameLookup {
    string match_name = 1 [json_name = "matchName"];
    bool is_unique = 2 [json_name = "isUnique"];
    bool is_latest = 3 [json_name = "isLatest"];
    bool result = 4 [json_name = "result"];
  }
  NameLookup name_lookup = 1 [json_name = "nameLookup"];
  message UanLookup {
    string current_employer = 1 [json_name = "currentEmployer"];
    float match_score = 2 [json_name = "matchScore"];
    bool result = 3 [json_name = "result"];
    bool uan_name_match = 4 [json_name = "uanNameMatch"];
  }
  UanLookup uan_lookup = 2 [json_name = "uanLookup"];
  bool waive_fi = 3 [json_name = "waiveFi"];
}

message FindUanByPanRequest {
  // true denotes consent is given
  // false denotes consent is not given
  string consent = 1 [json_name = "consent"];
  // Contains the pan number for which details are to be fetched
  string pan = 2 [json_name = "pan"];
}

message FindUanByPanResponse {
  int32 status = 1 [json_name = "statusCode"];
  UanPanResult result = 2 [json_name = "result"];
}

message UanPanResult {
  string uan = 1 [json_name = "uan"];
}

message EPFGetPassbookRequestV2 {
  // Unique Request ID generated in the response from /get-otp API.
  string request_id = 1 [json_name = "request_id"];
  // 6 digit OTP Received and submitted by the Account Holder from EPFO.
  string otp = 2 [json_name = "otp"];
  // If EPF Passbook in PDF format is required y/n.
  string is_pdf_required = 3 [json_name = "is_pdf_required"];
  // If Partial data is required y/n
  string partial_data = 4 [json_name = "partial_data"];
  // EPF balance details is required y/n
  string epf_balance = 5 [json_name = "epf_balance"];
  // Data of the user sharing consent (optional)
  message ClientData {
    // Unique case id/lead id of the user sharing consent
    string case_id = 1 [json_name = "caseId"];
  }
  // Data of the user sharing consent (optional)
  ClientData client_data = 6 [json_name = "clientData"];
}


message EPFGetPassbookResponseV2 {
  Result result = 1 [json_name = "result"];
  // Vendor specific status code that denotes the status of the request.
  string status_code = 2 [json_name = "status-code"];
  // Unique id of the api request.
  string request_id = 3 [json_name = "request_id"];
  // Optional: Data of the user sharing consent
  ClientData client_data = 4 [json_name = "clientData"];
  // Http status in case of error
  uint32 status = 5 [json_name = "status"];
  // Error message
  string error = 6 [json_name = "error"];

  message Result {
    // Demographic details of the EPF Account Holder
    EmployeeDetails employee_details = 1 [json_name = "employee_details"];
    // Array containing the details of all open EPF Employer Accounts of the Member along with contribution details
    repeated ESTDetails est_details = 2 [json_name = "est_details"];

    OverallPFBalance overall_pf_balance = 3 [json_name = "overall_pf_balance"];
    // Array of pdf details of employee passbook
    repeated PDF pdf = 4 [json_name = "pdf"];

    // Demographic details of the EPF Account Holder
    message EmployeeDetails {
      // Registered Name of the EPF Account Holder / member per EPFO.
      string member_name = 1 [json_name = "member_name"];
      // Complete Father's Name of the Member as per EPFOO Records.
      string father_name = 2 [json_name = "father_name"];
      // Date of Birth of the member as per EPFO Records.
      string dob = 3 [json_name = "dob"];
    }

    message ESTDetails {
      // Name of the Employer Establishment.
      string est_name = 1 [json_name = "est_name"];
      // Member ID as per Employer Establishment Records.
      string member_id = 2 [json_name = "member_id"];
      // EPFO Office Code applicable to the Employer Establishment.
      string office = 3 [json_name = "office"];
      // Date of Joining EPF
      string doj_epf = 4 [json_name = "doj_epf"];
      // Date of Enquiry of EPF Data.
      string doe_epf = 5 [json_name = "doe_epf"];
      // Date of Enquiry of Employee Pension Scheme Data.
      string doe_eps = 6 [json_name = "doe_eps"];
      // PF Balance details for specific employer
      PFBalance pf_balance = 7 [json_name = "pf_balance"];
      // Array Containing the monthwise contribution to EPF and EPS by the employer and employee along with transaction dates.
      repeated Passbook passbook = 8 [json_name = "passbook"];

      // PF Balance details for specific employer
      message PFBalance {
        // Total Net balance
        int64 net_balance = 1 [json_name = "net_balance"];
        // Whether PF amount was fully withdrawn by the employee (This can possibly be transferred or withdrawn from the account)
        bool is_pf_full_withdrawn = 2 [json_name = "is_pf_full_withdrawn"];
        // Whether PF amount was partially withdrawn by the employee
        bool is_pf_partial_withdrawn = 3 [json_name = "is_pf_partial_withdrawn"];

        // Employee share details
        message EmployeeShare {
          // Amount debited by the employee if any
          int64 debit = 1 [json_name = "debit"];
          // Amount credited by the employee
          int64 credit = 2 [json_name = "credit"];
          // Total balance amount for employee share
          int64 balance = 3 [json_name = "balance"];
        }
        EmployeeShare employee_share = 4 [json_name = "employee_share"];

        // Employer share details
        message EmployerShare {
          // Amount debited by the employee if any
          int64 debit = 1 [json_name = "debit"];
          // Amount credited by the employer
          int64 credit = 2 [json_name = "credit"];
          // Total balance amount for employer share
          int64 balance = 3 [json_name = "balance"];
        }
        EmployerShare employer_share = 5 [json_name = "employer_share"];
      }

      // Monthwise contribution to EPF and EPS by the employer and employee along with transaction dates
      message Passbook {
        // Date of transaction.
        string tr_date_my = 1 [json_name = "tr_date_my"];
        // Date of approval of the transaction.
        string approved_on = 2 [json_name = "approved_on"];
        // Employees Share of Contribution to EPF for the month.
        string cr_ee_share = 3 [json_name = "cr_ee_share"];
        // Employer's share of contribution to EPF for the month.
        string cr_er_share = 4 [json_name = "cr_er_share"];
        // Amount credited to Pension Account.
        string cr_pen_bal = 5 [json_name = "cr_pen_bal"];
        // Transaction Type Debit "D" or Credit "C".
        string db_cr_flag = 6 [json_name = "db_cr_flag"];
        // Description of the transaction as per EPF Passbook.
        string particular = 7 [json_name = "particular"];
        // Month and Year for which the contribution is made "MYYYY" OR "MMYYYY".
        string month_year = 8 [json_name = "month_year"];
        // Transaction Approval Date.
        string tr_approved_date = 9 [json_name = "tr_approved"];
      }
    }

    message OverallPFBalance {
      // Total Pension balance
      int64 pension_balance = 1 [json_name = "pension_balance"];
      // Current PF balance
      int64 current_pf_balance = 2 [json_name = "current_pf_balance"];
      EmployeeShare employee_share_total = 3 [json_name = "employee_share_total"];
      EmployeeShare employer_share_total = 4 [json_name = "employer_share_total"];

      // Employee share details
      message EmployeeShare {
        // Amount debited by the employee if any
        int64 debit = 1 [json_name = "debit"];
        // Amount credited by the employee
        int64 credit = 2 [json_name = "credit"];
        // Total balance amount for employee share
        int64 balance = 3 [json_name = "balance"];
      }

      // Employer share details
      message EmployerShare {
        // Amount debited by the employee if any
        int64 debit = 1 [json_name = "debit"];
        // Amount credited by the employer
        int64 credit = 2 [json_name = "credit"];
        // Total balance amount for employer share
        int64 balance = 3 [json_name = "balance"];
      }
    }

    // Pdf details of employee passbook
    message PDF {
      // Establishment ID of the employer
      string est_id = 1 [json_name = "est_id"];
      // EPF Passbook in Pdf format
      string pdf_data = 2 [json_name = "pdf_data"];
    }
  }

  // Data of the user sharing consent
  message ClientData {
    // Unique case id/lead id of the user sharing consent
    string case_id = 1 [json_name = "caseId"];
  }
}

message EmploymentVerificationAdvancedRequest {
  repeated string uans = 1 [json_name = "uans"];
  string entity_id = 2 [json_name = "entityId"];
  string employer_name = 3 [json_name = "employerName"];
  string employee_name = 4 [json_name = "employeeName"];
  string mobile = 5 [json_name = "mobile"];
  string email_id = 6 [json_name = "emailId"];
  bool run_pan_flow = 7 [json_name = "runPanFlow"];
  string pan = 8 [json_name = "pan"];
  bool show_failures = 9 [json_name = "showFailures"];
  bool pdf = 10 [json_name = "pdf"];
  ClientData client_data = 11 [json_name = "clientData"];
  message ClientData {
    string case_id = 1 [json_name = "caseId"];
  }
}

message EmploymentVerificationAdvancedResponse {
  //  Result result = 1 [json_name = "result"];
  //  string request_id = 2 [json_name = "request_id"];
  string status_code = 3 [json_name = "status-code"];
  //  ClientData client_data = 4 [json_name = "clientData"];

  message ClientData {
    string case_id = 1 [json_name = "caseId"];
  }
  message Result {
    Email email = 1 [json_name = "email"];
    NameLookup name_lookup = 2 [json_name = "nameLookup"];
    repeated UanData uan = 3 [json_name = "uan"];
    PersonalInfo personal_info = 4 [json_name = "personalInfo"];
    Summary summary = 5 [json_name = "summary"];
    repeated string failures = 6 [json_name = "failures"];
    string pdf_link = 7 [json_name = "pdfLink"];

    message Email {
      AdditionalInfo additional_info = 1 [json_name = "additionalInfo"];

      Data data = 2 [json_name = "data"];

      bool result = 3 [json_name = "result"];

      message AdditionalInfo {
        repeated CompanyInfo company_info = 1 [json_name = "companyInfo"];

        repeated IndividualMatch individual_match = 2 [json_name = "individualMatch"];

        SpamRecord spam_record = 3 [json_name = "spamRecord"];

        WhoisInfo who_is_info = 4 [json_name = "whoisInfo"];

        message CompanyInfo {
          repeated OrgDomainMatch org_domain_match = 1 [json_name = "orgDomainMatch"];

          repeated OrgEmailMatch org_email_match = 2 [json_name = "orgEmailMatch"];

          repeated UsrDirectorMatch usr_director_match = 3 [json_name = "usrDirectorMatch"];
          message OrgDomainMatch {
            string domain = 1 [json_name = "domain"];
            bool match = 2 [json_name = "match"];
            string org_name = 3 [json_name = "orgName"];
            string source = 4 [json_name = "source"];
          }
          message OrgEmailMatch {
            string company_email = 1 [json_name = "companyEmail"];
            bool match = 2 [json_name = "match"];
            string org_name = 3 [json_name = "orgName"];
            string source = 4 [json_name = "source"];
          }
          message UsrDirectorMatch {
            bool match = 1 [json_name = "match"];
            string name = 2 [json_name = "name"];
            string org_name = 3 [json_name = "orgName"];
            double score = 4 [json_name = "score"];
          }
        }
        message IndividualMatch {
          bool match = 1 [json_name = "match"];
          string name = 2 [json_name = "name"];
          double score = 3 [json_name = "score"];
        }
        message SpamRecord {
          bool spam_email = 1 [json_name = "spamEmail"];
          int32 report_count = 2 [json_name = "reportCount"];
          bool ip_blacklist = 3 [json_name = "ipBlacklist"];
        }
        message WhoisInfo {
          double age_year = 1 [json_name = "ageYear"];
          string creation_date = 2 [json_name = "creationDate"];
          string expiration_date = 3 [json_name = "expirationDate"];
          string update_date = 4 [json_name = "updateDate"];
          bool expired = 5 [json_name = "expired"];
          repeated WhoIsEmailDomainMatch who_is_email_domain_match = 6 [json_name = "whoisEmailDomainMatch"];
          repeated WhoIsEmailMatch who_is_email_match = 7 [json_name = "whoisEmailMatch"];
          repeated WhoIsIndvName who_is_indv_name = 8 [json_name = "whoisIndvName"];
          repeated WhoisOrgName who_is_org_name = 9 [json_name = "whoisOrgName"];

          message WhoIsEmailDomainMatch {
            string domain = 1 [json_name = "domain"];
            bool match = 2 [json_name = "match"];
          }
          message WhoIsEmailMatch {
            string company_email = 1 [json_name = "companyEmail"];
            bool match = 2 [json_name = "match"];
          }
          message WhoIsIndvName {
            string name = 1 [json_name = "name"];
            bool match = 2 [json_name = "match"];
            double score = 3 [json_name = "score"];
          }
          message WhoisOrgName {
            bool match = 1 [json_name = "match"];
            string org_name = 2 [json_name = "orgName"];
            double score = 3 [json_name = "score"];
          }

        }
      }
      message Data {
        bool accept_all = 1 [json_name = "acceptAll"];
        bool disposable = 2 [json_name = "disposable"];
        string email = 3 [json_name = "email"];
        bool generic_email = 4 [json_name = "genericEmail"];
        bool mx_records = 5 [json_name = "mxRecords"];
        bool regexp = 6 [json_name = "regexp"];
        bool result = 7 [json_name = "result"];
        bool smtp_check = 8 [json_name = "smtpCheck"];
        bool webmail = 9 [json_name = "webmail"];
      }
    }

    message NameLookup {
      string organization_name = 1 [json_name = "organizationName"];
      repeated EpfHistory epf_history = 2 [json_name = "epfHistory"];
      repeated EstInfo est_info = 3 [json_name = "estInfo"];
      repeated Match matches = 4 [json_name = "matches"];
      bool is_name_exact = 5 [json_name = "isNameExact"];
      bool is_employed = 6 [json_name = "isEmployed"];
      bool is_recent = 7 [json_name = "isRecent"];
      bool is_name_unique = 8 [json_name = "isNameUnique"];
      string employee_name = 9 [json_name = "employeeName"];

      message EpfHistory {
        int32 total_amount = 1 [json_name = "totalAmount"];
        int32 total_members = 2 [json_name = "totalMembers"];
        string formatted_wage_month = 3 [json_name = "formatted_wage_month"];
        string wage_month = 4 [json_name = "wageMonth"];
      }

      message EstInfo {
        string address = 1 [json_name = "address"];
        string contact_no = 2 [json_name = "contactNo"];
        string email_id = 3 [json_name = "emailId"];
        string est_id = 4 [json_name = "estId"];
      }

      message Match {
        string name = 1 [json_name = "name"];
        double confidence = 2 [json_name = "confidence"];
        string est_id = 3 [json_name = "estId"];
        map<string, bool> epf_history = 4 [json_name = "epfHistory"];
      }
    }

    message PersonalInfo {
      string name = 1 [json_name = "name"];
      string date_of_birth = 2 [json_name = "dateOfBirth"];
      string gender = 3 [json_name = "gender"];
      string father_husband_name = 4 [json_name = "fatherHusbandName"];
      string relation = 5 [json_name = "relation"];
      string nationality = 6 [json_name = "nationality"];
      string marital_status = 7 [json_name = "maritalStatus"];
      string qualification = 8 [json_name = "qualification"];
      string mobile_number = 9 [json_name = "mobileNumber"];
      string email_id = 10 [json_name = "emailId"];
      string pan = 11 [json_name = "pan"];
      string passport = 12 [json_name = "passport"];
      string uan = 13 [json_name = "uan"];
    }

    message Summary {
      EmailParameters email_parameters = 1 [json_name = "emailParameters"];
      bool email_valid = 2 [json_name = "emailValid"];
      NameLookup name_lookup = 3 [json_name = "nameLookup"];
      UANLookup uan_lookup = 4 [json_name = "uanLookup"];
      bool waive_fi = 5 [json_name = "waiveFi"];
      message EmailParameters {
        bool is_valid = 1 [json_name = "isValid"];
        Person person = 2 [json_name = "person"];
        Organization organization = 3 [json_name = "organization"];
        bool alerts = 4 [json_name = "alerts"];
        message Person {
          bool match = 1 [json_name = "match"];
          double match_score = 2 [json_name = "matchScore"];
        }
        message Organization {
          bool match = 1 [json_name = "match"];
          string org_name = 2 [json_name = "orgName"];
        }
      }
      message NameLookup {
        string match_name = 1 [json_name = "matchName"];
        bool is_unique = 2 [json_name = "isUnique"];
        bool is_latest = 3 [json_name = "isLatest"];
        bool result = 4 [json_name = "result"];
      }
      message UANLookup {
        string current_employer = 1 [json_name = "currentEmployer"];
        int64 match_score = 2 [json_name = "matchScore"];
        bool result = 3 [json_name = "result"];
        bool uan_name_match = 4 [json_name = "uanNameMatch"];
      }
    }

    message UanData {
      string uan = 1 [json_name = "uan"];
      string uan_source = 2 [json_name = "uanSource"];
      repeated Employer employer = 3 [json_name = "employer"];
      repeated string failures = 4 [json_name = "failures"];

      message Employer {
        string name = 1 [json_name = "name"];
        string member_id = 2 [json_name = "memberId"];
        string settled = 3 [json_name = "settled"];
        string date_of_exit = 4 [json_name = "dateOfExit"];
        string date_of_joining = 5 [json_name = "dateOfJoining"];
        string last_month_year = 6 [json_name = "lastMonthYear"];
        string start_month_year = 7 [json_name = "startMonthYear"];
        int64 employment_period = 8 [json_name = "employmentPeriod"]; // in months
        bool is_name_unique = 9 [json_name = "isNameUnique"];
        string match_name = 10 [json_name = "matchName"];
        string last_month = 11 [json_name = "lastMonth"];
        bool is_recent = 12 [json_name = "isRecent"];
        bool is_name_exact = 13 [json_name = "isNameExact"];
        bool is_employed = 14 [json_name = "isEmployed"];
        double name_confidence = 15 [json_name = "nameConfidence"];
        double emplr_score = 16 [json_name = "emplrScore"];
        bool uan_name_match = 17 [json_name = "uanNameMatch"];
      }
    }
  }
}
