syntax = "proto3";

package vendors.thriwe;

option go_package = "github.com/epifi/gamma/api/vendors/thriwe";
option java_package = "com.github.epifi.gamma.api.vendors.thriwe";

message CreateCustomerRequest {
  string fi_user_id = 1 [json_name = "fiUserId"];
  string first_name = 2 [json_name = "firstName"];
  string last_name = 3 [json_name = "lastName"];
  string gender = 4 [json_name = "gender"];
  string mobile_number = 5 [json_name = "mobileNumber"];
  string country_code = 6 [json_name = "countryCode"];
  string email = 7 [json_name = "email"];
}

message CreateCustomerResponse {
  // denotes a unique identifier generated at thriwe's end for every unique customer/user.
  string object_id = 1 [json_name = "objectId"];
  // denotes the timestamp (in RFC3339 format) when the user/customer was created in RFC339 format.
  string created_at = 2 [json_name = "createdAt"];
}
