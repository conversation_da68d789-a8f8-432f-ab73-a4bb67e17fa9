// protolint:disable MAX_LINE_LENGTH
//go:generate gen_queue_pb
syntax = "proto3";

package card.event;

import "api/card/card.proto";
import "api/card/control.proto";
import "api/card/control/card_limit.proto";
import "api/card/provisioning/card_tracking_request.proto";
import "api/queue/consumer_headers.proto";
import "api/typesv2/common/boolean.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/card/event";
option java_package = "com.github.epifi.gamma.api.card.event";

message DebitCardUpdateEvent {
  queue.ConsumerRequestHeader request_header = 1;
  DebitCardEventPayload payload = 2;
}

// exhaustive data for any type of debit card action that has taken place based on which
// processing decisions will be made in the consumer
// CURRENTlY USED ONLY IN CARD ACTIVATION EVENT
message DebitCardEventPayload {
  string card_id = 1;
  string actor_id = 2;
  // details regarding various types of limits like atm, pos, etc.
  // it will be null in case the event does not concern card limits
  card.control.CardLimitData card_limit_data = 3;
  // contains enabled/disabled config for all card controls.
  // it will be null in case the event does not concern card controls
  card.ControlData card_control_data = 4;
  // boolean denoting whether physical card activation has been done for the card .
  // It will be BOOLEAN_ENUM_UNSPECIFIED in case the event is not for card activation.
  api.typesv2.common.BooleanEnum physical_card_activated = 5;
  // current state of the card . it will be  CARD_STATE_UNSPECIFIED in case event does not concern
  // card state
  card.CardState card_state = 6;
  // delivery status of physical debit card. CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED in case the
  // event does not concern card delivery
  card.provisioning.CardTrackingDeliveryState card_delivery_status = 7;
  // timestamp of update
  google.protobuf.Timestamp updated_at = 8;
}


