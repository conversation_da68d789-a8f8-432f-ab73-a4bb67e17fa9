// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/omegle/enums/remark_tag.pb.go

package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the RemarkTag in string format in DB
func (p RemarkTag) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing RemarkTag while reading from DB
func (p *RemarkTag) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := RemarkTag_value[val]
	if !ok {
		return fmt.Errorf("unexpected RemarkTag value: %s", val)
	}
	*p = RemarkTag(valInt)
	return nil
}

// Marshaler interface implementation for RemarkTag
func (x RemarkTag) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for RemarkTag
func (x *RemarkTag) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = RemarkTag(RemarkTag_value[val])
	return nil
}
