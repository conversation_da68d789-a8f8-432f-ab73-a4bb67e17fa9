// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package wealthonboarding.db;

import "api/typesv2/marital_status.proto";
import "api/typesv2/document_proof.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/nominee.proto";
import "api/typesv2/residential_status.proto";
import "api/typesv2/politically_exposed_status.proto";
import "api/typesv2/nationality.proto";
import "api/typesv2/income_slab.proto";
import "google/type/date.proto";
import "api/wealthonboarding/data.proto";
import "api/wealthonboarding/ocr_document_proof.proto";
import "api/wealthonboarding/manual_review.proto";

option go_package = "github.com/epifi/gamma/api/wealthonboarding/db";
option java_package = "com.github.epifi.gamma.api.wealthonboarding/db";

// personal details of the user captured during onboarding
message PersonalDetails {
  api.typesv2.common.Name name = 1;
  api.typesv2.Gender Gender = 2;
  api.typesv2.common.PhoneNumber phone_number = 3;
  string email = 4;
  google.type.Date dob = 5;
  // photo of the user that will be permanently stored with us.
  api.typesv2.DocumentProof photo = 6;
  api.typesv2.MaritalStatus marital_status = 7;
  api.typesv2.common.Name father_name = 9;
  api.typesv2.common.Name mother_name = 11;

  // nominees of user retrieved from EPIFI TECH
  repeated api.typesv2.Nominee nominees = 12;

  api.typesv2.Nationality nationality = 13;
  api.typesv2.ResidentialStatus residential_status = 15;
  api.typesv2.PoliticallyExposedStatus politically_exposed_status = 17;
  // signature of the customer
  api.typesv2.DocumentProof signature = 18;
  string occupation = 19;
  api.typesv2.IncomeSlab income_slab = 21 [deprecated = true]; // use income instead
  api.typesv2.DocumentProof pan_details = 22;
  api.typesv2.DocumentProof poi_details = 23;
  api.typesv2.DocumentProof poa_details = 24;
  EmploymentData employment_data = 25;
  BankDetails bank_details = 26;
  OcrDocumentProof poa_with_ocr = 27;
  int32 income = 28;
  NomineeDeclarationDetails nominee_declaration_details = 29;

  // PAN from docket (with OCR results) for users with KYC records existing with KRA
  // used as an identity proof for opening US stockbroking account of user
  OcrDocumentProof kra_docket_pan = 30;

  // PAN from CKYC (with OCR results) for users who have records with CKYC
  // used as an identity proof for opening US stockbroking account of user
  OcrDocumentProof ckyc_pan = 31;
  // investment advisory agreement details
  AdvisoryAgreementDetails advisory_agreement_details = 32;
}

message MatchData {
  FaceMatchData face_match_data = 1;
  NameMatchInfo name_match_info = 2;
  LivenessData liveness_data = 3;
  ManualReviewAttempts manual_review_attempts = 4;
}

// data captured during onboarding
message OnboardingMetadata {
  DocketInfo agreement_docket_info = 11;
  bool is_fresh_kra = 14;
  string customer_ip_address = 15;
  int32 pan_validate_attempts_count = 16;
  bool is_investment_risk_survey_complete = 17;
}

message KraData {
  KraStatusData status_data = 1;
  KraDownloadedData downloaded_data = 2;
  UploadKraDocData upload_kra_doc_data = 3;
  DownloadedKraDocData downloaded_kra_doc_data = 4;
  DocketInfo kra_docket_info = 5;
}
