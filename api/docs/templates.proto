// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package docs;

option go_package = "github.com/epifi/gamma/api/docs";
option java_package = "com.github.epifi.gamma.api.docs";

// PDFTemplate will hold all template created for generating pdf.
// For each template enum there will be a template file created in comms server (docs service) config/pdf-templates/ folder.
// File name should be <enum in uppercase>.html
enum PDFTemplate {
  PDF_TEMPLATE_UNSPECIFIED = 0;
  // Test template to run test for pdf generation
  TEST = 1;
  // Savings statement template for pdf generation
  SAVINGS_STATEMENT = 2;
  // Wealth template for Kra form pdf generation
  WEALTH_KRA = 3;
  // Wealth template for agreement pdf generation
  WEALTH_AGREEMENT_SIGNATURE = 4;
  // Wealth template for Aadhaar Card in pdf form generation
  AADHAAR_CARD_DIGILOCKER = 5;
  // Wealth template for rendering multiple images as pdf
  WEALTH_MULTIPLE_IMAGES_CONVERSION = 6;
  // p2p vendor liquiloans agreement which need to be generated for all the users those are investing
  P2P_INVESTMENT_LIQUILOANS_AGREEMENT = 7;
  // Mutual fund related pdf that needs to be generated for the users who wants to change their mobile numbers associated with a folio.
  MF_MOBILE_NUMBER_CHANGE_ACKNOWLEDGEMENT = 8;
  // A2 form template for international fund transfer via Federal Bank
  INTERNATIONAL_FUND_TRANSFER_FEDERAL_BANK_A2_FORM = 9;
  // Connected Account Statement template for pdf generation of connected account transaction summary
  CONNECTED_ACCOUNT_STATEMENT = 10;
  // Credit card statement template for pdf generation
  CREDIT_CARD_STATEMENT = 11;
  // Tax Statement template for pdf generation for mutual funds
  MF_TAX_STATEMENT = 12;
  // Credit card statement template v2
  CREDIT_CARD_STATEMENT_V2 = 13;
  // Deposit statement template for pdf generation
  DEPOSIT_STATEMENT = 14;
  // https://drive.google.com/file/d/1J6Ry7ng0n_dU1YM-hojxYv1Nj2x0t6q7/view
  P2P_MATURITY_ACTION_CONSENT_LETTER = 15;
  // loan agreement document for IDFC pre-approved loans
  PL_IDFC_LOAN_AGREEMENT = 16;
  // loan agreement document for Liquiloans EarlySalary program
  PL_LL_ES_LOAN_AGREEMENT = 17;
  // Federal Savings Account summary sent to users after successful account creation.
  FEDERAL_SAVINGS_ACCOUNT_SUMMARY = 18;
  // loan agreement document for ABFL pre-approved loans
  PL_ABFL_LOAN_AGREEMENT = 19;
  // savings statement template with tiering rewards summary
  SAVINGS_STATEMENT_WITH_TIERING_REWARDS_SUMMARY = 20;
}
