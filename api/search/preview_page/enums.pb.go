// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/search/preview_page/enums.proto

package preview_page

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ComponentType int32

const (
	ComponentType_COMPONENT_TYPE_UNSPECIFIED ComponentType = 0
	ComponentType_QUICK_ACTION               ComponentType = 1
	ComponentType_TRANSACTION_FETCHER        ComponentType = 2
)

// Enum value maps for ComponentType.
var (
	ComponentType_name = map[int32]string{
		0: "COMPONENT_TYPE_UNSPECIFIED",
		1: "QUICK_ACTION",
		2: "TRANSACTION_FETCHER",
	}
	ComponentType_value = map[string]int32{
		"COMPONENT_TYPE_UNSPECIFIED": 0,
		"QUICK_ACTION":               1,
		"TRANSACTION_FETCHER":        2,
	}
)

func (x ComponentType) Enum() *ComponentType {
	p := new(ComponentType)
	*p = x
	return p
}

func (x ComponentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ComponentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_search_preview_page_enums_proto_enumTypes[0].Descriptor()
}

func (ComponentType) Type() protoreflect.EnumType {
	return &file_api_search_preview_page_enums_proto_enumTypes[0]
}

func (x ComponentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ComponentType.Descriptor instead.
func (ComponentType) EnumDescriptor() ([]byte, []int) {
	return file_api_search_preview_page_enums_proto_rawDescGZIP(), []int{0}
}

var File_api_search_preview_page_enums_proto protoreflect.FileDescriptor

var file_api_search_preview_page_enums_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2f, 0x70, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x70, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x2a, 0x5a, 0x0a, 0x0d, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x43,
	0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x51,
	0x55, 0x49, 0x43, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x17, 0x0a,
	0x13, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x45, 0x54,
	0x43, 0x48, 0x45, 0x52, 0x10, 0x02, 0x42, 0x60, 0x0a, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5a, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2f, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_search_preview_page_enums_proto_rawDescOnce sync.Once
	file_api_search_preview_page_enums_proto_rawDescData = file_api_search_preview_page_enums_proto_rawDesc
)

func file_api_search_preview_page_enums_proto_rawDescGZIP() []byte {
	file_api_search_preview_page_enums_proto_rawDescOnce.Do(func() {
		file_api_search_preview_page_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_search_preview_page_enums_proto_rawDescData)
	})
	return file_api_search_preview_page_enums_proto_rawDescData
}

var file_api_search_preview_page_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_search_preview_page_enums_proto_goTypes = []interface{}{
	(ComponentType)(0), // 0: search.preview_page.ComponentType
}
var file_api_search_preview_page_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_search_preview_page_enums_proto_init() }
func file_api_search_preview_page_enums_proto_init() {
	if File_api_search_preview_page_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_search_preview_page_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_search_preview_page_enums_proto_goTypes,
		DependencyIndexes: file_api_search_preview_page_enums_proto_depIdxs,
		EnumInfos:         file_api_search_preview_page_enums_proto_enumTypes,
	}.Build()
	File_api_search_preview_page_enums_proto = out.File
	file_api_search_preview_page_enums_proto_rawDesc = nil
	file_api_search_preview_page_enums_proto_goTypes = nil
	file_api_search_preview_page_enums_proto_depIdxs = nil
}
