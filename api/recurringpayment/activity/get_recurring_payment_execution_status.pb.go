// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/recurringpayment/activity/get_recurring_payment_execution_status.proto

package activity

import (
	activity "github.com/epifi/be-common/api/celestial/activity"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	payment "github.com/epifi/gamma/api/order/payment"
	recurringpayment "github.com/epifi/gamma/api/recurringpayment"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetRecurringPaymentExecutionStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader        *activity.RequestHeader               `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	OriginalRequestId    string                                `protobuf:"bytes,2,opt,name=original_request_id,json=originalRequestId,proto3" json:"original_request_id,omitempty"`
	PartnerBank          vendorgateway.Vendor                  `protobuf:"varint,3,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	ActorId              string                                `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	PaymentProtocol      payment.PaymentProtocol               `protobuf:"varint,5,opt,name=payment_protocol,json=paymentProtocol,proto3,enum=order.payment.PaymentProtocol" json:"payment_protocol,omitempty"`
	RecurringPaymentType recurringpayment.RecurringPaymentType `protobuf:"varint,6,opt,name=recurring_payment_type,json=recurringPaymentType,proto3,enum=recurringpayment.RecurringPaymentType" json:"recurring_payment_type,omitempty"`
}

func (x *GetRecurringPaymentExecutionStatusRequest) Reset() {
	*x = GetRecurringPaymentExecutionStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecurringPaymentExecutionStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecurringPaymentExecutionStatusRequest) ProtoMessage() {}

func (x *GetRecurringPaymentExecutionStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecurringPaymentExecutionStatusRequest.ProtoReflect.Descriptor instead.
func (*GetRecurringPaymentExecutionStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDescGZIP(), []int{0}
}

func (x *GetRecurringPaymentExecutionStatusRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GetRecurringPaymentExecutionStatusRequest) GetOriginalRequestId() string {
	if x != nil {
		return x.OriginalRequestId
	}
	return ""
}

func (x *GetRecurringPaymentExecutionStatusRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *GetRecurringPaymentExecutionStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRecurringPaymentExecutionStatusRequest) GetPaymentProtocol() payment.PaymentProtocol {
	if x != nil {
		return x.PaymentProtocol
	}
	return payment.PaymentProtocol(0)
}

func (x *GetRecurringPaymentExecutionStatusRequest) GetRecurringPaymentType() recurringpayment.RecurringPaymentType {
	if x != nil {
		return x.RecurringPaymentType
	}
	return recurringpayment.RecurringPaymentType(0)
}

type GetRecurringPaymentExecutionStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader             *activity.ResponseHeader    `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	TransactionStatus          payment.TransactionStatus   `protobuf:"varint,2,opt,name=transaction_status,json=transactionStatus,proto3,enum=order.payment.TransactionStatus" json:"transaction_status,omitempty"`
	ExecutedTransactionDetails *ExecutedTransactionDetails `protobuf:"bytes,3,opt,name=executed_transaction_details,json=executedTransactionDetails,proto3" json:"executed_transaction_details,omitempty"`
}

func (x *GetRecurringPaymentExecutionStatusResponse) Reset() {
	*x = GetRecurringPaymentExecutionStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecurringPaymentExecutionStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecurringPaymentExecutionStatusResponse) ProtoMessage() {}

func (x *GetRecurringPaymentExecutionStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecurringPaymentExecutionStatusResponse.ProtoReflect.Descriptor instead.
func (*GetRecurringPaymentExecutionStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDescGZIP(), []int{1}
}

func (x *GetRecurringPaymentExecutionStatusResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *GetRecurringPaymentExecutionStatusResponse) GetTransactionStatus() payment.TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return payment.TransactionStatus(0)
}

func (x *GetRecurringPaymentExecutionStatusResponse) GetExecutedTransactionDetails() *ExecutedTransactionDetails {
	if x != nil {
		return x.ExecutedTransactionDetails
	}
	return nil
}

type ExecutedTransactionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Utr                    string                 `protobuf:"bytes,1,opt,name=utr,proto3" json:"utr,omitempty"`
	RawResponseCode        string                 `protobuf:"bytes,2,opt,name=raw_response_code,json=rawResponseCode,proto3" json:"raw_response_code,omitempty"`
	RawResponseDescription string                 `protobuf:"bytes,3,opt,name=raw_response_description,json=rawResponseDescription,proto3" json:"raw_response_description,omitempty"`
	StatusCode             string                 `protobuf:"bytes,4,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`
	StatusDescriptionPayer string                 `protobuf:"bytes,5,opt,name=status_description_payer,json=statusDescriptionPayer,proto3" json:"status_description_payer,omitempty"`
	TransactionExecutedAt  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=transaction_executed_at,json=transactionExecutedAt,proto3" json:"transaction_executed_at,omitempty"`
}

func (x *ExecutedTransactionDetails) Reset() {
	*x = ExecutedTransactionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutedTransactionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutedTransactionDetails) ProtoMessage() {}

func (x *ExecutedTransactionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutedTransactionDetails.ProtoReflect.Descriptor instead.
func (*ExecutedTransactionDetails) Descriptor() ([]byte, []int) {
	return file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDescGZIP(), []int{2}
}

func (x *ExecutedTransactionDetails) GetUtr() string {
	if x != nil {
		return x.Utr
	}
	return ""
}

func (x *ExecutedTransactionDetails) GetRawResponseCode() string {
	if x != nil {
		return x.RawResponseCode
	}
	return ""
}

func (x *ExecutedTransactionDetails) GetRawResponseDescription() string {
	if x != nil {
		return x.RawResponseDescription
	}
	return ""
}

func (x *ExecutedTransactionDetails) GetStatusCode() string {
	if x != nil {
		return x.StatusCode
	}
	return ""
}

func (x *ExecutedTransactionDetails) GetStatusDescriptionPayer() string {
	if x != nil {
		return x.StatusDescriptionPayer
	}
	return ""
}

func (x *ExecutedTransactionDetails) GetTransactionExecutedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionExecutedAt
	}
	return nil
}

var File_api_recurringpayment_activity_get_recurring_payment_execution_status_proto protoreflect.FileDescriptor

var file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDesc = []byte{
	0x0a, 0x4a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f,
	0x67, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x72, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70,
	0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x03, 0x0a, 0x29, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x38, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61,
	0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52,
	0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1e, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x12, 0x5c, 0x0a, 0x16, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x14, 0x72, 0x65, 0x63, 0x75,
	0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x22, 0xc3, 0x02, 0x0a, 0x2a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4f, 0x0a, 0x12,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x77, 0x0a,
	0x1c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x1a, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xc3, 0x02, 0x0a, 0x1a, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x74, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x74, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x61, 0x77, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x72, 0x61, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x38,
	0x0a, 0x18, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x16, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x61, 0x79, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x17, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x15, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x6c, 0x0a, 0x34,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x69, 0x6e, 0x67, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDescOnce sync.Once
	file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDescData = file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDesc
)

func file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDescGZIP() []byte {
	file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDescOnce.Do(func() {
		file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDescData)
	})
	return file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDescData
}

var file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_goTypes = []interface{}{
	(*GetRecurringPaymentExecutionStatusRequest)(nil),  // 0: recurringpayment.activity.GetRecurringPaymentExecutionStatusRequest
	(*GetRecurringPaymentExecutionStatusResponse)(nil), // 1: recurringpayment.activity.GetRecurringPaymentExecutionStatusResponse
	(*ExecutedTransactionDetails)(nil),                 // 2: recurringpayment.activity.ExecutedTransactionDetails
	(*activity.RequestHeader)(nil),                     // 3: celestial.activity.RequestHeader
	(vendorgateway.Vendor)(0),                          // 4: vendorgateway.Vendor
	(payment.PaymentProtocol)(0),                       // 5: order.payment.PaymentProtocol
	(recurringpayment.RecurringPaymentType)(0),         // 6: recurringpayment.RecurringPaymentType
	(*activity.ResponseHeader)(nil),                    // 7: celestial.activity.ResponseHeader
	(payment.TransactionStatus)(0),                     // 8: order.payment.TransactionStatus
	(*timestamppb.Timestamp)(nil),                      // 9: google.protobuf.Timestamp
}
var file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_depIdxs = []int32{
	3, // 0: recurringpayment.activity.GetRecurringPaymentExecutionStatusRequest.request_header:type_name -> celestial.activity.RequestHeader
	4, // 1: recurringpayment.activity.GetRecurringPaymentExecutionStatusRequest.partner_bank:type_name -> vendorgateway.Vendor
	5, // 2: recurringpayment.activity.GetRecurringPaymentExecutionStatusRequest.payment_protocol:type_name -> order.payment.PaymentProtocol
	6, // 3: recurringpayment.activity.GetRecurringPaymentExecutionStatusRequest.recurring_payment_type:type_name -> recurringpayment.RecurringPaymentType
	7, // 4: recurringpayment.activity.GetRecurringPaymentExecutionStatusResponse.response_header:type_name -> celestial.activity.ResponseHeader
	8, // 5: recurringpayment.activity.GetRecurringPaymentExecutionStatusResponse.transaction_status:type_name -> order.payment.TransactionStatus
	2, // 6: recurringpayment.activity.GetRecurringPaymentExecutionStatusResponse.executed_transaction_details:type_name -> recurringpayment.activity.ExecutedTransactionDetails
	9, // 7: recurringpayment.activity.ExecutedTransactionDetails.transaction_executed_at:type_name -> google.protobuf.Timestamp
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_init() }
func file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_init() {
	if File_api_recurringpayment_activity_get_recurring_payment_execution_status_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecurringPaymentExecutionStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecurringPaymentExecutionStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutedTransactionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_goTypes,
		DependencyIndexes: file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_depIdxs,
		MessageInfos:      file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_msgTypes,
	}.Build()
	File_api_recurringpayment_activity_get_recurring_payment_execution_status_proto = out.File
	file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_rawDesc = nil
	file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_goTypes = nil
	file_api_recurringpayment_activity_get_recurring_payment_execution_status_proto_depIdxs = nil
}
