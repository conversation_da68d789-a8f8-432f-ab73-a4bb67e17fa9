syntax = "proto3";

package recurringpayment.activity;

import "api/celestial/activity/header.proto";
import "api/order/payment/transaction.proto";
import "api/order/payment/payment_protocol.proto";
import "api/typesv2/common/ownership.proto";
import "google/type/money.proto";
import "api/vendorgateway/vendor.proto";

option go_package = "github.com/epifi/gamma/api/recurringpayment/activity";
option java_package = "com.github.epifi.gamma.api.recurringpayment.activity";

message CreateTransactionRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  message TransactionRequestParams {

    // server side generated unique request id per transaction (Surrogate key).
    // This req id is passed to all the external systems to uniquely identify an system transaction.
    //
    // This is unique ID generated for all on-app transaction and shared with vendor only. Any other external entity
    // might not aware of the value of this field. For example: For on-app bank account transfer, this value generated
    // by us and send to FEDERAL for transaction initiation. Federal do not share this id with any other entity involve in payment.
    order.payment.PaymentRequestInformation req_info = 1;

    // payment instrument from which transaction is initiated
    string pi_from = 2;

    // payment instrument to which transaction is made
    string pi_to = 3;

    // amount involved in the transaction
    google.type.Money amount = 4;

    // comments/remarks added by an user during transaction.
    // this can be auto-populated by partner banks in some case.
    string remarks = 5;

    // unique order identifier to which the transaction is linked to
    // there is a 1:N mapping between order<>transaction and transaction
    // can't exist without order. The other-way round might be possible
    string order_id = 6;

    order.payment.PaymentProtocol payment_protocol = 7;

    // the state of a transaction
    // transaction state machine varies with payment mode.
    order.payment.TransactionStatus status = 8;

    // protocol specific granular status code, that is needed for processing a txn.
    order.payment.TransactionProtocolStatus protocol_status = 9;

    order.payment.TransactionDetailedStatus detailed_status = 10;

    string utr = 11;

    // ownership under which transaction data is persisted
    api.typesv2.common.Ownership ownership = 12;

    // vendor bank through which transaction is done
    vendorgateway.Vendor partner_bank = 13;
  }
  // Transaction which need to be created
  TransactionRequestParams transaction_request_params = 2;
}

message CreateTransactionResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;

  // Transaction which was created
  order.payment.Transaction transaction = 2;
}

