//go:generate gen_queue_pb

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/recurringpayment/enach/consumer/consumer.proto

package consumer

import (
	context "context"
	order "github.com/epifi/gamma/api/order"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Consumer_ProcessOrderEventForExecutionStatusUpdate_FullMethodName = "/recurringpayment.enach.consumer.Consumer/ProcessOrderEventForExecutionStatusUpdate"
)

// ConsumerClient is the client API for Consumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConsumerClient interface {
	// ProcessOrderEventForExecutionStatusUpdate processes order update events for getting notified about the execution status of enach execution.
	// It validates if the order is for an enach execution, if yes it checks the order status and updates the execution status of enach execution.
	// If order is not related to enach execution, then it discards the event.
	ProcessOrderEventForExecutionStatusUpdate(ctx context.Context, in *order.OrderUpdate, opts ...grpc.CallOption) (*ConsumerResponse, error)
}

type consumerClient struct {
	cc grpc.ClientConnInterface
}

func NewConsumerClient(cc grpc.ClientConnInterface) ConsumerClient {
	return &consumerClient{cc}
}

func (c *consumerClient) ProcessOrderEventForExecutionStatusUpdate(ctx context.Context, in *order.OrderUpdate, opts ...grpc.CallOption) (*ConsumerResponse, error) {
	out := new(ConsumerResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessOrderEventForExecutionStatusUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConsumerServer is the server API for Consumer service.
// All implementations should embed UnimplementedConsumerServer
// for forward compatibility
type ConsumerServer interface {
	// ProcessOrderEventForExecutionStatusUpdate processes order update events for getting notified about the execution status of enach execution.
	// It validates if the order is for an enach execution, if yes it checks the order status and updates the execution status of enach execution.
	// If order is not related to enach execution, then it discards the event.
	ProcessOrderEventForExecutionStatusUpdate(context.Context, *order.OrderUpdate) (*ConsumerResponse, error)
}

// UnimplementedConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedConsumerServer struct {
}

func (UnimplementedConsumerServer) ProcessOrderEventForExecutionStatusUpdate(context.Context, *order.OrderUpdate) (*ConsumerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessOrderEventForExecutionStatusUpdate not implemented")
}

// UnsafeConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConsumerServer will
// result in compilation errors.
type UnsafeConsumerServer interface {
	mustEmbedUnimplementedConsumerServer()
}

func RegisterConsumerServer(s grpc.ServiceRegistrar, srv ConsumerServer) {
	s.RegisterService(&Consumer_ServiceDesc, srv)
}

func _Consumer_ProcessOrderEventForExecutionStatusUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(order.OrderUpdate)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessOrderEventForExecutionStatusUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessOrderEventForExecutionStatusUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessOrderEventForExecutionStatusUpdate(ctx, req.(*order.OrderUpdate))
	}
	return interceptor(ctx, in, info, handler)
}

// Consumer_ServiceDesc is the grpc.ServiceDesc for Consumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Consumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "recurringpayment.enach.consumer.Consumer",
	HandlerType: (*ConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessOrderEventForExecutionStatusUpdate",
			Handler:    _Consumer_ProcessOrderEventForExecutionStatusUpdate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/recurringpayment/enach/consumer/consumer.proto",
}
