// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/timeline/timeline_events.proto

package timeline

import (
	queue "github.com/epifi/be-common/api/queue"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// `TimeLineCreateOrUpdateEvent` published via sns, when
// a new timeline is created or old timeline gets updated
type TimeLineCreateOrUpdateEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the consumer grpc services.
	// contains important information related to message retry state.
	// passed along by queue subscriber.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	Timeline      *Timeline                    `protobuf:"bytes,2,opt,name=timeline,proto3" json:"timeline,omitempty"`
}

func (x *TimeLineCreateOrUpdateEvent) Reset() {
	*x = TimeLineCreateOrUpdateEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_timeline_timeline_events_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeLineCreateOrUpdateEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeLineCreateOrUpdateEvent) ProtoMessage() {}

func (x *TimeLineCreateOrUpdateEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_timeline_timeline_events_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeLineCreateOrUpdateEvent.ProtoReflect.Descriptor instead.
func (*TimeLineCreateOrUpdateEvent) Descriptor() ([]byte, []int) {
	return file_api_timeline_timeline_events_proto_rawDescGZIP(), []int{0}
}

func (x *TimeLineCreateOrUpdateEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *TimeLineCreateOrUpdateEvent) GetTimeline() *Timeline {
	if x != nil {
		return x.Timeline
	}
	return nil
}

var File_api_timeline_timeline_events_proto protoreflect.FileDescriptor

var file_api_timeline_timeline_events_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x20,
	0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x92, 0x01,
	0x0a, 0x1b, 0x54, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x2e, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x42, 0x4a, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5a, 0x23, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_timeline_timeline_events_proto_rawDescOnce sync.Once
	file_api_timeline_timeline_events_proto_rawDescData = file_api_timeline_timeline_events_proto_rawDesc
)

func file_api_timeline_timeline_events_proto_rawDescGZIP() []byte {
	file_api_timeline_timeline_events_proto_rawDescOnce.Do(func() {
		file_api_timeline_timeline_events_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_timeline_timeline_events_proto_rawDescData)
	})
	return file_api_timeline_timeline_events_proto_rawDescData
}

var file_api_timeline_timeline_events_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_timeline_timeline_events_proto_goTypes = []interface{}{
	(*TimeLineCreateOrUpdateEvent)(nil), // 0: timeline.TimeLineCreateOrUpdateEvent
	(*queue.ConsumerRequestHeader)(nil), // 1: queue.ConsumerRequestHeader
	(*Timeline)(nil),                    // 2: timeline.Timeline
}
var file_api_timeline_timeline_events_proto_depIdxs = []int32{
	1, // 0: timeline.TimeLineCreateOrUpdateEvent.request_header:type_name -> queue.ConsumerRequestHeader
	2, // 1: timeline.TimeLineCreateOrUpdateEvent.timeline:type_name -> timeline.Timeline
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_timeline_timeline_events_proto_init() }
func file_api_timeline_timeline_events_proto_init() {
	if File_api_timeline_timeline_events_proto != nil {
		return
	}
	file_api_timeline_timeline_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_timeline_timeline_events_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeLineCreateOrUpdateEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_timeline_timeline_events_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_timeline_timeline_events_proto_goTypes,
		DependencyIndexes: file_api_timeline_timeline_events_proto_depIdxs,
		MessageInfos:      file_api_timeline_timeline_events_proto_msgTypes,
	}.Build()
	File_api_timeline_timeline_events_proto = out.File
	file_api_timeline_timeline_events_proto_rawDesc = nil
	file_api_timeline_timeline_events_proto_goTypes = nil
	file_api_timeline_timeline_events_proto_depIdxs = nil
}
