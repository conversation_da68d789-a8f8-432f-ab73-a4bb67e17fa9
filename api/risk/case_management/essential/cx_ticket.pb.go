// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/essential/cx_ticket.proto

package essential

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	ticket "github.com/epifi/gamma/api/cx/ticket"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateCXTicketParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ticket           *ticket.Ticket                 `protobuf:"bytes,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
	UpdateFieldMasks []ticket.TicketFieldUpdateMask `protobuf:"varint,2,rep,packed,name=update_field_masks,json=updateFieldMasks,proto3,enum=cx.ticket.TicketFieldUpdateMask" json:"update_field_masks,omitempty"`
	// Flag to apply same updates to all secondary tickets as well.
	UpdateSecondaryTickets bool `protobuf:"varint,3,opt,name=update_secondary_tickets,json=updateSecondaryTickets,proto3" json:"update_secondary_tickets,omitempty"`
}

func (x *UpdateCXTicketParams) Reset() {
	*x = UpdateCXTicketParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_essential_cx_ticket_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCXTicketParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCXTicketParams) ProtoMessage() {}

func (x *UpdateCXTicketParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_essential_cx_ticket_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCXTicketParams.ProtoReflect.Descriptor instead.
func (*UpdateCXTicketParams) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_essential_cx_ticket_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateCXTicketParams) GetTicket() *ticket.Ticket {
	if x != nil {
		return x.Ticket
	}
	return nil
}

func (x *UpdateCXTicketParams) GetUpdateFieldMasks() []ticket.TicketFieldUpdateMask {
	if x != nil {
		return x.UpdateFieldMasks
	}
	return nil
}

func (x *UpdateCXTicketParams) GetUpdateSecondaryTickets() bool {
	if x != nil {
		return x.UpdateSecondaryTickets
	}
	return false
}

var File_api_risk_case_management_essential_cx_ticket_proto protoreflect.FileDescriptor

var file_api_risk_case_management_essential_cx_ticket_proto_rawDesc = []byte{
	0x0a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x73, 0x73, 0x65, 0x6e,
	0x74, 0x69, 0x61, 0x6c, 0x2f, 0x63, 0x78, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x65, 0x73, 0x73, 0x65, 0x6e,
	0x74, 0x69, 0x61, 0x6c, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2f, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdf, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x58, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x33, 0x0a,
	0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x12, 0x58, 0x0a, 0x12, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x10, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x38, 0x0a, 0x18,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79,
	0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x42, 0x76, 0x0a, 0x39, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x65, 0x73, 0x73, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x5a, 0x39, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x73, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_essential_cx_ticket_proto_rawDescOnce sync.Once
	file_api_risk_case_management_essential_cx_ticket_proto_rawDescData = file_api_risk_case_management_essential_cx_ticket_proto_rawDesc
)

func file_api_risk_case_management_essential_cx_ticket_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_essential_cx_ticket_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_essential_cx_ticket_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_essential_cx_ticket_proto_rawDescData)
	})
	return file_api_risk_case_management_essential_cx_ticket_proto_rawDescData
}

var file_api_risk_case_management_essential_cx_ticket_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_risk_case_management_essential_cx_ticket_proto_goTypes = []interface{}{
	(*UpdateCXTicketParams)(nil),      // 0: risk.case_management.essential.UpdateCXTicketParams
	(*ticket.Ticket)(nil),             // 1: cx.ticket.Ticket
	(ticket.TicketFieldUpdateMask)(0), // 2: cx.ticket.TicketFieldUpdateMask
}
var file_api_risk_case_management_essential_cx_ticket_proto_depIdxs = []int32{
	1, // 0: risk.case_management.essential.UpdateCXTicketParams.ticket:type_name -> cx.ticket.Ticket
	2, // 1: risk.case_management.essential.UpdateCXTicketParams.update_field_masks:type_name -> cx.ticket.TicketFieldUpdateMask
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_essential_cx_ticket_proto_init() }
func file_api_risk_case_management_essential_cx_ticket_proto_init() {
	if File_api_risk_case_management_essential_cx_ticket_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_case_management_essential_cx_ticket_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCXTicketParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_essential_cx_ticket_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_case_management_essential_cx_ticket_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_essential_cx_ticket_proto_depIdxs,
		MessageInfos:      file_api_risk_case_management_essential_cx_ticket_proto_msgTypes,
	}.Build()
	File_api_risk_case_management_essential_cx_ticket_proto = out.File
	file_api_risk_case_management_essential_cx_ticket_proto_rawDesc = nil
	file_api_risk_case_management_essential_cx_ticket_proto_goTypes = nil
	file_api_risk_case_management_essential_cx_ticket_proto_depIdxs = nil
}
