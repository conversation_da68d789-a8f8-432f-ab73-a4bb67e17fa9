//go:generate gen_queue_pb
syntax = "proto3";

package risk.transaction_monitoring;

import "api/order/order.proto";
import "api/queue/consumer_headers.proto";
import "api/typesv2/actor.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/risk/transaction_monitoring";
option java_package = "com.github.epifi.gamma.api.risk.transaction_monitoring";

service TxnMonitoringConsumer {
  // ProcessPayment orchestrates the transaction state machine in accordance with payment state at the bank ends.
  // This rpc is invoked by queue subscriber to consume order related queue packet.
  rpc ProcessOrderTxnMonitoring (order.OrderUpdate) returns (ProcessOrderTxnMonitoringResponse) {}

  // SyncLeaActors syncs lea complaints against actors with risk evaluator entity
  rpc SyncLeaActors(SyncLeaActorsRequest) returns (SyncLeaActorsResponse);
}

message ProcessOrderTxnMonitoringResponse {
  // common response header across all the consumer grpc services.
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

// TransactionParty encapsulates the details of one of the parties involved in a transaction e.g., remitter or beneficiary
message TransactionParty {

  string payment_instrument = 1 [(validate.rules).string.min_len = 1];

  string actor_id = 2;

  api.typesv2.Actor.Type actor_type = 3;

  string saving_bank_account_id = 4;
}

message SyncLeaActorsRequest {
  queue.ConsumerRequestHeader request_header = 1;
  // actor ids for which details need to be synced with risk evaluator entity
  repeated string actor_ids = 2 [(validate.rules).repeated = {min_items: 1, max_items: 100}];
}

message SyncLeaActorsResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
