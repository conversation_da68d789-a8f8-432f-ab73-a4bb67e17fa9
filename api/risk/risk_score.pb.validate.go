// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/risk_score.proto

package risk

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on RiskScoreDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RiskScoreDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskScoreDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskScoreDetailMultiError, or nil if none found.
func (m *RiskScoreDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskScoreDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RuleName

	// no validation rules for Remark

	// no validation rules for Score

	if len(errors) > 0 {
		return RiskScoreDetailMultiError(errors)
	}

	return nil
}

// RiskScoreDetailMultiError is an error wrapping multiple validation errors
// returned by RiskScoreDetail.ValidateAll() if the designated constraints
// aren't met.
type RiskScoreDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskScoreDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskScoreDetailMultiError) AllErrors() []error { return m }

// RiskScoreDetailValidationError is the validation error returned by
// RiskScoreDetail.Validate if the designated constraints aren't met.
type RiskScoreDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskScoreDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskScoreDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskScoreDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskScoreDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskScoreDetailValidationError) ErrorName() string { return "RiskScoreDetailValidationError" }

// Error satisfies the builtin error interface
func (e RiskScoreDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskScoreDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskScoreDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskScoreDetailValidationError{}

// Validate checks the field values on CreateRiskScoreRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRiskScoreRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRiskScoreRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRiskScoreRequestMultiError, or nil if none found.
func (m *CreateRiskScoreRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRiskScoreRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vendor

	if l := utf8.RuneCountInString(m.GetTxnId()); l < 4 || l > 100 {
		err := CreateRiskScoreRequestValidationError{
			field:  "TxnId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetVendorResponseTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRiskScoreRequestValidationError{
					field:  "VendorResponseTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRiskScoreRequestValidationError{
					field:  "VendorResponseTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorResponseTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRiskScoreRequestValidationError{
				field:  "VendorResponseTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for Reason

	// no validation rules for Score

	for idx, item := range m.GetDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateRiskScoreRequestValidationError{
						field:  fmt.Sprintf("Details[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateRiskScoreRequestValidationError{
						field:  fmt.Sprintf("Details[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateRiskScoreRequestValidationError{
					field:  fmt.Sprintf("Details[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateRiskScoreRequestMultiError(errors)
	}

	return nil
}

// CreateRiskScoreRequestMultiError is an error wrapping multiple validation
// errors returned by CreateRiskScoreRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateRiskScoreRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRiskScoreRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRiskScoreRequestMultiError) AllErrors() []error { return m }

// CreateRiskScoreRequestValidationError is the validation error returned by
// CreateRiskScoreRequest.Validate if the designated constraints aren't met.
type CreateRiskScoreRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRiskScoreRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRiskScoreRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRiskScoreRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRiskScoreRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRiskScoreRequestValidationError) ErrorName() string {
	return "CreateRiskScoreRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRiskScoreRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRiskScoreRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRiskScoreRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRiskScoreRequestValidationError{}

// Validate checks the field values on CreateRiskScoreResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRiskScoreResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRiskScoreResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRiskScoreResponseMultiError, or nil if none found.
func (m *CreateRiskScoreResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRiskScoreResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRiskScoreResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRiskScoreResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRiskScoreResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRiskScoreResponseMultiError(errors)
	}

	return nil
}

// CreateRiskScoreResponseMultiError is an error wrapping multiple validation
// errors returned by CreateRiskScoreResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateRiskScoreResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRiskScoreResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRiskScoreResponseMultiError) AllErrors() []error { return m }

// CreateRiskScoreResponseValidationError is the validation error returned by
// CreateRiskScoreResponse.Validate if the designated constraints aren't met.
type CreateRiskScoreResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRiskScoreResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRiskScoreResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRiskScoreResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRiskScoreResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRiskScoreResponseValidationError) ErrorName() string {
	return "CreateRiskScoreResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRiskScoreResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRiskScoreResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRiskScoreResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRiskScoreResponseValidationError{}
