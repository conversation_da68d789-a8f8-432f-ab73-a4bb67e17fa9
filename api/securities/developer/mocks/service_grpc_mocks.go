// Code generated by MockGen. DO NOT EDIT.
// Source: api/securities/developer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockSecuritiesDevClient is a mock of SecuritiesDevClient interface.
type MockSecuritiesDevClient struct {
	ctrl     *gomock.Controller
	recorder *MockSecuritiesDevClientMockRecorder
}

// MockSecuritiesDevClientMockRecorder is the mock recorder for MockSecuritiesDevClient.
type MockSecuritiesDevClientMockRecorder struct {
	mock *MockSecuritiesDevClient
}

// NewMockSecuritiesDevClient creates a new mock instance.
func NewMockSecuritiesDevClient(ctrl *gomock.Controller) *MockSecuritiesDevClient {
	mock := &MockSecuritiesDevClient{ctrl: ctrl}
	mock.recorder = &MockSecuritiesDevClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSecuritiesDevClient) EXPECT() *MockSecuritiesDevClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockSecuritiesDevClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockSecuritiesDevClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockSecuritiesDevClient)(nil).GetData), varargs...)
}

// GetEntityList mocks base method.
func (m *MockSecuritiesDevClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityList", varargs...)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockSecuritiesDevClientMockRecorder) GetEntityList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockSecuritiesDevClient)(nil).GetEntityList), varargs...)
}

// GetParameterList mocks base method.
func (m *MockSecuritiesDevClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterList", varargs...)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockSecuritiesDevClientMockRecorder) GetParameterList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockSecuritiesDevClient)(nil).GetParameterList), varargs...)
}

// MockSecuritiesDevServer is a mock of SecuritiesDevServer interface.
type MockSecuritiesDevServer struct {
	ctrl     *gomock.Controller
	recorder *MockSecuritiesDevServerMockRecorder
}

// MockSecuritiesDevServerMockRecorder is the mock recorder for MockSecuritiesDevServer.
type MockSecuritiesDevServerMockRecorder struct {
	mock *MockSecuritiesDevServer
}

// NewMockSecuritiesDevServer creates a new mock instance.
func NewMockSecuritiesDevServer(ctrl *gomock.Controller) *MockSecuritiesDevServer {
	mock := &MockSecuritiesDevServer{ctrl: ctrl}
	mock.recorder = &MockSecuritiesDevServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSecuritiesDevServer) EXPECT() *MockSecuritiesDevServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockSecuritiesDevServer) GetData(arg0 context.Context, arg1 *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockSecuritiesDevServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockSecuritiesDevServer)(nil).GetData), arg0, arg1)
}

// GetEntityList mocks base method.
func (m *MockSecuritiesDevServer) GetEntityList(arg0 context.Context, arg1 *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockSecuritiesDevServerMockRecorder) GetEntityList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockSecuritiesDevServer)(nil).GetEntityList), arg0, arg1)
}

// GetParameterList mocks base method.
func (m *MockSecuritiesDevServer) GetParameterList(arg0 context.Context, arg1 *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockSecuritiesDevServerMockRecorder) GetParameterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockSecuritiesDevServer)(nil).GetParameterList), arg0, arg1)
}

// MockUnsafeSecuritiesDevServer is a mock of UnsafeSecuritiesDevServer interface.
type MockUnsafeSecuritiesDevServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeSecuritiesDevServerMockRecorder
}

// MockUnsafeSecuritiesDevServerMockRecorder is the mock recorder for MockUnsafeSecuritiesDevServer.
type MockUnsafeSecuritiesDevServerMockRecorder struct {
	mock *MockUnsafeSecuritiesDevServer
}

// NewMockUnsafeSecuritiesDevServer creates a new mock instance.
func NewMockUnsafeSecuritiesDevServer(ctrl *gomock.Controller) *MockUnsafeSecuritiesDevServer {
	mock := &MockUnsafeSecuritiesDevServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeSecuritiesDevServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeSecuritiesDevServer) EXPECT() *MockUnsafeSecuritiesDevServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedSecuritiesDevServer mocks base method.
func (m *MockUnsafeSecuritiesDevServer) mustEmbedUnimplementedSecuritiesDevServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedSecuritiesDevServer")
}

// mustEmbedUnimplementedSecuritiesDevServer indicates an expected call of mustEmbedUnimplementedSecuritiesDevServer.
func (mr *MockUnsafeSecuritiesDevServerMockRecorder) mustEmbedUnimplementedSecuritiesDevServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedSecuritiesDevServer", reflect.TypeOf((*MockUnsafeSecuritiesDevServer)(nil).mustEmbedUnimplementedSecuritiesDevServer))
}
