// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/securities/developer/service.proto

package developer

import (
	context "context"
	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SecuritiesDev_GetEntityList_FullMethodName    = "/securities.developer.SecuritiesDev/GetEntityList"
	SecuritiesDev_GetParameterList_FullMethodName = "/securities.developer.SecuritiesDev/GetParameterList"
	SecuritiesDev_GetData_FullMethodName          = "/securities.developer.SecuritiesDev/GetData"
)

// SecuritiesDevClient is the client API for SecuritiesDev service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SecuritiesDevClient interface {
	// GetEntityList returns the list of available Securities entities that can be queried.
	// Used to populate entity options in the UI or client applications.
	GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error)
	// GetParameterList returns the list of parameters required for a specific Securities entity.
	// Used to determine input fields needed to fetch data for the selected entity.
	GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error)
	// GetData fetches data for a specific Securities entity using provided parameters.
	// Returns the entity data as a JSON string for debugging or analysis.
	GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error)
}

type securitiesDevClient struct {
	cc grpc.ClientConnInterface
}

func NewSecuritiesDevClient(cc grpc.ClientConnInterface) SecuritiesDevClient {
	return &securitiesDevClient{cc}
}

func (c *securitiesDevClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	out := new(db_state.GetEntityListResponse)
	err := c.cc.Invoke(ctx, SecuritiesDev_GetEntityList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securitiesDevClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	out := new(db_state.GetParameterListResponse)
	err := c.cc.Invoke(ctx, SecuritiesDev_GetParameterList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *securitiesDevClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	out := new(db_state.GetDataResponse)
	err := c.cc.Invoke(ctx, SecuritiesDev_GetData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SecuritiesDevServer is the server API for SecuritiesDev service.
// All implementations should embed UnimplementedSecuritiesDevServer
// for forward compatibility
type SecuritiesDevServer interface {
	// GetEntityList returns the list of available Securities entities that can be queried.
	// Used to populate entity options in the UI or client applications.
	GetEntityList(context.Context, *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error)
	// GetParameterList returns the list of parameters required for a specific Securities entity.
	// Used to determine input fields needed to fetch data for the selected entity.
	GetParameterList(context.Context, *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error)
	// GetData fetches data for a specific Securities entity using provided parameters.
	// Returns the entity data as a JSON string for debugging or analysis.
	GetData(context.Context, *db_state.GetDataRequest) (*db_state.GetDataResponse, error)
}

// UnimplementedSecuritiesDevServer should be embedded to have forward compatible implementations.
type UnimplementedSecuritiesDevServer struct {
}

func (UnimplementedSecuritiesDevServer) GetEntityList(context.Context, *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntityList not implemented")
}
func (UnimplementedSecuritiesDevServer) GetParameterList(context.Context, *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetParameterList not implemented")
}
func (UnimplementedSecuritiesDevServer) GetData(context.Context, *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetData not implemented")
}

// UnsafeSecuritiesDevServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SecuritiesDevServer will
// result in compilation errors.
type UnsafeSecuritiesDevServer interface {
	mustEmbedUnimplementedSecuritiesDevServer()
}

func RegisterSecuritiesDevServer(s grpc.ServiceRegistrar, srv SecuritiesDevServer) {
	s.RegisterService(&SecuritiesDev_ServiceDesc, srv)
}

func _SecuritiesDev_GetEntityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetEntityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecuritiesDevServer).GetEntityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecuritiesDev_GetEntityList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecuritiesDevServer).GetEntityList(ctx, req.(*db_state.GetEntityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SecuritiesDev_GetParameterList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetParameterListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecuritiesDevServer).GetParameterList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecuritiesDev_GetParameterList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecuritiesDevServer).GetParameterList(ctx, req.(*db_state.GetParameterListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SecuritiesDev_GetData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecuritiesDevServer).GetData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecuritiesDev_GetData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecuritiesDevServer).GetData(ctx, req.(*db_state.GetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SecuritiesDev_ServiceDesc is the grpc.ServiceDesc for SecuritiesDev service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SecuritiesDev_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "securities.developer.SecuritiesDev",
	HandlerType: (*SecuritiesDevServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEntityList",
			Handler:    _SecuritiesDev_GetEntityList_Handler,
		},
		{
			MethodName: "GetParameterList",
			Handler:    _SecuritiesDev_GetParameterList_Handler,
		},
		{
			MethodName: "GetData",
			Handler:    _SecuritiesDev_GetData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/securities/developer/service.proto",
}
