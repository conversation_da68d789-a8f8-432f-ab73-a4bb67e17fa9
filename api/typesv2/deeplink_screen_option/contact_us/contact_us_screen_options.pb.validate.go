// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/contact_us/contact_us_screen_options.proto

package contact_us

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ContactUsLandingScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ContactUsLandingScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContactUsLandingScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ContactUsLandingScreenOptionsMultiError, or nil if none found.
func (m *ContactUsLandingScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *ContactUsLandingScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsLandingScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsBottomSheetPresent

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsLandingScreenOptionsValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsLandingScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsLandingScreenOptionsValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsLandingScreenOptionsValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScreenTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "ScreenTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "ScreenTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsLandingScreenOptionsValidationError{
				field:  "ScreenTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBannerImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "BannerImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "BannerImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBannerImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsLandingScreenOptionsValidationError{
				field:  "BannerImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScreenDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "ScreenDescription",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsLandingScreenOptionsValidationError{
					field:  "ScreenDescription",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsLandingScreenOptionsValidationError{
				field:  "ScreenDescription",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ContactUsLandingScreenOptionsMultiError(errors)
	}

	return nil
}

// ContactUsLandingScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by ContactUsLandingScreenOptions.ValidateAll()
// if the designated constraints aren't met.
type ContactUsLandingScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContactUsLandingScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContactUsLandingScreenOptionsMultiError) AllErrors() []error { return m }

// ContactUsLandingScreenOptionsValidationError is the validation error
// returned by ContactUsLandingScreenOptions.Validate if the designated
// constraints aren't met.
type ContactUsLandingScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContactUsLandingScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContactUsLandingScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContactUsLandingScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContactUsLandingScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContactUsLandingScreenOptionsValidationError) ErrorName() string {
	return "ContactUsLandingScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e ContactUsLandingScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContactUsLandingScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContactUsLandingScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContactUsLandingScreenOptionsValidationError{}

// Validate checks the field values on ContactUsTerminalScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ContactUsTerminalScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContactUsTerminalScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ContactUsTerminalScreenOptionsMultiError, or nil if none found.
func (m *ContactUsTerminalScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *ContactUsTerminalScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsTerminalScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsTerminalScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsTerminalScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IssueId

	if all {
		switch v := interface{}(m.GetRequestParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsTerminalScreenOptionsValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsTerminalScreenOptionsValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsTerminalScreenOptionsValidationError{
				field:  "RequestParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for QueryId

	if len(errors) > 0 {
		return ContactUsTerminalScreenOptionsMultiError(errors)
	}

	return nil
}

// ContactUsTerminalScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by ContactUsTerminalScreenOptions.ValidateAll()
// if the designated constraints aren't met.
type ContactUsTerminalScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContactUsTerminalScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContactUsTerminalScreenOptionsMultiError) AllErrors() []error { return m }

// ContactUsTerminalScreenOptionsValidationError is the validation error
// returned by ContactUsTerminalScreenOptions.Validate if the designated
// constraints aren't met.
type ContactUsTerminalScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContactUsTerminalScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContactUsTerminalScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContactUsTerminalScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContactUsTerminalScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContactUsTerminalScreenOptionsValidationError) ErrorName() string {
	return "ContactUsTerminalScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e ContactUsTerminalScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContactUsTerminalScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContactUsTerminalScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContactUsTerminalScreenOptionsValidationError{}

// Validate checks the field values on ContactUsCategorySelectionScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ContactUsCategorySelectionScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ContactUsCategorySelectionScreenOptions with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ContactUsCategorySelectionScreenOptionsMultiError, or nil if none found.
func (m *ContactUsCategorySelectionScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *ContactUsCategorySelectionScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsCategorySelectionScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsCategorySelectionScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsCategorySelectionScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIssueIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsCategorySelectionScreenOptionsValidationError{
					field:  "IssueIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsCategorySelectionScreenOptionsValidationError{
					field:  "IssueIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIssueIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsCategorySelectionScreenOptionsValidationError{
				field:  "IssueIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsIssueNotListed

	// no validation rules for QueryId

	if len(errors) > 0 {
		return ContactUsCategorySelectionScreenOptionsMultiError(errors)
	}

	return nil
}

// ContactUsCategorySelectionScreenOptionsMultiError is an error wrapping
// multiple validation errors returned by
// ContactUsCategorySelectionScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type ContactUsCategorySelectionScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContactUsCategorySelectionScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContactUsCategorySelectionScreenOptionsMultiError) AllErrors() []error { return m }

// ContactUsCategorySelectionScreenOptionsValidationError is the validation
// error returned by ContactUsCategorySelectionScreenOptions.Validate if the
// designated constraints aren't met.
type ContactUsCategorySelectionScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContactUsCategorySelectionScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContactUsCategorySelectionScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContactUsCategorySelectionScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContactUsCategorySelectionScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContactUsCategorySelectionScreenOptionsValidationError) ErrorName() string {
	return "ContactUsCategorySelectionScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e ContactUsCategorySelectionScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContactUsCategorySelectionScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContactUsCategorySelectionScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContactUsCategorySelectionScreenOptionsValidationError{}

// Validate checks the field values on
// ContactUsCategorySelectionViewMoreScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ContactUsCategorySelectionViewMoreScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ContactUsCategorySelectionViewMoreScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ContactUsCategorySelectionViewMoreScreenOptionsMultiError, or nil if none found.
func (m *ContactUsCategorySelectionViewMoreScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *ContactUsCategorySelectionViewMoreScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsCategorySelectionViewMoreScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsCategorySelectionViewMoreScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsCategorySelectionViewMoreScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIssueIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ContactUsCategorySelectionViewMoreScreenOptionsValidationError{
					field:  "IssueIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ContactUsCategorySelectionViewMoreScreenOptionsValidationError{
					field:  "IssueIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIssueIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ContactUsCategorySelectionViewMoreScreenOptionsValidationError{
				field:  "IssueIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for QueryId

	if len(errors) > 0 {
		return ContactUsCategorySelectionViewMoreScreenOptionsMultiError(errors)
	}

	return nil
}

// ContactUsCategorySelectionViewMoreScreenOptionsMultiError is an error
// wrapping multiple validation errors returned by
// ContactUsCategorySelectionViewMoreScreenOptions.ValidateAll() if the
// designated constraints aren't met.
type ContactUsCategorySelectionViewMoreScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContactUsCategorySelectionViewMoreScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContactUsCategorySelectionViewMoreScreenOptionsMultiError) AllErrors() []error { return m }

// ContactUsCategorySelectionViewMoreScreenOptionsValidationError is the
// validation error returned by
// ContactUsCategorySelectionViewMoreScreenOptions.Validate if the designated
// constraints aren't met.
type ContactUsCategorySelectionViewMoreScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContactUsCategorySelectionViewMoreScreenOptionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ContactUsCategorySelectionViewMoreScreenOptionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ContactUsCategorySelectionViewMoreScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContactUsCategorySelectionViewMoreScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContactUsCategorySelectionViewMoreScreenOptionsValidationError) ErrorName() string {
	return "ContactUsCategorySelectionViewMoreScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e ContactUsCategorySelectionViewMoreScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContactUsCategorySelectionViewMoreScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContactUsCategorySelectionViewMoreScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContactUsCategorySelectionViewMoreScreenOptionsValidationError{}
