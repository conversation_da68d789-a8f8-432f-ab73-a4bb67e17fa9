// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/billpay/screen_options.proto

package billpay

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RechargePollingScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RechargePollingScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RechargePollingScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RechargePollingScreenOptionsMultiError, or nil if none found.
func (m *RechargePollingScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *RechargePollingScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientRequestId

	// no validation rules for RetryIntervalMs

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargePollingScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargePollingScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargePollingScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RechargePollingScreenOptionsMultiError(errors)
	}

	return nil
}

// RechargePollingScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by RechargePollingScreenOptions.ValidateAll() if
// the designated constraints aren't met.
type RechargePollingScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RechargePollingScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RechargePollingScreenOptionsMultiError) AllErrors() []error { return m }

// RechargePollingScreenOptionsValidationError is the validation error returned
// by RechargePollingScreenOptions.Validate if the designated constraints
// aren't met.
type RechargePollingScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RechargePollingScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RechargePollingScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RechargePollingScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RechargePollingScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RechargePollingScreenOptionsValidationError) ErrorName() string {
	return "RechargePollingScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e RechargePollingScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRechargePollingScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RechargePollingScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RechargePollingScreenOptionsValidationError{}

// Validate checks the field values on RechargeIntroScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RechargeIntroScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RechargeIntroScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RechargeIntroScreenOptionsMultiError, or nil if none found.
func (m *RechargeIntroScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *RechargeIntroScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargeIntroScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargeIntroScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargeIntroScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountType

	if len(errors) > 0 {
		return RechargeIntroScreenOptionsMultiError(errors)
	}

	return nil
}

// RechargeIntroScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by RechargeIntroScreenOptions.ValidateAll() if
// the designated constraints aren't met.
type RechargeIntroScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RechargeIntroScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RechargeIntroScreenOptionsMultiError) AllErrors() []error { return m }

// RechargeIntroScreenOptionsValidationError is the validation error returned
// by RechargeIntroScreenOptions.Validate if the designated constraints aren't met.
type RechargeIntroScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RechargeIntroScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RechargeIntroScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RechargeIntroScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RechargeIntroScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RechargeIntroScreenOptionsValidationError) ErrorName() string {
	return "RechargeIntroScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e RechargeIntroScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRechargeIntroScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RechargeIntroScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RechargeIntroScreenOptionsValidationError{}

// Validate checks the field values on RechargePlansScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RechargePlansScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RechargePlansScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RechargePlansScreenOptionsMultiError, or nil if none found.
func (m *RechargePlansScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *RechargePlansScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargePlansScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargePlansScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargePlansScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountType

	// no validation rules for AccountIdentifier

	// no validation rules for OperatorId

	if len(errors) > 0 {
		return RechargePlansScreenOptionsMultiError(errors)
	}

	return nil
}

// RechargePlansScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by RechargePlansScreenOptions.ValidateAll() if
// the designated constraints aren't met.
type RechargePlansScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RechargePlansScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RechargePlansScreenOptionsMultiError) AllErrors() []error { return m }

// RechargePlansScreenOptionsValidationError is the validation error returned
// by RechargePlansScreenOptions.Validate if the designated constraints aren't met.
type RechargePlansScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RechargePlansScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RechargePlansScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RechargePlansScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RechargePlansScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RechargePlansScreenOptionsValidationError) ErrorName() string {
	return "RechargePlansScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e RechargePlansScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRechargePlansScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RechargePlansScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RechargePlansScreenOptionsValidationError{}

// Validate checks the field values on BillDetailsConfirmationScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BillDetailsConfirmationScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BillDetailsConfirmationScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BillDetailsConfirmationScreenOptionsMultiError, or nil if none found.
func (m *BillDetailsConfirmationScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *BillDetailsConfirmationScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BillDetailsConfirmationScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BillDetailsConfirmationScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BillDetailsConfirmationScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BillDetailsConfirmationScreenOptionsMultiError(errors)
	}

	return nil
}

// BillDetailsConfirmationScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// BillDetailsConfirmationScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type BillDetailsConfirmationScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BillDetailsConfirmationScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BillDetailsConfirmationScreenOptionsMultiError) AllErrors() []error { return m }

// BillDetailsConfirmationScreenOptionsValidationError is the validation error
// returned by BillDetailsConfirmationScreenOptions.Validate if the designated
// constraints aren't met.
type BillDetailsConfirmationScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BillDetailsConfirmationScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BillDetailsConfirmationScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BillDetailsConfirmationScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BillDetailsConfirmationScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BillDetailsConfirmationScreenOptionsValidationError) ErrorName() string {
	return "BillDetailsConfirmationScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e BillDetailsConfirmationScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBillDetailsConfirmationScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BillDetailsConfirmationScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BillDetailsConfirmationScreenOptionsValidationError{}
