// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/billpay/screen_options.proto

package billpay

import (
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Screen options for RECHARGE_POLLING_SCREEN deeplink
type RechargePollingScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Client request id to be passed to GetRechargeOrderStatus RPC
	ClientRequestId string `protobuf:"bytes,1,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// Retry interval for polling in milliseconds
	RetryIntervalMs int32                                      `protobuf:"varint,2,opt,name=retry_interval_ms,json=retryIntervalMs,proto3" json:"retry_interval_ms,omitempty"`
	Header          *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,3,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *RechargePollingScreenOptions) Reset() {
	*x = RechargePollingScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RechargePollingScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargePollingScreenOptions) ProtoMessage() {}

func (x *RechargePollingScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargePollingScreenOptions.ProtoReflect.Descriptor instead.
func (*RechargePollingScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *RechargePollingScreenOptions) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *RechargePollingScreenOptions) GetRetryIntervalMs() int32 {
	if x != nil {
		return x.RetryIntervalMs
	}
	return 0
}

func (x *RechargePollingScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

// Screen options for RECHARGE_INTRO_SCREEN deeplink
// https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-96670&t=XhP8BlFamoV8q77J-4
type RechargeIntroScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// expected to be of RechargeAccountType enum
	AccountType string `protobuf:"bytes,2,opt,name=account_type,json=accountType,proto3" json:"account_type,omitempty"`
}

func (x *RechargeIntroScreenOptions) Reset() {
	*x = RechargeIntroScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RechargeIntroScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeIntroScreenOptions) ProtoMessage() {}

func (x *RechargeIntroScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeIntroScreenOptions.ProtoReflect.Descriptor instead.
func (*RechargeIntroScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDescGZIP(), []int{1}
}

func (x *RechargeIntroScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *RechargeIntroScreenOptions) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

// Screen options for RECHARGE_PLANS_SCREEN deeplink
// https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-97597&t=XhP8BlFamoV8q77J-4
type RechargePlansScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// expected to be of RechargeAccountType enum
	AccountType string `protobuf:"bytes,2,opt,name=account_type,json=accountType,proto3" json:"account_type,omitempty"`
	// account identifier of corresponding account_type e.g. mobile number for MOBILE account
	AccountIdentifier string `protobuf:"bytes,3,opt,name=account_identifier,json=accountIdentifier,proto3" json:"account_identifier,omitempty"`
	// operator for the recharge e.g. Jio/Airtel/Vi for mobile recharges
	OperatorId string `protobuf:"bytes,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
}

func (x *RechargePlansScreenOptions) Reset() {
	*x = RechargePlansScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RechargePlansScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargePlansScreenOptions) ProtoMessage() {}

func (x *RechargePlansScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargePlansScreenOptions.ProtoReflect.Descriptor instead.
func (*RechargePlansScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDescGZIP(), []int{2}
}

func (x *RechargePlansScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *RechargePlansScreenOptions) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

func (x *RechargePlansScreenOptions) GetAccountIdentifier() string {
	if x != nil {
		return x.AccountIdentifier
	}
	return ""
}

func (x *RechargePlansScreenOptions) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

// Screen options for BILL_DETAILS_CONFIRMATION_SCREEN deeplink
// https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-99237&t=XhP8BlFamoV8q77J-4
type BillDetailsConfirmationScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *BillDetailsConfirmationScreenOptions) Reset() {
	*x = BillDetailsConfirmationScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BillDetailsConfirmationScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillDetailsConfirmationScreenOptions) ProtoMessage() {}

func (x *BillDetailsConfirmationScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillDetailsConfirmationScreenOptions.ProtoReflect.Descriptor instead.
func (*BillDetailsConfirmationScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDescGZIP(), []int{3}
}

func (x *BillDetailsConfirmationScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

var File_api_typesv2_deeplink_screen_option_billpay_screen_options_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x2a, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x1a, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc6,
	0x01, 0x0a, 0x1c, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6f, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x6d, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x4d, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x8f, 0x01, 0x0a, 0x1a, 0x52, 0x65, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xdf, 0x01, 0x0a, 0x1a, 0x52, 0x65,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x76, 0x0a, 0x24, 0x42,
	0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x42, 0x86, 0x01, 0x0a, 0x41, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDescData = file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_goTypes = []interface{}{
	(*RechargePollingScreenOptions)(nil),              // 0: api.typesv2.deeplink_screen_option.billpay.RechargePollingScreenOptions
	(*RechargeIntroScreenOptions)(nil),                // 1: api.typesv2.deeplink_screen_option.billpay.RechargeIntroScreenOptions
	(*RechargePlansScreenOptions)(nil),                // 2: api.typesv2.deeplink_screen_option.billpay.RechargePlansScreenOptions
	(*BillDetailsConfirmationScreenOptions)(nil),      // 3: api.typesv2.deeplink_screen_option.billpay.BillDetailsConfirmationScreenOptions
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 4: api.typesv2.deeplink_screen_option.ScreenOptionHeader
}
var file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_depIdxs = []int32{
	4, // 0: api.typesv2.deeplink_screen_option.billpay.RechargePollingScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	4, // 1: api.typesv2.deeplink_screen_option.billpay.RechargeIntroScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	4, // 2: api.typesv2.deeplink_screen_option.billpay.RechargePlansScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	4, // 3: api.typesv2.deeplink_screen_option.billpay.BillDetailsConfirmationScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_init() }
func file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_init() {
	if File_api_typesv2_deeplink_screen_option_billpay_screen_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RechargePollingScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RechargeIntroScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RechargePlansScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BillDetailsConfirmationScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_depIdxs,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_billpay_screen_options_proto = out.File
	file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_billpay_screen_options_proto_depIdxs = nil
}
