// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/pkg/generic_sdui_bottom_sheet_screen_options.proto

package pkg

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SduiBottomSheetOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SduiBottomSheetOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SduiBottomSheetOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SduiBottomSheetOptionsMultiError, or nil if none found.
func (m *SduiBottomSheetOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *SduiBottomSheetOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SduiBottomSheetOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SduiBottomSheetOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SduiBottomSheetOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SduiBottomSheetOptionsValidationError{
					field:  "Section",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SduiBottomSheetOptionsValidationError{
					field:  "Section",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SduiBottomSheetOptionsValidationError{
				field:  "Section",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsFullScreen

	if all {
		switch v := interface{}(m.GetBackground()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SduiBottomSheetOptionsValidationError{
					field:  "Background",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SduiBottomSheetOptionsValidationError{
					field:  "Background",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackground()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SduiBottomSheetOptionsValidationError{
				field:  "Background",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PopParentScreenOnClose

	if len(errors) > 0 {
		return SduiBottomSheetOptionsMultiError(errors)
	}

	return nil
}

// SduiBottomSheetOptionsMultiError is an error wrapping multiple validation
// errors returned by SduiBottomSheetOptions.ValidateAll() if the designated
// constraints aren't met.
type SduiBottomSheetOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SduiBottomSheetOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SduiBottomSheetOptionsMultiError) AllErrors() []error { return m }

// SduiBottomSheetOptionsValidationError is the validation error returned by
// SduiBottomSheetOptions.Validate if the designated constraints aren't met.
type SduiBottomSheetOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SduiBottomSheetOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SduiBottomSheetOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SduiBottomSheetOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SduiBottomSheetOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SduiBottomSheetOptionsValidationError) ErrorName() string {
	return "SduiBottomSheetOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e SduiBottomSheetOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSduiBottomSheetOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SduiBottomSheetOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SduiBottomSheetOptionsValidationError{}
