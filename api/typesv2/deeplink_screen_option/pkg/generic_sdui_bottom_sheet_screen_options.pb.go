// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/pkg/generic_sdui_bottom_sheet_screen_options.proto

package pkg

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	sections "github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Screen options for 'SDUI_BOTTOM_SHEET'
type SduiBottomSheetOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// SDUI Content
	Section *sections.Section `protobuf:"bytes,2,opt,name=section,proto3" json:"section,omitempty"`
	// If true, the bottom sheet should be rendered as a full-screen modal.
	IsFullScreen bool `protobuf:"varint,3,opt,name=is_full_screen,json=isFullScreen,proto3" json:"is_full_screen,omitempty"`
	// Optional background visual element that spans the entire bottom sheet, ignoring internal padding.
	// Can be used to add background styling, gradients, images, or other visual treatments to the entire bottom sheet surface.
	Background *common.VisualElement `protobuf:"bytes,4,opt,name=background,proto3" json:"background,omitempty"`
	// Optional: When set to true, the user will be redirected back to the screen
	// that originally navigated to the current screen containing the deeplink
	// which opened this bottom sheet.
	//
	// Use this flag in cases where the bottom sheet is opened via a deeplink
	// from a specific screen, and the desired behavior upon closing is to
	// navigate back beyond the current screen to its parent/previous screen.
	//
	// Default behavior (false): The navigation stack remains unchanged when
	// the bottom sheet is dismissed.
	PopParentScreenOnClose bool `protobuf:"varint,5,opt,name=pop_parent_screen_on_close,json=popParentScreenOnClose,proto3" json:"pop_parent_screen_on_close,omitempty"`
}

func (x *SduiBottomSheetOptions) Reset() {
	*x = SduiBottomSheetOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SduiBottomSheetOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SduiBottomSheetOptions) ProtoMessage() {}

func (x *SduiBottomSheetOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SduiBottomSheetOptions.ProtoReflect.Descriptor instead.
func (*SduiBottomSheetOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *SduiBottomSheetOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SduiBottomSheetOptions) GetSection() *sections.Section {
	if x != nil {
		return x.Section
	}
	return nil
}

func (x *SduiBottomSheetOptions) GetIsFullScreen() bool {
	if x != nil {
		return x.IsFullScreen
	}
	return false
}

func (x *SduiBottomSheetOptions) GetBackground() *common.VisualElement {
	if x != nil {
		return x.Background
	}
	return nil
}

func (x *SduiBottomSheetOptions) GetPopParentScreenOnClose() bool {
	if x != nil {
		return x.PopParentScreenOnClose
	}
	return false
}

var File_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_rawDesc = []byte{
	0x0a, 0x55, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x69, 0x63,
	0x5f, 0x73, 0x64, 0x75, 0x69, 0x5f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x73, 0x68, 0x65,
	0x65, 0x74, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x26, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x6b, 0x67, 0x1a,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x73, 0x64, 0x75, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x73,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xce, 0x02, 0x0a, 0x16, 0x53, 0x64, 0x75, 0x69, 0x42, 0x6f,
	0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x3f, 0x0a, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x46, 0x75, 0x6c,
	0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x41, 0x0a, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x3a, 0x0a, 0x1a, 0x70, 0x6f,
	0x70, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x6e, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16,
	0x70, 0x6f, 0x70, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x6e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x42, 0x80, 0x01, 0x0a, 0x3d, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x6b, 0x67, 0x50, 0x01, 0x5a, 0x3d, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_rawDescData = file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_goTypes = []interface{}{
	(*SduiBottomSheetOptions)(nil),                    // 0: api.typesv2.deeplink_screen_option.pkg.SduiBottomSheetOptions
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 1: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*sections.Section)(nil),                          // 2: api.typesv2.ui.sdui.sections.Section
	(*common.VisualElement)(nil),                      // 3: api.typesv2.common.VisualElement
}
var file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_depIdxs = []int32{
	1, // 0: api.typesv2.deeplink_screen_option.pkg.SduiBottomSheetOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	2, // 1: api.typesv2.deeplink_screen_option.pkg.SduiBottomSheetOptions.section:type_name -> api.typesv2.ui.sdui.sections.Section
	3, // 2: api.typesv2.deeplink_screen_option.pkg.SduiBottomSheetOptions.background:type_name -> api.typesv2.common.VisualElement
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() {
	file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_init()
}
func file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_init() {
	if File_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SduiBottomSheetOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_depIdxs,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto = out.File
	file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_pkg_generic_sdui_bottom_sheet_screen_options_proto_depIdxs = nil
}
