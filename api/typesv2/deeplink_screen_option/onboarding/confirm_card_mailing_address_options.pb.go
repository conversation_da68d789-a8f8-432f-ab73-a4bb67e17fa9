// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/onboarding/confirm_card_mailing_address_options.proto

package onboarding

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConfirmCardMailingAddressOptions_Flow int32

const (
	ConfirmCardMailingAddressOptions_FLOW_UNSPECIFIED ConfirmCardMailingAddressOptions_Flow = 0
	ConfirmCardMailingAddressOptions_FLOW_ONBOARDING  ConfirmCardMailingAddressOptions_Flow = 1
	// Figma link corresponding to debit card flow: https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=0%3A1&t=9qKFpwq2IcmVuFs0-0
	ConfirmCardMailingAddressOptions_FLOW_DEBIT_CARD ConfirmCardMailingAddressOptions_Flow = 2
)

// Enum value maps for ConfirmCardMailingAddressOptions_Flow.
var (
	ConfirmCardMailingAddressOptions_Flow_name = map[int32]string{
		0: "FLOW_UNSPECIFIED",
		1: "FLOW_ONBOARDING",
		2: "FLOW_DEBIT_CARD",
	}
	ConfirmCardMailingAddressOptions_Flow_value = map[string]int32{
		"FLOW_UNSPECIFIED": 0,
		"FLOW_ONBOARDING":  1,
		"FLOW_DEBIT_CARD":  2,
	}
)

func (x ConfirmCardMailingAddressOptions_Flow) Enum() *ConfirmCardMailingAddressOptions_Flow {
	p := new(ConfirmCardMailingAddressOptions_Flow)
	*p = x
	return p
}

func (x ConfirmCardMailingAddressOptions_Flow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConfirmCardMailingAddressOptions_Flow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_enumTypes[0].Descriptor()
}

func (ConfirmCardMailingAddressOptions_Flow) Type() protoreflect.EnumType {
	return &file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_enumTypes[0]
}

func (x ConfirmCardMailingAddressOptions_Flow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConfirmCardMailingAddressOptions_Flow.Descriptor instead.
func (ConfirmCardMailingAddressOptions_Flow) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDescGZIP(), []int{0, 0}
}

// screen options for CONFIRM_CARD_MAILING_ADDRESS
// https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=3007-12881&t=YVQoKyxLjIDwNxCR-0
type ConfirmCardMailingAddressOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KycLevel typesv2.KYCLevel `protobuf:"varint,1,opt,name=kyc_level,json=kycLevel,proto3,enum=api.typesv2.KYCLevel" json:"kyc_level,omitempty"`
	// Deprecated: in favor of screen_title
	//
	// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/onboarding/confirm_card_mailing_address_options.proto.
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Deprecated: in favor of screen_subtitle
	//
	// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/onboarding/confirm_card_mailing_address_options.proto.
	Subtitle              string                                           `protobuf:"bytes,3,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	PlaceHolderForName    string                                           `protobuf:"bytes,4,opt,name=place_holder_for_name,json=placeHolderForName,proto3" json:"place_holder_for_name,omitempty"`
	PlaceHolderForAddress string                                           `protobuf:"bytes,5,opt,name=place_holder_for_address,json=placeHolderForAddress,proto3" json:"place_holder_for_address,omitempty"`
	CheckBoxTexts         []*ConfirmCardMailingAddressOptions_CheckBoxText `protobuf:"bytes,6,rep,name=check_box_texts,json=checkBoxTexts,proto3" json:"check_box_texts,omitempty"`
	Flow                  ConfirmCardMailingAddressOptions_Flow            `protobuf:"varint,7,opt,name=flow,proto3,enum=api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions_Flow" json:"flow,omitempty"`
	// total amount to be paid by the user (inclusive of gst charges if any)
	Amount *typesv2.Money `protobuf:"bytes,8,opt,name=amount,proto3" json:"amount,omitempty"`
	// unique identifier for card
	CardId string `protobuf:"bytes,9,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// Image corresponding to the CONFIRM_CARD_MAILING_ADDRESS_SCREEN
	Image *common.Image `protobuf:"bytes,10,opt,name=image,proto3" json:"image,omitempty"`
	// color associated with checkbox texts
	CheckboxTextColor string `protobuf:"bytes,11,opt,name=checkbox_text_color,json=checkboxTextColor,proto3" json:"checkbox_text_color,omitempty"`
	// placeholder to be used to display the amount to be paid by user
	PlaceHolderForAmount string `protobuf:"bytes,12,opt,name=place_holder_for_amount,json=placeHolderForAmount,proto3" json:"place_holder_for_amount,omitempty"`
	// Corresponding to the amount placeholder, a hint text is associated which can be stored as part of this field.
	HintTextAmount string `protobuf:"bytes,13,opt,name=hint_text_amount,json=hintTextAmount,proto3" json:"hint_text_amount,omitempty"`
	// color associated with place holders
	PlaceHolderColor string `protobuf:"bytes,14,opt,name=place_holder_color,json=placeHolderColor,proto3" json:"place_holder_color,omitempty"`
	// color associated with the placeholder content
	ContentColor string `protobuf:"bytes,15,opt,name=content_color,json=contentColor,proto3" json:"content_color,omitempty"`
	// color associated with hint
	HintColor string `protobuf:"bytes,16,opt,name=hint_color,json=hintColor,proto3" json:"hint_color,omitempty"`
	// color associated with divider line
	DividerColor string `protobuf:"bytes,17,opt,name=divider_color,json=dividerColor,proto3" json:"divider_color,omitempty"`
	// color associated with edit icon
	EditIconColor string `protobuf:"bytes,18,opt,name=edit_icon_color,json=editIconColor,proto3" json:"edit_icon_color,omitempty"`
	// color associated with the card in the background encapsulating placeholders & related content
	CardColor string `protobuf:"bytes,19,opt,name=card_color,json=cardColor,proto3" json:"card_color,omitempty"`
	// background color for CONFIRM_CARD_MAILING_ADDRESS_SCREEN
	BackgroundColor string `protobuf:"bytes,20,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	// this encapsulates the title related details for CONFIRM_CARD_MAILING_ADDRESS_SCREEN
	ScreenTitle *common.Text `protobuf:"bytes,21,opt,name=screen_title,json=screenTitle,proto3" json:"screen_title,omitempty"`
	// this encapsulates the sub title related details for CONFIRM_CARD_MAILING_ADDRESS_SCREEN
	ScreenSubtitle *common.Text `protobuf:"bytes,22,opt,name=screen_subtitle,json=screenSubtitle,proto3" json:"screen_subtitle,omitempty"`
	// the amount to be displayed to user as a break up of the card fee & gst value. This is set from backend.
	DisplayAmount string `protobuf:"bytes,23,opt,name=display_amount,json=displayAmount,proto3" json:"display_amount,omitempty"`
	// cta associated with CONFIRM_CARD_MAILING_ADDRESS_SCREEN
	Cta *deeplink.Cta `protobuf:"bytes,24,opt,name=cta,proto3" json:"cta,omitempty"`
	// name to be shown on card mailing address screen
	// this will be used in case of debit card charges flow to show the name to be printed on the card
	Name string `protobuf:"bytes,25,opt,name=name,proto3" json:"name,omitempty"`
	// the message to be shown for address confirmation
	AddressConfirmationMessage string `protobuf:"bytes,26,opt,name=address_confirmation_message,json=addressConfirmationMessage,proto3" json:"address_confirmation_message,omitempty"`
	// flag decides whether to hide the address field or not. eg, for NR onb address field won't be shown.
	HideAddressField bool `protobuf:"varint,27,opt,name=hide_address_field,json=hideAddressField,proto3" json:"hide_address_field,omitempty"`
	// common header for all screen options
	Header                   *deeplink_screen_option.ScreenOptionHeader                `protobuf:"bytes,28,opt,name=header,proto3" json:"header,omitempty"`
	HeaderBar                *deeplink.HeaderBar                                       `protobuf:"bytes,29,opt,name=header_bar,json=headerBar,proto3" json:"header_bar,omitempty"`
	DebitCardNameDescription *common.Text                                              `protobuf:"bytes,30,opt,name=debit_card_name_description,json=debitCardNameDescription,proto3" json:"debit_card_name_description,omitempty"`
	Gender                   *ConfirmCardMailingAddressOptions_PlaceHolder             `protobuf:"bytes,31,opt,name=gender,proto3" json:"gender,omitempty"`
	PurposeOfSavingsAccount  *ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount `protobuf:"bytes,32,opt,name=purpose_of_savings_account,json=purposeOfSavingsAccount,proto3" json:"purpose_of_savings_account,omitempty"`
	ConfirmBottomSheetHeader *ui.IconTextComponent                                     `protobuf:"bytes,33,opt,name=confirm_bottom_sheet_header,json=confirmBottomSheetHeader,proto3" json:"confirm_bottom_sheet_header,omitempty"`
	// backend will send properties and client will use it
	NamePlaceholder *ConfirmCardMailingAddressOptions_PlaceHolder `protobuf:"bytes,34,opt,name=name_placeholder,json=namePlaceholder,proto3" json:"name_placeholder,omitempty"`
}

func (x *ConfirmCardMailingAddressOptions) Reset() {
	*x = ConfirmCardMailingAddressOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmCardMailingAddressOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmCardMailingAddressOptions) ProtoMessage() {}

func (x *ConfirmCardMailingAddressOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmCardMailingAddressOptions.ProtoReflect.Descriptor instead.
func (*ConfirmCardMailingAddressOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDescGZIP(), []int{0}
}

func (x *ConfirmCardMailingAddressOptions) GetKycLevel() typesv2.KYCLevel {
	if x != nil {
		return x.KycLevel
	}
	return typesv2.KYCLevel(0)
}

// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/onboarding/confirm_card_mailing_address_options.proto.
func (x *ConfirmCardMailingAddressOptions) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/onboarding/confirm_card_mailing_address_options.proto.
func (x *ConfirmCardMailingAddressOptions) GetSubtitle() string {
	if x != nil {
		return x.Subtitle
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetPlaceHolderForName() string {
	if x != nil {
		return x.PlaceHolderForName
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetPlaceHolderForAddress() string {
	if x != nil {
		return x.PlaceHolderForAddress
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetCheckBoxTexts() []*ConfirmCardMailingAddressOptions_CheckBoxText {
	if x != nil {
		return x.CheckBoxTexts
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions) GetFlow() ConfirmCardMailingAddressOptions_Flow {
	if x != nil {
		return x.Flow
	}
	return ConfirmCardMailingAddressOptions_FLOW_UNSPECIFIED
}

func (x *ConfirmCardMailingAddressOptions) GetAmount() *typesv2.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetImage() *common.Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions) GetCheckboxTextColor() string {
	if x != nil {
		return x.CheckboxTextColor
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetPlaceHolderForAmount() string {
	if x != nil {
		return x.PlaceHolderForAmount
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetHintTextAmount() string {
	if x != nil {
		return x.HintTextAmount
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetPlaceHolderColor() string {
	if x != nil {
		return x.PlaceHolderColor
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetContentColor() string {
	if x != nil {
		return x.ContentColor
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetHintColor() string {
	if x != nil {
		return x.HintColor
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetDividerColor() string {
	if x != nil {
		return x.DividerColor
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetEditIconColor() string {
	if x != nil {
		return x.EditIconColor
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetCardColor() string {
	if x != nil {
		return x.CardColor
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetScreenTitle() *common.Text {
	if x != nil {
		return x.ScreenTitle
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions) GetScreenSubtitle() *common.Text {
	if x != nil {
		return x.ScreenSubtitle
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions) GetDisplayAmount() string {
	if x != nil {
		return x.DisplayAmount
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetCta() *deeplink.Cta {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetAddressConfirmationMessage() string {
	if x != nil {
		return x.AddressConfirmationMessage
	}
	return ""
}

func (x *ConfirmCardMailingAddressOptions) GetHideAddressField() bool {
	if x != nil {
		return x.HideAddressField
	}
	return false
}

func (x *ConfirmCardMailingAddressOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions) GetHeaderBar() *deeplink.HeaderBar {
	if x != nil {
		return x.HeaderBar
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions) GetDebitCardNameDescription() *common.Text {
	if x != nil {
		return x.DebitCardNameDescription
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions) GetGender() *ConfirmCardMailingAddressOptions_PlaceHolder {
	if x != nil {
		return x.Gender
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions) GetPurposeOfSavingsAccount() *ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount {
	if x != nil {
		return x.PurposeOfSavingsAccount
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions) GetConfirmBottomSheetHeader() *ui.IconTextComponent {
	if x != nil {
		return x.ConfirmBottomSheetHeader
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions) GetNamePlaceholder() *ConfirmCardMailingAddressOptions_PlaceHolder {
	if x != nil {
		return x.NamePlaceholder
	}
	return nil
}

// Corresponding to the address type selected by the user,
// check box text should be updated accordingly.
// If no check box text is present for the address type,
// check box should not be shown.
type ConfirmCardMailingAddressOptions_CheckBoxText struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type typesv2.AddressType `protobuf:"varint,1,opt,name=type,proto3,enum=api.typesv2.AddressType" json:"type,omitempty"`
	Text string              `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
}

func (x *ConfirmCardMailingAddressOptions_CheckBoxText) Reset() {
	*x = ConfirmCardMailingAddressOptions_CheckBoxText{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmCardMailingAddressOptions_CheckBoxText) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmCardMailingAddressOptions_CheckBoxText) ProtoMessage() {}

func (x *ConfirmCardMailingAddressOptions_CheckBoxText) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmCardMailingAddressOptions_CheckBoxText.ProtoReflect.Descriptor instead.
func (*ConfirmCardMailingAddressOptions_CheckBoxText) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ConfirmCardMailingAddressOptions_CheckBoxText) GetType() typesv2.AddressType {
	if x != nil {
		return x.Type
	}
	return typesv2.AddressType(0)
}

func (x *ConfirmCardMailingAddressOptions_CheckBoxText) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type ConfirmCardMailingAddressOptions_PlaceHolder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BgColor         *widget.BackgroundColour `protobuf:"bytes,1,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	BorderColor     *widget.BackgroundColour `protobuf:"bytes,2,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
	Label           *common.Text             `protobuf:"bytes,3,opt,name=label,proto3" json:"label,omitempty"`
	Value           *common.Text             `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
	ExplanatoryText *common.Text             `protobuf:"bytes,5,opt,name=explanatory_text,json=explanatoryText,proto3" json:"explanatory_text,omitempty"`
}

func (x *ConfirmCardMailingAddressOptions_PlaceHolder) Reset() {
	*x = ConfirmCardMailingAddressOptions_PlaceHolder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmCardMailingAddressOptions_PlaceHolder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmCardMailingAddressOptions_PlaceHolder) ProtoMessage() {}

func (x *ConfirmCardMailingAddressOptions_PlaceHolder) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmCardMailingAddressOptions_PlaceHolder.ProtoReflect.Descriptor instead.
func (*ConfirmCardMailingAddressOptions_PlaceHolder) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDescGZIP(), []int{0, 1}
}

func (x *ConfirmCardMailingAddressOptions_PlaceHolder) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions_PlaceHolder) GetBorderColor() *widget.BackgroundColour {
	if x != nil {
		return x.BorderColor
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions_PlaceHolder) GetLabel() *common.Text {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions_PlaceHolder) GetValue() *common.Text {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions_PlaceHolder) GetExplanatoryText() *common.Text {
	if x != nil {
		return x.ExplanatoryText
	}
	return nil
}

type ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title        *common.Text                                    `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	RadioOptions []*ConfirmCardMailingAddressOptions_RadioOption `protobuf:"bytes,2,rep,name=radio_options,json=radioOptions,proto3" json:"radio_options,omitempty"`
}

func (x *ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount) Reset() {
	*x = ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount) ProtoMessage() {}

func (x *ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount.ProtoReflect.Descriptor instead.
func (*ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDescGZIP(), []int{0, 2}
}

func (x *ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount) GetRadioOptions() []*ConfirmCardMailingAddressOptions_RadioOption {
	if x != nil {
		return x.RadioOptions
	}
	return nil
}

type ConfirmCardMailingAddressOptions_RadioOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Option text (e.g., "Yes", "No")
	Options                      *common.Text `protobuf:"bytes,1,opt,name=options,proto3" json:"options,omitempty"`
	PurposeOfSavingsAccountValue string       `protobuf:"bytes,2,opt,name=purpose_of_savings_account_value,json=purposeOfSavingsAccountValue,proto3" json:"purpose_of_savings_account_value,omitempty"`
}

func (x *ConfirmCardMailingAddressOptions_RadioOption) Reset() {
	*x = ConfirmCardMailingAddressOptions_RadioOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmCardMailingAddressOptions_RadioOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmCardMailingAddressOptions_RadioOption) ProtoMessage() {}

func (x *ConfirmCardMailingAddressOptions_RadioOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmCardMailingAddressOptions_RadioOption.ProtoReflect.Descriptor instead.
func (*ConfirmCardMailingAddressOptions_RadioOption) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDescGZIP(), []int{0, 3}
}

func (x *ConfirmCardMailingAddressOptions_RadioOption) GetOptions() *common.Text {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *ConfirmCardMailingAddressOptions_RadioOption) GetPurposeOfSavingsAccountValue() string {
	if x != nil {
		return x.PurposeOfSavingsAccountValue
	}
	return ""
}

var File_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDesc = []byte{
	0x0a, 0x58, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x61, 0x69,
	0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x2d, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x87, 0x17, 0x0a, 0x20, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x32, 0x0a, 0x09,
	0x6b, 0x79, 0x63, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4b, 0x59,
	0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x08, 0x6b, 0x79, 0x63, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x18, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x75,
	0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x70, 0x6c,
	0x61, 0x63, 0x65, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x6c, 0x61, 0x63, 0x65,
	0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a,
	0x18, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x66, 0x6f,
	0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x15, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x84, 0x01, 0x0a, 0x0f, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x5f, 0x62, 0x6f, 0x78, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x5c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x69, 0x6c,
	0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x6f, 0x78, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0d,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x6f, 0x78, 0x54, 0x65, 0x78, 0x74, 0x73, 0x12, 0x68, 0x0a,
	0x04, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x54, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x6c, 0x6f,
	0x77, 0x52, 0x04, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a,
	0x13, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x62, 0x6f, 0x78, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x62, 0x6f, 0x78, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x35, 0x0a,
	0x17, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x66, 0x6f,
	0x72, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x70, 0x6c, 0x61, 0x63, 0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x68, 0x69, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x68, 0x69, 0x6e, 0x74, 0x54, 0x65, 0x78, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c,
	0x0a, 0x12, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x6c, 0x61, 0x63,
	0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x68, 0x69, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x69, 0x63,
	0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x65, 0x64, 0x69, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x29, 0x0a, 0x10,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3b, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x41, 0x0a, 0x0f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x73,
	0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x53,
	0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28,
	0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x43, 0x74, 0x61, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x1c,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x1a, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2c,
	0x0a, 0x12, 0x68, 0x69, 0x64, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x68, 0x69, 0x64, 0x65,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x4e, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x0a,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x72, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x52, 0x09,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x12, 0x57, 0x0a, 0x1b, 0x64, 0x65, 0x62,
	0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x18, 0x64, 0x65, 0x62, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x73, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x61,
	0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0xa4, 0x01, 0x0a, 0x1a, 0x70, 0x75, 0x72, 0x70,
	0x6f, 0x73, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x67, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x50, 0x75,
	0x72, 0x70, 0x6f, 0x73, 0x65, 0x4f, 0x66, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x17, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x4f, 0x66,
	0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x60,
	0x0a, 0x1b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x5f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d,
	0x5f, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x21, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x18, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x42,
	0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x86, 0x01, 0x0a, 0x10, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x50, 0x6c, 0x61,
	0x63, 0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x52, 0x0f, 0x6e, 0x61, 0x6d, 0x65, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x1a, 0x50, 0x0a, 0x0c, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x42, 0x6f, 0x78, 0x54, 0x65, 0x78, 0x74, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x1a, 0xd0, 0x02, 0x0a, 0x0b,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x08, 0x62,
	0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62,
	0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x51, 0x0a, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0b, 0x62, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x2e, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x43, 0x0a, 0x10, 0x65, 0x78, 0x70,
	0x6c, 0x61, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0f, 0x65,
	0x78, 0x70, 0x6c, 0x61, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x65, 0x78, 0x74, 0x1a, 0xcc,
	0x01, 0x0a, 0x17, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x4f, 0x66, 0x53, 0x61, 0x76, 0x69,
	0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x0d, 0x72,
	0x61, 0x64, 0x69, 0x6f, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x61,
	0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x52, 0x61, 0x64, 0x69, 0x6f, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0c, 0x72, 0x61, 0x64, 0x69, 0x6f, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x89, 0x01,
	0x0a, 0x0b, 0x52, 0x61, 0x64, 0x69, 0x6f, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a,
	0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x46, 0x0a, 0x20, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x6f, 0x66, 0x5f,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x70, 0x75, 0x72,
	0x70, 0x6f, 0x73, 0x65, 0x4f, 0x66, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x46, 0x0a, 0x04, 0x46, 0x6c, 0x6f,
	0x77, 0x12, 0x14, 0x0a, 0x10, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10,
	0x02, 0x42, 0x8e, 0x01, 0x0a, 0x44, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x01, 0x5a, 0x44, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDescData = file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_goTypes = []interface{}{
	(ConfirmCardMailingAddressOptions_Flow)(0),                       // 0: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.Flow
	(*ConfirmCardMailingAddressOptions)(nil),                         // 1: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions
	(*ConfirmCardMailingAddressOptions_CheckBoxText)(nil),            // 2: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.CheckBoxText
	(*ConfirmCardMailingAddressOptions_PlaceHolder)(nil),             // 3: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.PlaceHolder
	(*ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount)(nil), // 4: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.PurposeOfSavingsAccount
	(*ConfirmCardMailingAddressOptions_RadioOption)(nil),             // 5: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.RadioOption
	(typesv2.KYCLevel)(0),                                            // 6: api.typesv2.KYCLevel
	(*typesv2.Money)(nil),                                            // 7: api.typesv2.Money
	(*common.Image)(nil),                                             // 8: api.typesv2.common.Image
	(*common.Text)(nil),                                              // 9: api.typesv2.common.Text
	(*deeplink.Cta)(nil),                                             // 10: frontend.deeplink.Cta
	(*deeplink_screen_option.ScreenOptionHeader)(nil),                // 11: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*deeplink.HeaderBar)(nil),                                       // 12: frontend.deeplink.HeaderBar
	(*ui.IconTextComponent)(nil),                                     // 13: api.typesv2.ui.IconTextComponent
	(typesv2.AddressType)(0),                                         // 14: api.typesv2.AddressType
	(*widget.BackgroundColour)(nil),                                  // 15: api.typesv2.common.ui.widget.BackgroundColour
}
var file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_depIdxs = []int32{
	6,  // 0: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.kyc_level:type_name -> api.typesv2.KYCLevel
	2,  // 1: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.check_box_texts:type_name -> api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.CheckBoxText
	0,  // 2: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.flow:type_name -> api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.Flow
	7,  // 3: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.amount:type_name -> api.typesv2.Money
	8,  // 4: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.image:type_name -> api.typesv2.common.Image
	9,  // 5: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.screen_title:type_name -> api.typesv2.common.Text
	9,  // 6: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.screen_subtitle:type_name -> api.typesv2.common.Text
	10, // 7: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.cta:type_name -> frontend.deeplink.Cta
	11, // 8: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	12, // 9: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.header_bar:type_name -> frontend.deeplink.HeaderBar
	9,  // 10: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.debit_card_name_description:type_name -> api.typesv2.common.Text
	3,  // 11: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.gender:type_name -> api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.PlaceHolder
	4,  // 12: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.purpose_of_savings_account:type_name -> api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.PurposeOfSavingsAccount
	13, // 13: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.confirm_bottom_sheet_header:type_name -> api.typesv2.ui.IconTextComponent
	3,  // 14: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.name_placeholder:type_name -> api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.PlaceHolder
	14, // 15: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.CheckBoxText.type:type_name -> api.typesv2.AddressType
	15, // 16: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.PlaceHolder.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	15, // 17: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.PlaceHolder.border_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	9,  // 18: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.PlaceHolder.label:type_name -> api.typesv2.common.Text
	9,  // 19: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.PlaceHolder.value:type_name -> api.typesv2.common.Text
	9,  // 20: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.PlaceHolder.explanatory_text:type_name -> api.typesv2.common.Text
	9,  // 21: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.PurposeOfSavingsAccount.title:type_name -> api.typesv2.common.Text
	5,  // 22: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.PurposeOfSavingsAccount.radio_options:type_name -> api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.RadioOption
	9,  // 23: api.typesv2.deeplink_screen_option.onboarding.ConfirmCardMailingAddressOptions.RadioOption.options:type_name -> api.typesv2.common.Text
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() {
	file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_init()
}
func file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_init() {
	if File_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmCardMailingAddressOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmCardMailingAddressOptions_CheckBoxText); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmCardMailingAddressOptions_PlaceHolder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmCardMailingAddressOptions_RadioOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_depIdxs,
		EnumInfos:         file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_enumTypes,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto = out.File
	file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_onboarding_confirm_card_mailing_address_options_proto_depIdxs = nil
}
