// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/nr_onboarding/passport_data_verification.proto

package nr_onboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on
// GlobalIssuedPassportDataVerificationScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GlobalIssuedPassportDataVerificationScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GlobalIssuedPassportDataVerificationScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GlobalIssuedPassportDataVerificationScreenOptionsMultiError, or nil if none found.
func (m *GlobalIssuedPassportDataVerificationScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *GlobalIssuedPassportDataVerificationScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInputFieldBlocks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
						field:  fmt.Sprintf("InputFieldBlocks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
						field:  fmt.Sprintf("InputFieldBlocks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
					field:  fmt.Sprintf("InputFieldBlocks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetSeparator()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
					field:  "Separator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
					field:  "Separator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSeparator()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
				field:  "Separator",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCtas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
					field:  fmt.Sprintf("Ctas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetHeaderBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GlobalIssuedPassportDataVerificationScreenOptionsValidationError{
				field:  "HeaderBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Flow

	if len(errors) > 0 {
		return GlobalIssuedPassportDataVerificationScreenOptionsMultiError(errors)
	}

	return nil
}

// GlobalIssuedPassportDataVerificationScreenOptionsMultiError is an error
// wrapping multiple validation errors returned by
// GlobalIssuedPassportDataVerificationScreenOptions.ValidateAll() if the
// designated constraints aren't met.
type GlobalIssuedPassportDataVerificationScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GlobalIssuedPassportDataVerificationScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GlobalIssuedPassportDataVerificationScreenOptionsMultiError) AllErrors() []error { return m }

// GlobalIssuedPassportDataVerificationScreenOptionsValidationError is the
// validation error returned by
// GlobalIssuedPassportDataVerificationScreenOptions.Validate if the
// designated constraints aren't met.
type GlobalIssuedPassportDataVerificationScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GlobalIssuedPassportDataVerificationScreenOptionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GlobalIssuedPassportDataVerificationScreenOptionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GlobalIssuedPassportDataVerificationScreenOptionsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GlobalIssuedPassportDataVerificationScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GlobalIssuedPassportDataVerificationScreenOptionsValidationError) ErrorName() string {
	return "GlobalIssuedPassportDataVerificationScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e GlobalIssuedPassportDataVerificationScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGlobalIssuedPassportDataVerificationScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GlobalIssuedPassportDataVerificationScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GlobalIssuedPassportDataVerificationScreenOptionsValidationError{}

// Validate checks the field values on
// GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlock with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlock) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlock with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockMultiError,
// or nil if none found.
func (m *GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlock) ValidateAll() error {
	return m.validate(true)
}

func (m *GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlock) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{
					field:  "BackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{
					field:  "BackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{
				field:  "BackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBlockTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{
					field:  "BlockTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{
					field:  "BlockTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBlockTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{
				field:  "BlockTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInputFieldHint()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{
					field:  "InputFieldHint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{
					field:  "InputFieldHint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInputFieldHint()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{
				field:  "InputFieldHint",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFooter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{
				field:  "Footer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FieldId

	if len(errors) > 0 {
		return GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockMultiError(errors)
	}

	return nil
}

// GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockMultiError
// is an error wrapping multiple validation errors returned by
// GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlock.ValidateAll()
// if the designated constraints aren't met.
type GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockMultiError) AllErrors() []error {
	return m
}

// GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError
// is the validation error returned by
// GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlock.Validate
// if the designated constraints aren't met.
type GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError) ErrorName() string {
	return "GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError"
}

// Error satisfies the builtin error interface
func (e GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlock.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GlobalIssuedPassportDataVerificationScreenOptions_InputFieldBlockValidationError{}
