// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/ui/add_money_popup.proto

package ui

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// https://www.figma.com/file/CajNnIA8zayFZXYMGAZKce/SA-balances-%E2%80%A2-FFF?node-id=604%3A1599&t=qxrRrmasVyYU98wW-1
type AddMoneyBottomSheet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Main title of bottom sheet
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// List of sections to be shown in Add Money bottom sheet
	Sections []*ActivitiesSection `protobuf:"bytes,2,rep,name=sections,proto3" json:"sections,omitempty"`
	// Icon with CTA
	Cta *IconTextComponent `protobuf:"bytes,3,opt,name=cta,proto3" json:"cta,omitempty"`
}

func (x *AddMoneyBottomSheet) Reset() {
	*x = AddMoneyBottomSheet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_add_money_popup_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddMoneyBottomSheet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMoneyBottomSheet) ProtoMessage() {}

func (x *AddMoneyBottomSheet) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_add_money_popup_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMoneyBottomSheet.ProtoReflect.Descriptor instead.
func (*AddMoneyBottomSheet) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_add_money_popup_proto_rawDescGZIP(), []int{0}
}

func (x *AddMoneyBottomSheet) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AddMoneyBottomSheet) GetSections() []*ActivitiesSection {
	if x != nil {
		return x.Sections
	}
	return nil
}

func (x *AddMoneyBottomSheet) GetCta() *IconTextComponent {
	if x != nil {
		return x.Cta
	}
	return nil
}

type ActivitiesSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// General title for the section
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// List of activities that should go under each section
	Activities []*IconTextComponent `protobuf:"bytes,2,rep,name=activities,proto3" json:"activities,omitempty"`
	// Background colour to be used in each ui
	BgColour *BackgroundColour `protobuf:"bytes,3,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
}

func (x *ActivitiesSection) Reset() {
	*x = ActivitiesSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_add_money_popup_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivitiesSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivitiesSection) ProtoMessage() {}

func (x *ActivitiesSection) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_add_money_popup_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivitiesSection.ProtoReflect.Descriptor instead.
func (*ActivitiesSection) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_add_money_popup_proto_rawDescGZIP(), []int{1}
}

func (x *ActivitiesSection) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ActivitiesSection) GetActivities() []*IconTextComponent {
	if x != nil {
		return x.Activities
	}
	return nil
}

func (x *ActivitiesSection) GetBgColour() *BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

var File_api_typesv2_ui_add_money_popup_proto protoreflect.FileDescriptor

var file_api_typesv2_ui_add_money_popup_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x61, 0x64, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x5f, 0x70, 0x6f, 0x70, 0x75, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xb9, 0x01, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x12, 0x2e, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3d, 0x0a, 0x08, 0x73,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x08, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x0a, 0x03, 0x63, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x63, 0x74, 0x61, 0x22,
	0xc5, 0x01, 0x0a, 0x11, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x41, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62,
	0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_ui_add_money_popup_proto_rawDescOnce sync.Once
	file_api_typesv2_ui_add_money_popup_proto_rawDescData = file_api_typesv2_ui_add_money_popup_proto_rawDesc
)

func file_api_typesv2_ui_add_money_popup_proto_rawDescGZIP() []byte {
	file_api_typesv2_ui_add_money_popup_proto_rawDescOnce.Do(func() {
		file_api_typesv2_ui_add_money_popup_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_ui_add_money_popup_proto_rawDescData)
	})
	return file_api_typesv2_ui_add_money_popup_proto_rawDescData
}

var file_api_typesv2_ui_add_money_popup_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_typesv2_ui_add_money_popup_proto_goTypes = []interface{}{
	(*AddMoneyBottomSheet)(nil), // 0: api.typesv2.ui.AddMoneyBottomSheet
	(*ActivitiesSection)(nil),   // 1: api.typesv2.ui.ActivitiesSection
	(*common.Text)(nil),         // 2: api.typesv2.common.Text
	(*IconTextComponent)(nil),   // 3: api.typesv2.ui.IconTextComponent
	(*BackgroundColour)(nil),    // 4: api.typesv2.ui.BackgroundColour
}
var file_api_typesv2_ui_add_money_popup_proto_depIdxs = []int32{
	2, // 0: api.typesv2.ui.AddMoneyBottomSheet.title:type_name -> api.typesv2.common.Text
	1, // 1: api.typesv2.ui.AddMoneyBottomSheet.sections:type_name -> api.typesv2.ui.ActivitiesSection
	3, // 2: api.typesv2.ui.AddMoneyBottomSheet.cta:type_name -> api.typesv2.ui.IconTextComponent
	2, // 3: api.typesv2.ui.ActivitiesSection.title:type_name -> api.typesv2.common.Text
	3, // 4: api.typesv2.ui.ActivitiesSection.activities:type_name -> api.typesv2.ui.IconTextComponent
	4, // 5: api.typesv2.ui.ActivitiesSection.bg_colour:type_name -> api.typesv2.ui.BackgroundColour
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_typesv2_ui_add_money_popup_proto_init() }
func file_api_typesv2_ui_add_money_popup_proto_init() {
	if File_api_typesv2_ui_add_money_popup_proto != nil {
		return
	}
	file_api_typesv2_ui_icon_text_component_proto_init()
	file_api_typesv2_ui_widget_themes_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_ui_add_money_popup_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddMoneyBottomSheet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_add_money_popup_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivitiesSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_ui_add_money_popup_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_ui_add_money_popup_proto_goTypes,
		DependencyIndexes: file_api_typesv2_ui_add_money_popup_proto_depIdxs,
		MessageInfos:      file_api_typesv2_ui_add_money_popup_proto_msgTypes,
	}.Build()
	File_api_typesv2_ui_add_money_popup_proto = out.File
	file_api_typesv2_ui_add_money_popup_proto_rawDesc = nil
	file_api_typesv2_ui_add_money_popup_proto_goTypes = nil
	file_api_typesv2_ui_add_money_popup_proto_depIdxs = nil
}
