// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/datasharing/data_sharing_record.proto

package datasharing

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Client int32

const (
	Client_CLIENT_UNSPECIFIED       Client = 0
	Client_CLIENT_CA_ANALYTICS      Client = 1
	Client_CLIENT_SALARY_ESTIMATION Client = 2
)

// Enum value maps for Client.
var (
	Client_name = map[int32]string{
		0: "CLIENT_UNSPECIFIED",
		1: "CLIENT_CA_ANALYTICS",
		2: "CLIENT_SALARY_ESTIMATION",
	}
	Client_value = map[string]int32{
		"CLIENT_UNSPECIFIED":       0,
		"CLIENT_CA_ANALYTICS":      1,
		"CLIENT_SALARY_ESTIMATION": 2,
	}
)

func (x Client) Enum() *Client {
	p := new(Client)
	*p = x
	return p
}

func (x Client) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Client) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_datasharing_data_sharing_record_proto_enumTypes[0].Descriptor()
}

func (Client) Type() protoreflect.EnumType {
	return &file_api_typesv2_datasharing_data_sharing_record_proto_enumTypes[0]
}

func (x Client) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Client.Descriptor instead.
func (Client) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_datasharing_data_sharing_record_proto_rawDescGZIP(), []int{0}
}

type DataType int32

const (
	// Data type for Account Aggregator (AA) data
	DataType_DATA_TYPE_UNSPECIFIED DataType = 0
	// Account Aggregator (AA) data for all accounts of a user along with transactions
	DataType_DATA_TYPE_AA_ACCOUNTS DataType = 1
)

// Enum value maps for DataType.
var (
	DataType_name = map[int32]string{
		0: "DATA_TYPE_UNSPECIFIED",
		1: "DATA_TYPE_AA_ACCOUNTS",
	}
	DataType_value = map[string]int32{
		"DATA_TYPE_UNSPECIFIED": 0,
		"DATA_TYPE_AA_ACCOUNTS": 1,
	}
)

func (x DataType) Enum() *DataType {
	p := new(DataType)
	*p = x
	return p
}

func (x DataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_datasharing_data_sharing_record_proto_enumTypes[1].Descriptor()
}

func (DataType) Type() protoreflect.EnumType {
	return &file_api_typesv2_datasharing_data_sharing_record_proto_enumTypes[1]
}

func (x DataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataType.Descriptor instead.
func (DataType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_datasharing_data_sharing_record_proto_rawDescGZIP(), []int{1}
}

// DataSharingRecord represents a user's data shared with clients such as the user via Fi app.
// This data may be large, comprising one or more files, and is often intended for auditing or processing purposes.
// The message includes metadata that provides critical information about the file contents,
// allowing clients to process the data without loading the entire dataset.
type DataSharingRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Client requesting the data to be shared, used to decide the service to call back after the data is shared.
	Client Client `protobuf:"varint,1,opt,name=client,proto3,enum=api.typesv2.datasharing.Client" json:"client,omitempty"`
	// Unique client-specific request ID used to track this data shared across different requests.
	// Example Use Case: Epifi Tech's analytics service can use this identifier to track the status of
	// the data request from Epifi Wealth and poll until the data is received.
	ClientRequestId string `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// Identifier of the user whose data is being shared.
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Ownership of the final entity, with which data is to be shared
	ClientOwnership common.Owner `protobuf:"varint,4,opt,name=client_ownership,json=clientOwnership,proto3,enum=api.typesv2.common.Owner" json:"client_ownership,omitempty"`
	// Type of data being shared
	DataType DataType `protobuf:"varint,5,opt,name=data_type,json=dataType,proto3,enum=api.typesv2.datasharing.DataType" json:"data_type,omitempty"`
	// Data being shared
	Data *Data `protobuf:"bytes,6,opt,name=data,proto3" json:"data,omitempty"`
	// Identifier of the consent enabling the data to be shared
	ConsentId string `protobuf:"bytes,7,opt,name=consent_id,json=consentId,proto3" json:"consent_id,omitempty"`
}

func (x *DataSharingRecord) Reset() {
	*x = DataSharingRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataSharingRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataSharingRecord) ProtoMessage() {}

func (x *DataSharingRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataSharingRecord.ProtoReflect.Descriptor instead.
func (*DataSharingRecord) Descriptor() ([]byte, []int) {
	return file_api_typesv2_datasharing_data_sharing_record_proto_rawDescGZIP(), []int{0}
}

func (x *DataSharingRecord) GetClient() Client {
	if x != nil {
		return x.Client
	}
	return Client_CLIENT_UNSPECIFIED
}

func (x *DataSharingRecord) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *DataSharingRecord) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DataSharingRecord) GetClientOwnership() common.Owner {
	if x != nil {
		return x.ClientOwnership
	}
	return common.Owner(0)
}

func (x *DataSharingRecord) GetDataType() DataType {
	if x != nil {
		return x.DataType
	}
	return DataType_DATA_TYPE_UNSPECIFIED
}

func (x *DataSharingRecord) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *DataSharingRecord) GetConsentId() string {
	if x != nil {
		return x.ConsentId
	}
	return ""
}

// Data represents the shared data, which may include multiple files.
type Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Data:
	//	*Data_AaAccountsData
	Data isData_Data `protobuf_oneof:"data"`
}

func (x *Data) Reset() {
	*x = Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_api_typesv2_datasharing_data_sharing_record_proto_rawDescGZIP(), []int{1}
}

func (m *Data) GetData() isData_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *Data) GetAaAccountsData() *AaAccountsData {
	if x, ok := x.GetData().(*Data_AaAccountsData); ok {
		return x.AaAccountsData
	}
	return nil
}

type isData_Data interface {
	isData_Data()
}

type Data_AaAccountsData struct {
	// Data shared for AA raw transactions
	AaAccountsData *AaAccountsData `protobuf:"bytes,1,opt,name=aa_accounts_data,json=aaAccountsData,proto3,oneof"`
}

func (*Data_AaAccountsData) isData_Data() {}

type AaAccountsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Files []*File `protobuf:"bytes,1,rep,name=files,proto3" json:"files,omitempty"`
}

func (x *AaAccountsData) Reset() {
	*x = AaAccountsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaAccountsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaAccountsData) ProtoMessage() {}

func (x *AaAccountsData) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaAccountsData.ProtoReflect.Descriptor instead.
func (*AaAccountsData) Descriptor() ([]byte, []int) {
	return file_api_typesv2_datasharing_data_sharing_record_proto_rawDescGZIP(), []int{2}
}

func (x *AaAccountsData) GetFiles() []*File {
	if x != nil {
		return x.Files
	}
	return nil
}

// File represents an individual file containing the user's data.
// Storing and transferring data in files is beneficial for handling large datasets
// where querying is not required, rather the data needs to be stored for audits.
type File struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique name of the file to be used for referencing in both source and destination.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// URL to temporarily access the file
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	// Metadata providing essential information about the contents of the file.
	// which can be used by clients to process the data inside file without loading the file itself.
	Metadata *FileMetadata `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *File) Reset() {
	*x = File{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *File) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*File) ProtoMessage() {}

func (x *File) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use File.ProtoReflect.Descriptor instead.
func (*File) Descriptor() ([]byte, []int) {
	return file_api_typesv2_datasharing_data_sharing_record_proto_rawDescGZIP(), []int{3}
}

func (x *File) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *File) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *File) GetMetadata() *FileMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// FileMetadata contains additional details about the contents of a file
type FileMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to FileTypeMetadata:
	//	*FileMetadata_AaAccountFileMetadata
	FileTypeMetadata isFileMetadata_FileTypeMetadata `protobuf_oneof:"file_type_metadata"`
}

func (x *FileMetadata) Reset() {
	*x = FileMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileMetadata) ProtoMessage() {}

func (x *FileMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileMetadata.ProtoReflect.Descriptor instead.
func (*FileMetadata) Descriptor() ([]byte, []int) {
	return file_api_typesv2_datasharing_data_sharing_record_proto_rawDescGZIP(), []int{4}
}

func (m *FileMetadata) GetFileTypeMetadata() isFileMetadata_FileTypeMetadata {
	if m != nil {
		return m.FileTypeMetadata
	}
	return nil
}

func (x *FileMetadata) GetAaAccountFileMetadata() *AaAccountFileMetadata {
	if x, ok := x.GetFileTypeMetadata().(*FileMetadata_AaAccountFileMetadata); ok {
		return x.AaAccountFileMetadata
	}
	return nil
}

type isFileMetadata_FileTypeMetadata interface {
	isFileMetadata_FileTypeMetadata()
}

type FileMetadata_AaAccountFileMetadata struct {
	AaAccountFileMetadata *AaAccountFileMetadata `protobuf:"bytes,1,opt,name=aa_account_file_metadata,json=aaAccountFileMetadata,proto3,oneof"`
}

func (*FileMetadata_AaAccountFileMetadata) isFileMetadata_FileTypeMetadata() {}

// AaAccountMetadata provides details about the data of a specific connected account
// that is included in the shared file.
type AaAccountFileMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifier of the connected account whose data is being shared.
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Timestamp of the earliest transaction (based on "updated_at") included in the file.
	// This helps define the beginning of the transaction time range within the file.
	OldestTransactionTs *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=oldest_transaction_ts,json=oldestTransactionTs,proto3" json:"oldest_transaction_ts,omitempty"`
	// Timestamp of the most recent transaction (based on "updated_at") included in the file.
	// This defines the end of the transaction time range within the file.
	LatestTransactionTs *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=latest_transaction_ts,json=latestTransactionTs,proto3" json:"latest_transaction_ts,omitempty"`
}

func (x *AaAccountFileMetadata) Reset() {
	*x = AaAccountFileMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaAccountFileMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaAccountFileMetadata) ProtoMessage() {}

func (x *AaAccountFileMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaAccountFileMetadata.ProtoReflect.Descriptor instead.
func (*AaAccountFileMetadata) Descriptor() ([]byte, []int) {
	return file_api_typesv2_datasharing_data_sharing_record_proto_rawDescGZIP(), []int{5}
}

func (x *AaAccountFileMetadata) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *AaAccountFileMetadata) GetOldestTransactionTs() *timestamppb.Timestamp {
	if x != nil {
		return x.OldestTransactionTs
	}
	return nil
}

func (x *AaAccountFileMetadata) GetLatestTransactionTs() *timestamppb.Timestamp {
	if x != nil {
		return x.LatestTransactionTs
	}
	return nil
}

var File_api_typesv2_datasharing_data_sharing_record_proto protoreflect.FileDescriptor

var file_api_typesv2_datasharing_data_sharing_record_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73,
	0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x17, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x1a, 0x22, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xeb, 0x02, 0x0a, 0x11, 0x44, 0x61, 0x74, 0x61, 0x53, 0x68, 0x61, 0x72, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x37, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x10, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x0f, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12, 0x3e, 0x0a,
	0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x68,
	0x61, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0x63, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x53, 0x0a, 0x10, 0x61, 0x61, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x61, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0e, 0x61, 0x61,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x42, 0x06, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x45, 0x0a, 0x0e, 0x41, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x33, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0x6f, 0x0a, 0x04, 0x46,
	0x69, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x41, 0x0a, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x68,
	0x61, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x8f, 0x01, 0x0a,
	0x0c, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x69, 0x0a,
	0x18, 0x61, 0x61, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x61, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x15, 0x61, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x14, 0x0a, 0x12, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0xd6,
	0x01, 0x0a, 0x15, 0x41, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x15, 0x6f, 0x6c, 0x64, 0x65, 0x73,
	0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x13, 0x6f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x73, 0x12, 0x4e, 0x0a, 0x15, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x13, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x73, 0x2a, 0x57, 0x0a, 0x06, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x4c, 0x49,
	0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x54, 0x49, 0x43, 0x53,
	0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x41, 0x4c,
	0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02,
	0x2a, 0x40, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x41, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53,
	0x10, 0x01, 0x42, 0x60, 0x0a, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x68, 0x61,
	0x72, 0x69, 0x6e, 0x67, 0x5a, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x68, 0x61,
	0x72, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_datasharing_data_sharing_record_proto_rawDescOnce sync.Once
	file_api_typesv2_datasharing_data_sharing_record_proto_rawDescData = file_api_typesv2_datasharing_data_sharing_record_proto_rawDesc
)

func file_api_typesv2_datasharing_data_sharing_record_proto_rawDescGZIP() []byte {
	file_api_typesv2_datasharing_data_sharing_record_proto_rawDescOnce.Do(func() {
		file_api_typesv2_datasharing_data_sharing_record_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_datasharing_data_sharing_record_proto_rawDescData)
	})
	return file_api_typesv2_datasharing_data_sharing_record_proto_rawDescData
}

var file_api_typesv2_datasharing_data_sharing_record_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_typesv2_datasharing_data_sharing_record_proto_goTypes = []interface{}{
	(Client)(0),                   // 0: api.typesv2.datasharing.Client
	(DataType)(0),                 // 1: api.typesv2.datasharing.DataType
	(*DataSharingRecord)(nil),     // 2: api.typesv2.datasharing.DataSharingRecord
	(*Data)(nil),                  // 3: api.typesv2.datasharing.Data
	(*AaAccountsData)(nil),        // 4: api.typesv2.datasharing.AaAccountsData
	(*File)(nil),                  // 5: api.typesv2.datasharing.File
	(*FileMetadata)(nil),          // 6: api.typesv2.datasharing.FileMetadata
	(*AaAccountFileMetadata)(nil), // 7: api.typesv2.datasharing.AaAccountFileMetadata
	(common.Owner)(0),             // 8: api.typesv2.common.Owner
	(*timestamppb.Timestamp)(nil), // 9: google.protobuf.Timestamp
}
var file_api_typesv2_datasharing_data_sharing_record_proto_depIdxs = []int32{
	0,  // 0: api.typesv2.datasharing.DataSharingRecord.client:type_name -> api.typesv2.datasharing.Client
	8,  // 1: api.typesv2.datasharing.DataSharingRecord.client_ownership:type_name -> api.typesv2.common.Owner
	1,  // 2: api.typesv2.datasharing.DataSharingRecord.data_type:type_name -> api.typesv2.datasharing.DataType
	3,  // 3: api.typesv2.datasharing.DataSharingRecord.data:type_name -> api.typesv2.datasharing.Data
	4,  // 4: api.typesv2.datasharing.Data.aa_accounts_data:type_name -> api.typesv2.datasharing.AaAccountsData
	5,  // 5: api.typesv2.datasharing.AaAccountsData.files:type_name -> api.typesv2.datasharing.File
	6,  // 6: api.typesv2.datasharing.File.metadata:type_name -> api.typesv2.datasharing.FileMetadata
	7,  // 7: api.typesv2.datasharing.FileMetadata.aa_account_file_metadata:type_name -> api.typesv2.datasharing.AaAccountFileMetadata
	9,  // 8: api.typesv2.datasharing.AaAccountFileMetadata.oldest_transaction_ts:type_name -> google.protobuf.Timestamp
	9,  // 9: api.typesv2.datasharing.AaAccountFileMetadata.latest_transaction_ts:type_name -> google.protobuf.Timestamp
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_typesv2_datasharing_data_sharing_record_proto_init() }
func file_api_typesv2_datasharing_data_sharing_record_proto_init() {
	if File_api_typesv2_datasharing_data_sharing_record_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataSharingRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaAccountsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*File); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaAccountFileMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Data_AaAccountsData)(nil),
	}
	file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*FileMetadata_AaAccountFileMetadata)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_datasharing_data_sharing_record_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_datasharing_data_sharing_record_proto_goTypes,
		DependencyIndexes: file_api_typesv2_datasharing_data_sharing_record_proto_depIdxs,
		EnumInfos:         file_api_typesv2_datasharing_data_sharing_record_proto_enumTypes,
		MessageInfos:      file_api_typesv2_datasharing_data_sharing_record_proto_msgTypes,
	}.Build()
	File_api_typesv2_datasharing_data_sharing_record_proto = out.File
	file_api_typesv2_datasharing_data_sharing_record_proto_rawDesc = nil
	file_api_typesv2_datasharing_data_sharing_record_proto_goTypes = nil
	file_api_typesv2_datasharing_data_sharing_record_proto_depIdxs = nil
}
