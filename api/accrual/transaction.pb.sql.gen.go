// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /Users/<USER>/go/src/github.com/epifi/gamma/api/accrual/transaction.pb.go

package accrual

import (
	"database/sql/driver"
	"fmt"
	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing TxnMetaData while reading from DB
func (a *TxnMetaData) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the TxnMetaData in string format in DB
func (a *TxnMetaData) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for TxnMetaData
func (a *TxnMetaData) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for TxnMetaData
func (a *TxnMetaData) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
