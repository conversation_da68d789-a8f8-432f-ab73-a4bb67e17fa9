// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v4.23.4
// source: api/accrual/consumer/consumer.proto

package consumer

import (
	context "context"
	operstatus "github.com/epifi/gamma/api/accounts/operstatus"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ConsumerClient is the client API for Consumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConsumerClient interface {
	// ProcessAccountStatusUpdateEvent consumes OperationalStatusUpdateEvent to expire all available fi coins for an actor if account status is OPERATIONAL_STATUS_CLOSED.
	ProcessAccountStatusUpdateEvent(ctx context.Context, in *operstatus.OperationalStatusUpdateEvent, opts ...grpc.CallOption) (*ConsumerResponse, error)
}

type consumerClient struct {
	cc grpc.ClientConnInterface
}

func NewConsumerClient(cc grpc.ClientConnInterface) ConsumerClient {
	return &consumerClient{cc}
}

func (c *consumerClient) ProcessAccountStatusUpdateEvent(ctx context.Context, in *operstatus.OperationalStatusUpdateEvent, opts ...grpc.CallOption) (*ConsumerResponse, error) {
	out := new(ConsumerResponse)
	err := c.cc.Invoke(ctx, "/accrual.consumer.Consumer/ProcessAccountStatusUpdateEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConsumerServer is the server API for Consumer service.
// All implementations should embed UnimplementedConsumerServer
// for forward compatibility
type ConsumerServer interface {
	// ProcessAccountStatusUpdateEvent consumes OperationalStatusUpdateEvent to expire all available fi coins for an actor if account status is OPERATIONAL_STATUS_CLOSED.
	ProcessAccountStatusUpdateEvent(context.Context, *operstatus.OperationalStatusUpdateEvent) (*ConsumerResponse, error)
}

// UnimplementedConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedConsumerServer struct {
}

func (UnimplementedConsumerServer) ProcessAccountStatusUpdateEvent(context.Context, *operstatus.OperationalStatusUpdateEvent) (*ConsumerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessAccountStatusUpdateEvent not implemented")
}

// UnsafeConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConsumerServer will
// result in compilation errors.
type UnsafeConsumerServer interface {
	mustEmbedUnimplementedConsumerServer()
}

func RegisterConsumerServer(s grpc.ServiceRegistrar, srv ConsumerServer) {
	s.RegisterService(&Consumer_ServiceDesc, srv)
}

func _Consumer_ProcessAccountStatusUpdateEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(operstatus.OperationalStatusUpdateEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessAccountStatusUpdateEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/accrual.consumer.Consumer/ProcessAccountStatusUpdateEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessAccountStatusUpdateEvent(ctx, req.(*operstatus.OperationalStatusUpdateEvent))
	}
	return interceptor(ctx, in, info, handler)
}

// Consumer_ServiceDesc is the grpc.ServiceDesc for Consumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Consumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "accrual.consumer.Consumer",
	HandlerType: (*ConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessAccountStatusUpdateEvent",
			Handler:    _Consumer_ProcessAccountStatusUpdateEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/accrual/consumer/consumer.proto",
}
