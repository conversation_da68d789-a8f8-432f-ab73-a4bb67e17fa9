//go:generate gen_sql -types=TxnMetaData

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.4
// source: api/accrual/transaction.proto

package accrual

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// TransactionType represents the type of transaction that was requested/completed.
// CREDIT denotes given amount was added to the account in the transaction.
// DEBIT implies that given amount was debited from the account in the transaction.
type TransactionType int32

const (
	TransactionType_TRANSACTION_TYPE_UNSPECIFIED TransactionType = 0
	TransactionType_TRANSACTION_TYPE_CREDIT      TransactionType = 1
	TransactionType_TRANSACTION_TYPE_DEBIT       TransactionType = 2
)

// Enum value maps for TransactionType.
var (
	TransactionType_name = map[int32]string{
		0: "TRANSACTION_TYPE_UNSPECIFIED",
		1: "TRANSACTION_TYPE_CREDIT",
		2: "TRANSACTION_TYPE_DEBIT",
	}
	TransactionType_value = map[string]int32{
		"TRANSACTION_TYPE_UNSPECIFIED": 0,
		"TRANSACTION_TYPE_CREDIT":      1,
		"TRANSACTION_TYPE_DEBIT":       2,
	}
)

func (x TransactionType) Enum() *TransactionType {
	p := new(TransactionType)
	*p = x
	return p
}

func (x TransactionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_accrual_transaction_proto_enumTypes[0].Descriptor()
}

func (TransactionType) Type() protoreflect.EnumType {
	return &file_api_accrual_transaction_proto_enumTypes[0]
}

func (x TransactionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionType.Descriptor instead.
func (TransactionType) EnumDescriptor() ([]byte, []int) {
	return file_api_accrual_transaction_proto_rawDescGZIP(), []int{0}
}

// TransactionSubType represents the subtype of transaction that was requested/completed.
type TransactionSubType int32

const (
	// This is used for general purpose transactions where no specific subtype is required
	// Valid Transaction Types: TRANSACTION_TYPE_DEBIT,TRANSACTION_TYPE_CREDIT
	TransactionSubType_TRANSACTION_SUB_TYPE_UNSPECIFIED TransactionSubType = 0
	// This is used when a particular transaction is a reversal of a previous transaction.
	// Valid Transaction Types: TRANSACTION_TYPE_DEBIT,TRANSACTION_TYPE_CREDIT
	TransactionSubType_TRANSACTION_SUB_TYPE_REVERSE TransactionSubType = 1
	// This is used when we perform a transaction to expire some points whose expiry time has passed
	// Valid Transaction Types: TRANSACTION_TYPE_DEBIT
	TransactionSubType_TRANSACTION_SUB_TYPE_EXPIRY TransactionSubType = 2
	// This is used when we perform a transaction to expire the whole balance of an account
	// Valid Transaction Types: TRANSACTION_TYPE_DEBIT
	TransactionSubType_TRANSACTION_SUB_TYPE_ACCOUNT_CLOSURE_EXPIRY TransactionSubType = 3
	// This is used when we transfer points from account of type A to account of type B
	// Valid Transaction Types: TRANSACTION_TYPE_DEBIT,TRANSACTION_TYPE_CREDIT
	TransactionSubType_TRANSACTION_SUB_TYPE_POINT_MIGRATION TransactionSubType = 4
)

// Enum value maps for TransactionSubType.
var (
	TransactionSubType_name = map[int32]string{
		0: "TRANSACTION_SUB_TYPE_UNSPECIFIED",
		1: "TRANSACTION_SUB_TYPE_REVERSE",
		2: "TRANSACTION_SUB_TYPE_EXPIRY",
		3: "TRANSACTION_SUB_TYPE_ACCOUNT_CLOSURE_EXPIRY",
		4: "TRANSACTION_SUB_TYPE_POINT_MIGRATION",
	}
	TransactionSubType_value = map[string]int32{
		"TRANSACTION_SUB_TYPE_UNSPECIFIED":            0,
		"TRANSACTION_SUB_TYPE_REVERSE":                1,
		"TRANSACTION_SUB_TYPE_EXPIRY":                 2,
		"TRANSACTION_SUB_TYPE_ACCOUNT_CLOSURE_EXPIRY": 3,
		"TRANSACTION_SUB_TYPE_POINT_MIGRATION":        4,
	}
)

func (x TransactionSubType) Enum() *TransactionSubType {
	p := new(TransactionSubType)
	*p = x
	return p
}

func (x TransactionSubType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionSubType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_accrual_transaction_proto_enumTypes[1].Descriptor()
}

func (TransactionSubType) Type() protoreflect.EnumType {
	return &file_api_accrual_transaction_proto_enumTypes[1]
}

func (x TransactionSubType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionSubType.Descriptor instead.
func (TransactionSubType) EnumDescriptor() ([]byte, []int) {
	return file_api_accrual_transaction_proto_rawDescGZIP(), []int{1}
}

// TransactionStatus denotes status of transaction.
// Following are the allowed  state changes
// PENDING -> COMPLETED
type TransactionStatus int32

const (
	TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED TransactionStatus = 0
	// TRANSACTION_STATUS_NOT_PERFORMED denotes the transaction was not performed.
	TransactionStatus_TRANSACTION_STATUS_NOT_PERFORMED TransactionStatus = 1
	// TRANSACTION_STATUS_PENDING denotes the transaction is pending.
	TransactionStatus_TRANSACTION_STATUS_PENDING TransactionStatus = 2
	// TRANSACTION_STATUS_PENDING denotes the transaction is completed.
	TransactionStatus_TRANSACTION_STATUS_COMPLETED TransactionStatus = 3
)

// Enum value maps for TransactionStatus.
var (
	TransactionStatus_name = map[int32]string{
		0: "TRANSACTION_STATUS_UNSPECIFIED",
		1: "TRANSACTION_STATUS_NOT_PERFORMED",
		2: "TRANSACTION_STATUS_PENDING",
		3: "TRANSACTION_STATUS_COMPLETED",
	}
	TransactionStatus_value = map[string]int32{
		"TRANSACTION_STATUS_UNSPECIFIED":   0,
		"TRANSACTION_STATUS_NOT_PERFORMED": 1,
		"TRANSACTION_STATUS_PENDING":       2,
		"TRANSACTION_STATUS_COMPLETED":     3,
	}
)

func (x TransactionStatus) Enum() *TransactionStatus {
	p := new(TransactionStatus)
	*p = x
	return p
}

func (x TransactionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_accrual_transaction_proto_enumTypes[2].Descriptor()
}

func (TransactionStatus) Type() protoreflect.EnumType {
	return &file_api_accrual_transaction_proto_enumTypes[2]
}

func (x TransactionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionStatus.Descriptor instead.
func (TransactionStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_accrual_transaction_proto_rawDescGZIP(), []int{2}
}

type TxnMetaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// metadata for fi coins to fi points conversion
	CoinToPoints *FiCoinsToFiPointsConversion `protobuf:"bytes,1,opt,name=coin_to_points,json=coinToPoints,proto3" json:"coin_to_points,omitempty"`
}

func (x *TxnMetaData) Reset() {
	*x = TxnMetaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_transaction_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TxnMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxnMetaData) ProtoMessage() {}

func (x *TxnMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_transaction_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxnMetaData.ProtoReflect.Descriptor instead.
func (*TxnMetaData) Descriptor() ([]byte, []int) {
	return file_api_accrual_transaction_proto_rawDescGZIP(), []int{0}
}

func (x *TxnMetaData) GetCoinToPoints() *FiCoinsToFiPointsConversion {
	if x != nil {
		return x.CoinToPoints
	}
	return nil
}

type FiCoinsToFiPointsConversion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// number of fi coins converted to fi points
	CoinsConverted int32 `protobuf:"varint,1,opt,name=coins_converted,json=coinsConverted,proto3" json:"coins_converted,omitempty"`
	// whether the fi coins to fi points conversion was performed for a credit card user
	// at the time of migration
	IsCcUser bool `protobuf:"varint,2,opt,name=is_cc_user,json=isCcUser,proto3" json:"is_cc_user,omitempty"`
}

func (x *FiCoinsToFiPointsConversion) Reset() {
	*x = FiCoinsToFiPointsConversion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_transaction_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiCoinsToFiPointsConversion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiCoinsToFiPointsConversion) ProtoMessage() {}

func (x *FiCoinsToFiPointsConversion) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_transaction_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiCoinsToFiPointsConversion.ProtoReflect.Descriptor instead.
func (*FiCoinsToFiPointsConversion) Descriptor() ([]byte, []int) {
	return file_api_accrual_transaction_proto_rawDescGZIP(), []int{1}
}

func (x *FiCoinsToFiPointsConversion) GetCoinsConverted() int32 {
	if x != nil {
		return x.CoinsConverted
	}
	return 0
}

func (x *FiCoinsToFiPointsConversion) GetIsCcUser() bool {
	if x != nil {
		return x.IsCcUser
	}
	return false
}

var File_api_accrual_transaction_proto protoreflect.FileDescriptor

var file_api_accrual_transaction_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x07, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x22, 0x59, 0x0a, 0x0b, 0x54, 0x78, 0x6e, 0x4d,
	0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4a, 0x0a, 0x0e, 0x63, 0x6f, 0x69, 0x6e, 0x5f,
	0x74, 0x6f, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e,
	0x73, 0x54, 0x6f, 0x46, 0x69, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x63, 0x6f, 0x69, 0x6e, 0x54, 0x6f, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x22, 0x64, 0x0a, 0x1b, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f,
	0x46, 0x69, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x6f, 0x69,
	0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x69,
	0x73, 0x5f, 0x63, 0x63, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x69, 0x73, 0x43, 0x63, 0x55, 0x73, 0x65, 0x72, 0x2a, 0x6c, 0x0a, 0x0f, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b,
	0x0a, 0x17, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x44, 0x45, 0x42, 0x49, 0x54, 0x10, 0x02, 0x2a, 0xd8, 0x01, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24,
	0x0a, 0x20, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x56,
	0x45, 0x52, 0x53, 0x45, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45,
	0x58, 0x50, 0x49, 0x52, 0x59, 0x10, 0x02, 0x12, 0x2f, 0x0a, 0x2b, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f,
	0x45, 0x58, 0x50, 0x49, 0x52, 0x59, 0x10, 0x03, 0x12, 0x28, 0x0a, 0x24, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x4d, 0x49, 0x47, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x04, 0x2a, 0x9f, 0x01, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x4f, 0x52, 0x4d, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54,
	0x45, 0x44, 0x10, 0x03, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_accrual_transaction_proto_rawDescOnce sync.Once
	file_api_accrual_transaction_proto_rawDescData = file_api_accrual_transaction_proto_rawDesc
)

func file_api_accrual_transaction_proto_rawDescGZIP() []byte {
	file_api_accrual_transaction_proto_rawDescOnce.Do(func() {
		file_api_accrual_transaction_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_accrual_transaction_proto_rawDescData)
	})
	return file_api_accrual_transaction_proto_rawDescData
}

var file_api_accrual_transaction_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_accrual_transaction_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_accrual_transaction_proto_goTypes = []interface{}{
	(TransactionType)(0),                // 0: accrual.TransactionType
	(TransactionSubType)(0),             // 1: accrual.TransactionSubType
	(TransactionStatus)(0),              // 2: accrual.TransactionStatus
	(*TxnMetaData)(nil),                 // 3: accrual.TxnMetaData
	(*FiCoinsToFiPointsConversion)(nil), // 4: accrual.FiCoinsToFiPointsConversion
}
var file_api_accrual_transaction_proto_depIdxs = []int32{
	4, // 0: accrual.TxnMetaData.coin_to_points:type_name -> accrual.FiCoinsToFiPointsConversion
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_accrual_transaction_proto_init() }
func file_api_accrual_transaction_proto_init() {
	if File_api_accrual_transaction_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_accrual_transaction_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TxnMetaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_transaction_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiCoinsToFiPointsConversion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_accrual_transaction_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_accrual_transaction_proto_goTypes,
		DependencyIndexes: file_api_accrual_transaction_proto_depIdxs,
		EnumInfos:         file_api_accrual_transaction_proto_enumTypes,
		MessageInfos:      file_api_accrual_transaction_proto_msgTypes,
	}.Build()
	File_api_accrual_transaction_proto = out.File
	file_api_accrual_transaction_proto_rawDesc = nil
	file_api_accrual_transaction_proto_goTypes = nil
	file_api_accrual_transaction_proto_depIdxs = nil
}
