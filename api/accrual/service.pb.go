// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.4
// source: api/accrual/service.proto

package accrual

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// TransactRequest contains relevant data required to perform a transaction
type TransactRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// request_ref_id is unique request id passed by client for performing this transaction
	RequestRefId string `protobuf:"bytes,1,opt,name=request_ref_id,json=requestRefId,proto3" json:"request_ref_id,omitempty"`
	// actor_id is actor_id is actor's id into/from who's account the given
	// amount should be credited/debited
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// amount denotes the amount to be processed in the transaction
	Amount int32 `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// amountExpiryTime for credit transactions denotes the expiry time of the credited balance;
	// has no meaning for other transaction api.typesv2.
	AmountExpiryTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=amount_expiry_time,json=amountExpiryTime,proto3" json:"amount_expiry_time,omitempty"`
	// account_type represents type to account
	// an actor can have accounts of different account api.typesv2.
	// there is only one account type for now.
	AccountType AccountType `protobuf:"varint,5,opt,name=account_type,json=accountType,proto3,enum=accrual.AccountType" json:"account_type,omitempty"`
	// transaction_type denotes the type of transaction namely CREDIT or DEBIT.
	TransactionType TransactionType `protobuf:"varint,6,opt,name=transaction_type,json=transactionType,proto3,enum=accrual.TransactionType" json:"transaction_type,omitempty"`
	// additional options for a transaction request
	Options *TransactRequest_Options `protobuf:"bytes,7,opt,name=options,proto3" json:"options,omitempty"`
	// metadata for a transaction
	TxnMetaData *TxnMetaData `protobuf:"bytes,8,opt,name=txn_meta_data,json=txnMetaData,proto3" json:"txn_meta_data,omitempty"`
}

func (x *TransactRequest) Reset() {
	*x = TransactRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactRequest) ProtoMessage() {}

func (x *TransactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactRequest.ProtoReflect.Descriptor instead.
func (*TransactRequest) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{0}
}

func (x *TransactRequest) GetRequestRefId() string {
	if x != nil {
		return x.RequestRefId
	}
	return ""
}

func (x *TransactRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *TransactRequest) GetAmount() int32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *TransactRequest) GetAmountExpiryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.AmountExpiryTime
	}
	return nil
}

func (x *TransactRequest) GetAccountType() AccountType {
	if x != nil {
		return x.AccountType
	}
	return AccountType_ACCOUNT_TYPE_UNSPECIFIED
}

func (x *TransactRequest) GetTransactionType() TransactionType {
	if x != nil {
		return x.TransactionType
	}
	return TransactionType_TRANSACTION_TYPE_UNSPECIFIED
}

func (x *TransactRequest) GetOptions() *TransactRequest_Options {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *TransactRequest) GetTxnMetaData() *TxnMetaData {
	if x != nil {
		return x.TxnMetaData
	}
	return nil
}

// Response to a TransactRequest
type TransactResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status represents status of request, will be OK if transaction was successful.
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// transaction_id denotes the id of transaction if it was successful otherwise
	// if transaction fails due to some validation error then it would be null
	TransactionId string `protobuf:"bytes,2,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	// status of txn
	TransactionStatus TransactionStatus `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,proto3,enum=accrual.TransactionStatus" json:"transaction_status,omitempty"`
}

func (x *TransactResponse) Reset() {
	*x = TransactResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactResponse) ProtoMessage() {}

func (x *TransactResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactResponse.ProtoReflect.Descriptor instead.
func (*TransactResponse) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{1}
}

func (x *TransactResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *TransactResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *TransactResponse) GetTransactionStatus() TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED
}

// CheckStatusRequest checks whether the status of transaction for a given unique request Id.
// In case of network errors on a client request, this can be used by client for checking whether the
// transaction was performed or whats the current status of transaction for the given request.
type CheckStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// request_ref_id denotes the request reference id of transaction whose status needs to be checked.
	RequestRefId string `protobuf:"bytes,1,opt,name=request_ref_id,json=requestRefId,proto3" json:"request_ref_id,omitempty"`
}

func (x *CheckStatusRequest) Reset() {
	*x = CheckStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckStatusRequest) ProtoMessage() {}

func (x *CheckStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{2}
}

func (x *CheckStatusRequest) GetRequestRefId() string {
	if x != nil {
		return x.RequestRefId
	}
	return ""
}

// CheckStatusResponse is the response for CheckStatusRequest
type CheckStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status represents status of request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// transaction_status represents status of requested transaction.
	TransactionStatus TransactionStatus `protobuf:"varint,2,opt,name=transaction_status,json=transactionStatus,proto3,enum=accrual.TransactionStatus" json:"transaction_status,omitempty"`
	// if status is completed, then return transaction id or empty.
	TxnId string `protobuf:"bytes,3,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
}

func (x *CheckStatusResponse) Reset() {
	*x = CheckStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckStatusResponse) ProtoMessage() {}

func (x *CheckStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{3}
}

func (x *CheckStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckStatusResponse) GetTransactionStatus() TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED
}

func (x *CheckStatusResponse) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

// ReverseTransactionRequest packages information for performing a transaction reverse.
type ReverseTransactionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// txn_ref_id denotes ref_id of the transaction that needs to be reversed.
	// This id was generate when that transaction was performed.
	TxnRefId string `protobuf:"bytes,1,opt,name=txn_ref_id,json=txnRefId,proto3" json:"txn_ref_id,omitempty"`
	// request_ref_id is unique request id passed by client for performing this reverse transaction
	RequestRefId string `protobuf:"bytes,2,opt,name=request_ref_id,json=requestRefId,proto3" json:"request_ref_id,omitempty"`
}

func (x *ReverseTransactionRequest) Reset() {
	*x = ReverseTransactionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReverseTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReverseTransactionRequest) ProtoMessage() {}

func (x *ReverseTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReverseTransactionRequest.ProtoReflect.Descriptor instead.
func (*ReverseTransactionRequest) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{4}
}

func (x *ReverseTransactionRequest) GetTxnRefId() string {
	if x != nil {
		return x.TxnRefId
	}
	return ""
}

func (x *ReverseTransactionRequest) GetRequestRefId() string {
	if x != nil {
		return x.RequestRefId
	}
	return ""
}

// ReverseTransactionRequest is the response for ReverseTransactionRequest
type ReverseTransactionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status represents status of request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// reverse_transaction_id denotes the id of reverse transaction performed.
	ReverseTransactionId string `protobuf:"bytes,2,opt,name=reverse_transaction_id,json=reverseTransactionId,proto3" json:"reverse_transaction_id,omitempty"`
	// transaction_status represents status of requested reverse transaction.
	TransactionStatus TransactionStatus `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,proto3,enum=accrual.TransactionStatus" json:"transaction_status,omitempty"`
}

func (x *ReverseTransactionResponse) Reset() {
	*x = ReverseTransactionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReverseTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReverseTransactionResponse) ProtoMessage() {}

func (x *ReverseTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReverseTransactionResponse.ProtoReflect.Descriptor instead.
func (*ReverseTransactionResponse) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{5}
}

func (x *ReverseTransactionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ReverseTransactionResponse) GetReverseTransactionId() string {
	if x != nil {
		return x.ReverseTransactionId
	}
	return ""
}

func (x *ReverseTransactionResponse) GetTransactionStatus() TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED
}

type GetAccountDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor_id is actor_id is actor's id into/from who's account the given
	// amount should be credited/debited
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// account_type represents type to account
	// an actor can have accounts of different account api.typesv2.
	// there is only one account type for now.
	AccountType AccountType `protobuf:"varint,2,opt,name=account_type,json=accountType,proto3,enum=accrual.AccountType" json:"account_type,omitempty"`
}

func (x *GetAccountDetailsRequest) Reset() {
	*x = GetAccountDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountDetailsRequest) ProtoMessage() {}

func (x *GetAccountDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetAccountDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetAccountDetailsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAccountDetailsRequest) GetAccountType() AccountType {
	if x != nil {
		return x.AccountType
	}
	return AccountType_ACCOUNT_TYPE_UNSPECIFIED
}

// ReverseTransactionRequest is the response for ReverseTransactionRequest
type GetAccountDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status represents status of request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// account identifier
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// account balance
	// Deprecated in favour of current_balance
	//
	// Deprecated: Do not use.
	AccountBalance int32 `protobuf:"varint,3,opt,name=account_balance,json=accountBalance,proto3" json:"account_balance,omitempty"`
	// next_expiry_time denotes latest when some or all the points will expire
	NextExpiryTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=next_expiry_time,json=nextExpiryTime,proto3" json:"next_expiry_time,omitempty"`
	// denotes amount of balance present in the account
	CurrentBalance int32 `protobuf:"varint,5,opt,name=current_balance,json=currentBalance,proto3" json:"current_balance,omitempty"`
	// denotes the amount of balance available for usage from the account.
	// available balance is the current balance minus any pending debits that haven’t yet been processed.
	AvailableBalance int32 `protobuf:"varint,6,opt,name=available_balance,json=availableBalance,proto3" json:"available_balance,omitempty"`
	// account_operational_status denotes the operational status of the user account.
	AccountOperationalStatus AccountOperationalStatus `protobuf:"varint,7,opt,name=account_operational_status,json=accountOperationalStatus,proto3,enum=accrual.AccountOperationalStatus" json:"account_operational_status,omitempty"`
}

func (x *GetAccountDetailsResponse) Reset() {
	*x = GetAccountDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountDetailsResponse) ProtoMessage() {}

func (x *GetAccountDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetAccountDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetAccountDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAccountDetailsResponse) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

// Deprecated: Do not use.
func (x *GetAccountDetailsResponse) GetAccountBalance() int32 {
	if x != nil {
		return x.AccountBalance
	}
	return 0
}

func (x *GetAccountDetailsResponse) GetNextExpiryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.NextExpiryTime
	}
	return nil
}

func (x *GetAccountDetailsResponse) GetCurrentBalance() int32 {
	if x != nil {
		return x.CurrentBalance
	}
	return 0
}

func (x *GetAccountDetailsResponse) GetAvailableBalance() int32 {
	if x != nil {
		return x.AvailableBalance
	}
	return 0
}

func (x *GetAccountDetailsResponse) GetAccountOperationalStatus() AccountOperationalStatus {
	if x != nil {
		return x.AccountOperationalStatus
	}
	return AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_UNSPECIFIED
}

type ResolveBalanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account identifier
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *ResolveBalanceRequest) Reset() {
	*x = ResolveBalanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResolveBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveBalanceRequest) ProtoMessage() {}

func (x *ResolveBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveBalanceRequest.ProtoReflect.Descriptor instead.
func (*ResolveBalanceRequest) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{8}
}

func (x *ResolveBalanceRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type ResolveBalanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status represents status of request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ResolveBalanceResponse) Reset() {
	*x = ResolveBalanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResolveBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveBalanceResponse) ProtoMessage() {}

func (x *ResolveBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveBalanceResponse.ProtoReflect.Descriptor instead.
func (*ResolveBalanceResponse) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{9}
}

func (x *ResolveBalanceResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DebitFiCoinsForAccountClosureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *DebitFiCoinsForAccountClosureRequest) Reset() {
	*x = DebitFiCoinsForAccountClosureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebitFiCoinsForAccountClosureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebitFiCoinsForAccountClosureRequest) ProtoMessage() {}

func (x *DebitFiCoinsForAccountClosureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebitFiCoinsForAccountClosureRequest.ProtoReflect.Descriptor instead.
func (*DebitFiCoinsForAccountClosureRequest) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{10}
}

func (x *DebitFiCoinsForAccountClosureRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type DebitFiCoinsForAccountClosureResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status represents status of request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DebitFiCoinsForAccountClosureResponse) Reset() {
	*x = DebitFiCoinsForAccountClosureResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebitFiCoinsForAccountClosureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebitFiCoinsForAccountClosureResponse) ProtoMessage() {}

func (x *DebitFiCoinsForAccountClosureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebitFiCoinsForAccountClosureResponse.ProtoReflect.Descriptor instead.
func (*DebitFiCoinsForAccountClosureResponse) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{11}
}

func (x *DebitFiCoinsForAccountClosureResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CloseAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique system identifier for a user
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// account_type represents the type of accrual account whose balance needs to be debited
	AccountType AccountType `protobuf:"varint,2,opt,name=account_type,json=accountType,proto3,enum=accrual.AccountType" json:"account_type,omitempty"`
}

func (x *CloseAccountRequest) Reset() {
	*x = CloseAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseAccountRequest) ProtoMessage() {}

func (x *CloseAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseAccountRequest.ProtoReflect.Descriptor instead.
func (*CloseAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{12}
}

func (x *CloseAccountRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CloseAccountRequest) GetAccountType() AccountType {
	if x != nil {
		return x.AccountType
	}
	return AccountType_ACCOUNT_TYPE_UNSPECIFIED
}

type CloseAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status represents status of request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CloseAccountResponse) Reset() {
	*x = CloseAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseAccountResponse) ProtoMessage() {}

func (x *CloseAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseAccountResponse.ProtoReflect.Descriptor instead.
func (*CloseAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{13}
}

func (x *CloseAccountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetFiCoinsToPointsMigrationDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetFiCoinsToPointsMigrationDetailsRequest) Reset() {
	*x = GetFiCoinsToPointsMigrationDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFiCoinsToPointsMigrationDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFiCoinsToPointsMigrationDetailsRequest) ProtoMessage() {}

func (x *GetFiCoinsToPointsMigrationDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFiCoinsToPointsMigrationDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetFiCoinsToPointsMigrationDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetFiCoinsToPointsMigrationDetailsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetFiCoinsToPointsMigrationDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status represents status of request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// total fi coins migrated to fi points
	TotalFiCoinsMigrated int32 `protobuf:"varint,2,opt,name=total_fi_coins_migrated,json=totalFiCoinsMigrated,proto3" json:"total_fi_coins_migrated,omitempty"`
	// total fi points migrated from fi coins
	TotalFiPointsMigrated int32 `protobuf:"varint,3,opt,name=total_fi_points_migrated,json=totalFiPointsMigrated,proto3" json:"total_fi_points_migrated,omitempty"`
}

func (x *GetFiCoinsToPointsMigrationDetailsResponse) Reset() {
	*x = GetFiCoinsToPointsMigrationDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFiCoinsToPointsMigrationDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFiCoinsToPointsMigrationDetailsResponse) ProtoMessage() {}

func (x *GetFiCoinsToPointsMigrationDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFiCoinsToPointsMigrationDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetFiCoinsToPointsMigrationDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetFiCoinsToPointsMigrationDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFiCoinsToPointsMigrationDetailsResponse) GetTotalFiCoinsMigrated() int32 {
	if x != nil {
		return x.TotalFiCoinsMigrated
	}
	return 0
}

func (x *GetFiCoinsToPointsMigrationDetailsResponse) GetTotalFiPointsMigrated() int32 {
	if x != nil {
		return x.TotalFiPointsMigrated
	}
	return 0
}

type TransactRequest_Options struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// todo (utkarsh) : rename it with a better name
	// is_pending_debit_allowed denotes if a pending debit txn should be allowed or not,
	// if false, then debit txn is ONLY allowed if sufficient balance is currently available in the account.
	// if true, then debit txn is allowed even if sufficient balance is not currently available in the account, the txn remains
	// in 'PENDING' state till the complete txn amount is debited from the account.
	IsPendingDebitAllowed bool `protobuf:"varint,1,opt,name=is_pending_debit_allowed,json=isPendingDebitAllowed,proto3" json:"is_pending_debit_allowed,omitempty"`
}

func (x *TransactRequest_Options) Reset() {
	*x = TransactRequest_Options{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_accrual_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactRequest_Options) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactRequest_Options) ProtoMessage() {}

func (x *TransactRequest_Options) ProtoReflect() protoreflect.Message {
	mi := &file_api_accrual_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactRequest_Options.ProtoReflect.Descriptor instead.
func (*TransactRequest_Options) Descriptor() ([]byte, []int) {
	return file_api_accrual_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *TransactRequest_Options) GetIsPendingDebitAllowed() bool {
	if x != nil {
		return x.IsPendingDebitAllowed
	}
	return false
}

var File_api_accrual_service_proto protoreflect.FileDescriptor

var file_api_accrual_service_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x63, 0x63,
	0x72, 0x75, 0x61, 0x6c, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61,
	0x6c, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9b,
	0x04, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2d, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65,
	0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x66, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x48, 0x0a, 0x12, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x41, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x4d, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e,
	0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20,
	0x00, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x3a, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x38,
	0x0a, 0x0d, 0x74, 0x78, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e,
	0x54, 0x78, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x74, 0x78, 0x6e,
	0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x42, 0x0a, 0x07, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x69, 0x73, 0x5f, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x73, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x62, 0x69, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x22, 0xa9, 0x01, 0x0a,
	0x10, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x49, 0x0a,
	0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x63, 0x63, 0x72,
	0x75, 0x61, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x43, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x66, 0x49, 0x64, 0x22, 0x9c, 0x01,
	0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x12, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x22, 0x71, 0x0a, 0x19,
	0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0a, 0x74, 0x78, 0x6e,
	0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x74, 0x78, 0x6e, 0x52, 0x65, 0x66, 0x49, 0x64,
	0x12, 0x2d, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x66, 0x49, 0x64, 0x22,
	0xc2, 0x01, 0x0a, 0x1a, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x12, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x63,
	0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x89, 0x03, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x10, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6e,
	0x65, 0x78, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x5f, 0x0a, 0x1a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61,
	0x6c, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x18, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x36, 0x0a, 0x15, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x3d, 0x0a, 0x16,
	0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x41, 0x0a, 0x24, 0x44,
	0x65, 0x62, 0x69, 0x74, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x4c,
	0x0a, 0x25, 0x44, 0x65, 0x62, 0x69, 0x74, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x46, 0x6f,
	0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x7c, 0x0a, 0x13,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x3b, 0x0a, 0x14, 0x43, 0x6c,
	0x6f, 0x73, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x46, 0x0a, 0x29, 0x47, 0x65, 0x74, 0x46, 0x69,
	0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x4d, 0x69, 0x67,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22,
	0xc1, 0x01, 0x0a, 0x2a, 0x47, 0x65, 0x74, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x17, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x5f,
	0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x69, 0x43, 0x6f, 0x69,
	0x6e, 0x73, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x18, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x6d, 0x69,
	0x67, 0x72, 0x61, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x46, 0x69, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x4d, 0x69, 0x67, 0x72, 0x61,
	0x74, 0x65, 0x64, 0x32, 0x90, 0x06, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x12,
	0x3f, 0x0a, 0x08, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x12, 0x18, 0x2e, 0x61, 0x63,
	0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x5d, 0x0a, 0x12, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c,
	0x2e, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x63, 0x63,
	0x72, 0x75, 0x61, 0x6c, 0x2e, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x53, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x2e, 0x61, 0x63, 0x63, 0x72,
	0x75, 0x61, 0x6c, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x21, 0x2e, 0x61, 0x63, 0x63, 0x72,
	0x75, 0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61,
	0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x51, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x1e, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x6c, 0x76, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x6c, 0x76, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x1d, 0x44, 0x65, 0x62, 0x69, 0x74, 0x46, 0x69, 0x43,
	0x6f, 0x69, 0x6e, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x2d, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e,
	0x44, 0x65, 0x62, 0x69, 0x74, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x46, 0x6f, 0x72, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e, 0x44,
	0x65, 0x62, 0x69, 0x74, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x8d, 0x01, 0x0a, 0x22, 0x47, 0x65,
	0x74, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x32, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69,
	0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x4d, 0x69, 0x67,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x2e, 0x47,
	0x65, 0x74, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0c, 0x43, 0x6c, 0x6f,
	0x73, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x2e, 0x61, 0x63, 0x63, 0x72,
	0x75, 0x61, 0x6c, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61,
	0x6c, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c, 0x5a, 0x22, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x72, 0x75, 0x61, 0x6c,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_accrual_service_proto_rawDescOnce sync.Once
	file_api_accrual_service_proto_rawDescData = file_api_accrual_service_proto_rawDesc
)

func file_api_accrual_service_proto_rawDescGZIP() []byte {
	file_api_accrual_service_proto_rawDescOnce.Do(func() {
		file_api_accrual_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_accrual_service_proto_rawDescData)
	})
	return file_api_accrual_service_proto_rawDescData
}

var file_api_accrual_service_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_api_accrual_service_proto_goTypes = []interface{}{
	(*TransactRequest)(nil),                            // 0: accrual.TransactRequest
	(*TransactResponse)(nil),                           // 1: accrual.TransactResponse
	(*CheckStatusRequest)(nil),                         // 2: accrual.CheckStatusRequest
	(*CheckStatusResponse)(nil),                        // 3: accrual.CheckStatusResponse
	(*ReverseTransactionRequest)(nil),                  // 4: accrual.ReverseTransactionRequest
	(*ReverseTransactionResponse)(nil),                 // 5: accrual.ReverseTransactionResponse
	(*GetAccountDetailsRequest)(nil),                   // 6: accrual.GetAccountDetailsRequest
	(*GetAccountDetailsResponse)(nil),                  // 7: accrual.GetAccountDetailsResponse
	(*ResolveBalanceRequest)(nil),                      // 8: accrual.ResolveBalanceRequest
	(*ResolveBalanceResponse)(nil),                     // 9: accrual.ResolveBalanceResponse
	(*DebitFiCoinsForAccountClosureRequest)(nil),       // 10: accrual.DebitFiCoinsForAccountClosureRequest
	(*DebitFiCoinsForAccountClosureResponse)(nil),      // 11: accrual.DebitFiCoinsForAccountClosureResponse
	(*CloseAccountRequest)(nil),                        // 12: accrual.CloseAccountRequest
	(*CloseAccountResponse)(nil),                       // 13: accrual.CloseAccountResponse
	(*GetFiCoinsToPointsMigrationDetailsRequest)(nil),  // 14: accrual.GetFiCoinsToPointsMigrationDetailsRequest
	(*GetFiCoinsToPointsMigrationDetailsResponse)(nil), // 15: accrual.GetFiCoinsToPointsMigrationDetailsResponse
	(*TransactRequest_Options)(nil),                    // 16: accrual.TransactRequest.Options
	(*timestamppb.Timestamp)(nil),                      // 17: google.protobuf.Timestamp
	(AccountType)(0),                                   // 18: accrual.AccountType
	(TransactionType)(0),                               // 19: accrual.TransactionType
	(*TxnMetaData)(nil),                                // 20: accrual.TxnMetaData
	(*rpc.Status)(nil),                                 // 21: rpc.Status
	(TransactionStatus)(0),                             // 22: accrual.TransactionStatus
	(AccountOperationalStatus)(0),                      // 23: accrual.AccountOperationalStatus
}
var file_api_accrual_service_proto_depIdxs = []int32{
	17, // 0: accrual.TransactRequest.amount_expiry_time:type_name -> google.protobuf.Timestamp
	18, // 1: accrual.TransactRequest.account_type:type_name -> accrual.AccountType
	19, // 2: accrual.TransactRequest.transaction_type:type_name -> accrual.TransactionType
	16, // 3: accrual.TransactRequest.options:type_name -> accrual.TransactRequest.Options
	20, // 4: accrual.TransactRequest.txn_meta_data:type_name -> accrual.TxnMetaData
	21, // 5: accrual.TransactResponse.status:type_name -> rpc.Status
	22, // 6: accrual.TransactResponse.transaction_status:type_name -> accrual.TransactionStatus
	21, // 7: accrual.CheckStatusResponse.status:type_name -> rpc.Status
	22, // 8: accrual.CheckStatusResponse.transaction_status:type_name -> accrual.TransactionStatus
	21, // 9: accrual.ReverseTransactionResponse.status:type_name -> rpc.Status
	22, // 10: accrual.ReverseTransactionResponse.transaction_status:type_name -> accrual.TransactionStatus
	18, // 11: accrual.GetAccountDetailsRequest.account_type:type_name -> accrual.AccountType
	21, // 12: accrual.GetAccountDetailsResponse.status:type_name -> rpc.Status
	17, // 13: accrual.GetAccountDetailsResponse.next_expiry_time:type_name -> google.protobuf.Timestamp
	23, // 14: accrual.GetAccountDetailsResponse.account_operational_status:type_name -> accrual.AccountOperationalStatus
	21, // 15: accrual.ResolveBalanceResponse.status:type_name -> rpc.Status
	21, // 16: accrual.DebitFiCoinsForAccountClosureResponse.status:type_name -> rpc.Status
	18, // 17: accrual.CloseAccountRequest.account_type:type_name -> accrual.AccountType
	21, // 18: accrual.CloseAccountResponse.status:type_name -> rpc.Status
	21, // 19: accrual.GetFiCoinsToPointsMigrationDetailsResponse.status:type_name -> rpc.Status
	0,  // 20: accrual.Accrual.Transact:input_type -> accrual.TransactRequest
	4,  // 21: accrual.Accrual.ReverseTransaction:input_type -> accrual.ReverseTransactionRequest
	2,  // 22: accrual.Accrual.CheckTransactionStatus:input_type -> accrual.CheckStatusRequest
	6,  // 23: accrual.Accrual.GetAccountDetails:input_type -> accrual.GetAccountDetailsRequest
	8,  // 24: accrual.Accrual.ResolveBalance:input_type -> accrual.ResolveBalanceRequest
	10, // 25: accrual.Accrual.DebitFiCoinsForAccountClosure:input_type -> accrual.DebitFiCoinsForAccountClosureRequest
	14, // 26: accrual.Accrual.GetFiCoinsToPointsMigrationDetails:input_type -> accrual.GetFiCoinsToPointsMigrationDetailsRequest
	12, // 27: accrual.Accrual.CloseAccount:input_type -> accrual.CloseAccountRequest
	1,  // 28: accrual.Accrual.Transact:output_type -> accrual.TransactResponse
	5,  // 29: accrual.Accrual.ReverseTransaction:output_type -> accrual.ReverseTransactionResponse
	3,  // 30: accrual.Accrual.CheckTransactionStatus:output_type -> accrual.CheckStatusResponse
	7,  // 31: accrual.Accrual.GetAccountDetails:output_type -> accrual.GetAccountDetailsResponse
	9,  // 32: accrual.Accrual.ResolveBalance:output_type -> accrual.ResolveBalanceResponse
	11, // 33: accrual.Accrual.DebitFiCoinsForAccountClosure:output_type -> accrual.DebitFiCoinsForAccountClosureResponse
	15, // 34: accrual.Accrual.GetFiCoinsToPointsMigrationDetails:output_type -> accrual.GetFiCoinsToPointsMigrationDetailsResponse
	13, // 35: accrual.Accrual.CloseAccount:output_type -> accrual.CloseAccountResponse
	28, // [28:36] is the sub-list for method output_type
	20, // [20:28] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_api_accrual_service_proto_init() }
func file_api_accrual_service_proto_init() {
	if File_api_accrual_service_proto != nil {
		return
	}
	file_api_accrual_account_proto_init()
	file_api_accrual_transaction_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_accrual_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReverseTransactionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReverseTransactionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResolveBalanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResolveBalanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebitFiCoinsForAccountClosureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebitFiCoinsForAccountClosureResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFiCoinsToPointsMigrationDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFiCoinsToPointsMigrationDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_accrual_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactRequest_Options); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_accrual_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_accrual_service_proto_goTypes,
		DependencyIndexes: file_api_accrual_service_proto_depIdxs,
		MessageInfos:      file_api_accrual_service_proto_msgTypes,
	}.Build()
	File_api_accrual_service_proto = out.File
	file_api_accrual_service_proto_rawDesc = nil
	file_api_accrual_service_proto_goTypes = nil
	file_api_accrual_service_proto_depIdxs = nil
}
