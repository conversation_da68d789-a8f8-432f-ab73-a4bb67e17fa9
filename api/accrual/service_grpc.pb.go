// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v4.23.4
// source: api/accrual/service.proto

package accrual

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AccrualClient is the client API for Accrual service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccrualClient interface {
	// Transact method performs a transaction of some given amount on a given actor's account.
	Transact(ctx context.Context, in *TransactRequest, opts ...grpc.CallOption) (*TransactResponse, error)
	// ReverseTransaction method performs reversal of an already performed transaction.
	ReverseTransaction(ctx context.Context, in *ReverseTransactionRequest, opts ...grpc.CallOption) (*ReverseTransactionResponse, error)
	// CheckTransactionStatus method for checks whether some transaction was performed or not
	// for a given requestRefId.
	CheckTransactionStatus(ctx context.Context, in *CheckStatusRequest, opts ...grpc.CallOption) (*CheckStatusResponse, error)
	// GetAccountDetails accepts actor_id and account type and returns account details
	GetAccountDetails(ctx context.Context, in *GetAccountDetailsRequest, opts ...grpc.CallOption) (*GetAccountDetailsResponse, error)
	// ResolveBalance method will resolve the balance for given account id.
	ResolveBalance(ctx context.Context, in *ResolveBalanceRequest, opts ...grpc.CallOption) (*ResolveBalanceResponse, error)
	// Deprecated: Do not use.
	// DebitFiCoinsForAccountClosure will make a debit txn against actor's total fi coins balance.
	// this is explicitly used when actor's account closed.
	DebitFiCoinsForAccountClosure(ctx context.Context, in *DebitFiCoinsForAccountClosureRequest, opts ...grpc.CallOption) (*DebitFiCoinsForAccountClosureResponse, error)
	// GetMigrationBalanceHistory will return the balance of the actor's account in fi coins and fi points at the date of migration.
	GetFiCoinsToPointsMigrationDetails(ctx context.Context, in *GetFiCoinsToPointsMigrationDetailsRequest, opts ...grpc.CallOption) (*GetFiCoinsToPointsMigrationDetailsResponse, error)
	// CloseAccount will debit all the available balance in the account and mark the operational status of the account as CLOSED.
	CloseAccount(ctx context.Context, in *CloseAccountRequest, opts ...grpc.CallOption) (*CloseAccountResponse, error)
}

type accrualClient struct {
	cc grpc.ClientConnInterface
}

func NewAccrualClient(cc grpc.ClientConnInterface) AccrualClient {
	return &accrualClient{cc}
}

func (c *accrualClient) Transact(ctx context.Context, in *TransactRequest, opts ...grpc.CallOption) (*TransactResponse, error) {
	out := new(TransactResponse)
	err := c.cc.Invoke(ctx, "/accrual.Accrual/Transact", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accrualClient) ReverseTransaction(ctx context.Context, in *ReverseTransactionRequest, opts ...grpc.CallOption) (*ReverseTransactionResponse, error) {
	out := new(ReverseTransactionResponse)
	err := c.cc.Invoke(ctx, "/accrual.Accrual/ReverseTransaction", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accrualClient) CheckTransactionStatus(ctx context.Context, in *CheckStatusRequest, opts ...grpc.CallOption) (*CheckStatusResponse, error) {
	out := new(CheckStatusResponse)
	err := c.cc.Invoke(ctx, "/accrual.Accrual/CheckTransactionStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accrualClient) GetAccountDetails(ctx context.Context, in *GetAccountDetailsRequest, opts ...grpc.CallOption) (*GetAccountDetailsResponse, error) {
	out := new(GetAccountDetailsResponse)
	err := c.cc.Invoke(ctx, "/accrual.Accrual/GetAccountDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accrualClient) ResolveBalance(ctx context.Context, in *ResolveBalanceRequest, opts ...grpc.CallOption) (*ResolveBalanceResponse, error) {
	out := new(ResolveBalanceResponse)
	err := c.cc.Invoke(ctx, "/accrual.Accrual/ResolveBalance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *accrualClient) DebitFiCoinsForAccountClosure(ctx context.Context, in *DebitFiCoinsForAccountClosureRequest, opts ...grpc.CallOption) (*DebitFiCoinsForAccountClosureResponse, error) {
	out := new(DebitFiCoinsForAccountClosureResponse)
	err := c.cc.Invoke(ctx, "/accrual.Accrual/DebitFiCoinsForAccountClosure", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accrualClient) GetFiCoinsToPointsMigrationDetails(ctx context.Context, in *GetFiCoinsToPointsMigrationDetailsRequest, opts ...grpc.CallOption) (*GetFiCoinsToPointsMigrationDetailsResponse, error) {
	out := new(GetFiCoinsToPointsMigrationDetailsResponse)
	err := c.cc.Invoke(ctx, "/accrual.Accrual/GetFiCoinsToPointsMigrationDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accrualClient) CloseAccount(ctx context.Context, in *CloseAccountRequest, opts ...grpc.CallOption) (*CloseAccountResponse, error) {
	out := new(CloseAccountResponse)
	err := c.cc.Invoke(ctx, "/accrual.Accrual/CloseAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccrualServer is the server API for Accrual service.
// All implementations should embed UnimplementedAccrualServer
// for forward compatibility
type AccrualServer interface {
	// Transact method performs a transaction of some given amount on a given actor's account.
	Transact(context.Context, *TransactRequest) (*TransactResponse, error)
	// ReverseTransaction method performs reversal of an already performed transaction.
	ReverseTransaction(context.Context, *ReverseTransactionRequest) (*ReverseTransactionResponse, error)
	// CheckTransactionStatus method for checks whether some transaction was performed or not
	// for a given requestRefId.
	CheckTransactionStatus(context.Context, *CheckStatusRequest) (*CheckStatusResponse, error)
	// GetAccountDetails accepts actor_id and account type and returns account details
	GetAccountDetails(context.Context, *GetAccountDetailsRequest) (*GetAccountDetailsResponse, error)
	// ResolveBalance method will resolve the balance for given account id.
	ResolveBalance(context.Context, *ResolveBalanceRequest) (*ResolveBalanceResponse, error)
	// Deprecated: Do not use.
	// DebitFiCoinsForAccountClosure will make a debit txn against actor's total fi coins balance.
	// this is explicitly used when actor's account closed.
	DebitFiCoinsForAccountClosure(context.Context, *DebitFiCoinsForAccountClosureRequest) (*DebitFiCoinsForAccountClosureResponse, error)
	// GetMigrationBalanceHistory will return the balance of the actor's account in fi coins and fi points at the date of migration.
	GetFiCoinsToPointsMigrationDetails(context.Context, *GetFiCoinsToPointsMigrationDetailsRequest) (*GetFiCoinsToPointsMigrationDetailsResponse, error)
	// CloseAccount will debit all the available balance in the account and mark the operational status of the account as CLOSED.
	CloseAccount(context.Context, *CloseAccountRequest) (*CloseAccountResponse, error)
}

// UnimplementedAccrualServer should be embedded to have forward compatible implementations.
type UnimplementedAccrualServer struct {
}

func (UnimplementedAccrualServer) Transact(context.Context, *TransactRequest) (*TransactResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Transact not implemented")
}
func (UnimplementedAccrualServer) ReverseTransaction(context.Context, *ReverseTransactionRequest) (*ReverseTransactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReverseTransaction not implemented")
}
func (UnimplementedAccrualServer) CheckTransactionStatus(context.Context, *CheckStatusRequest) (*CheckStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTransactionStatus not implemented")
}
func (UnimplementedAccrualServer) GetAccountDetails(context.Context, *GetAccountDetailsRequest) (*GetAccountDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountDetails not implemented")
}
func (UnimplementedAccrualServer) ResolveBalance(context.Context, *ResolveBalanceRequest) (*ResolveBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResolveBalance not implemented")
}
func (UnimplementedAccrualServer) DebitFiCoinsForAccountClosure(context.Context, *DebitFiCoinsForAccountClosureRequest) (*DebitFiCoinsForAccountClosureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DebitFiCoinsForAccountClosure not implemented")
}
func (UnimplementedAccrualServer) GetFiCoinsToPointsMigrationDetails(context.Context, *GetFiCoinsToPointsMigrationDetailsRequest) (*GetFiCoinsToPointsMigrationDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFiCoinsToPointsMigrationDetails not implemented")
}
func (UnimplementedAccrualServer) CloseAccount(context.Context, *CloseAccountRequest) (*CloseAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseAccount not implemented")
}

// UnsafeAccrualServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccrualServer will
// result in compilation errors.
type UnsafeAccrualServer interface {
	mustEmbedUnimplementedAccrualServer()
}

func RegisterAccrualServer(s grpc.ServiceRegistrar, srv AccrualServer) {
	s.RegisterService(&Accrual_ServiceDesc, srv)
}

func _Accrual_Transact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccrualServer).Transact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/accrual.Accrual/Transact",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccrualServer).Transact(ctx, req.(*TransactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Accrual_ReverseTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReverseTransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccrualServer).ReverseTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/accrual.Accrual/ReverseTransaction",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccrualServer).ReverseTransaction(ctx, req.(*ReverseTransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Accrual_CheckTransactionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccrualServer).CheckTransactionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/accrual.Accrual/CheckTransactionStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccrualServer).CheckTransactionStatus(ctx, req.(*CheckStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Accrual_GetAccountDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccrualServer).GetAccountDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/accrual.Accrual/GetAccountDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccrualServer).GetAccountDetails(ctx, req.(*GetAccountDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Accrual_ResolveBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResolveBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccrualServer).ResolveBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/accrual.Accrual/ResolveBalance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccrualServer).ResolveBalance(ctx, req.(*ResolveBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Accrual_DebitFiCoinsForAccountClosure_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DebitFiCoinsForAccountClosureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccrualServer).DebitFiCoinsForAccountClosure(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/accrual.Accrual/DebitFiCoinsForAccountClosure",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccrualServer).DebitFiCoinsForAccountClosure(ctx, req.(*DebitFiCoinsForAccountClosureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Accrual_GetFiCoinsToPointsMigrationDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFiCoinsToPointsMigrationDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccrualServer).GetFiCoinsToPointsMigrationDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/accrual.Accrual/GetFiCoinsToPointsMigrationDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccrualServer).GetFiCoinsToPointsMigrationDetails(ctx, req.(*GetFiCoinsToPointsMigrationDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Accrual_CloseAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccrualServer).CloseAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/accrual.Accrual/CloseAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccrualServer).CloseAccount(ctx, req.(*CloseAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Accrual_ServiceDesc is the grpc.ServiceDesc for Accrual service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Accrual_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "accrual.Accrual",
	HandlerType: (*AccrualServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Transact",
			Handler:    _Accrual_Transact_Handler,
		},
		{
			MethodName: "ReverseTransaction",
			Handler:    _Accrual_ReverseTransaction_Handler,
		},
		{
			MethodName: "CheckTransactionStatus",
			Handler:    _Accrual_CheckTransactionStatus_Handler,
		},
		{
			MethodName: "GetAccountDetails",
			Handler:    _Accrual_GetAccountDetails_Handler,
		},
		{
			MethodName: "ResolveBalance",
			Handler:    _Accrual_ResolveBalance_Handler,
		},
		{
			MethodName: "DebitFiCoinsForAccountClosure",
			Handler:    _Accrual_DebitFiCoinsForAccountClosure_Handler,
		},
		{
			MethodName: "GetFiCoinsToPointsMigrationDetails",
			Handler:    _Accrual_GetFiCoinsToPointsMigrationDetails_Handler,
		},
		{
			MethodName: "CloseAccount",
			Handler:    _Accrual_CloseAccount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/accrual/service.proto",
}
