// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/accrual/transaction.proto

package accrual

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TxnMetaData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TxnMetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TxnMetaData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TxnMetaDataMultiError, or
// nil if none found.
func (m *TxnMetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *TxnMetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCoinToPoints()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TxnMetaDataValidationError{
					field:  "CoinToPoints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TxnMetaDataValidationError{
					field:  "CoinToPoints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCoinToPoints()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TxnMetaDataValidationError{
				field:  "CoinToPoints",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TxnMetaDataMultiError(errors)
	}

	return nil
}

// TxnMetaDataMultiError is an error wrapping multiple validation errors
// returned by TxnMetaData.ValidateAll() if the designated constraints aren't met.
type TxnMetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TxnMetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TxnMetaDataMultiError) AllErrors() []error { return m }

// TxnMetaDataValidationError is the validation error returned by
// TxnMetaData.Validate if the designated constraints aren't met.
type TxnMetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TxnMetaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TxnMetaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TxnMetaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TxnMetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TxnMetaDataValidationError) ErrorName() string { return "TxnMetaDataValidationError" }

// Error satisfies the builtin error interface
func (e TxnMetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTxnMetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TxnMetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TxnMetaDataValidationError{}

// Validate checks the field values on FiCoinsToFiPointsConversion with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FiCoinsToFiPointsConversion) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiCoinsToFiPointsConversion with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FiCoinsToFiPointsConversionMultiError, or nil if none found.
func (m *FiCoinsToFiPointsConversion) ValidateAll() error {
	return m.validate(true)
}

func (m *FiCoinsToFiPointsConversion) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CoinsConverted

	// no validation rules for IsCcUser

	if len(errors) > 0 {
		return FiCoinsToFiPointsConversionMultiError(errors)
	}

	return nil
}

// FiCoinsToFiPointsConversionMultiError is an error wrapping multiple
// validation errors returned by FiCoinsToFiPointsConversion.ValidateAll() if
// the designated constraints aren't met.
type FiCoinsToFiPointsConversionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiCoinsToFiPointsConversionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiCoinsToFiPointsConversionMultiError) AllErrors() []error { return m }

// FiCoinsToFiPointsConversionValidationError is the validation error returned
// by FiCoinsToFiPointsConversion.Validate if the designated constraints
// aren't met.
type FiCoinsToFiPointsConversionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiCoinsToFiPointsConversionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiCoinsToFiPointsConversionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiCoinsToFiPointsConversionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiCoinsToFiPointsConversionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiCoinsToFiPointsConversionValidationError) ErrorName() string {
	return "FiCoinsToFiPointsConversionValidationError"
}

// Error satisfies the builtin error interface
func (e FiCoinsToFiPointsConversionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiCoinsToFiPointsConversion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiCoinsToFiPointsConversionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiCoinsToFiPointsConversionValidationError{}
