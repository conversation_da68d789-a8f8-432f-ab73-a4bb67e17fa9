// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/accrual/service.proto

package accrual

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TransactRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TransactRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactRequestMultiError, or nil if none found.
func (m *TransactRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetRequestRefId()) < 1 {
		err := TransactRequestValidationError{
			field:  "RequestRefId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := TransactRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAmount() <= 0 {
		err := TransactRequestValidationError{
			field:  "Amount",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmountExpiryTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactRequestValidationError{
					field:  "AmountExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactRequestValidationError{
					field:  "AmountExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountExpiryTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactRequestValidationError{
				field:  "AmountExpiryTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _TransactRequest_AccountType_NotInLookup[m.GetAccountType()]; ok {
		err := TransactRequestValidationError{
			field:  "AccountType",
			reason: "value must not be in list [0]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _TransactRequest_TransactionType_NotInLookup[m.GetTransactionType()]; ok {
		err := TransactRequestValidationError{
			field:  "TransactionType",
			reason: "value must not be in list [0]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactRequestValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactRequestValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactRequestValidationError{
				field:  "Options",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTxnMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactRequestValidationError{
					field:  "TxnMetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactRequestValidationError{
					field:  "TxnMetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactRequestValidationError{
				field:  "TxnMetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TransactRequestMultiError(errors)
	}

	return nil
}

// TransactRequestMultiError is an error wrapping multiple validation errors
// returned by TransactRequest.ValidateAll() if the designated constraints
// aren't met.
type TransactRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactRequestMultiError) AllErrors() []error { return m }

// TransactRequestValidationError is the validation error returned by
// TransactRequest.Validate if the designated constraints aren't met.
type TransactRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactRequestValidationError) ErrorName() string { return "TransactRequestValidationError" }

// Error satisfies the builtin error interface
func (e TransactRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactRequestValidationError{}

var _TransactRequest_AccountType_NotInLookup = map[AccountType]struct{}{
	0: {},
}

var _TransactRequest_TransactionType_NotInLookup = map[TransactionType]struct{}{
	0: {},
}

// Validate checks the field values on TransactResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TransactResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactResponseMultiError, or nil if none found.
func (m *TransactResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionId

	// no validation rules for TransactionStatus

	if len(errors) > 0 {
		return TransactResponseMultiError(errors)
	}

	return nil
}

// TransactResponseMultiError is an error wrapping multiple validation errors
// returned by TransactResponse.ValidateAll() if the designated constraints
// aren't met.
type TransactResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactResponseMultiError) AllErrors() []error { return m }

// TransactResponseValidationError is the validation error returned by
// TransactResponse.Validate if the designated constraints aren't met.
type TransactResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactResponseValidationError) ErrorName() string { return "TransactResponseValidationError" }

// Error satisfies the builtin error interface
func (e TransactResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactResponseValidationError{}

// Validate checks the field values on CheckStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckStatusRequestMultiError, or nil if none found.
func (m *CheckStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetRequestRefId()) < 1 {
		err := CheckStatusRequestValidationError{
			field:  "RequestRefId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CheckStatusRequestMultiError(errors)
	}

	return nil
}

// CheckStatusRequestMultiError is an error wrapping multiple validation errors
// returned by CheckStatusRequest.ValidateAll() if the designated constraints
// aren't met.
type CheckStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckStatusRequestMultiError) AllErrors() []error { return m }

// CheckStatusRequestValidationError is the validation error returned by
// CheckStatusRequest.Validate if the designated constraints aren't met.
type CheckStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckStatusRequestValidationError) ErrorName() string {
	return "CheckStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckStatusRequestValidationError{}

// Validate checks the field values on CheckStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckStatusResponseMultiError, or nil if none found.
func (m *CheckStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionStatus

	// no validation rules for TxnId

	if len(errors) > 0 {
		return CheckStatusResponseMultiError(errors)
	}

	return nil
}

// CheckStatusResponseMultiError is an error wrapping multiple validation
// errors returned by CheckStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckStatusResponseMultiError) AllErrors() []error { return m }

// CheckStatusResponseValidationError is the validation error returned by
// CheckStatusResponse.Validate if the designated constraints aren't met.
type CheckStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckStatusResponseValidationError) ErrorName() string {
	return "CheckStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckStatusResponseValidationError{}

// Validate checks the field values on ReverseTransactionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReverseTransactionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReverseTransactionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReverseTransactionRequestMultiError, or nil if none found.
func (m *ReverseTransactionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReverseTransactionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetTxnRefId()) < 1 {
		err := ReverseTransactionRequestValidationError{
			field:  "TxnRefId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetRequestRefId()) < 1 {
		err := ReverseTransactionRequestValidationError{
			field:  "RequestRefId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ReverseTransactionRequestMultiError(errors)
	}

	return nil
}

// ReverseTransactionRequestMultiError is an error wrapping multiple validation
// errors returned by ReverseTransactionRequest.ValidateAll() if the
// designated constraints aren't met.
type ReverseTransactionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReverseTransactionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReverseTransactionRequestMultiError) AllErrors() []error { return m }

// ReverseTransactionRequestValidationError is the validation error returned by
// ReverseTransactionRequest.Validate if the designated constraints aren't met.
type ReverseTransactionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReverseTransactionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReverseTransactionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReverseTransactionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReverseTransactionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReverseTransactionRequestValidationError) ErrorName() string {
	return "ReverseTransactionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReverseTransactionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReverseTransactionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReverseTransactionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReverseTransactionRequestValidationError{}

// Validate checks the field values on ReverseTransactionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReverseTransactionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReverseTransactionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReverseTransactionResponseMultiError, or nil if none found.
func (m *ReverseTransactionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReverseTransactionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReverseTransactionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReverseTransactionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReverseTransactionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReverseTransactionId

	// no validation rules for TransactionStatus

	if len(errors) > 0 {
		return ReverseTransactionResponseMultiError(errors)
	}

	return nil
}

// ReverseTransactionResponseMultiError is an error wrapping multiple
// validation errors returned by ReverseTransactionResponse.ValidateAll() if
// the designated constraints aren't met.
type ReverseTransactionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReverseTransactionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReverseTransactionResponseMultiError) AllErrors() []error { return m }

// ReverseTransactionResponseValidationError is the validation error returned
// by ReverseTransactionResponse.Validate if the designated constraints aren't met.
type ReverseTransactionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReverseTransactionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReverseTransactionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReverseTransactionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReverseTransactionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReverseTransactionResponseValidationError) ErrorName() string {
	return "ReverseTransactionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReverseTransactionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReverseTransactionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReverseTransactionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReverseTransactionResponseValidationError{}

// Validate checks the field values on GetAccountDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountDetailsRequestMultiError, or nil if none found.
func (m *GetAccountDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetAccountDetailsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetAccountDetailsRequest_AccountType_NotInLookup[m.GetAccountType()]; ok {
		err := GetAccountDetailsRequestValidationError{
			field:  "AccountType",
			reason: "value must not be in list [0]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetAccountDetailsRequestMultiError(errors)
	}

	return nil
}

// GetAccountDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetAccountDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAccountDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountDetailsRequestMultiError) AllErrors() []error { return m }

// GetAccountDetailsRequestValidationError is the validation error returned by
// GetAccountDetailsRequest.Validate if the designated constraints aren't met.
type GetAccountDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountDetailsRequestValidationError) ErrorName() string {
	return "GetAccountDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountDetailsRequestValidationError{}

var _GetAccountDetailsRequest_AccountType_NotInLookup = map[AccountType]struct{}{
	0: {},
}

// Validate checks the field values on GetAccountDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountDetailsResponseMultiError, or nil if none found.
func (m *GetAccountDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountId

	// no validation rules for AccountBalance

	if all {
		switch v := interface{}(m.GetNextExpiryTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountDetailsResponseValidationError{
					field:  "NextExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountDetailsResponseValidationError{
					field:  "NextExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextExpiryTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountDetailsResponseValidationError{
				field:  "NextExpiryTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrentBalance

	// no validation rules for AvailableBalance

	// no validation rules for AccountOperationalStatus

	if len(errors) > 0 {
		return GetAccountDetailsResponseMultiError(errors)
	}

	return nil
}

// GetAccountDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetAccountDetailsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetAccountDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountDetailsResponseMultiError) AllErrors() []error { return m }

// GetAccountDetailsResponseValidationError is the validation error returned by
// GetAccountDetailsResponse.Validate if the designated constraints aren't met.
type GetAccountDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountDetailsResponseValidationError) ErrorName() string {
	return "GetAccountDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountDetailsResponseValidationError{}

// Validate checks the field values on ResolveBalanceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResolveBalanceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResolveBalanceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResolveBalanceRequestMultiError, or nil if none found.
func (m *ResolveBalanceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResolveBalanceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	if len(errors) > 0 {
		return ResolveBalanceRequestMultiError(errors)
	}

	return nil
}

// ResolveBalanceRequestMultiError is an error wrapping multiple validation
// errors returned by ResolveBalanceRequest.ValidateAll() if the designated
// constraints aren't met.
type ResolveBalanceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResolveBalanceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResolveBalanceRequestMultiError) AllErrors() []error { return m }

// ResolveBalanceRequestValidationError is the validation error returned by
// ResolveBalanceRequest.Validate if the designated constraints aren't met.
type ResolveBalanceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResolveBalanceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResolveBalanceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResolveBalanceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResolveBalanceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResolveBalanceRequestValidationError) ErrorName() string {
	return "ResolveBalanceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResolveBalanceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResolveBalanceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResolveBalanceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResolveBalanceRequestValidationError{}

// Validate checks the field values on ResolveBalanceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResolveBalanceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResolveBalanceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResolveBalanceResponseMultiError, or nil if none found.
func (m *ResolveBalanceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResolveBalanceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResolveBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResolveBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResolveBalanceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResolveBalanceResponseMultiError(errors)
	}

	return nil
}

// ResolveBalanceResponseMultiError is an error wrapping multiple validation
// errors returned by ResolveBalanceResponse.ValidateAll() if the designated
// constraints aren't met.
type ResolveBalanceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResolveBalanceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResolveBalanceResponseMultiError) AllErrors() []error { return m }

// ResolveBalanceResponseValidationError is the validation error returned by
// ResolveBalanceResponse.Validate if the designated constraints aren't met.
type ResolveBalanceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResolveBalanceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResolveBalanceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResolveBalanceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResolveBalanceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResolveBalanceResponseValidationError) ErrorName() string {
	return "ResolveBalanceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResolveBalanceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResolveBalanceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResolveBalanceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResolveBalanceResponseValidationError{}

// Validate checks the field values on DebitFiCoinsForAccountClosureRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DebitFiCoinsForAccountClosureRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DebitFiCoinsForAccountClosureRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DebitFiCoinsForAccountClosureRequestMultiError, or nil if none found.
func (m *DebitFiCoinsForAccountClosureRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DebitFiCoinsForAccountClosureRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return DebitFiCoinsForAccountClosureRequestMultiError(errors)
	}

	return nil
}

// DebitFiCoinsForAccountClosureRequestMultiError is an error wrapping multiple
// validation errors returned by
// DebitFiCoinsForAccountClosureRequest.ValidateAll() if the designated
// constraints aren't met.
type DebitFiCoinsForAccountClosureRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DebitFiCoinsForAccountClosureRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DebitFiCoinsForAccountClosureRequestMultiError) AllErrors() []error { return m }

// DebitFiCoinsForAccountClosureRequestValidationError is the validation error
// returned by DebitFiCoinsForAccountClosureRequest.Validate if the designated
// constraints aren't met.
type DebitFiCoinsForAccountClosureRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DebitFiCoinsForAccountClosureRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DebitFiCoinsForAccountClosureRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DebitFiCoinsForAccountClosureRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DebitFiCoinsForAccountClosureRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DebitFiCoinsForAccountClosureRequestValidationError) ErrorName() string {
	return "DebitFiCoinsForAccountClosureRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DebitFiCoinsForAccountClosureRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDebitFiCoinsForAccountClosureRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DebitFiCoinsForAccountClosureRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DebitFiCoinsForAccountClosureRequestValidationError{}

// Validate checks the field values on DebitFiCoinsForAccountClosureResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DebitFiCoinsForAccountClosureResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DebitFiCoinsForAccountClosureResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DebitFiCoinsForAccountClosureResponseMultiError, or nil if none found.
func (m *DebitFiCoinsForAccountClosureResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DebitFiCoinsForAccountClosureResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DebitFiCoinsForAccountClosureResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DebitFiCoinsForAccountClosureResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DebitFiCoinsForAccountClosureResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DebitFiCoinsForAccountClosureResponseMultiError(errors)
	}

	return nil
}

// DebitFiCoinsForAccountClosureResponseMultiError is an error wrapping
// multiple validation errors returned by
// DebitFiCoinsForAccountClosureResponse.ValidateAll() if the designated
// constraints aren't met.
type DebitFiCoinsForAccountClosureResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DebitFiCoinsForAccountClosureResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DebitFiCoinsForAccountClosureResponseMultiError) AllErrors() []error { return m }

// DebitFiCoinsForAccountClosureResponseValidationError is the validation error
// returned by DebitFiCoinsForAccountClosureResponse.Validate if the
// designated constraints aren't met.
type DebitFiCoinsForAccountClosureResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DebitFiCoinsForAccountClosureResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DebitFiCoinsForAccountClosureResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DebitFiCoinsForAccountClosureResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DebitFiCoinsForAccountClosureResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DebitFiCoinsForAccountClosureResponseValidationError) ErrorName() string {
	return "DebitFiCoinsForAccountClosureResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DebitFiCoinsForAccountClosureResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDebitFiCoinsForAccountClosureResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DebitFiCoinsForAccountClosureResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DebitFiCoinsForAccountClosureResponseValidationError{}

// Validate checks the field values on CloseAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CloseAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CloseAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CloseAccountRequestMultiError, or nil if none found.
func (m *CloseAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CloseAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := CloseAccountRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CloseAccountRequest_AccountType_NotInLookup[m.GetAccountType()]; ok {
		err := CloseAccountRequestValidationError{
			field:  "AccountType",
			reason: "value must not be in list [0]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CloseAccountRequestMultiError(errors)
	}

	return nil
}

// CloseAccountRequestMultiError is an error wrapping multiple validation
// errors returned by CloseAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type CloseAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CloseAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CloseAccountRequestMultiError) AllErrors() []error { return m }

// CloseAccountRequestValidationError is the validation error returned by
// CloseAccountRequest.Validate if the designated constraints aren't met.
type CloseAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CloseAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CloseAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CloseAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CloseAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CloseAccountRequestValidationError) ErrorName() string {
	return "CloseAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CloseAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCloseAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CloseAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CloseAccountRequestValidationError{}

var _CloseAccountRequest_AccountType_NotInLookup = map[AccountType]struct{}{
	0: {},
}

// Validate checks the field values on CloseAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CloseAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CloseAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CloseAccountResponseMultiError, or nil if none found.
func (m *CloseAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CloseAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CloseAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CloseAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CloseAccountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CloseAccountResponseMultiError(errors)
	}

	return nil
}

// CloseAccountResponseMultiError is an error wrapping multiple validation
// errors returned by CloseAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type CloseAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CloseAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CloseAccountResponseMultiError) AllErrors() []error { return m }

// CloseAccountResponseValidationError is the validation error returned by
// CloseAccountResponse.Validate if the designated constraints aren't met.
type CloseAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CloseAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CloseAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CloseAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CloseAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CloseAccountResponseValidationError) ErrorName() string {
	return "CloseAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CloseAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCloseAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CloseAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CloseAccountResponseValidationError{}

// Validate checks the field values on
// GetFiCoinsToPointsMigrationDetailsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFiCoinsToPointsMigrationDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFiCoinsToPointsMigrationDetailsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetFiCoinsToPointsMigrationDetailsRequestMultiError, or nil if none found.
func (m *GetFiCoinsToPointsMigrationDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFiCoinsToPointsMigrationDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetFiCoinsToPointsMigrationDetailsRequestMultiError(errors)
	}

	return nil
}

// GetFiCoinsToPointsMigrationDetailsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetFiCoinsToPointsMigrationDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFiCoinsToPointsMigrationDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFiCoinsToPointsMigrationDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFiCoinsToPointsMigrationDetailsRequestMultiError) AllErrors() []error { return m }

// GetFiCoinsToPointsMigrationDetailsRequestValidationError is the validation
// error returned by GetFiCoinsToPointsMigrationDetailsRequest.Validate if the
// designated constraints aren't met.
type GetFiCoinsToPointsMigrationDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFiCoinsToPointsMigrationDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFiCoinsToPointsMigrationDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFiCoinsToPointsMigrationDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFiCoinsToPointsMigrationDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFiCoinsToPointsMigrationDetailsRequestValidationError) ErrorName() string {
	return "GetFiCoinsToPointsMigrationDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFiCoinsToPointsMigrationDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFiCoinsToPointsMigrationDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFiCoinsToPointsMigrationDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFiCoinsToPointsMigrationDetailsRequestValidationError{}

// Validate checks the field values on
// GetFiCoinsToPointsMigrationDetailsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFiCoinsToPointsMigrationDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFiCoinsToPointsMigrationDetailsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetFiCoinsToPointsMigrationDetailsResponseMultiError, or nil if none found.
func (m *GetFiCoinsToPointsMigrationDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFiCoinsToPointsMigrationDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFiCoinsToPointsMigrationDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFiCoinsToPointsMigrationDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFiCoinsToPointsMigrationDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TotalFiCoinsMigrated

	// no validation rules for TotalFiPointsMigrated

	if len(errors) > 0 {
		return GetFiCoinsToPointsMigrationDetailsResponseMultiError(errors)
	}

	return nil
}

// GetFiCoinsToPointsMigrationDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetFiCoinsToPointsMigrationDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFiCoinsToPointsMigrationDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFiCoinsToPointsMigrationDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFiCoinsToPointsMigrationDetailsResponseMultiError) AllErrors() []error { return m }

// GetFiCoinsToPointsMigrationDetailsResponseValidationError is the validation
// error returned by GetFiCoinsToPointsMigrationDetailsResponse.Validate if
// the designated constraints aren't met.
type GetFiCoinsToPointsMigrationDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFiCoinsToPointsMigrationDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFiCoinsToPointsMigrationDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFiCoinsToPointsMigrationDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFiCoinsToPointsMigrationDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFiCoinsToPointsMigrationDetailsResponseValidationError) ErrorName() string {
	return "GetFiCoinsToPointsMigrationDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFiCoinsToPointsMigrationDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFiCoinsToPointsMigrationDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFiCoinsToPointsMigrationDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFiCoinsToPointsMigrationDetailsResponseValidationError{}

// Validate checks the field values on TransactRequest_Options with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TransactRequest_Options) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactRequest_Options with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactRequest_OptionsMultiError, or nil if none found.
func (m *TransactRequest_Options) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactRequest_Options) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsPendingDebitAllowed

	if len(errors) > 0 {
		return TransactRequest_OptionsMultiError(errors)
	}

	return nil
}

// TransactRequest_OptionsMultiError is an error wrapping multiple validation
// errors returned by TransactRequest_Options.ValidateAll() if the designated
// constraints aren't met.
type TransactRequest_OptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactRequest_OptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactRequest_OptionsMultiError) AllErrors() []error { return m }

// TransactRequest_OptionsValidationError is the validation error returned by
// TransactRequest_Options.Validate if the designated constraints aren't met.
type TransactRequest_OptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactRequest_OptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactRequest_OptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactRequest_OptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactRequest_OptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactRequest_OptionsValidationError) ErrorName() string {
	return "TransactRequest_OptionsValidationError"
}

// Error satisfies the builtin error interface
func (e TransactRequest_OptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactRequest_Options.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactRequest_OptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactRequest_OptionsValidationError{}
