//go:generate gen_sql -types=CollateralProcessStageDetails
syntax = "proto3";

package collateralmgrtsp.collateralmanager.model;

import "api/collateralmgrtsp/collateralmanager/common/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/collateralmgrtsp/collateralmanager/model";
option java_package = "com.github.epifi.gamma.api.collateralmgrtsp.collateralmanager.model";

message CollateralProcessStage {
  string id = 1;
  // identifier of the collateral process this stage is part of
  string process_id = 2;
  // celestial workflow id
  string orch_id = 3;
  // name of the stage
  common.ProcessStageName name = 4;
  // stage execution details
  CollateralProcessStageDetails details = 5;
  common.StageStatus status = 6;
  common.StageSubStatus sub_status = 7;
  // will be used to make step stale so that re-execution of the step can be done
  google.protobuf.Timestamp staled_at = 8;
  // timestamp denoting the completion of this stage
  google.protobuf.Timestamp completed_at = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
  google.protobuf.Timestamp deleted_at = 12;
}

message CollateralProcessStageDetails {
}

enum CollateralProcessStageFieldMask {
  COLLATERAL_PROCESS_STAGE_FIELD_MASK_UNSPECIFIED = 0;
  COLLATERAL_PROCESS_STAGE_FIELD_MASK_ID = 1;
  COLLATERAL_PROCESS_STAGE_FIELD_MASK_PROCESS_ID = 2;
  COLLATERAL_PROCESS_STAGE_FIELD_MASK_ORCH_ID = 3;
  COLLATERAL_PROCESS_STAGE_FIELD_MASK_NAME = 4;
  COLLATERAL_PROCESS_STAGE_FIELD_MASK_DETAILS = 5;
  COLLATERAL_PROCESS_STAGE_FIELD_MASK_STATUS = 6;
  COLLATERAL_PROCESS_STAGE_FIELD_MASK_SUB_STATUS = 7;
  COLLATERAL_PROCESS_STAGE_FIELD_MASK_STALED_AT = 8;
  COLLATERAL_PROCESS_STAGE_FIELD_MASK_COMPLETED_AT = 9;
  COLLATERAL_PROCESS_STAGE_FIELD_MASK_CREATED_AT = 10;
  COLLATERAL_PROCESS_STAGE_FIELD_MASK_UPDATED_AT = 11;
  COLLATERAL_PROCESS_STAGE_FIELD_MASK_DELETED_AT = 12;
}
