// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/billpay/recharge.proto

package billpaypb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/billpay/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.RechargeAccountType(0)
)

// Validate checks the field values on RechargeOrder with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RechargeOrder) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RechargeOrder with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RechargeOrderMultiError, or
// nil if none found.
func (m *RechargeOrder) ValidateAll() error {
	return m.validate(true)
}

func (m *RechargeOrder) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ClientRequestId

	// no validation rules for ActorId

	// no validation rules for AccountType

	// no validation rules for AccountIdentifier

	// no validation rules for AccountOperator

	if all {
		switch v := interface{}(m.GetPlanDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargeOrderValidationError{
					field:  "PlanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargeOrderValidationError{
					field:  "PlanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlanDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargeOrderValidationError{
				field:  "PlanDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PlanIdentifier

	// no validation rules for Status

	// no validation rules for SubStatus

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargeOrderValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargeOrderValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargeOrderValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargeOrderValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargeOrderValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargeOrderValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargeOrderValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargeOrderValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargeOrderValidationError{
				field:  "CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RechargeOrderMultiError(errors)
	}

	return nil
}

// RechargeOrderMultiError is an error wrapping multiple validation errors
// returned by RechargeOrder.ValidateAll() if the designated constraints
// aren't met.
type RechargeOrderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RechargeOrderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RechargeOrderMultiError) AllErrors() []error { return m }

// RechargeOrderValidationError is the validation error returned by
// RechargeOrder.Validate if the designated constraints aren't met.
type RechargeOrderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RechargeOrderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RechargeOrderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RechargeOrderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RechargeOrderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RechargeOrderValidationError) ErrorName() string { return "RechargeOrderValidationError" }

// Error satisfies the builtin error interface
func (e RechargeOrderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRechargeOrder.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RechargeOrderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RechargeOrderValidationError{}

// Validate checks the field values on PlanDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PlanDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlanDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PlanDetailsMultiError, or
// nil if none found.
func (m *PlanDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PlanDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMobileRechargePlanDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PlanDetailsValidationError{
					field:  "MobileRechargePlanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PlanDetailsValidationError{
					field:  "MobileRechargePlanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMobileRechargePlanDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PlanDetailsValidationError{
				field:  "MobileRechargePlanDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PlanDetailsMultiError(errors)
	}

	return nil
}

// PlanDetailsMultiError is an error wrapping multiple validation errors
// returned by PlanDetails.ValidateAll() if the designated constraints aren't met.
type PlanDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlanDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlanDetailsMultiError) AllErrors() []error { return m }

// PlanDetailsValidationError is the validation error returned by
// PlanDetails.Validate if the designated constraints aren't met.
type PlanDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlanDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlanDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlanDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlanDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlanDetailsValidationError) ErrorName() string { return "PlanDetailsValidationError" }

// Error satisfies the builtin error interface
func (e PlanDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlanDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlanDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlanDetailsValidationError{}

// Validate checks the field values on MobileRechargePlan with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MobileRechargePlan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MobileRechargePlan with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MobileRechargePlanMultiError, or nil if none found.
func (m *MobileRechargePlan) ValidateAll() error {
	return m.validate(true)
}

func (m *MobileRechargePlan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Talktime

	// no validation rules for PlanName

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MobileRechargePlanValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MobileRechargePlanValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MobileRechargePlanValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Validity

	// no validation rules for PlanDescription

	// no validation rules for ServiceProvider

	// no validation rules for Operator

	if len(errors) > 0 {
		return MobileRechargePlanMultiError(errors)
	}

	return nil
}

// MobileRechargePlanMultiError is an error wrapping multiple validation errors
// returned by MobileRechargePlan.ValidateAll() if the designated constraints
// aren't met.
type MobileRechargePlanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MobileRechargePlanMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MobileRechargePlanMultiError) AllErrors() []error { return m }

// MobileRechargePlanValidationError is the validation error returned by
// MobileRechargePlan.Validate if the designated constraints aren't met.
type MobileRechargePlanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MobileRechargePlanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MobileRechargePlanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MobileRechargePlanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MobileRechargePlanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MobileRechargePlanValidationError) ErrorName() string {
	return "MobileRechargePlanValidationError"
}

// Error satisfies the builtin error interface
func (e MobileRechargePlanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMobileRechargePlan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MobileRechargePlanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MobileRechargePlanValidationError{}

// Validate checks the field values on RechargeOrderStage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RechargeOrderStage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RechargeOrderStage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RechargeOrderStageMultiError, or nil if none found.
func (m *RechargeOrderStage) ValidateAll() error {
	return m.validate(true)
}

func (m *RechargeOrderStage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for RechargeOrderId

	// no validation rules for ClientRequestId

	// no validation rules for Stage

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetExpiresAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargeOrderStageValidationError{
					field:  "ExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargeOrderStageValidationError{
					field:  "ExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiresAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargeOrderStageValidationError{
				field:  "ExpiresAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargeOrderStageValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargeOrderStageValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargeOrderStageValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargeOrderStageValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargeOrderStageValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargeOrderStageValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargeOrderStageValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargeOrderStageValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargeOrderStageValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargeOrderStageValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargeOrderStageValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargeOrderStageValidationError{
				field:  "CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RechargeOrderStageMultiError(errors)
	}

	return nil
}

// RechargeOrderStageMultiError is an error wrapping multiple validation errors
// returned by RechargeOrderStage.ValidateAll() if the designated constraints
// aren't met.
type RechargeOrderStageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RechargeOrderStageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RechargeOrderStageMultiError) AllErrors() []error { return m }

// RechargeOrderStageValidationError is the validation error returned by
// RechargeOrderStage.Validate if the designated constraints aren't met.
type RechargeOrderStageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RechargeOrderStageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RechargeOrderStageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RechargeOrderStageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RechargeOrderStageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RechargeOrderStageValidationError) ErrorName() string {
	return "RechargeOrderStageValidationError"
}

// Error satisfies the builtin error interface
func (e RechargeOrderStageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRechargeOrderStage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RechargeOrderStageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RechargeOrderStageValidationError{}

// Validate checks the field values on RechargeStageData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RechargeStageData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RechargeStageData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RechargeStageDataMultiError, or nil if none found.
func (m *RechargeStageData) ValidateAll() error {
	return m.validate(true)
}

func (m *RechargeStageData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Data.(type) {
	case *RechargeStageData_PoolAccountPaymentDetails:
		if v == nil {
			err := RechargeStageDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPoolAccountPaymentDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RechargeStageDataValidationError{
						field:  "PoolAccountPaymentDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RechargeStageDataValidationError{
						field:  "PoolAccountPaymentDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPoolAccountPaymentDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RechargeStageDataValidationError{
					field:  "PoolAccountPaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RechargeStageData_FulfillmentDetails:
		if v == nil {
			err := RechargeStageDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFulfillmentDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RechargeStageDataValidationError{
						field:  "FulfillmentDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RechargeStageDataValidationError{
						field:  "FulfillmentDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFulfillmentDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RechargeStageDataValidationError{
					field:  "FulfillmentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RechargeStageData_RefundDetails:
		if v == nil {
			err := RechargeStageDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRefundDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RechargeStageDataValidationError{
						field:  "RefundDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RechargeStageDataValidationError{
						field:  "RefundDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRefundDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RechargeStageDataValidationError{
					field:  "RefundDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RechargeStageData_ReconDetails:
		if v == nil {
			err := RechargeStageDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetReconDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RechargeStageDataValidationError{
						field:  "ReconDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RechargeStageDataValidationError{
						field:  "ReconDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetReconDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RechargeStageDataValidationError{
					field:  "ReconDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RechargeStageDataMultiError(errors)
	}

	return nil
}

// RechargeStageDataMultiError is an error wrapping multiple validation errors
// returned by RechargeStageData.ValidateAll() if the designated constraints
// aren't met.
type RechargeStageDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RechargeStageDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RechargeStageDataMultiError) AllErrors() []error { return m }

// RechargeStageDataValidationError is the validation error returned by
// RechargeStageData.Validate if the designated constraints aren't met.
type RechargeStageDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RechargeStageDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RechargeStageDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RechargeStageDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RechargeStageDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RechargeStageDataValidationError) ErrorName() string {
	return "RechargeStageDataValidationError"
}

// Error satisfies the builtin error interface
func (e RechargeStageDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRechargeStageData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RechargeStageDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RechargeStageDataValidationError{}

// Validate checks the field values on RechargePoolAccountPaymentStageData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RechargePoolAccountPaymentStageData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RechargePoolAccountPaymentStageData
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RechargePoolAccountPaymentStageDataMultiError, or nil if none found.
func (m *RechargePoolAccountPaymentStageData) ValidateAll() error {
	return m.validate(true)
}

func (m *RechargePoolAccountPaymentStageData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrderId

	// no validation rules for PaymentMode

	if all {
		switch v := interface{}(m.GetPaymentTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargePoolAccountPaymentStageDataValidationError{
					field:  "PaymentTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargePoolAccountPaymentStageDataValidationError{
					field:  "PaymentTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargePoolAccountPaymentStageDataValidationError{
				field:  "PaymentTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RechargePoolAccountPaymentStageDataMultiError(errors)
	}

	return nil
}

// RechargePoolAccountPaymentStageDataMultiError is an error wrapping multiple
// validation errors returned by
// RechargePoolAccountPaymentStageData.ValidateAll() if the designated
// constraints aren't met.
type RechargePoolAccountPaymentStageDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RechargePoolAccountPaymentStageDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RechargePoolAccountPaymentStageDataMultiError) AllErrors() []error { return m }

// RechargePoolAccountPaymentStageDataValidationError is the validation error
// returned by RechargePoolAccountPaymentStageData.Validate if the designated
// constraints aren't met.
type RechargePoolAccountPaymentStageDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RechargePoolAccountPaymentStageDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RechargePoolAccountPaymentStageDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RechargePoolAccountPaymentStageDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RechargePoolAccountPaymentStageDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RechargePoolAccountPaymentStageDataValidationError) ErrorName() string {
	return "RechargePoolAccountPaymentStageDataValidationError"
}

// Error satisfies the builtin error interface
func (e RechargePoolAccountPaymentStageDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRechargePoolAccountPaymentStageData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RechargePoolAccountPaymentStageDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RechargePoolAccountPaymentStageDataValidationError{}

// Validate checks the field values on RechargeDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RechargeDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RechargeDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RechargeDetailsMultiError, or nil if none found.
func (m *RechargeDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RechargeDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMobileNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargeDetailsValidationError{
					field:  "MobileNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargeDetailsValidationError{
					field:  "MobileNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMobileNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargeDetailsValidationError{
				field:  "MobileNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Provider

	// no validation rules for IsPostpaid

	// no validation rules for IsSpecial

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargeDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargeDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargeDetailsValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionRefId

	// no validation rules for Status

	// no validation rules for OperatorRefId

	if len(errors) > 0 {
		return RechargeDetailsMultiError(errors)
	}

	return nil
}

// RechargeDetailsMultiError is an error wrapping multiple validation errors
// returned by RechargeDetails.ValidateAll() if the designated constraints
// aren't met.
type RechargeDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RechargeDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RechargeDetailsMultiError) AllErrors() []error { return m }

// RechargeDetailsValidationError is the validation error returned by
// RechargeDetails.Validate if the designated constraints aren't met.
type RechargeDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RechargeDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RechargeDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RechargeDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RechargeDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RechargeDetailsValidationError) ErrorName() string { return "RechargeDetailsValidationError" }

// Error satisfies the builtin error interface
func (e RechargeDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRechargeDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RechargeDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RechargeDetailsValidationError{}

// Validate checks the field values on RechargeFulfillmentStageData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RechargeFulfillmentStageData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RechargeFulfillmentStageData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RechargeFulfillmentStageDataMultiError, or nil if none found.
func (m *RechargeFulfillmentStageData) ValidateAll() error {
	return m.validate(true)
}

func (m *RechargeFulfillmentStageData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRechargeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RechargeFulfillmentStageDataValidationError{
					field:  "RechargeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RechargeFulfillmentStageDataValidationError{
					field:  "RechargeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRechargeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RechargeFulfillmentStageDataValidationError{
				field:  "RechargeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InitiateRechargeRefId

	// no validation rules for InitiateRechargeTraceId

	// no validation rules for EnquireRechargeStatusTraceId

	if len(errors) > 0 {
		return RechargeFulfillmentStageDataMultiError(errors)
	}

	return nil
}

// RechargeFulfillmentStageDataMultiError is an error wrapping multiple
// validation errors returned by RechargeFulfillmentStageData.ValidateAll() if
// the designated constraints aren't met.
type RechargeFulfillmentStageDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RechargeFulfillmentStageDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RechargeFulfillmentStageDataMultiError) AllErrors() []error { return m }

// RechargeFulfillmentStageDataValidationError is the validation error returned
// by RechargeFulfillmentStageData.Validate if the designated constraints
// aren't met.
type RechargeFulfillmentStageDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RechargeFulfillmentStageDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RechargeFulfillmentStageDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RechargeFulfillmentStageDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RechargeFulfillmentStageDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RechargeFulfillmentStageDataValidationError) ErrorName() string {
	return "RechargeFulfillmentStageDataValidationError"
}

// Error satisfies the builtin error interface
func (e RechargeFulfillmentStageDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRechargeFulfillmentStageData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RechargeFulfillmentStageDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RechargeFulfillmentStageDataValidationError{}

// Validate checks the field values on RechargeRefundStageData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RechargeRefundStageData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RechargeRefundStageData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RechargeRefundStageDataMultiError, or nil if none found.
func (m *RechargeRefundStageData) ValidateAll() error {
	return m.validate(true)
}

func (m *RechargeRefundStageData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrderId

	if len(errors) > 0 {
		return RechargeRefundStageDataMultiError(errors)
	}

	return nil
}

// RechargeRefundStageDataMultiError is an error wrapping multiple validation
// errors returned by RechargeRefundStageData.ValidateAll() if the designated
// constraints aren't met.
type RechargeRefundStageDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RechargeRefundStageDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RechargeRefundStageDataMultiError) AllErrors() []error { return m }

// RechargeRefundStageDataValidationError is the validation error returned by
// RechargeRefundStageData.Validate if the designated constraints aren't met.
type RechargeRefundStageDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RechargeRefundStageDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RechargeRefundStageDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RechargeRefundStageDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RechargeRefundStageDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RechargeRefundStageDataValidationError) ErrorName() string {
	return "RechargeRefundStageDataValidationError"
}

// Error satisfies the builtin error interface
func (e RechargeRefundStageDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRechargeRefundStageData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RechargeRefundStageDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RechargeRefundStageDataValidationError{}

// Validate checks the field values on RechargeReconStageDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RechargeReconStageDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RechargeReconStageDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RechargeReconStageDetailsMultiError, or nil if none found.
func (m *RechargeReconStageDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RechargeReconStageDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RechargeReconStageDetailsMultiError(errors)
	}

	return nil
}

// RechargeReconStageDetailsMultiError is an error wrapping multiple validation
// errors returned by RechargeReconStageDetails.ValidateAll() if the
// designated constraints aren't met.
type RechargeReconStageDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RechargeReconStageDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RechargeReconStageDetailsMultiError) AllErrors() []error { return m }

// RechargeReconStageDetailsValidationError is the validation error returned by
// RechargeReconStageDetails.Validate if the designated constraints aren't met.
type RechargeReconStageDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RechargeReconStageDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RechargeReconStageDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RechargeReconStageDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RechargeReconStageDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RechargeReconStageDetailsValidationError) ErrorName() string {
	return "RechargeReconStageDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e RechargeReconStageDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRechargeReconStageDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RechargeReconStageDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RechargeReconStageDetailsValidationError{}
