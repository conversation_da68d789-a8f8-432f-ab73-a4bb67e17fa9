// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/billpay/service.proto

package billpaypb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/billpay/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.RechargeAccountType(0)
)

// Validate checks the field values on FetchAndManageBillersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchAndManageBillersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchAndManageBillersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchAndManageBillersRequestMultiError, or nil if none found.
func (m *FetchAndManageBillersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchAndManageBillersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CategoryName

	if len(errors) > 0 {
		return FetchAndManageBillersRequestMultiError(errors)
	}

	return nil
}

// FetchAndManageBillersRequestMultiError is an error wrapping multiple
// validation errors returned by FetchAndManageBillersRequest.ValidateAll() if
// the designated constraints aren't met.
type FetchAndManageBillersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchAndManageBillersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchAndManageBillersRequestMultiError) AllErrors() []error { return m }

// FetchAndManageBillersRequestValidationError is the validation error returned
// by FetchAndManageBillersRequest.Validate if the designated constraints
// aren't met.
type FetchAndManageBillersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchAndManageBillersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchAndManageBillersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchAndManageBillersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchAndManageBillersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchAndManageBillersRequestValidationError) ErrorName() string {
	return "FetchAndManageBillersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchAndManageBillersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchAndManageBillersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchAndManageBillersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchAndManageBillersRequestValidationError{}

// Validate checks the field values on FetchAndManageBillersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchAndManageBillersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchAndManageBillersResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FetchAndManageBillersResponseMultiError, or nil if none found.
func (m *FetchAndManageBillersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchAndManageBillersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAndManageBillersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAndManageBillersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAndManageBillersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBillersSummaries() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FetchAndManageBillersResponseValidationError{
						field:  fmt.Sprintf("BillersSummaries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FetchAndManageBillersResponseValidationError{
						field:  fmt.Sprintf("BillersSummaries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FetchAndManageBillersResponseValidationError{
					field:  fmt.Sprintf("BillersSummaries[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FetchAndManageBillersResponseMultiError(errors)
	}

	return nil
}

// FetchAndManageBillersResponseMultiError is an error wrapping multiple
// validation errors returned by FetchAndManageBillersResponse.ValidateAll()
// if the designated constraints aren't met.
type FetchAndManageBillersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchAndManageBillersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchAndManageBillersResponseMultiError) AllErrors() []error { return m }

// FetchAndManageBillersResponseValidationError is the validation error
// returned by FetchAndManageBillersResponse.Validate if the designated
// constraints aren't met.
type FetchAndManageBillersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchAndManageBillersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchAndManageBillersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchAndManageBillersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchAndManageBillersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchAndManageBillersResponseValidationError) ErrorName() string {
	return "FetchAndManageBillersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchAndManageBillersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchAndManageBillersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchAndManageBillersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchAndManageBillersResponseValidationError{}

// Validate checks the field values on UpdateBillerStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBillerStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBillerStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateBillerStatusRequestMultiError, or nil if none found.
func (m *UpdateBillerStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBillerStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BillerId

	// no validation rules for FromStatus

	// no validation rules for ToStatus

	if len(errors) > 0 {
		return UpdateBillerStatusRequestMultiError(errors)
	}

	return nil
}

// UpdateBillerStatusRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateBillerStatusRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateBillerStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBillerStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBillerStatusRequestMultiError) AllErrors() []error { return m }

// UpdateBillerStatusRequestValidationError is the validation error returned by
// UpdateBillerStatusRequest.Validate if the designated constraints aren't met.
type UpdateBillerStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBillerStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBillerStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBillerStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBillerStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBillerStatusRequestValidationError) ErrorName() string {
	return "UpdateBillerStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBillerStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBillerStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBillerStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBillerStatusRequestValidationError{}

// Validate checks the field values on UpdateBillerStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBillerStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBillerStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateBillerStatusResponseMultiError, or nil if none found.
func (m *UpdateBillerStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBillerStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateBillerStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateBillerStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateBillerStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateBillerStatusResponseMultiError(errors)
	}

	return nil
}

// UpdateBillerStatusResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateBillerStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateBillerStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBillerStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBillerStatusResponseMultiError) AllErrors() []error { return m }

// UpdateBillerStatusResponseValidationError is the validation error returned
// by UpdateBillerStatusResponse.Validate if the designated constraints aren't met.
type UpdateBillerStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBillerStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBillerStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBillerStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBillerStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBillerStatusResponseValidationError) ErrorName() string {
	return "UpdateBillerStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBillerStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBillerStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBillerStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBillerStatusResponseValidationError{}

// Validate checks the field values on FetchRechargeOrdersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchRechargeOrdersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchRechargeOrdersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchRechargeOrdersRequestMultiError, or nil if none found.
func (m *FetchRechargeOrdersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchRechargeOrdersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchRechargeOrdersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchRechargeOrdersRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchRechargeOrdersRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchRechargeOrdersRequestMultiError(errors)
	}

	return nil
}

// FetchRechargeOrdersRequestMultiError is an error wrapping multiple
// validation errors returned by FetchRechargeOrdersRequest.ValidateAll() if
// the designated constraints aren't met.
type FetchRechargeOrdersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchRechargeOrdersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchRechargeOrdersRequestMultiError) AllErrors() []error { return m }

// FetchRechargeOrdersRequestValidationError is the validation error returned
// by FetchRechargeOrdersRequest.Validate if the designated constraints aren't met.
type FetchRechargeOrdersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchRechargeOrdersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchRechargeOrdersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchRechargeOrdersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchRechargeOrdersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchRechargeOrdersRequestValidationError) ErrorName() string {
	return "FetchRechargeOrdersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchRechargeOrdersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchRechargeOrdersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchRechargeOrdersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchRechargeOrdersRequestValidationError{}

// Validate checks the field values on FetchRechargeOrdersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchRechargeOrdersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchRechargeOrdersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchRechargeOrdersResponseMultiError, or nil if none found.
func (m *FetchRechargeOrdersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchRechargeOrdersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchRechargeOrdersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchRechargeOrdersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchRechargeOrdersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOrders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FetchRechargeOrdersResponseValidationError{
						field:  fmt.Sprintf("Orders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FetchRechargeOrdersResponseValidationError{
						field:  fmt.Sprintf("Orders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FetchRechargeOrdersResponseValidationError{
					field:  fmt.Sprintf("Orders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContextResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchRechargeOrdersResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchRechargeOrdersResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchRechargeOrdersResponseValidationError{
				field:  "PageContextResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchRechargeOrdersResponseMultiError(errors)
	}

	return nil
}

// FetchRechargeOrdersResponseMultiError is an error wrapping multiple
// validation errors returned by FetchRechargeOrdersResponse.ValidateAll() if
// the designated constraints aren't met.
type FetchRechargeOrdersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchRechargeOrdersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchRechargeOrdersResponseMultiError) AllErrors() []error { return m }

// FetchRechargeOrdersResponseValidationError is the validation error returned
// by FetchRechargeOrdersResponse.Validate if the designated constraints
// aren't met.
type FetchRechargeOrdersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchRechargeOrdersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchRechargeOrdersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchRechargeOrdersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchRechargeOrdersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchRechargeOrdersResponseValidationError) ErrorName() string {
	return "FetchRechargeOrdersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchRechargeOrdersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchRechargeOrdersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchRechargeOrdersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchRechargeOrdersResponseValidationError{}

// Validate checks the field values on
// GetLatestRechargeOrdersForUniqueAccountsRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLatestRechargeOrdersForUniqueAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLatestRechargeOrdersForUniqueAccountsRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetLatestRechargeOrdersForUniqueAccountsRequestMultiError, or nil if none found.
func (m *GetLatestRechargeOrdersForUniqueAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLatestRechargeOrdersForUniqueAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AccountType

	if len(errors) > 0 {
		return GetLatestRechargeOrdersForUniqueAccountsRequestMultiError(errors)
	}

	return nil
}

// GetLatestRechargeOrdersForUniqueAccountsRequestMultiError is an error
// wrapping multiple validation errors returned by
// GetLatestRechargeOrdersForUniqueAccountsRequest.ValidateAll() if the
// designated constraints aren't met.
type GetLatestRechargeOrdersForUniqueAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLatestRechargeOrdersForUniqueAccountsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLatestRechargeOrdersForUniqueAccountsRequestMultiError) AllErrors() []error { return m }

// GetLatestRechargeOrdersForUniqueAccountsRequestValidationError is the
// validation error returned by
// GetLatestRechargeOrdersForUniqueAccountsRequest.Validate if the designated
// constraints aren't met.
type GetLatestRechargeOrdersForUniqueAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLatestRechargeOrdersForUniqueAccountsRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetLatestRechargeOrdersForUniqueAccountsRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetLatestRechargeOrdersForUniqueAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLatestRechargeOrdersForUniqueAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLatestRechargeOrdersForUniqueAccountsRequestValidationError) ErrorName() string {
	return "GetLatestRechargeOrdersForUniqueAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLatestRechargeOrdersForUniqueAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLatestRechargeOrdersForUniqueAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLatestRechargeOrdersForUniqueAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLatestRechargeOrdersForUniqueAccountsRequestValidationError{}

// Validate checks the field values on
// GetLatestRechargeOrdersForUniqueAccountsResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLatestRechargeOrdersForUniqueAccountsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLatestRechargeOrdersForUniqueAccountsResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetLatestRechargeOrdersForUniqueAccountsResponseMultiError, or nil if none found.
func (m *GetLatestRechargeOrdersForUniqueAccountsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLatestRechargeOrdersForUniqueAccountsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLatestRechargeOrdersForUniqueAccountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLatestRechargeOrdersForUniqueAccountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLatestRechargeOrdersForUniqueAccountsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLatestOrders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLatestRechargeOrdersForUniqueAccountsResponseValidationError{
						field:  fmt.Sprintf("LatestOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLatestRechargeOrdersForUniqueAccountsResponseValidationError{
						field:  fmt.Sprintf("LatestOrders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLatestRechargeOrdersForUniqueAccountsResponseValidationError{
					field:  fmt.Sprintf("LatestOrders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLatestRechargeOrdersForUniqueAccountsResponseMultiError(errors)
	}

	return nil
}

// GetLatestRechargeOrdersForUniqueAccountsResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetLatestRechargeOrdersForUniqueAccountsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetLatestRechargeOrdersForUniqueAccountsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLatestRechargeOrdersForUniqueAccountsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLatestRechargeOrdersForUniqueAccountsResponseMultiError) AllErrors() []error { return m }

// GetLatestRechargeOrdersForUniqueAccountsResponseValidationError is the
// validation error returned by
// GetLatestRechargeOrdersForUniqueAccountsResponse.Validate if the designated
// constraints aren't met.
type GetLatestRechargeOrdersForUniqueAccountsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLatestRechargeOrdersForUniqueAccountsResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetLatestRechargeOrdersForUniqueAccountsResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetLatestRechargeOrdersForUniqueAccountsResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetLatestRechargeOrdersForUniqueAccountsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLatestRechargeOrdersForUniqueAccountsResponseValidationError) ErrorName() string {
	return "GetLatestRechargeOrdersForUniqueAccountsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLatestRechargeOrdersForUniqueAccountsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLatestRechargeOrdersForUniqueAccountsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLatestRechargeOrdersForUniqueAccountsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLatestRechargeOrdersForUniqueAccountsResponseValidationError{}

// Validate checks the field values on FetchRechargePlansRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchRechargePlansRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchRechargePlansRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchRechargePlansRequestMultiError, or nil if none found.
func (m *FetchRechargePlansRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchRechargePlansRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AccountType

	// no validation rules for AccountIdentifier

	if len(errors) > 0 {
		return FetchRechargePlansRequestMultiError(errors)
	}

	return nil
}

// FetchRechargePlansRequestMultiError is an error wrapping multiple validation
// errors returned by FetchRechargePlansRequest.ValidateAll() if the
// designated constraints aren't met.
type FetchRechargePlansRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchRechargePlansRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchRechargePlansRequestMultiError) AllErrors() []error { return m }

// FetchRechargePlansRequestValidationError is the validation error returned by
// FetchRechargePlansRequest.Validate if the designated constraints aren't met.
type FetchRechargePlansRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchRechargePlansRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchRechargePlansRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchRechargePlansRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchRechargePlansRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchRechargePlansRequestValidationError) ErrorName() string {
	return "FetchRechargePlansRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchRechargePlansRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchRechargePlansRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchRechargePlansRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchRechargePlansRequestValidationError{}

// Validate checks the field values on FetchRechargePlansResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchRechargePlansResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchRechargePlansResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchRechargePlansResponseMultiError, or nil if none found.
func (m *FetchRechargePlansResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchRechargePlansResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchRechargePlansResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchRechargePlansResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchRechargePlansResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPlans() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FetchRechargePlansResponseValidationError{
						field:  fmt.Sprintf("Plans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FetchRechargePlansResponseValidationError{
						field:  fmt.Sprintf("Plans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FetchRechargePlansResponseValidationError{
					field:  fmt.Sprintf("Plans[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Operator

	if len(errors) > 0 {
		return FetchRechargePlansResponseMultiError(errors)
	}

	return nil
}

// FetchRechargePlansResponseMultiError is an error wrapping multiple
// validation errors returned by FetchRechargePlansResponse.ValidateAll() if
// the designated constraints aren't met.
type FetchRechargePlansResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchRechargePlansResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchRechargePlansResponseMultiError) AllErrors() []error { return m }

// FetchRechargePlansResponseValidationError is the validation error returned
// by FetchRechargePlansResponse.Validate if the designated constraints aren't met.
type FetchRechargePlansResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchRechargePlansResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchRechargePlansResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchRechargePlansResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchRechargePlansResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchRechargePlansResponseValidationError) ErrorName() string {
	return "FetchRechargePlansResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchRechargePlansResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchRechargePlansResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchRechargePlansResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchRechargePlansResponseValidationError{}

// Validate checks the field values on CreateRechargeOrderRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRechargeOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRechargeOrderRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRechargeOrderRequestMultiError, or nil if none found.
func (m *CreateRechargeOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRechargeOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AccountType

	// no validation rules for AccountIdentifier

	// no validation rules for Operator

	// no validation rules for PlanId

	if len(errors) > 0 {
		return CreateRechargeOrderRequestMultiError(errors)
	}

	return nil
}

// CreateRechargeOrderRequestMultiError is an error wrapping multiple
// validation errors returned by CreateRechargeOrderRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateRechargeOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRechargeOrderRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRechargeOrderRequestMultiError) AllErrors() []error { return m }

// CreateRechargeOrderRequestValidationError is the validation error returned
// by CreateRechargeOrderRequest.Validate if the designated constraints aren't met.
type CreateRechargeOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRechargeOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRechargeOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRechargeOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRechargeOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRechargeOrderRequestValidationError) ErrorName() string {
	return "CreateRechargeOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRechargeOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRechargeOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRechargeOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRechargeOrderRequestValidationError{}

// Validate checks the field values on CreateRechargeOrderResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRechargeOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRechargeOrderResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRechargeOrderResponseMultiError, or nil if none found.
func (m *CreateRechargeOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRechargeOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRechargeOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRechargeOrderResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRechargeOrderResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return CreateRechargeOrderResponseMultiError(errors)
	}

	return nil
}

// CreateRechargeOrderResponseMultiError is an error wrapping multiple
// validation errors returned by CreateRechargeOrderResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateRechargeOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRechargeOrderResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRechargeOrderResponseMultiError) AllErrors() []error { return m }

// CreateRechargeOrderResponseValidationError is the validation error returned
// by CreateRechargeOrderResponse.Validate if the designated constraints
// aren't met.
type CreateRechargeOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRechargeOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRechargeOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRechargeOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRechargeOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRechargeOrderResponseValidationError) ErrorName() string {
	return "CreateRechargeOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRechargeOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRechargeOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRechargeOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRechargeOrderResponseValidationError{}

// Validate checks the field values on GetRechargeOrderStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRechargeOrderStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRechargeOrderStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRechargeOrderStatusRequestMultiError, or nil if none found.
func (m *GetRechargeOrderStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRechargeOrderStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return GetRechargeOrderStatusRequestMultiError(errors)
	}

	return nil
}

// GetRechargeOrderStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetRechargeOrderStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type GetRechargeOrderStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRechargeOrderStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRechargeOrderStatusRequestMultiError) AllErrors() []error { return m }

// GetRechargeOrderStatusRequestValidationError is the validation error
// returned by GetRechargeOrderStatusRequest.Validate if the designated
// constraints aren't met.
type GetRechargeOrderStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRechargeOrderStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRechargeOrderStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRechargeOrderStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRechargeOrderStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRechargeOrderStatusRequestValidationError) ErrorName() string {
	return "GetRechargeOrderStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRechargeOrderStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRechargeOrderStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRechargeOrderStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRechargeOrderStatusRequestValidationError{}

// Validate checks the field values on GetRechargeOrderStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRechargeOrderStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRechargeOrderStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRechargeOrderStatusResponseMultiError, or nil if none found.
func (m *GetRechargeOrderStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRechargeOrderStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRechargeOrderStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRechargeOrderStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRechargeOrderStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderStatus

	// no validation rules for OrderSubStatus

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRechargeOrderStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRechargeOrderStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRechargeOrderStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRechargeOrderStatusResponseMultiError(errors)
	}

	return nil
}

// GetRechargeOrderStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetRechargeOrderStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type GetRechargeOrderStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRechargeOrderStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRechargeOrderStatusResponseMultiError) AllErrors() []error { return m }

// GetRechargeOrderStatusResponseValidationError is the validation error
// returned by GetRechargeOrderStatusResponse.Validate if the designated
// constraints aren't met.
type GetRechargeOrderStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRechargeOrderStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRechargeOrderStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRechargeOrderStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRechargeOrderStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRechargeOrderStatusResponseValidationError) ErrorName() string {
	return "GetRechargeOrderStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRechargeOrderStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRechargeOrderStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRechargeOrderStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRechargeOrderStatusResponseValidationError{}

// Validate checks the field values on
// FetchAndManageBillersResponse_BillersSummary with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FetchAndManageBillersResponse_BillersSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchAndManageBillersResponse_BillersSummary with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FetchAndManageBillersResponse_BillersSummaryMultiError, or nil if none found.
func (m *FetchAndManageBillersResponse_BillersSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchAndManageBillersResponse_BillersSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BillerId

	// no validation rules for BillerName

	// no validation rules for CategoryId

	// no validation rules for CategoryName

	// no validation rules for OldStatus

	// no validation rules for NewStatus

	// no validation rules for IsUpdated

	if len(errors) > 0 {
		return FetchAndManageBillersResponse_BillersSummaryMultiError(errors)
	}

	return nil
}

// FetchAndManageBillersResponse_BillersSummaryMultiError is an error wrapping
// multiple validation errors returned by
// FetchAndManageBillersResponse_BillersSummary.ValidateAll() if the
// designated constraints aren't met.
type FetchAndManageBillersResponse_BillersSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchAndManageBillersResponse_BillersSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchAndManageBillersResponse_BillersSummaryMultiError) AllErrors() []error { return m }

// FetchAndManageBillersResponse_BillersSummaryValidationError is the
// validation error returned by
// FetchAndManageBillersResponse_BillersSummary.Validate if the designated
// constraints aren't met.
type FetchAndManageBillersResponse_BillersSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchAndManageBillersResponse_BillersSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchAndManageBillersResponse_BillersSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchAndManageBillersResponse_BillersSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchAndManageBillersResponse_BillersSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchAndManageBillersResponse_BillersSummaryValidationError) ErrorName() string {
	return "FetchAndManageBillersResponse_BillersSummaryValidationError"
}

// Error satisfies the builtin error interface
func (e FetchAndManageBillersResponse_BillersSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchAndManageBillersResponse_BillersSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchAndManageBillersResponse_BillersSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchAndManageBillersResponse_BillersSummaryValidationError{}
