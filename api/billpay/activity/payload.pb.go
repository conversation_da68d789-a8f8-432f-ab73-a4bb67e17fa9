// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/billpay/activity/payload.proto

package billpayActPb

import (
	activity "github.com/epifi/be-common/api/celestial/activity"
	billpay "github.com/epifi/gamma/api/billpay"
	enums "github.com/epifi/gamma/api/billpay/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateRechargeOrderStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader     *activity.RequestHeader      `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	StatusToUpdate    enums.RechargeOrderStatus    `protobuf:"varint,2,opt,name=status_to_update,json=statusToUpdate,proto3,enum=api.billpay.enums.RechargeOrderStatus" json:"status_to_update,omitempty"`
	SubStatusToUpdate enums.RechargeOrderSubStatus `protobuf:"varint,3,opt,name=sub_status_to_update,json=subStatusToUpdate,proto3,enum=api.billpay.enums.RechargeOrderSubStatus" json:"sub_status_to_update,omitempty"`
}

func (x *UpdateRechargeOrderStatusRequest) Reset() {
	*x = UpdateRechargeOrderStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRechargeOrderStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRechargeOrderStatusRequest) ProtoMessage() {}

func (x *UpdateRechargeOrderStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRechargeOrderStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateRechargeOrderStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateRechargeOrderStatusRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UpdateRechargeOrderStatusRequest) GetStatusToUpdate() enums.RechargeOrderStatus {
	if x != nil {
		return x.StatusToUpdate
	}
	return enums.RechargeOrderStatus(0)
}

func (x *UpdateRechargeOrderStatusRequest) GetSubStatusToUpdate() enums.RechargeOrderSubStatus {
	if x != nil {
		return x.SubStatusToUpdate
	}
	return enums.RechargeOrderSubStatus(0)
}

type UpdateRechargeOrderStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *UpdateRechargeOrderStatusResponse) Reset() {
	*x = UpdateRechargeOrderStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRechargeOrderStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRechargeOrderStatusResponse) ProtoMessage() {}

func (x *UpdateRechargeOrderStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRechargeOrderStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateRechargeOrderStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateRechargeOrderStatusResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type CreateRechargeOrderStageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	Stage         enums.RechargeStage     `protobuf:"varint,2,opt,name=stage,proto3,enum=api.billpay.enums.RechargeStage" json:"stage,omitempty"`
}

func (x *CreateRechargeOrderStageRequest) Reset() {
	*x = CreateRechargeOrderStageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRechargeOrderStageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRechargeOrderStageRequest) ProtoMessage() {}

func (x *CreateRechargeOrderStageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRechargeOrderStageRequest.ProtoReflect.Descriptor instead.
func (*CreateRechargeOrderStageRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{2}
}

func (x *CreateRechargeOrderStageRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CreateRechargeOrderStageRequest) GetStage() enums.RechargeStage {
	if x != nil {
		return x.Stage
	}
	return enums.RechargeStage(0)
}

type CreateRechargeOrderStageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader     *activity.ResponseHeader    `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	RechargeOrderStage *billpay.RechargeOrderStage `protobuf:"bytes,2,opt,name=recharge_order_stage,json=rechargeOrderStage,proto3" json:"recharge_order_stage,omitempty"`
}

func (x *CreateRechargeOrderStageResponse) Reset() {
	*x = CreateRechargeOrderStageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRechargeOrderStageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRechargeOrderStageResponse) ProtoMessage() {}

func (x *CreateRechargeOrderStageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRechargeOrderStageResponse.ProtoReflect.Descriptor instead.
func (*CreateRechargeOrderStageResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{3}
}

func (x *CreateRechargeOrderStageResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *CreateRechargeOrderStageResponse) GetRechargeOrderStage() *billpay.RechargeOrderStage {
	if x != nil {
		return x.RechargeOrderStage
	}
	return nil
}

type UpdateRechargeOrderStageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader       *activity.RequestHeader   `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	Stage               enums.RechargeStage       `protobuf:"varint,2,opt,name=stage,proto3,enum=api.billpay.enums.RechargeStage" json:"stage,omitempty"`
	StageStatusToUpdate enums.RechargeStageStatus `protobuf:"varint,3,opt,name=stage_status_to_update,json=stageStatusToUpdate,proto3,enum=api.billpay.enums.RechargeStageStatus" json:"stage_status_to_update,omitempty"`
}

func (x *UpdateRechargeOrderStageRequest) Reset() {
	*x = UpdateRechargeOrderStageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRechargeOrderStageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRechargeOrderStageRequest) ProtoMessage() {}

func (x *UpdateRechargeOrderStageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRechargeOrderStageRequest.ProtoReflect.Descriptor instead.
func (*UpdateRechargeOrderStageRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateRechargeOrderStageRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UpdateRechargeOrderStageRequest) GetStage() enums.RechargeStage {
	if x != nil {
		return x.Stage
	}
	return enums.RechargeStage(0)
}

func (x *UpdateRechargeOrderStageRequest) GetStageStatusToUpdate() enums.RechargeStageStatus {
	if x != nil {
		return x.StageStatusToUpdate
	}
	return enums.RechargeStageStatus(0)
}

type UpdateRechargeOrderStageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader     *activity.ResponseHeader    `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	RechargeOrderStage *billpay.RechargeOrderStage `protobuf:"bytes,2,opt,name=recharge_order_stage,json=rechargeOrderStage,proto3" json:"recharge_order_stage,omitempty"`
}

func (x *UpdateRechargeOrderStageResponse) Reset() {
	*x = UpdateRechargeOrderStageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRechargeOrderStageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRechargeOrderStageResponse) ProtoMessage() {}

func (x *UpdateRechargeOrderStageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRechargeOrderStageResponse.ProtoReflect.Descriptor instead.
func (*UpdateRechargeOrderStageResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateRechargeOrderStageResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *UpdateRechargeOrderStageResponse) GetRechargeOrderStage() *billpay.RechargeOrderStage {
	if x != nil {
		return x.RechargeOrderStage
	}
	return nil
}

type EnquireRechargePaymentStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader               *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	PaymentStageClientRequestId string                  `protobuf:"bytes,2,opt,name=payment_stage_client_request_id,json=paymentStageClientRequestId,proto3" json:"payment_stage_client_request_id,omitempty"`
}

func (x *EnquireRechargePaymentStatusRequest) Reset() {
	*x = EnquireRechargePaymentStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireRechargePaymentStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireRechargePaymentStatusRequest) ProtoMessage() {}

func (x *EnquireRechargePaymentStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireRechargePaymentStatusRequest.ProtoReflect.Descriptor instead.
func (*EnquireRechargePaymentStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{6}
}

func (x *EnquireRechargePaymentStatusRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *EnquireRechargePaymentStatusRequest) GetPaymentStageClientRequestId() string {
	if x != nil {
		return x.PaymentStageClientRequestId
	}
	return ""
}

type EnquireRechargePaymentStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *EnquireRechargePaymentStatusResponse) Reset() {
	*x = EnquireRechargePaymentStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireRechargePaymentStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireRechargePaymentStatusResponse) ProtoMessage() {}

func (x *EnquireRechargePaymentStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireRechargePaymentStatusResponse.ProtoReflect.Descriptor instead.
func (*EnquireRechargePaymentStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{7}
}

func (x *EnquireRechargePaymentStatusResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type InitiateRechargeWithVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader                  *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	FulfilmentStageClientRequestId string                  `protobuf:"bytes,2,opt,name=fulfilment_stage_client_request_id,json=fulfilmentStageClientRequestId,proto3" json:"fulfilment_stage_client_request_id,omitempty"`
}

func (x *InitiateRechargeWithVendorRequest) Reset() {
	*x = InitiateRechargeWithVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateRechargeWithVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateRechargeWithVendorRequest) ProtoMessage() {}

func (x *InitiateRechargeWithVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateRechargeWithVendorRequest.ProtoReflect.Descriptor instead.
func (*InitiateRechargeWithVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{8}
}

func (x *InitiateRechargeWithVendorRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *InitiateRechargeWithVendorRequest) GetFulfilmentStageClientRequestId() string {
	if x != nil {
		return x.FulfilmentStageClientRequestId
	}
	return ""
}

type InitiateRechargeWithVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *InitiateRechargeWithVendorResponse) Reset() {
	*x = InitiateRechargeWithVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateRechargeWithVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateRechargeWithVendorResponse) ProtoMessage() {}

func (x *InitiateRechargeWithVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateRechargeWithVendorResponse.ProtoReflect.Descriptor instead.
func (*InitiateRechargeWithVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{9}
}

func (x *InitiateRechargeWithVendorResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type EnquireRechargeStatusWithVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader                  *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	FulfilmentStageClientRequestId string                  `protobuf:"bytes,2,opt,name=fulfilment_stage_client_request_id,json=fulfilmentStageClientRequestId,proto3" json:"fulfilment_stage_client_request_id,omitempty"`
}

func (x *EnquireRechargeStatusWithVendorRequest) Reset() {
	*x = EnquireRechargeStatusWithVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireRechargeStatusWithVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireRechargeStatusWithVendorRequest) ProtoMessage() {}

func (x *EnquireRechargeStatusWithVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireRechargeStatusWithVendorRequest.ProtoReflect.Descriptor instead.
func (*EnquireRechargeStatusWithVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{10}
}

func (x *EnquireRechargeStatusWithVendorRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *EnquireRechargeStatusWithVendorRequest) GetFulfilmentStageClientRequestId() string {
	if x != nil {
		return x.FulfilmentStageClientRequestId
	}
	return ""
}

type EnquireRechargeStatusWithVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *EnquireRechargeStatusWithVendorResponse) Reset() {
	*x = EnquireRechargeStatusWithVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireRechargeStatusWithVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireRechargeStatusWithVendorResponse) ProtoMessage() {}

func (x *EnquireRechargeStatusWithVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireRechargeStatusWithVendorResponse.ProtoReflect.Descriptor instead.
func (*EnquireRechargeStatusWithVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{11}
}

func (x *EnquireRechargeStatusWithVendorResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ShouldInitiateRechargeRefundOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *ShouldInitiateRechargeRefundOrderRequest) Reset() {
	*x = ShouldInitiateRechargeRefundOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShouldInitiateRechargeRefundOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShouldInitiateRechargeRefundOrderRequest) ProtoMessage() {}

func (x *ShouldInitiateRechargeRefundOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShouldInitiateRechargeRefundOrderRequest.ProtoReflect.Descriptor instead.
func (*ShouldInitiateRechargeRefundOrderRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{12}
}

func (x *ShouldInitiateRechargeRefundOrderRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type ShouldInitiateRechargeRefundOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader       *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	ShouldInitiateRefund bool                     `protobuf:"varint,2,opt,name=should_initiate_refund,json=shouldInitiateRefund,proto3" json:"should_initiate_refund,omitempty"`
}

func (x *ShouldInitiateRechargeRefundOrderResponse) Reset() {
	*x = ShouldInitiateRechargeRefundOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShouldInitiateRechargeRefundOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShouldInitiateRechargeRefundOrderResponse) ProtoMessage() {}

func (x *ShouldInitiateRechargeRefundOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShouldInitiateRechargeRefundOrderResponse.ProtoReflect.Descriptor instead.
func (*ShouldInitiateRechargeRefundOrderResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{13}
}

func (x *ShouldInitiateRechargeRefundOrderResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *ShouldInitiateRechargeRefundOrderResponse) GetShouldInitiateRefund() bool {
	if x != nil {
		return x.ShouldInitiateRefund
	}
	return false
}

type InitiateRechargeRefundOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader         *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	RefundClientRequestId string                  `protobuf:"bytes,2,opt,name=refund_client_request_id,json=refundClientRequestId,proto3" json:"refund_client_request_id,omitempty"`
}

func (x *InitiateRechargeRefundOrderRequest) Reset() {
	*x = InitiateRechargeRefundOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateRechargeRefundOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateRechargeRefundOrderRequest) ProtoMessage() {}

func (x *InitiateRechargeRefundOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateRechargeRefundOrderRequest.ProtoReflect.Descriptor instead.
func (*InitiateRechargeRefundOrderRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{14}
}

func (x *InitiateRechargeRefundOrderRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *InitiateRechargeRefundOrderRequest) GetRefundClientRequestId() string {
	if x != nil {
		return x.RefundClientRequestId
	}
	return ""
}

type InitiateRechargeRefundOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *InitiateRechargeRefundOrderResponse) Reset() {
	*x = InitiateRechargeRefundOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateRechargeRefundOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateRechargeRefundOrderResponse) ProtoMessage() {}

func (x *InitiateRechargeRefundOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateRechargeRefundOrderResponse.ProtoReflect.Descriptor instead.
func (*InitiateRechargeRefundOrderResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{15}
}

func (x *InitiateRechargeRefundOrderResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type EnquireRechargeRefundOrderStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader         *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	RefundClientRequestId string                  `protobuf:"bytes,2,opt,name=refund_client_request_id,json=refundClientRequestId,proto3" json:"refund_client_request_id,omitempty"`
}

func (x *EnquireRechargeRefundOrderStatusRequest) Reset() {
	*x = EnquireRechargeRefundOrderStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireRechargeRefundOrderStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireRechargeRefundOrderStatusRequest) ProtoMessage() {}

func (x *EnquireRechargeRefundOrderStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireRechargeRefundOrderStatusRequest.ProtoReflect.Descriptor instead.
func (*EnquireRechargeRefundOrderStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{16}
}

func (x *EnquireRechargeRefundOrderStatusRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *EnquireRechargeRefundOrderStatusRequest) GetRefundClientRequestId() string {
	if x != nil {
		return x.RefundClientRequestId
	}
	return ""
}

type EnquireRechargeRefundOrderStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *EnquireRechargeRefundOrderStatusResponse) Reset() {
	*x = EnquireRechargeRefundOrderStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireRechargeRefundOrderStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireRechargeRefundOrderStatusResponse) ProtoMessage() {}

func (x *EnquireRechargeRefundOrderStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireRechargeRefundOrderStatusResponse.ProtoReflect.Descriptor instead.
func (*EnquireRechargeRefundOrderStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{17}
}

func (x *EnquireRechargeRefundOrderStatusResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ShouldInitiateRechargeFulfilmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *ShouldInitiateRechargeFulfilmentRequest) Reset() {
	*x = ShouldInitiateRechargeFulfilmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShouldInitiateRechargeFulfilmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShouldInitiateRechargeFulfilmentRequest) ProtoMessage() {}

func (x *ShouldInitiateRechargeFulfilmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShouldInitiateRechargeFulfilmentRequest.ProtoReflect.Descriptor instead.
func (*ShouldInitiateRechargeFulfilmentRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{18}
}

func (x *ShouldInitiateRechargeFulfilmentRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type ShouldInitiateRechargeFulfilmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader           *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	ShouldInitiateFulfilment bool                     `protobuf:"varint,2,opt,name=should_initiate_fulfilment,json=shouldInitiateFulfilment,proto3" json:"should_initiate_fulfilment,omitempty"`
}

func (x *ShouldInitiateRechargeFulfilmentResponse) Reset() {
	*x = ShouldInitiateRechargeFulfilmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_activity_payload_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShouldInitiateRechargeFulfilmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShouldInitiateRechargeFulfilmentResponse) ProtoMessage() {}

func (x *ShouldInitiateRechargeFulfilmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_activity_payload_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShouldInitiateRechargeFulfilmentResponse.ProtoReflect.Descriptor instead.
func (*ShouldInitiateRechargeFulfilmentResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_activity_payload_proto_rawDescGZIP(), []int{19}
}

func (x *ShouldInitiateRechargeFulfilmentResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *ShouldInitiateRechargeFulfilmentResponse) GetShouldInitiateFulfilment() bool {
	if x != nil {
		return x.ShouldInitiateFulfilment
	}
	return false
}

var File_api_billpay_activity_payload_proto protoreflect.FileDescriptor

var file_api_billpay_activity_payload_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61,
	0x79, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f,
	0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x62,
	0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9a, 0x02, 0x0a, 0x20, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x10, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61,
	0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x54, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x5a, 0x0a, 0x14, 0x73,
	0x75, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x75, 0x62, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x73, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54,
	0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0x70, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xa3, 0x01, 0x0a, 0x1f, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x22,
	0xc2, 0x01, 0x0a, 0x20, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x51, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x52, 0x65,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x52, 0x12, 0x72, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x22, 0x80, 0x02, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x36, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x5b, 0x0a, 0x16, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52,
	0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x13, 0x73, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54,
	0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0xc2, 0x01, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x51, 0x0a, 0x14, 0x72, 0x65, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x12, 0x72, 0x65, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x67, 0x65, 0x22, 0xb5, 0x01, 0x0a,
	0x23, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63,
	0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x44,
	0x0a, 0x1f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x22, 0x73, 0x0a, 0x24, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65, 0x52,
	0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xb9, 0x01, 0x0a, 0x21, 0x49, 0x6e,
	0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x57, 0x69,
	0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4a, 0x0a, 0x22, 0x66, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x71, 0x0a, 0x22, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x57, 0x69, 0x74, 0x68, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xbe, 0x01, 0x0a, 0x26, 0x45, 0x6e, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x57, 0x69, 0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65,
	0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4a, 0x0a,
	0x22, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1e, 0x66, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x76, 0x0a, 0x27, 0x45, 0x6e, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x57, 0x69, 0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0x74, 0x0a, 0x28, 0x53, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x49, 0x6e, 0x69, 0x74, 0x69,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xae, 0x01, 0x0a, 0x29, 0x53, 0x68, 0x6f, 0x75,
	0x6c, 0x64, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x34, 0x0a, 0x16, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x14, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x22, 0xa7, 0x01, 0x0a, 0x22, 0x49, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x18, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x22, 0x72, 0x0a, 0x23, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xac, 0x01, 0x0a, 0x27, 0x45, 0x6e, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x18,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x77, 0x0a, 0x28, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x73,
	0x0a, 0x27, 0x53, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x22, 0xb5, 0x01, 0x0a, 0x28, 0x53, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x49, 0x6e,
	0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x46, 0x75,
	0x6c, 0x66, 0x69, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a,
	0x1a, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x5f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x18, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74,
	0x65, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x67, 0x0a, 0x2b, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61,
	0x79, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x3b, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x41,
	0x63, 0x74, 0x50, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_billpay_activity_payload_proto_rawDescOnce sync.Once
	file_api_billpay_activity_payload_proto_rawDescData = file_api_billpay_activity_payload_proto_rawDesc
)

func file_api_billpay_activity_payload_proto_rawDescGZIP() []byte {
	file_api_billpay_activity_payload_proto_rawDescOnce.Do(func() {
		file_api_billpay_activity_payload_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_billpay_activity_payload_proto_rawDescData)
	})
	return file_api_billpay_activity_payload_proto_rawDescData
}

var file_api_billpay_activity_payload_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_api_billpay_activity_payload_proto_goTypes = []interface{}{
	(*UpdateRechargeOrderStatusRequest)(nil),          // 0: api.billpay.activity.UpdateRechargeOrderStatusRequest
	(*UpdateRechargeOrderStatusResponse)(nil),         // 1: api.billpay.activity.UpdateRechargeOrderStatusResponse
	(*CreateRechargeOrderStageRequest)(nil),           // 2: api.billpay.activity.CreateRechargeOrderStageRequest
	(*CreateRechargeOrderStageResponse)(nil),          // 3: api.billpay.activity.CreateRechargeOrderStageResponse
	(*UpdateRechargeOrderStageRequest)(nil),           // 4: api.billpay.activity.UpdateRechargeOrderStageRequest
	(*UpdateRechargeOrderStageResponse)(nil),          // 5: api.billpay.activity.UpdateRechargeOrderStageResponse
	(*EnquireRechargePaymentStatusRequest)(nil),       // 6: api.billpay.activity.EnquireRechargePaymentStatusRequest
	(*EnquireRechargePaymentStatusResponse)(nil),      // 7: api.billpay.activity.EnquireRechargePaymentStatusResponse
	(*InitiateRechargeWithVendorRequest)(nil),         // 8: api.billpay.activity.InitiateRechargeWithVendorRequest
	(*InitiateRechargeWithVendorResponse)(nil),        // 9: api.billpay.activity.InitiateRechargeWithVendorResponse
	(*EnquireRechargeStatusWithVendorRequest)(nil),    // 10: api.billpay.activity.EnquireRechargeStatusWithVendorRequest
	(*EnquireRechargeStatusWithVendorResponse)(nil),   // 11: api.billpay.activity.EnquireRechargeStatusWithVendorResponse
	(*ShouldInitiateRechargeRefundOrderRequest)(nil),  // 12: api.billpay.activity.ShouldInitiateRechargeRefundOrderRequest
	(*ShouldInitiateRechargeRefundOrderResponse)(nil), // 13: api.billpay.activity.ShouldInitiateRechargeRefundOrderResponse
	(*InitiateRechargeRefundOrderRequest)(nil),        // 14: api.billpay.activity.InitiateRechargeRefundOrderRequest
	(*InitiateRechargeRefundOrderResponse)(nil),       // 15: api.billpay.activity.InitiateRechargeRefundOrderResponse
	(*EnquireRechargeRefundOrderStatusRequest)(nil),   // 16: api.billpay.activity.EnquireRechargeRefundOrderStatusRequest
	(*EnquireRechargeRefundOrderStatusResponse)(nil),  // 17: api.billpay.activity.EnquireRechargeRefundOrderStatusResponse
	(*ShouldInitiateRechargeFulfilmentRequest)(nil),   // 18: api.billpay.activity.ShouldInitiateRechargeFulfilmentRequest
	(*ShouldInitiateRechargeFulfilmentResponse)(nil),  // 19: api.billpay.activity.ShouldInitiateRechargeFulfilmentResponse
	(*activity.RequestHeader)(nil),                    // 20: celestial.activity.RequestHeader
	(enums.RechargeOrderStatus)(0),                    // 21: api.billpay.enums.RechargeOrderStatus
	(enums.RechargeOrderSubStatus)(0),                 // 22: api.billpay.enums.RechargeOrderSubStatus
	(*activity.ResponseHeader)(nil),                   // 23: celestial.activity.ResponseHeader
	(enums.RechargeStage)(0),                          // 24: api.billpay.enums.RechargeStage
	(*billpay.RechargeOrderStage)(nil),                // 25: api.billpay.RechargeOrderStage
	(enums.RechargeStageStatus)(0),                    // 26: api.billpay.enums.RechargeStageStatus
}
var file_api_billpay_activity_payload_proto_depIdxs = []int32{
	20, // 0: api.billpay.activity.UpdateRechargeOrderStatusRequest.request_header:type_name -> celestial.activity.RequestHeader
	21, // 1: api.billpay.activity.UpdateRechargeOrderStatusRequest.status_to_update:type_name -> api.billpay.enums.RechargeOrderStatus
	22, // 2: api.billpay.activity.UpdateRechargeOrderStatusRequest.sub_status_to_update:type_name -> api.billpay.enums.RechargeOrderSubStatus
	23, // 3: api.billpay.activity.UpdateRechargeOrderStatusResponse.response_header:type_name -> celestial.activity.ResponseHeader
	20, // 4: api.billpay.activity.CreateRechargeOrderStageRequest.request_header:type_name -> celestial.activity.RequestHeader
	24, // 5: api.billpay.activity.CreateRechargeOrderStageRequest.stage:type_name -> api.billpay.enums.RechargeStage
	23, // 6: api.billpay.activity.CreateRechargeOrderStageResponse.response_header:type_name -> celestial.activity.ResponseHeader
	25, // 7: api.billpay.activity.CreateRechargeOrderStageResponse.recharge_order_stage:type_name -> api.billpay.RechargeOrderStage
	20, // 8: api.billpay.activity.UpdateRechargeOrderStageRequest.request_header:type_name -> celestial.activity.RequestHeader
	24, // 9: api.billpay.activity.UpdateRechargeOrderStageRequest.stage:type_name -> api.billpay.enums.RechargeStage
	26, // 10: api.billpay.activity.UpdateRechargeOrderStageRequest.stage_status_to_update:type_name -> api.billpay.enums.RechargeStageStatus
	23, // 11: api.billpay.activity.UpdateRechargeOrderStageResponse.response_header:type_name -> celestial.activity.ResponseHeader
	25, // 12: api.billpay.activity.UpdateRechargeOrderStageResponse.recharge_order_stage:type_name -> api.billpay.RechargeOrderStage
	20, // 13: api.billpay.activity.EnquireRechargePaymentStatusRequest.request_header:type_name -> celestial.activity.RequestHeader
	23, // 14: api.billpay.activity.EnquireRechargePaymentStatusResponse.response_header:type_name -> celestial.activity.ResponseHeader
	20, // 15: api.billpay.activity.InitiateRechargeWithVendorRequest.request_header:type_name -> celestial.activity.RequestHeader
	23, // 16: api.billpay.activity.InitiateRechargeWithVendorResponse.response_header:type_name -> celestial.activity.ResponseHeader
	20, // 17: api.billpay.activity.EnquireRechargeStatusWithVendorRequest.request_header:type_name -> celestial.activity.RequestHeader
	23, // 18: api.billpay.activity.EnquireRechargeStatusWithVendorResponse.response_header:type_name -> celestial.activity.ResponseHeader
	20, // 19: api.billpay.activity.ShouldInitiateRechargeRefundOrderRequest.request_header:type_name -> celestial.activity.RequestHeader
	23, // 20: api.billpay.activity.ShouldInitiateRechargeRefundOrderResponse.response_header:type_name -> celestial.activity.ResponseHeader
	20, // 21: api.billpay.activity.InitiateRechargeRefundOrderRequest.request_header:type_name -> celestial.activity.RequestHeader
	23, // 22: api.billpay.activity.InitiateRechargeRefundOrderResponse.response_header:type_name -> celestial.activity.ResponseHeader
	20, // 23: api.billpay.activity.EnquireRechargeRefundOrderStatusRequest.request_header:type_name -> celestial.activity.RequestHeader
	23, // 24: api.billpay.activity.EnquireRechargeRefundOrderStatusResponse.response_header:type_name -> celestial.activity.ResponseHeader
	20, // 25: api.billpay.activity.ShouldInitiateRechargeFulfilmentRequest.request_header:type_name -> celestial.activity.RequestHeader
	23, // 26: api.billpay.activity.ShouldInitiateRechargeFulfilmentResponse.response_header:type_name -> celestial.activity.ResponseHeader
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_api_billpay_activity_payload_proto_init() }
func file_api_billpay_activity_payload_proto_init() {
	if File_api_billpay_activity_payload_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_billpay_activity_payload_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRechargeOrderStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRechargeOrderStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRechargeOrderStageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRechargeOrderStageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRechargeOrderStageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRechargeOrderStageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireRechargePaymentStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireRechargePaymentStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateRechargeWithVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateRechargeWithVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireRechargeStatusWithVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireRechargeStatusWithVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShouldInitiateRechargeRefundOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShouldInitiateRechargeRefundOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateRechargeRefundOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateRechargeRefundOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireRechargeRefundOrderStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireRechargeRefundOrderStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShouldInitiateRechargeFulfilmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_activity_payload_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShouldInitiateRechargeFulfilmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_billpay_activity_payload_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_billpay_activity_payload_proto_goTypes,
		DependencyIndexes: file_api_billpay_activity_payload_proto_depIdxs,
		MessageInfos:      file_api_billpay_activity_payload_proto_msgTypes,
	}.Build()
	File_api_billpay_activity_payload_proto = out.File
	file_api_billpay_activity_payload_proto_rawDesc = nil
	file_api_billpay_activity_payload_proto_goTypes = nil
	file_api_billpay_activity_payload_proto_depIdxs = nil
}
