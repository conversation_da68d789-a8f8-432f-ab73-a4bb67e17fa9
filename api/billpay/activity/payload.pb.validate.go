// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/billpay/activity/payload.proto

package billpayActPb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/billpay/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.RechargeOrderStatus(0)
)

// Validate checks the field values on UpdateRechargeOrderStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateRechargeOrderStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRechargeOrderStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateRechargeOrderStatusRequestMultiError, or nil if none found.
func (m *UpdateRechargeOrderStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRechargeOrderStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRechargeOrderStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRechargeOrderStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRechargeOrderStatusRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StatusToUpdate

	// no validation rules for SubStatusToUpdate

	if len(errors) > 0 {
		return UpdateRechargeOrderStatusRequestMultiError(errors)
	}

	return nil
}

// UpdateRechargeOrderStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateRechargeOrderStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateRechargeOrderStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRechargeOrderStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRechargeOrderStatusRequestMultiError) AllErrors() []error { return m }

// UpdateRechargeOrderStatusRequestValidationError is the validation error
// returned by UpdateRechargeOrderStatusRequest.Validate if the designated
// constraints aren't met.
type UpdateRechargeOrderStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRechargeOrderStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRechargeOrderStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRechargeOrderStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRechargeOrderStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRechargeOrderStatusRequestValidationError) ErrorName() string {
	return "UpdateRechargeOrderStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRechargeOrderStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRechargeOrderStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRechargeOrderStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRechargeOrderStatusRequestValidationError{}

// Validate checks the field values on UpdateRechargeOrderStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateRechargeOrderStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRechargeOrderStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateRechargeOrderStatusResponseMultiError, or nil if none found.
func (m *UpdateRechargeOrderStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRechargeOrderStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRechargeOrderStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRechargeOrderStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRechargeOrderStatusResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateRechargeOrderStatusResponseMultiError(errors)
	}

	return nil
}

// UpdateRechargeOrderStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateRechargeOrderStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateRechargeOrderStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRechargeOrderStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRechargeOrderStatusResponseMultiError) AllErrors() []error { return m }

// UpdateRechargeOrderStatusResponseValidationError is the validation error
// returned by UpdateRechargeOrderStatusResponse.Validate if the designated
// constraints aren't met.
type UpdateRechargeOrderStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRechargeOrderStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRechargeOrderStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRechargeOrderStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRechargeOrderStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRechargeOrderStatusResponseValidationError) ErrorName() string {
	return "UpdateRechargeOrderStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRechargeOrderStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRechargeOrderStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRechargeOrderStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRechargeOrderStatusResponseValidationError{}

// Validate checks the field values on CreateRechargeOrderStageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRechargeOrderStageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRechargeOrderStageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateRechargeOrderStageRequestMultiError, or nil if none found.
func (m *CreateRechargeOrderStageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRechargeOrderStageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRechargeOrderStageRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRechargeOrderStageRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRechargeOrderStageRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Stage

	if len(errors) > 0 {
		return CreateRechargeOrderStageRequestMultiError(errors)
	}

	return nil
}

// CreateRechargeOrderStageRequestMultiError is an error wrapping multiple
// validation errors returned by CreateRechargeOrderStageRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateRechargeOrderStageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRechargeOrderStageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRechargeOrderStageRequestMultiError) AllErrors() []error { return m }

// CreateRechargeOrderStageRequestValidationError is the validation error
// returned by CreateRechargeOrderStageRequest.Validate if the designated
// constraints aren't met.
type CreateRechargeOrderStageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRechargeOrderStageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRechargeOrderStageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRechargeOrderStageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRechargeOrderStageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRechargeOrderStageRequestValidationError) ErrorName() string {
	return "CreateRechargeOrderStageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRechargeOrderStageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRechargeOrderStageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRechargeOrderStageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRechargeOrderStageRequestValidationError{}

// Validate checks the field values on CreateRechargeOrderStageResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateRechargeOrderStageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRechargeOrderStageResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateRechargeOrderStageResponseMultiError, or nil if none found.
func (m *CreateRechargeOrderStageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRechargeOrderStageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRechargeOrderStageResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRechargeOrderStageResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRechargeOrderStageResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRechargeOrderStage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRechargeOrderStageResponseValidationError{
					field:  "RechargeOrderStage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRechargeOrderStageResponseValidationError{
					field:  "RechargeOrderStage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRechargeOrderStage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRechargeOrderStageResponseValidationError{
				field:  "RechargeOrderStage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRechargeOrderStageResponseMultiError(errors)
	}

	return nil
}

// CreateRechargeOrderStageResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateRechargeOrderStageResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateRechargeOrderStageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRechargeOrderStageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRechargeOrderStageResponseMultiError) AllErrors() []error { return m }

// CreateRechargeOrderStageResponseValidationError is the validation error
// returned by CreateRechargeOrderStageResponse.Validate if the designated
// constraints aren't met.
type CreateRechargeOrderStageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRechargeOrderStageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRechargeOrderStageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRechargeOrderStageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRechargeOrderStageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRechargeOrderStageResponseValidationError) ErrorName() string {
	return "CreateRechargeOrderStageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRechargeOrderStageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRechargeOrderStageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRechargeOrderStageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRechargeOrderStageResponseValidationError{}

// Validate checks the field values on UpdateRechargeOrderStageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateRechargeOrderStageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRechargeOrderStageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateRechargeOrderStageRequestMultiError, or nil if none found.
func (m *UpdateRechargeOrderStageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRechargeOrderStageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRechargeOrderStageRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRechargeOrderStageRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRechargeOrderStageRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Stage

	// no validation rules for StageStatusToUpdate

	if len(errors) > 0 {
		return UpdateRechargeOrderStageRequestMultiError(errors)
	}

	return nil
}

// UpdateRechargeOrderStageRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateRechargeOrderStageRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateRechargeOrderStageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRechargeOrderStageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRechargeOrderStageRequestMultiError) AllErrors() []error { return m }

// UpdateRechargeOrderStageRequestValidationError is the validation error
// returned by UpdateRechargeOrderStageRequest.Validate if the designated
// constraints aren't met.
type UpdateRechargeOrderStageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRechargeOrderStageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRechargeOrderStageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRechargeOrderStageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRechargeOrderStageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRechargeOrderStageRequestValidationError) ErrorName() string {
	return "UpdateRechargeOrderStageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRechargeOrderStageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRechargeOrderStageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRechargeOrderStageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRechargeOrderStageRequestValidationError{}

// Validate checks the field values on UpdateRechargeOrderStageResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateRechargeOrderStageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRechargeOrderStageResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateRechargeOrderStageResponseMultiError, or nil if none found.
func (m *UpdateRechargeOrderStageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRechargeOrderStageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRechargeOrderStageResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRechargeOrderStageResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRechargeOrderStageResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRechargeOrderStage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRechargeOrderStageResponseValidationError{
					field:  "RechargeOrderStage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRechargeOrderStageResponseValidationError{
					field:  "RechargeOrderStage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRechargeOrderStage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRechargeOrderStageResponseValidationError{
				field:  "RechargeOrderStage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateRechargeOrderStageResponseMultiError(errors)
	}

	return nil
}

// UpdateRechargeOrderStageResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateRechargeOrderStageResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateRechargeOrderStageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRechargeOrderStageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRechargeOrderStageResponseMultiError) AllErrors() []error { return m }

// UpdateRechargeOrderStageResponseValidationError is the validation error
// returned by UpdateRechargeOrderStageResponse.Validate if the designated
// constraints aren't met.
type UpdateRechargeOrderStageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRechargeOrderStageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRechargeOrderStageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRechargeOrderStageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRechargeOrderStageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRechargeOrderStageResponseValidationError) ErrorName() string {
	return "UpdateRechargeOrderStageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRechargeOrderStageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRechargeOrderStageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRechargeOrderStageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRechargeOrderStageResponseValidationError{}

// Validate checks the field values on EnquireRechargePaymentStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *EnquireRechargePaymentStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnquireRechargePaymentStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// EnquireRechargePaymentStatusRequestMultiError, or nil if none found.
func (m *EnquireRechargePaymentStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireRechargePaymentStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnquireRechargePaymentStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnquireRechargePaymentStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnquireRechargePaymentStatusRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PaymentStageClientRequestId

	if len(errors) > 0 {
		return EnquireRechargePaymentStatusRequestMultiError(errors)
	}

	return nil
}

// EnquireRechargePaymentStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// EnquireRechargePaymentStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type EnquireRechargePaymentStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireRechargePaymentStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireRechargePaymentStatusRequestMultiError) AllErrors() []error { return m }

// EnquireRechargePaymentStatusRequestValidationError is the validation error
// returned by EnquireRechargePaymentStatusRequest.Validate if the designated
// constraints aren't met.
type EnquireRechargePaymentStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireRechargePaymentStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireRechargePaymentStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireRechargePaymentStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireRechargePaymentStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireRechargePaymentStatusRequestValidationError) ErrorName() string {
	return "EnquireRechargePaymentStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireRechargePaymentStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireRechargePaymentStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireRechargePaymentStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireRechargePaymentStatusRequestValidationError{}

// Validate checks the field values on EnquireRechargePaymentStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *EnquireRechargePaymentStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnquireRechargePaymentStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// EnquireRechargePaymentStatusResponseMultiError, or nil if none found.
func (m *EnquireRechargePaymentStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireRechargePaymentStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnquireRechargePaymentStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnquireRechargePaymentStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnquireRechargePaymentStatusResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EnquireRechargePaymentStatusResponseMultiError(errors)
	}

	return nil
}

// EnquireRechargePaymentStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// EnquireRechargePaymentStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type EnquireRechargePaymentStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireRechargePaymentStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireRechargePaymentStatusResponseMultiError) AllErrors() []error { return m }

// EnquireRechargePaymentStatusResponseValidationError is the validation error
// returned by EnquireRechargePaymentStatusResponse.Validate if the designated
// constraints aren't met.
type EnquireRechargePaymentStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireRechargePaymentStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireRechargePaymentStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireRechargePaymentStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireRechargePaymentStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireRechargePaymentStatusResponseValidationError) ErrorName() string {
	return "EnquireRechargePaymentStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireRechargePaymentStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireRechargePaymentStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireRechargePaymentStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireRechargePaymentStatusResponseValidationError{}

// Validate checks the field values on InitiateRechargeWithVendorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitiateRechargeWithVendorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateRechargeWithVendorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiateRechargeWithVendorRequestMultiError, or nil if none found.
func (m *InitiateRechargeWithVendorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateRechargeWithVendorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateRechargeWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateRechargeWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateRechargeWithVendorRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FulfilmentStageClientRequestId

	if len(errors) > 0 {
		return InitiateRechargeWithVendorRequestMultiError(errors)
	}

	return nil
}

// InitiateRechargeWithVendorRequestMultiError is an error wrapping multiple
// validation errors returned by
// InitiateRechargeWithVendorRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateRechargeWithVendorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateRechargeWithVendorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateRechargeWithVendorRequestMultiError) AllErrors() []error { return m }

// InitiateRechargeWithVendorRequestValidationError is the validation error
// returned by InitiateRechargeWithVendorRequest.Validate if the designated
// constraints aren't met.
type InitiateRechargeWithVendorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateRechargeWithVendorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateRechargeWithVendorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateRechargeWithVendorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateRechargeWithVendorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateRechargeWithVendorRequestValidationError) ErrorName() string {
	return "InitiateRechargeWithVendorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateRechargeWithVendorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateRechargeWithVendorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateRechargeWithVendorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateRechargeWithVendorRequestValidationError{}

// Validate checks the field values on InitiateRechargeWithVendorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitiateRechargeWithVendorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateRechargeWithVendorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiateRechargeWithVendorResponseMultiError, or nil if none found.
func (m *InitiateRechargeWithVendorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateRechargeWithVendorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateRechargeWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateRechargeWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateRechargeWithVendorResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateRechargeWithVendorResponseMultiError(errors)
	}

	return nil
}

// InitiateRechargeWithVendorResponseMultiError is an error wrapping multiple
// validation errors returned by
// InitiateRechargeWithVendorResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateRechargeWithVendorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateRechargeWithVendorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateRechargeWithVendorResponseMultiError) AllErrors() []error { return m }

// InitiateRechargeWithVendorResponseValidationError is the validation error
// returned by InitiateRechargeWithVendorResponse.Validate if the designated
// constraints aren't met.
type InitiateRechargeWithVendorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateRechargeWithVendorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateRechargeWithVendorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateRechargeWithVendorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateRechargeWithVendorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateRechargeWithVendorResponseValidationError) ErrorName() string {
	return "InitiateRechargeWithVendorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateRechargeWithVendorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateRechargeWithVendorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateRechargeWithVendorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateRechargeWithVendorResponseValidationError{}

// Validate checks the field values on EnquireRechargeStatusWithVendorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *EnquireRechargeStatusWithVendorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// EnquireRechargeStatusWithVendorRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// EnquireRechargeStatusWithVendorRequestMultiError, or nil if none found.
func (m *EnquireRechargeStatusWithVendorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireRechargeStatusWithVendorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnquireRechargeStatusWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnquireRechargeStatusWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnquireRechargeStatusWithVendorRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FulfilmentStageClientRequestId

	if len(errors) > 0 {
		return EnquireRechargeStatusWithVendorRequestMultiError(errors)
	}

	return nil
}

// EnquireRechargeStatusWithVendorRequestMultiError is an error wrapping
// multiple validation errors returned by
// EnquireRechargeStatusWithVendorRequest.ValidateAll() if the designated
// constraints aren't met.
type EnquireRechargeStatusWithVendorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireRechargeStatusWithVendorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireRechargeStatusWithVendorRequestMultiError) AllErrors() []error { return m }

// EnquireRechargeStatusWithVendorRequestValidationError is the validation
// error returned by EnquireRechargeStatusWithVendorRequest.Validate if the
// designated constraints aren't met.
type EnquireRechargeStatusWithVendorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireRechargeStatusWithVendorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireRechargeStatusWithVendorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireRechargeStatusWithVendorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireRechargeStatusWithVendorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireRechargeStatusWithVendorRequestValidationError) ErrorName() string {
	return "EnquireRechargeStatusWithVendorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireRechargeStatusWithVendorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireRechargeStatusWithVendorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireRechargeStatusWithVendorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireRechargeStatusWithVendorRequestValidationError{}

// Validate checks the field values on EnquireRechargeStatusWithVendorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *EnquireRechargeStatusWithVendorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// EnquireRechargeStatusWithVendorResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// EnquireRechargeStatusWithVendorResponseMultiError, or nil if none found.
func (m *EnquireRechargeStatusWithVendorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireRechargeStatusWithVendorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnquireRechargeStatusWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnquireRechargeStatusWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnquireRechargeStatusWithVendorResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EnquireRechargeStatusWithVendorResponseMultiError(errors)
	}

	return nil
}

// EnquireRechargeStatusWithVendorResponseMultiError is an error wrapping
// multiple validation errors returned by
// EnquireRechargeStatusWithVendorResponse.ValidateAll() if the designated
// constraints aren't met.
type EnquireRechargeStatusWithVendorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireRechargeStatusWithVendorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireRechargeStatusWithVendorResponseMultiError) AllErrors() []error { return m }

// EnquireRechargeStatusWithVendorResponseValidationError is the validation
// error returned by EnquireRechargeStatusWithVendorResponse.Validate if the
// designated constraints aren't met.
type EnquireRechargeStatusWithVendorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireRechargeStatusWithVendorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireRechargeStatusWithVendorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireRechargeStatusWithVendorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireRechargeStatusWithVendorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireRechargeStatusWithVendorResponseValidationError) ErrorName() string {
	return "EnquireRechargeStatusWithVendorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireRechargeStatusWithVendorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireRechargeStatusWithVendorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireRechargeStatusWithVendorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireRechargeStatusWithVendorResponseValidationError{}

// Validate checks the field values on ShouldInitiateRechargeRefundOrderRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ShouldInitiateRechargeRefundOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ShouldInitiateRechargeRefundOrderRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ShouldInitiateRechargeRefundOrderRequestMultiError, or nil if none found.
func (m *ShouldInitiateRechargeRefundOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ShouldInitiateRechargeRefundOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ShouldInitiateRechargeRefundOrderRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ShouldInitiateRechargeRefundOrderRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShouldInitiateRechargeRefundOrderRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ShouldInitiateRechargeRefundOrderRequestMultiError(errors)
	}

	return nil
}

// ShouldInitiateRechargeRefundOrderRequestMultiError is an error wrapping
// multiple validation errors returned by
// ShouldInitiateRechargeRefundOrderRequest.ValidateAll() if the designated
// constraints aren't met.
type ShouldInitiateRechargeRefundOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShouldInitiateRechargeRefundOrderRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShouldInitiateRechargeRefundOrderRequestMultiError) AllErrors() []error { return m }

// ShouldInitiateRechargeRefundOrderRequestValidationError is the validation
// error returned by ShouldInitiateRechargeRefundOrderRequest.Validate if the
// designated constraints aren't met.
type ShouldInitiateRechargeRefundOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShouldInitiateRechargeRefundOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShouldInitiateRechargeRefundOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShouldInitiateRechargeRefundOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShouldInitiateRechargeRefundOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShouldInitiateRechargeRefundOrderRequestValidationError) ErrorName() string {
	return "ShouldInitiateRechargeRefundOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ShouldInitiateRechargeRefundOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShouldInitiateRechargeRefundOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShouldInitiateRechargeRefundOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShouldInitiateRechargeRefundOrderRequestValidationError{}

// Validate checks the field values on
// ShouldInitiateRechargeRefundOrderResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ShouldInitiateRechargeRefundOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ShouldInitiateRechargeRefundOrderResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ShouldInitiateRechargeRefundOrderResponseMultiError, or nil if none found.
func (m *ShouldInitiateRechargeRefundOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ShouldInitiateRechargeRefundOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ShouldInitiateRechargeRefundOrderResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ShouldInitiateRechargeRefundOrderResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShouldInitiateRechargeRefundOrderResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShouldInitiateRefund

	if len(errors) > 0 {
		return ShouldInitiateRechargeRefundOrderResponseMultiError(errors)
	}

	return nil
}

// ShouldInitiateRechargeRefundOrderResponseMultiError is an error wrapping
// multiple validation errors returned by
// ShouldInitiateRechargeRefundOrderResponse.ValidateAll() if the designated
// constraints aren't met.
type ShouldInitiateRechargeRefundOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShouldInitiateRechargeRefundOrderResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShouldInitiateRechargeRefundOrderResponseMultiError) AllErrors() []error { return m }

// ShouldInitiateRechargeRefundOrderResponseValidationError is the validation
// error returned by ShouldInitiateRechargeRefundOrderResponse.Validate if the
// designated constraints aren't met.
type ShouldInitiateRechargeRefundOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShouldInitiateRechargeRefundOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShouldInitiateRechargeRefundOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShouldInitiateRechargeRefundOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShouldInitiateRechargeRefundOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShouldInitiateRechargeRefundOrderResponseValidationError) ErrorName() string {
	return "ShouldInitiateRechargeRefundOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ShouldInitiateRechargeRefundOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShouldInitiateRechargeRefundOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShouldInitiateRechargeRefundOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShouldInitiateRechargeRefundOrderResponseValidationError{}

// Validate checks the field values on InitiateRechargeRefundOrderRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitiateRechargeRefundOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateRechargeRefundOrderRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiateRechargeRefundOrderRequestMultiError, or nil if none found.
func (m *InitiateRechargeRefundOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateRechargeRefundOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateRechargeRefundOrderRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateRechargeRefundOrderRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateRechargeRefundOrderRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RefundClientRequestId

	if len(errors) > 0 {
		return InitiateRechargeRefundOrderRequestMultiError(errors)
	}

	return nil
}

// InitiateRechargeRefundOrderRequestMultiError is an error wrapping multiple
// validation errors returned by
// InitiateRechargeRefundOrderRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateRechargeRefundOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateRechargeRefundOrderRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateRechargeRefundOrderRequestMultiError) AllErrors() []error { return m }

// InitiateRechargeRefundOrderRequestValidationError is the validation error
// returned by InitiateRechargeRefundOrderRequest.Validate if the designated
// constraints aren't met.
type InitiateRechargeRefundOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateRechargeRefundOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateRechargeRefundOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateRechargeRefundOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateRechargeRefundOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateRechargeRefundOrderRequestValidationError) ErrorName() string {
	return "InitiateRechargeRefundOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateRechargeRefundOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateRechargeRefundOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateRechargeRefundOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateRechargeRefundOrderRequestValidationError{}

// Validate checks the field values on InitiateRechargeRefundOrderResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitiateRechargeRefundOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateRechargeRefundOrderResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiateRechargeRefundOrderResponseMultiError, or nil if none found.
func (m *InitiateRechargeRefundOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateRechargeRefundOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateRechargeRefundOrderResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateRechargeRefundOrderResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateRechargeRefundOrderResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateRechargeRefundOrderResponseMultiError(errors)
	}

	return nil
}

// InitiateRechargeRefundOrderResponseMultiError is an error wrapping multiple
// validation errors returned by
// InitiateRechargeRefundOrderResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateRechargeRefundOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateRechargeRefundOrderResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateRechargeRefundOrderResponseMultiError) AllErrors() []error { return m }

// InitiateRechargeRefundOrderResponseValidationError is the validation error
// returned by InitiateRechargeRefundOrderResponse.Validate if the designated
// constraints aren't met.
type InitiateRechargeRefundOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateRechargeRefundOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateRechargeRefundOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateRechargeRefundOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateRechargeRefundOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateRechargeRefundOrderResponseValidationError) ErrorName() string {
	return "InitiateRechargeRefundOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateRechargeRefundOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateRechargeRefundOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateRechargeRefundOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateRechargeRefundOrderResponseValidationError{}

// Validate checks the field values on EnquireRechargeRefundOrderStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *EnquireRechargeRefundOrderStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// EnquireRechargeRefundOrderStatusRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// EnquireRechargeRefundOrderStatusRequestMultiError, or nil if none found.
func (m *EnquireRechargeRefundOrderStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireRechargeRefundOrderStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnquireRechargeRefundOrderStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnquireRechargeRefundOrderStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnquireRechargeRefundOrderStatusRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RefundClientRequestId

	if len(errors) > 0 {
		return EnquireRechargeRefundOrderStatusRequestMultiError(errors)
	}

	return nil
}

// EnquireRechargeRefundOrderStatusRequestMultiError is an error wrapping
// multiple validation errors returned by
// EnquireRechargeRefundOrderStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type EnquireRechargeRefundOrderStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireRechargeRefundOrderStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireRechargeRefundOrderStatusRequestMultiError) AllErrors() []error { return m }

// EnquireRechargeRefundOrderStatusRequestValidationError is the validation
// error returned by EnquireRechargeRefundOrderStatusRequest.Validate if the
// designated constraints aren't met.
type EnquireRechargeRefundOrderStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireRechargeRefundOrderStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireRechargeRefundOrderStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireRechargeRefundOrderStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireRechargeRefundOrderStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireRechargeRefundOrderStatusRequestValidationError) ErrorName() string {
	return "EnquireRechargeRefundOrderStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireRechargeRefundOrderStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireRechargeRefundOrderStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireRechargeRefundOrderStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireRechargeRefundOrderStatusRequestValidationError{}

// Validate checks the field values on EnquireRechargeRefundOrderStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *EnquireRechargeRefundOrderStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// EnquireRechargeRefundOrderStatusResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// EnquireRechargeRefundOrderStatusResponseMultiError, or nil if none found.
func (m *EnquireRechargeRefundOrderStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireRechargeRefundOrderStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnquireRechargeRefundOrderStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnquireRechargeRefundOrderStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnquireRechargeRefundOrderStatusResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EnquireRechargeRefundOrderStatusResponseMultiError(errors)
	}

	return nil
}

// EnquireRechargeRefundOrderStatusResponseMultiError is an error wrapping
// multiple validation errors returned by
// EnquireRechargeRefundOrderStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type EnquireRechargeRefundOrderStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireRechargeRefundOrderStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireRechargeRefundOrderStatusResponseMultiError) AllErrors() []error { return m }

// EnquireRechargeRefundOrderStatusResponseValidationError is the validation
// error returned by EnquireRechargeRefundOrderStatusResponse.Validate if the
// designated constraints aren't met.
type EnquireRechargeRefundOrderStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireRechargeRefundOrderStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireRechargeRefundOrderStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireRechargeRefundOrderStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireRechargeRefundOrderStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireRechargeRefundOrderStatusResponseValidationError) ErrorName() string {
	return "EnquireRechargeRefundOrderStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireRechargeRefundOrderStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireRechargeRefundOrderStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireRechargeRefundOrderStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireRechargeRefundOrderStatusResponseValidationError{}

// Validate checks the field values on ShouldInitiateRechargeFulfilmentRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ShouldInitiateRechargeFulfilmentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ShouldInitiateRechargeFulfilmentRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ShouldInitiateRechargeFulfilmentRequestMultiError, or nil if none found.
func (m *ShouldInitiateRechargeFulfilmentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ShouldInitiateRechargeFulfilmentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ShouldInitiateRechargeFulfilmentRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ShouldInitiateRechargeFulfilmentRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShouldInitiateRechargeFulfilmentRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ShouldInitiateRechargeFulfilmentRequestMultiError(errors)
	}

	return nil
}

// ShouldInitiateRechargeFulfilmentRequestMultiError is an error wrapping
// multiple validation errors returned by
// ShouldInitiateRechargeFulfilmentRequest.ValidateAll() if the designated
// constraints aren't met.
type ShouldInitiateRechargeFulfilmentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShouldInitiateRechargeFulfilmentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShouldInitiateRechargeFulfilmentRequestMultiError) AllErrors() []error { return m }

// ShouldInitiateRechargeFulfilmentRequestValidationError is the validation
// error returned by ShouldInitiateRechargeFulfilmentRequest.Validate if the
// designated constraints aren't met.
type ShouldInitiateRechargeFulfilmentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShouldInitiateRechargeFulfilmentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShouldInitiateRechargeFulfilmentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShouldInitiateRechargeFulfilmentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShouldInitiateRechargeFulfilmentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShouldInitiateRechargeFulfilmentRequestValidationError) ErrorName() string {
	return "ShouldInitiateRechargeFulfilmentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ShouldInitiateRechargeFulfilmentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShouldInitiateRechargeFulfilmentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShouldInitiateRechargeFulfilmentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShouldInitiateRechargeFulfilmentRequestValidationError{}

// Validate checks the field values on ShouldInitiateRechargeFulfilmentResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ShouldInitiateRechargeFulfilmentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ShouldInitiateRechargeFulfilmentResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ShouldInitiateRechargeFulfilmentResponseMultiError, or nil if none found.
func (m *ShouldInitiateRechargeFulfilmentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ShouldInitiateRechargeFulfilmentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ShouldInitiateRechargeFulfilmentResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ShouldInitiateRechargeFulfilmentResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShouldInitiateRechargeFulfilmentResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShouldInitiateFulfilment

	if len(errors) > 0 {
		return ShouldInitiateRechargeFulfilmentResponseMultiError(errors)
	}

	return nil
}

// ShouldInitiateRechargeFulfilmentResponseMultiError is an error wrapping
// multiple validation errors returned by
// ShouldInitiateRechargeFulfilmentResponse.ValidateAll() if the designated
// constraints aren't met.
type ShouldInitiateRechargeFulfilmentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShouldInitiateRechargeFulfilmentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShouldInitiateRechargeFulfilmentResponseMultiError) AllErrors() []error { return m }

// ShouldInitiateRechargeFulfilmentResponseValidationError is the validation
// error returned by ShouldInitiateRechargeFulfilmentResponse.Validate if the
// designated constraints aren't met.
type ShouldInitiateRechargeFulfilmentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShouldInitiateRechargeFulfilmentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShouldInitiateRechargeFulfilmentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShouldInitiateRechargeFulfilmentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShouldInitiateRechargeFulfilmentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShouldInitiateRechargeFulfilmentResponseValidationError) ErrorName() string {
	return "ShouldInitiateRechargeFulfilmentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ShouldInitiateRechargeFulfilmentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShouldInitiateRechargeFulfilmentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShouldInitiateRechargeFulfilmentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShouldInitiateRechargeFulfilmentResponseValidationError{}
