// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/billpay/enums/enums.pb.go

package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the RechargeAccountType in string format in DB
func (p RechargeAccountType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing RechargeAccountType while reading from DB
func (p *RechargeAccountType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := RechargeAccountType_value[val]
	if !ok {
		return fmt.Errorf("unexpected RechargeAccountType value: %s", val)
	}
	*p = RechargeAccountType(valInt)
	return nil
}

// Marshaler interface implementation for RechargeAccountType
func (x RechargeAccountType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for RechargeAccountType
func (x *RechargeAccountType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = RechargeAccountType(RechargeAccountType_value[val])
	return nil
}

// Valuer interface implementation for storing the RechargeOrderStatus in string format in DB
func (p RechargeOrderStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing RechargeOrderStatus while reading from DB
func (p *RechargeOrderStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := RechargeOrderStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected RechargeOrderStatus value: %s", val)
	}
	*p = RechargeOrderStatus(valInt)
	return nil
}

// Marshaler interface implementation for RechargeOrderStatus
func (x RechargeOrderStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for RechargeOrderStatus
func (x *RechargeOrderStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = RechargeOrderStatus(RechargeOrderStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the RechargeOrderSubStatus in string format in DB
func (p RechargeOrderSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing RechargeOrderSubStatus while reading from DB
func (p *RechargeOrderSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := RechargeOrderSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected RechargeOrderSubStatus value: %s", val)
	}
	*p = RechargeOrderSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for RechargeOrderSubStatus
func (x RechargeOrderSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for RechargeOrderSubStatus
func (x *RechargeOrderSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = RechargeOrderSubStatus(RechargeOrderSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the RechargeStage in string format in DB
func (p RechargeStage) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing RechargeStage while reading from DB
func (p *RechargeStage) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := RechargeStage_value[val]
	if !ok {
		return fmt.Errorf("unexpected RechargeStage value: %s", val)
	}
	*p = RechargeStage(valInt)
	return nil
}

// Marshaler interface implementation for RechargeStage
func (x RechargeStage) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for RechargeStage
func (x *RechargeStage) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = RechargeStage(RechargeStage_value[val])
	return nil
}

// Valuer interface implementation for storing the RechargeStageStatus in string format in DB
func (p RechargeStageStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing RechargeStageStatus while reading from DB
func (p *RechargeStageStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := RechargeStageStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected RechargeStageStatus value: %s", val)
	}
	*p = RechargeStageStatus(valInt)
	return nil
}

// Marshaler interface implementation for RechargeStageStatus
func (x RechargeStageStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for RechargeStageStatus
func (x *RechargeStageStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = RechargeStageStatus(RechargeStageStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the Operator in string format in DB
func (p Operator) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Operator while reading from DB
func (p *Operator) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Operator_value[val]
	if !ok {
		return fmt.Errorf("unexpected Operator value: %s", val)
	}
	*p = Operator(valInt)
	return nil
}

// Marshaler interface implementation for Operator
func (x Operator) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Operator
func (x *Operator) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Operator(Operator_value[val])
	return nil
}
