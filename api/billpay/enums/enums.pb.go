//go:generate gen_sql -types=RechargeAccountType,RechargeOrderStatus,RechargeOrderSubStatus,RechargeStage,RechargeStageStatus,Operator

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/billpay/enums/enums.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Enum for account type
type RechargeAccountType int32

const (
	RechargeAccountType_RECHARGE_ACCOUNT_TYPE_UNSPECIFIED RechargeAccountType = 0
	RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE      RechargeAccountType = 1
)

// Enum value maps for RechargeAccountType.
var (
	RechargeAccountType_name = map[int32]string{
		0: "RECHARGE_ACCOUNT_TYPE_UNSPECIFIED",
		1: "RECHARGE_ACCOUNT_TYPE_MOBILE",
	}
	RechargeAccountType_value = map[string]int32{
		"RECHARGE_ACCOUNT_TYPE_UNSPECIFIED": 0,
		"RECHARGE_ACCOUNT_TYPE_MOBILE":      1,
	}
)

func (x RechargeAccountType) Enum() *RechargeAccountType {
	p := new(RechargeAccountType)
	*p = x
	return p
}

func (x RechargeAccountType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RechargeAccountType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_billpay_enums_enums_proto_enumTypes[0].Descriptor()
}

func (RechargeAccountType) Type() protoreflect.EnumType {
	return &file_api_billpay_enums_enums_proto_enumTypes[0]
}

func (x RechargeAccountType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RechargeAccountType.Descriptor instead.
func (RechargeAccountType) EnumDescriptor() ([]byte, []int) {
	return file_api_billpay_enums_enums_proto_rawDescGZIP(), []int{0}
}

// Enum for RechargeOrder status
type RechargeOrderStatus int32

const (
	RechargeOrderStatus_RECHARGE_ORDER_STATUS_UNSPECIFIED         RechargeOrderStatus = 0
	RechargeOrderStatus_RECHARGE_ORDER_STATUS_INITIATED           RechargeOrderStatus = 1
	RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS         RechargeOrderStatus = 2
	RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS             RechargeOrderStatus = 3
	RechargeOrderStatus_RECHARGE_ORDER_STATUS_FAILED              RechargeOrderStatus = 4
	RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION RechargeOrderStatus = 5
	RechargeOrderStatus_RECHARGE_ORDER_STATUS_EXPIRED             RechargeOrderStatus = 6
)

// Enum value maps for RechargeOrderStatus.
var (
	RechargeOrderStatus_name = map[int32]string{
		0: "RECHARGE_ORDER_STATUS_UNSPECIFIED",
		1: "RECHARGE_ORDER_STATUS_INITIATED",
		2: "RECHARGE_ORDER_STATUS_IN_PROGRESS",
		3: "RECHARGE_ORDER_STATUS_SUCCESS",
		4: "RECHARGE_ORDER_STATUS_FAILED",
		5: "RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION",
		6: "RECHARGE_ORDER_STATUS_EXPIRED",
	}
	RechargeOrderStatus_value = map[string]int32{
		"RECHARGE_ORDER_STATUS_UNSPECIFIED":         0,
		"RECHARGE_ORDER_STATUS_INITIATED":           1,
		"RECHARGE_ORDER_STATUS_IN_PROGRESS":         2,
		"RECHARGE_ORDER_STATUS_SUCCESS":             3,
		"RECHARGE_ORDER_STATUS_FAILED":              4,
		"RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION": 5,
		"RECHARGE_ORDER_STATUS_EXPIRED":             6,
	}
)

func (x RechargeOrderStatus) Enum() *RechargeOrderStatus {
	p := new(RechargeOrderStatus)
	*p = x
	return p
}

func (x RechargeOrderStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RechargeOrderStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_billpay_enums_enums_proto_enumTypes[1].Descriptor()
}

func (RechargeOrderStatus) Type() protoreflect.EnumType {
	return &file_api_billpay_enums_enums_proto_enumTypes[1]
}

func (x RechargeOrderStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RechargeOrderStatus.Descriptor instead.
func (RechargeOrderStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_billpay_enums_enums_proto_rawDescGZIP(), []int{1}
}

// Enum for RechargeOrder substatus (can be extended later)
type RechargeOrderSubStatus int32

const (
	RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED RechargeOrderSubStatus = 0
	RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED    RechargeOrderSubStatus = 1
)

// Enum value maps for RechargeOrderSubStatus.
var (
	RechargeOrderSubStatus_name = map[int32]string{
		0: "RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED",
		1: "RECHARGE_ORDER_SUB_STATUS_REFUNDED",
	}
	RechargeOrderSubStatus_value = map[string]int32{
		"RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED": 0,
		"RECHARGE_ORDER_SUB_STATUS_REFUNDED":    1,
	}
)

func (x RechargeOrderSubStatus) Enum() *RechargeOrderSubStatus {
	p := new(RechargeOrderSubStatus)
	*p = x
	return p
}

func (x RechargeOrderSubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RechargeOrderSubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_billpay_enums_enums_proto_enumTypes[2].Descriptor()
}

func (RechargeOrderSubStatus) Type() protoreflect.EnumType {
	return &file_api_billpay_enums_enums_proto_enumTypes[2]
}

func (x RechargeOrderSubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RechargeOrderSubStatus.Descriptor instead.
func (RechargeOrderSubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_billpay_enums_enums_proto_rawDescGZIP(), []int{2}
}

// Enum for RechargeStage (stage type)
type RechargeStage int32

const (
	RechargeStage_RECHARGE_STAGE_UNSPECIFIED RechargeStage = 0
	RechargeStage_RECHARGE_STAGE_PAYMENT     RechargeStage = 1
	RechargeStage_RECHARGE_STAGE_FULFILLMENT RechargeStage = 2
	RechargeStage_RECHARGE_STAGE_REFUND      RechargeStage = 3
	RechargeStage_RECHARGE_STAGE_RECON       RechargeStage = 4
)

// Enum value maps for RechargeStage.
var (
	RechargeStage_name = map[int32]string{
		0: "RECHARGE_STAGE_UNSPECIFIED",
		1: "RECHARGE_STAGE_PAYMENT",
		2: "RECHARGE_STAGE_FULFILLMENT",
		3: "RECHARGE_STAGE_REFUND",
		4: "RECHARGE_STAGE_RECON",
	}
	RechargeStage_value = map[string]int32{
		"RECHARGE_STAGE_UNSPECIFIED": 0,
		"RECHARGE_STAGE_PAYMENT":     1,
		"RECHARGE_STAGE_FULFILLMENT": 2,
		"RECHARGE_STAGE_REFUND":      3,
		"RECHARGE_STAGE_RECON":       4,
	}
)

func (x RechargeStage) Enum() *RechargeStage {
	p := new(RechargeStage)
	*p = x
	return p
}

func (x RechargeStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RechargeStage) Descriptor() protoreflect.EnumDescriptor {
	return file_api_billpay_enums_enums_proto_enumTypes[3].Descriptor()
}

func (RechargeStage) Type() protoreflect.EnumType {
	return &file_api_billpay_enums_enums_proto_enumTypes[3]
}

func (x RechargeStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RechargeStage.Descriptor instead.
func (RechargeStage) EnumDescriptor() ([]byte, []int) {
	return file_api_billpay_enums_enums_proto_rawDescGZIP(), []int{3}
}

// Enum for RechargeStageStatus
type RechargeStageStatus int32

const (
	RechargeStageStatus_RECHARGE_STAGE_STATUS_UNSPECIFIED         RechargeStageStatus = 0
	RechargeStageStatus_RECHARGE_STAGE_STATUS_INITIATED           RechargeStageStatus = 1
	RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS         RechargeStageStatus = 2
	RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL          RechargeStageStatus = 3
	RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED              RechargeStageStatus = 4
	RechargeStageStatus_RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION RechargeStageStatus = 5
	RechargeStageStatus_RECHARGE_STAGE_STATUS_EXPIRED             RechargeStageStatus = 6
)

// Enum value maps for RechargeStageStatus.
var (
	RechargeStageStatus_name = map[int32]string{
		0: "RECHARGE_STAGE_STATUS_UNSPECIFIED",
		1: "RECHARGE_STAGE_STATUS_INITIATED",
		2: "RECHARGE_STAGE_STATUS_IN_PROGRESS",
		3: "RECHARGE_STAGE_STATUS_SUCCESSFUL",
		4: "RECHARGE_STAGE_STATUS_FAILED",
		5: "RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION",
		6: "RECHARGE_STAGE_STATUS_EXPIRED",
	}
	RechargeStageStatus_value = map[string]int32{
		"RECHARGE_STAGE_STATUS_UNSPECIFIED":         0,
		"RECHARGE_STAGE_STATUS_INITIATED":           1,
		"RECHARGE_STAGE_STATUS_IN_PROGRESS":         2,
		"RECHARGE_STAGE_STATUS_SUCCESSFUL":          3,
		"RECHARGE_STAGE_STATUS_FAILED":              4,
		"RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION": 5,
		"RECHARGE_STAGE_STATUS_EXPIRED":             6,
	}
)

func (x RechargeStageStatus) Enum() *RechargeStageStatus {
	p := new(RechargeStageStatus)
	*p = x
	return p
}

func (x RechargeStageStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RechargeStageStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_billpay_enums_enums_proto_enumTypes[4].Descriptor()
}

func (RechargeStageStatus) Type() protoreflect.EnumType {
	return &file_api_billpay_enums_enums_proto_enumTypes[4]
}

func (x RechargeStageStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RechargeStageStatus.Descriptor instead.
func (RechargeStageStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_billpay_enums_enums_proto_rawDescGZIP(), []int{4}
}

type Operator int32

const (
	// Unspecified operator
	Operator_OPERATOR_UNSPECIFIED Operator = 0
	// Jio operator
	Operator_OPERATOR_JIO Operator = 1
	// Airtel operator
	Operator_OPERATOR_AIRTEL Operator = 2
	// Vi operator
	Operator_OPERATOR_VI Operator = 3
	// BSNL operator
	Operator_OPERATOR_BSNL Operator = 4
	// MTNL operator
	Operator_OPERATOR_MTNL Operator = 5
)

// Enum value maps for Operator.
var (
	Operator_name = map[int32]string{
		0: "OPERATOR_UNSPECIFIED",
		1: "OPERATOR_JIO",
		2: "OPERATOR_AIRTEL",
		3: "OPERATOR_VI",
		4: "OPERATOR_BSNL",
		5: "OPERATOR_MTNL",
	}
	Operator_value = map[string]int32{
		"OPERATOR_UNSPECIFIED": 0,
		"OPERATOR_JIO":         1,
		"OPERATOR_AIRTEL":      2,
		"OPERATOR_VI":          3,
		"OPERATOR_BSNL":        4,
		"OPERATOR_MTNL":        5,
	}
)

func (x Operator) Enum() *Operator {
	p := new(Operator)
	*p = x
	return p
}

func (x Operator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Operator) Descriptor() protoreflect.EnumDescriptor {
	return file_api_billpay_enums_enums_proto_enumTypes[5].Descriptor()
}

func (Operator) Type() protoreflect.EnumType {
	return &file_api_billpay_enums_enums_proto_enumTypes[5]
}

func (x Operator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Operator.Descriptor instead.
func (Operator) EnumDescriptor() ([]byte, []int) {
	return file_api_billpay_enums_enums_proto_rawDescGZIP(), []int{5}
}

type PaymentMode int32

const (
	// Not specified or unknown payment mode.
	PaymentMode_PAYMENT_MODE_UNSPECIFIED PaymentMode = 0
	// Internet Banking.
	PaymentMode_PAYMENT_MODE_INTERNET_BANKING PaymentMode = 1
	// Unified Payments Interface.
	PaymentMode_PAYMENT_MODE_UPI PaymentMode = 2
	// Debit Card.
	PaymentMode_PAYMENT_MODE_DEBIT_CARD PaymentMode = 3
	// Credit Card.
	PaymentMode_PAYMENT_MODE_CREDIT_CARD PaymentMode = 4
	// Wallet.
	PaymentMode_PAYMENT_MODE_WALLET PaymentMode = 5
)

// Enum value maps for PaymentMode.
var (
	PaymentMode_name = map[int32]string{
		0: "PAYMENT_MODE_UNSPECIFIED",
		1: "PAYMENT_MODE_INTERNET_BANKING",
		2: "PAYMENT_MODE_UPI",
		3: "PAYMENT_MODE_DEBIT_CARD",
		4: "PAYMENT_MODE_CREDIT_CARD",
		5: "PAYMENT_MODE_WALLET",
	}
	PaymentMode_value = map[string]int32{
		"PAYMENT_MODE_UNSPECIFIED":      0,
		"PAYMENT_MODE_INTERNET_BANKING": 1,
		"PAYMENT_MODE_UPI":              2,
		"PAYMENT_MODE_DEBIT_CARD":       3,
		"PAYMENT_MODE_CREDIT_CARD":      4,
		"PAYMENT_MODE_WALLET":           5,
	}
)

func (x PaymentMode) Enum() *PaymentMode {
	p := new(PaymentMode)
	*p = x
	return p
}

func (x PaymentMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_billpay_enums_enums_proto_enumTypes[6].Descriptor()
}

func (PaymentMode) Type() protoreflect.EnumType {
	return &file_api_billpay_enums_enums_proto_enumTypes[6]
}

func (x PaymentMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentMode.Descriptor instead.
func (PaymentMode) EnumDescriptor() ([]byte, []int) {
	return file_api_billpay_enums_enums_proto_rawDescGZIP(), []int{6}
}

var File_api_billpay_enums_enums_proto protoreflect.FileDescriptor

var file_api_billpay_enums_enums_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x11, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2a, 0x5e, 0x0a, 0x13, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x45, 0x43,
	0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45,
	0x10, 0x01, 0x2a, 0x9f, 0x02, 0x0a, 0x13, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x45,
	0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52,
	0x47, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x21, 0x0a,
	0x1d, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03,
	0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x04, 0x12, 0x2d, 0x0a, 0x29, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55,
	0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x05, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52,
	0x45, 0x44, 0x10, 0x06, 0x2a, 0x6b, 0x0a, 0x16, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29,
	0x0a, 0x25, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x52, 0x45, 0x43,
	0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x45, 0x44, 0x10,
	0x01, 0x2a, 0xa0, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12,
	0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x46, 0x55, 0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12,
	0x19, 0x0a, 0x15, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45,
	0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x43,
	0x4f, 0x4e, 0x10, 0x04, 0x2a, 0xa2, 0x02, 0x0a, 0x13, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x21,
	0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49,
	0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x45, 0x43, 0x48,
	0x41, 0x52, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12,
	0x24, 0x0a, 0x20, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x46, 0x55, 0x4c, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x2d, 0x0a, 0x29, 0x52, 0x45, 0x43, 0x48, 0x41,
	0x52, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x45, 0x4e,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52,
	0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x06, 0x2a, 0x82, 0x01, 0x0a, 0x08, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54,
	0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x10, 0x0a, 0x0c, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x4a, 0x49, 0x4f,
	0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x41,
	0x49, 0x52, 0x54, 0x45, 0x4c, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x4f, 0x52, 0x5f, 0x56, 0x49, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x4f, 0x50, 0x45, 0x52,
	0x41, 0x54, 0x4f, 0x52, 0x5f, 0x42, 0x53, 0x4e, 0x4c, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x4d, 0x54, 0x4e, 0x4c, 0x10, 0x05, 0x2a, 0xb8,
	0x01, 0x0a, 0x0b, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1c,
	0x0a, 0x18, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x45, 0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12,
	0x14, 0x0a, 0x10, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f,
	0x55, 0x50, 0x49, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x4f,
	0x44, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x04,
	0x12, 0x17, 0x0a, 0x13, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x4f, 0x44, 0x45,
	0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x10, 0x05, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_billpay_enums_enums_proto_rawDescOnce sync.Once
	file_api_billpay_enums_enums_proto_rawDescData = file_api_billpay_enums_enums_proto_rawDesc
)

func file_api_billpay_enums_enums_proto_rawDescGZIP() []byte {
	file_api_billpay_enums_enums_proto_rawDescOnce.Do(func() {
		file_api_billpay_enums_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_billpay_enums_enums_proto_rawDescData)
	})
	return file_api_billpay_enums_enums_proto_rawDescData
}

var file_api_billpay_enums_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_api_billpay_enums_enums_proto_goTypes = []interface{}{
	(RechargeAccountType)(0),    // 0: api.billpay.enums.RechargeAccountType
	(RechargeOrderStatus)(0),    // 1: api.billpay.enums.RechargeOrderStatus
	(RechargeOrderSubStatus)(0), // 2: api.billpay.enums.RechargeOrderSubStatus
	(RechargeStage)(0),          // 3: api.billpay.enums.RechargeStage
	(RechargeStageStatus)(0),    // 4: api.billpay.enums.RechargeStageStatus
	(Operator)(0),               // 5: api.billpay.enums.Operator
	(PaymentMode)(0),            // 6: api.billpay.enums.PaymentMode
}
var file_api_billpay_enums_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_billpay_enums_enums_proto_init() }
func file_api_billpay_enums_enums_proto_init() {
	if File_api_billpay_enums_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_billpay_enums_enums_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_billpay_enums_enums_proto_goTypes,
		DependencyIndexes: file_api_billpay_enums_enums_proto_depIdxs,
		EnumInfos:         file_api_billpay_enums_enums_proto_enumTypes,
	}.Build()
	File_api_billpay_enums_enums_proto = out.File
	file_api_billpay_enums_enums_proto_rawDesc = nil
	file_api_billpay_enums_enums_proto_goTypes = nil
	file_api_billpay_enums_enums_proto_depIdxs = nil
}
