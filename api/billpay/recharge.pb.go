//go:generate gen_sql -types=RechargeOrder,MobileRechargePlan,PlanDetails,RechargeOrderStage,RechargeStageData,RechargePoolAccountPaymentStageData,RechargeFulfillmentStageData,RechargeRefundStageData,RechargeReconStageDetails,RechargeDetails

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/billpay/recharge.proto

package billpaypb

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	enums "github.com/epifi/gamma/api/billpay/enums"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RechargeOrder entity
type RechargeOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string                    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ClientRequestId string                    `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	ActorId         string                    `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AccountType     enums.RechargeAccountType `protobuf:"varint,4,opt,name=account_type,json=accountType,proto3,enum=api.billpay.enums.RechargeAccountType" json:"account_type,omitempty"`
	// raw phone number will be the account identifier for Mobile Recharge account type
	AccountIdentifier string         `protobuf:"bytes,5,opt,name=account_identifier,json=accountIdentifier,proto3" json:"account_identifier,omitempty"`
	AccountOperator   enums.Operator `protobuf:"varint,6,opt,name=account_operator,json=accountOperator,proto3,enum=api.billpay.enums.Operator" json:"account_operator,omitempty"`
	PlanDetails       *PlanDetails   `protobuf:"bytes,7,opt,name=plan_details,json=planDetails,proto3" json:"plan_details,omitempty"`
	// PlanDetails->MobileRechargePlanDetails->>Amount->>Units + nanos is the PlanIdentifier for Mobile Recharge account type
	PlanIdentifier string                       `protobuf:"bytes,8,opt,name=plan_identifier,json=planIdentifier,proto3" json:"plan_identifier,omitempty"`
	Status         enums.RechargeOrderStatus    `protobuf:"varint,9,opt,name=status,proto3,enum=api.billpay.enums.RechargeOrderStatus" json:"status,omitempty"`
	SubStatus      enums.RechargeOrderSubStatus `protobuf:"varint,10,opt,name=sub_status,json=subStatus,proto3,enum=api.billpay.enums.RechargeOrderSubStatus" json:"sub_status,omitempty"`
	CreatedAt      *timestamppb.Timestamp       `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      *timestamppb.Timestamp       `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	CompletedAt    *timestamppb.Timestamp       `protobuf:"bytes,13,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
}

func (x *RechargeOrder) Reset() {
	*x = RechargeOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_recharge_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RechargeOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeOrder) ProtoMessage() {}

func (x *RechargeOrder) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_recharge_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeOrder.ProtoReflect.Descriptor instead.
func (*RechargeOrder) Descriptor() ([]byte, []int) {
	return file_api_billpay_recharge_proto_rawDescGZIP(), []int{0}
}

func (x *RechargeOrder) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RechargeOrder) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *RechargeOrder) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *RechargeOrder) GetAccountType() enums.RechargeAccountType {
	if x != nil {
		return x.AccountType
	}
	return enums.RechargeAccountType(0)
}

func (x *RechargeOrder) GetAccountIdentifier() string {
	if x != nil {
		return x.AccountIdentifier
	}
	return ""
}

func (x *RechargeOrder) GetAccountOperator() enums.Operator {
	if x != nil {
		return x.AccountOperator
	}
	return enums.Operator(0)
}

func (x *RechargeOrder) GetPlanDetails() *PlanDetails {
	if x != nil {
		return x.PlanDetails
	}
	return nil
}

func (x *RechargeOrder) GetPlanIdentifier() string {
	if x != nil {
		return x.PlanIdentifier
	}
	return ""
}

func (x *RechargeOrder) GetStatus() enums.RechargeOrderStatus {
	if x != nil {
		return x.Status
	}
	return enums.RechargeOrderStatus(0)
}

func (x *RechargeOrder) GetSubStatus() enums.RechargeOrderSubStatus {
	if x != nil {
		return x.SubStatus
	}
	return enums.RechargeOrderSubStatus(0)
}

func (x *RechargeOrder) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RechargeOrder) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *RechargeOrder) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

// Billpay-specific plan details to avoid tight coupling with vendorgateway
type PlanDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MobileRechargePlanDetails *MobileRechargePlan `protobuf:"bytes,1,opt,name=mobile_recharge_plan_details,json=mobileRechargePlanDetails,proto3" json:"mobile_recharge_plan_details,omitempty"` // for Mobile Recharge
}

func (x *PlanDetails) Reset() {
	*x = PlanDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_recharge_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlanDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanDetails) ProtoMessage() {}

func (x *PlanDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_recharge_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanDetails.ProtoReflect.Descriptor instead.
func (*PlanDetails) Descriptor() ([]byte, []int) {
	return file_api_billpay_recharge_proto_rawDescGZIP(), []int{1}
}

func (x *PlanDetails) GetMobileRechargePlanDetails() *MobileRechargePlan {
	if x != nil {
		return x.MobileRechargePlanDetails
	}
	return nil
}

// Billpay-specific mobile recharge plan (decoupled from vendorgateway.recharge.Plan)
type MobileRechargePlan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Talktime available with the plan
	Talktime string `protobuf:"bytes,1,opt,name=talktime,proto3" json:"talktime,omitempty"`
	// Name of the prepaid plan
	PlanName string `protobuf:"bytes,2,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`
	// Price of the plan
	Amount *money.Money `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// Validity period of the plan
	Validity string `protobuf:"bytes,4,opt,name=validity,proto3" json:"validity,omitempty"`
	// Details of the plan benefits
	PlanDescription string `protobuf:"bytes,5,opt,name=plan_description,json=planDescription,proto3" json:"plan_description,omitempty"`
	// Service Provider
	ServiceProvider string `protobuf:"bytes,6,opt,name=service_provider,json=serviceProvider,proto3" json:"service_provider,omitempty"`
	// Operator for which this plan is applicable
	Operator enums.Operator `protobuf:"varint,7,opt,name=operator,proto3,enum=api.billpay.enums.Operator" json:"operator,omitempty"`
}

func (x *MobileRechargePlan) Reset() {
	*x = MobileRechargePlan{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_recharge_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MobileRechargePlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MobileRechargePlan) ProtoMessage() {}

func (x *MobileRechargePlan) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_recharge_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MobileRechargePlan.ProtoReflect.Descriptor instead.
func (*MobileRechargePlan) Descriptor() ([]byte, []int) {
	return file_api_billpay_recharge_proto_rawDescGZIP(), []int{2}
}

func (x *MobileRechargePlan) GetTalktime() string {
	if x != nil {
		return x.Talktime
	}
	return ""
}

func (x *MobileRechargePlan) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

func (x *MobileRechargePlan) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *MobileRechargePlan) GetValidity() string {
	if x != nil {
		return x.Validity
	}
	return ""
}

func (x *MobileRechargePlan) GetPlanDescription() string {
	if x != nil {
		return x.PlanDescription
	}
	return ""
}

func (x *MobileRechargePlan) GetServiceProvider() string {
	if x != nil {
		return x.ServiceProvider
	}
	return ""
}

func (x *MobileRechargePlan) GetOperator() enums.Operator {
	if x != nil {
		return x.Operator
	}
	return enums.Operator(0)
}

// RechargeOrderStage entity
type RechargeOrderStage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string                    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	RechargeOrderId string                    `protobuf:"bytes,2,opt,name=recharge_order_id,json=rechargeOrderId,proto3" json:"recharge_order_id,omitempty"`
	ClientRequestId string                    `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	Stage           enums.RechargeStage       `protobuf:"varint,4,opt,name=stage,proto3,enum=api.billpay.enums.RechargeStage" json:"stage,omitempty"`
	Status          enums.RechargeStageStatus `protobuf:"varint,5,opt,name=status,proto3,enum=api.billpay.enums.RechargeStageStatus" json:"status,omitempty"`
	ExpiresAt       *timestamppb.Timestamp    `protobuf:"bytes,6,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	Data            *RechargeStageData        `protobuf:"bytes,7,opt,name=data,proto3" json:"data,omitempty"`
	CreatedAt       *timestamppb.Timestamp    `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt       *timestamppb.Timestamp    `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	CompletedAt     *timestamppb.Timestamp    `protobuf:"bytes,10,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
}

func (x *RechargeOrderStage) Reset() {
	*x = RechargeOrderStage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_recharge_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RechargeOrderStage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeOrderStage) ProtoMessage() {}

func (x *RechargeOrderStage) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_recharge_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeOrderStage.ProtoReflect.Descriptor instead.
func (*RechargeOrderStage) Descriptor() ([]byte, []int) {
	return file_api_billpay_recharge_proto_rawDescGZIP(), []int{3}
}

func (x *RechargeOrderStage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RechargeOrderStage) GetRechargeOrderId() string {
	if x != nil {
		return x.RechargeOrderId
	}
	return ""
}

func (x *RechargeOrderStage) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *RechargeOrderStage) GetStage() enums.RechargeStage {
	if x != nil {
		return x.Stage
	}
	return enums.RechargeStage(0)
}

func (x *RechargeOrderStage) GetStatus() enums.RechargeStageStatus {
	if x != nil {
		return x.Status
	}
	return enums.RechargeStageStatus(0)
}

func (x *RechargeOrderStage) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *RechargeOrderStage) GetData() *RechargeStageData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *RechargeOrderStage) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RechargeOrderStage) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *RechargeOrderStage) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

// RechargeStageData message (to be extended with oneof fields later)
type RechargeStageData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Data:
	//
	//	*RechargeStageData_PoolAccountPaymentDetails
	//	*RechargeStageData_FulfillmentDetails
	//	*RechargeStageData_RefundDetails
	//	*RechargeStageData_ReconDetails
	Data isRechargeStageData_Data `protobuf_oneof:"data"`
}

func (x *RechargeStageData) Reset() {
	*x = RechargeStageData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_recharge_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RechargeStageData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeStageData) ProtoMessage() {}

func (x *RechargeStageData) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_recharge_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeStageData.ProtoReflect.Descriptor instead.
func (*RechargeStageData) Descriptor() ([]byte, []int) {
	return file_api_billpay_recharge_proto_rawDescGZIP(), []int{4}
}

func (m *RechargeStageData) GetData() isRechargeStageData_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *RechargeStageData) GetPoolAccountPaymentDetails() *RechargePoolAccountPaymentStageData {
	if x, ok := x.GetData().(*RechargeStageData_PoolAccountPaymentDetails); ok {
		return x.PoolAccountPaymentDetails
	}
	return nil
}

func (x *RechargeStageData) GetFulfillmentDetails() *RechargeFulfillmentStageData {
	if x, ok := x.GetData().(*RechargeStageData_FulfillmentDetails); ok {
		return x.FulfillmentDetails
	}
	return nil
}

func (x *RechargeStageData) GetRefundDetails() *RechargeRefundStageData {
	if x, ok := x.GetData().(*RechargeStageData_RefundDetails); ok {
		return x.RefundDetails
	}
	return nil
}

func (x *RechargeStageData) GetReconDetails() *RechargeReconStageDetails {
	if x, ok := x.GetData().(*RechargeStageData_ReconDetails); ok {
		return x.ReconDetails
	}
	return nil
}

type isRechargeStageData_Data interface {
	isRechargeStageData_Data()
}

type RechargeStageData_PoolAccountPaymentDetails struct {
	PoolAccountPaymentDetails *RechargePoolAccountPaymentStageData `protobuf:"bytes,1,opt,name=pool_account_payment_details,json=poolAccountPaymentDetails,proto3,oneof"`
}

type RechargeStageData_FulfillmentDetails struct {
	FulfillmentDetails *RechargeFulfillmentStageData `protobuf:"bytes,2,opt,name=fulfillment_details,json=fulfillmentDetails,proto3,oneof"`
}

type RechargeStageData_RefundDetails struct {
	RefundDetails *RechargeRefundStageData `protobuf:"bytes,3,opt,name=refund_details,json=refundDetails,proto3,oneof"`
}

type RechargeStageData_ReconDetails struct {
	ReconDetails *RechargeReconStageDetails `protobuf:"bytes,4,opt,name=recon_details,json=reconDetails,proto3,oneof"`
}

func (*RechargeStageData_PoolAccountPaymentDetails) isRechargeStageData_Data() {}

func (*RechargeStageData_FulfillmentDetails) isRechargeStageData_Data() {}

func (*RechargeStageData_RefundDetails) isRechargeStageData_Data() {}

func (*RechargeStageData_ReconDetails) isRechargeStageData_Data() {}

type RechargePoolAccountPaymentStageData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId          string                 `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	PaymentMode      enums.PaymentMode      `protobuf:"varint,2,opt,name=payment_mode,json=paymentMode,proto3,enum=api.billpay.enums.PaymentMode" json:"payment_mode,omitempty"`
	PaymentTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=payment_timestamp,json=paymentTimestamp,proto3" json:"payment_timestamp,omitempty"`
}

func (x *RechargePoolAccountPaymentStageData) Reset() {
	*x = RechargePoolAccountPaymentStageData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_recharge_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RechargePoolAccountPaymentStageData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargePoolAccountPaymentStageData) ProtoMessage() {}

func (x *RechargePoolAccountPaymentStageData) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_recharge_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargePoolAccountPaymentStageData.ProtoReflect.Descriptor instead.
func (*RechargePoolAccountPaymentStageData) Descriptor() ([]byte, []int) {
	return file_api_billpay_recharge_proto_rawDescGZIP(), []int{5}
}

func (x *RechargePoolAccountPaymentStageData) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *RechargePoolAccountPaymentStageData) GetPaymentMode() enums.PaymentMode {
	if x != nil {
		return x.PaymentMode
	}
	return enums.PaymentMode(0)
}

func (x *RechargePoolAccountPaymentStageData) GetPaymentTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.PaymentTimestamp
	}
	return nil
}

// Billpay-specific recharge details (decoupled from vendorgateway.recharge.RechargeDetails)
type RechargeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Mobile number associated with the transaction
	MobileNumber *common.PhoneNumber `protobuf:"bytes,1,opt,name=mobile_number,json=mobileNumber,proto3" json:"mobile_number,omitempty"`
	// Provider for the recharged number
	Provider enums.Operator `protobuf:"varint,2,opt,name=provider,proto3,enum=api.billpay.enums.Operator" json:"provider,omitempty"`
	// Indicates if the mobile number is postpaid
	IsPostpaid bool `protobuf:"varint,3,opt,name=is_postpaid,json=isPostpaid,proto3" json:"is_postpaid,omitempty"`
	// Indicates if this is a special service
	IsSpecial bool `protobuf:"varint,4,opt,name=is_special,json=isSpecial,proto3" json:"is_special,omitempty"`
	// Recharge amount
	Amount *money.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// Unique reference ID for the transaction from the vendor
	TransactionRefId string `protobuf:"bytes,6,opt,name=transaction_ref_id,json=transactionRefId,proto3" json:"transaction_ref_id,omitempty"`
	// Status of the recharge transaction
	Status enums.RechargeOrderStatus `protobuf:"varint,7,opt,name=status,proto3,enum=api.billpay.enums.RechargeOrderStatus" json:"status,omitempty"`
	// Reference ID provided by the operator
	OperatorRefId string `protobuf:"bytes,8,opt,name=operator_ref_id,json=operatorRefId,proto3" json:"operator_ref_id,omitempty"`
}

func (x *RechargeDetails) Reset() {
	*x = RechargeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_recharge_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RechargeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeDetails) ProtoMessage() {}

func (x *RechargeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_recharge_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeDetails.ProtoReflect.Descriptor instead.
func (*RechargeDetails) Descriptor() ([]byte, []int) {
	return file_api_billpay_recharge_proto_rawDescGZIP(), []int{6}
}

func (x *RechargeDetails) GetMobileNumber() *common.PhoneNumber {
	if x != nil {
		return x.MobileNumber
	}
	return nil
}

func (x *RechargeDetails) GetProvider() enums.Operator {
	if x != nil {
		return x.Provider
	}
	return enums.Operator(0)
}

func (x *RechargeDetails) GetIsPostpaid() bool {
	if x != nil {
		return x.IsPostpaid
	}
	return false
}

func (x *RechargeDetails) GetIsSpecial() bool {
	if x != nil {
		return x.IsSpecial
	}
	return false
}

func (x *RechargeDetails) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *RechargeDetails) GetTransactionRefId() string {
	if x != nil {
		return x.TransactionRefId
	}
	return ""
}

func (x *RechargeDetails) GetStatus() enums.RechargeOrderStatus {
	if x != nil {
		return x.Status
	}
	return enums.RechargeOrderStatus(0)
}

func (x *RechargeDetails) GetOperatorRefId() string {
	if x != nil {
		return x.OperatorRefId
	}
	return ""
}

type RechargeFulfillmentStageData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// recharge detail on successful fulfillment
	RechargeDetails *RechargeDetails `protobuf:"bytes,1,opt,name=recharge_details,json=rechargeDetails,proto3" json:"recharge_details,omitempty"`
	// vendor responses for the recharge process with vendor
	// ref id generated by us for initiating recharge with vendor
	InitiateRechargeRefId        string `protobuf:"bytes,2,opt,name=initiate_recharge_ref_id,json=initiateRechargeRefId,proto3" json:"initiate_recharge_ref_id,omitempty"`
	InitiateRechargeTraceId      string `protobuf:"bytes,3,opt,name=initiate_recharge_trace_id,json=initiateRechargeTraceId,proto3" json:"initiate_recharge_trace_id,omitempty"`
	EnquireRechargeStatusTraceId string `protobuf:"bytes,4,opt,name=enquire_recharge_status_trace_id,json=enquireRechargeStatusTraceId,proto3" json:"enquire_recharge_status_trace_id,omitempty"`
}

func (x *RechargeFulfillmentStageData) Reset() {
	*x = RechargeFulfillmentStageData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_recharge_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RechargeFulfillmentStageData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeFulfillmentStageData) ProtoMessage() {}

func (x *RechargeFulfillmentStageData) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_recharge_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeFulfillmentStageData.ProtoReflect.Descriptor instead.
func (*RechargeFulfillmentStageData) Descriptor() ([]byte, []int) {
	return file_api_billpay_recharge_proto_rawDescGZIP(), []int{7}
}

func (x *RechargeFulfillmentStageData) GetRechargeDetails() *RechargeDetails {
	if x != nil {
		return x.RechargeDetails
	}
	return nil
}

func (x *RechargeFulfillmentStageData) GetInitiateRechargeRefId() string {
	if x != nil {
		return x.InitiateRechargeRefId
	}
	return ""
}

func (x *RechargeFulfillmentStageData) GetInitiateRechargeTraceId() string {
	if x != nil {
		return x.InitiateRechargeTraceId
	}
	return ""
}

func (x *RechargeFulfillmentStageData) GetEnquireRechargeStatusTraceId() string {
	if x != nil {
		return x.EnquireRechargeStatusTraceId
	}
	return ""
}

type RechargeRefundStageData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *RechargeRefundStageData) Reset() {
	*x = RechargeRefundStageData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_recharge_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RechargeRefundStageData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeRefundStageData) ProtoMessage() {}

func (x *RechargeRefundStageData) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_recharge_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeRefundStageData.ProtoReflect.Descriptor instead.
func (*RechargeRefundStageData) Descriptor() ([]byte, []int) {
	return file_api_billpay_recharge_proto_rawDescGZIP(), []int{8}
}

func (x *RechargeRefundStageData) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type RechargeReconStageDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RechargeReconStageDetails) Reset() {
	*x = RechargeReconStageDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_recharge_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RechargeReconStageDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeReconStageDetails) ProtoMessage() {}

func (x *RechargeReconStageDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_recharge_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeReconStageDetails.ProtoReflect.Descriptor instead.
func (*RechargeReconStageDetails) Descriptor() ([]byte, []int) {
	return file_api_billpay_recharge_proto_rawDescGZIP(), []int{9}
}

var File_api_billpay_recharge_proto protoreflect.FileDescriptor

var file_api_billpay_recharge_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x72, 0x65,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70,
	0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x62,
	0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcd, 0x05, 0x0a, 0x0d, 0x52, 0x65,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x49, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a,
	0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x46, 0x0a, 0x10,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x52, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x12, 0x3b, 0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x70, 0x6c, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x6e,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52,
	0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x73, 0x75,
	0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x73, 0x75, 0x62, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x6f, 0x0a, 0x0b, 0x50, 0x6c, 0x61,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x60, 0x0a, 0x1c, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x6e,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x4d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x52,
	0x19, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50,
	0x6c, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xa4, 0x02, 0x0a, 0x12, 0x4d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6c, 0x61,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61, 0x6c, 0x6b, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x6c, 0x6b, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69,
	0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69,
	0x74, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x6c,
	0x61, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a,
	0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x22, 0x98, 0x04, 0x0a, 0x12, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x36, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x73, 0x41, 0x74, 0x12, 0x32, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e,
	0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3d, 0x0a,
	0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x8c, 0x03, 0x0a,
	0x11, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x73, 0x0a, 0x1c, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50,
	0x6f, 0x6f, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x19, 0x70, 0x6f,
	0x6f, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5c, 0x0a, 0x13, 0x66, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70,
	0x61, 0x79, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x46, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x12, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4d, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x4d, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xcc, 0x01, 0x0a, 0x23,
	0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x41,
	0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70,
	0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x47, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x92, 0x03, 0x0a, 0x0f, 0x52,
	0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x44,
	0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0c, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x1f, 0x0a,
	0x0b, 0x69, 0x73, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x70, 0x61, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x50, 0x6f, 0x73, 0x74, 0x70, 0x61, 0x69, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x12, 0x2a, 0x0a,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x66, 0x49, 0x64, 0x22,
	0xa5, 0x02, 0x0a, 0x1c, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x46, 0x75, 0x6c, 0x66,
	0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x47, 0x0a, 0x10, 0x72, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x72,
	0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x66,
	0x49, 0x64, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x72,
	0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x46, 0x0a, 0x20, 0x65, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x65, 0x6e, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0x34, 0x0a, 0x17, 0x52, 0x65, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x1b, 0x0a,
	0x19, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x52, 0x0a, 0x22, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79,
	0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x69, 0x6c,
	0x6c, 0x70, 0x61, 0x79, 0x3b, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_billpay_recharge_proto_rawDescOnce sync.Once
	file_api_billpay_recharge_proto_rawDescData = file_api_billpay_recharge_proto_rawDesc
)

func file_api_billpay_recharge_proto_rawDescGZIP() []byte {
	file_api_billpay_recharge_proto_rawDescOnce.Do(func() {
		file_api_billpay_recharge_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_billpay_recharge_proto_rawDescData)
	})
	return file_api_billpay_recharge_proto_rawDescData
}

var file_api_billpay_recharge_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_billpay_recharge_proto_goTypes = []interface{}{
	(*RechargeOrder)(nil),                       // 0: api.billpay.RechargeOrder
	(*PlanDetails)(nil),                         // 1: api.billpay.PlanDetails
	(*MobileRechargePlan)(nil),                  // 2: api.billpay.MobileRechargePlan
	(*RechargeOrderStage)(nil),                  // 3: api.billpay.RechargeOrderStage
	(*RechargeStageData)(nil),                   // 4: api.billpay.RechargeStageData
	(*RechargePoolAccountPaymentStageData)(nil), // 5: api.billpay.RechargePoolAccountPaymentStageData
	(*RechargeDetails)(nil),                     // 6: api.billpay.RechargeDetails
	(*RechargeFulfillmentStageData)(nil),        // 7: api.billpay.RechargeFulfillmentStageData
	(*RechargeRefundStageData)(nil),             // 8: api.billpay.RechargeRefundStageData
	(*RechargeReconStageDetails)(nil),           // 9: api.billpay.RechargeReconStageDetails
	(enums.RechargeAccountType)(0),              // 10: api.billpay.enums.RechargeAccountType
	(enums.Operator)(0),                         // 11: api.billpay.enums.Operator
	(enums.RechargeOrderStatus)(0),              // 12: api.billpay.enums.RechargeOrderStatus
	(enums.RechargeOrderSubStatus)(0),           // 13: api.billpay.enums.RechargeOrderSubStatus
	(*timestamppb.Timestamp)(nil),               // 14: google.protobuf.Timestamp
	(*money.Money)(nil),                         // 15: google.type.Money
	(enums.RechargeStage)(0),                    // 16: api.billpay.enums.RechargeStage
	(enums.RechargeStageStatus)(0),              // 17: api.billpay.enums.RechargeStageStatus
	(enums.PaymentMode)(0),                      // 18: api.billpay.enums.PaymentMode
	(*common.PhoneNumber)(nil),                  // 19: api.typesv2.common.PhoneNumber
}
var file_api_billpay_recharge_proto_depIdxs = []int32{
	10, // 0: api.billpay.RechargeOrder.account_type:type_name -> api.billpay.enums.RechargeAccountType
	11, // 1: api.billpay.RechargeOrder.account_operator:type_name -> api.billpay.enums.Operator
	1,  // 2: api.billpay.RechargeOrder.plan_details:type_name -> api.billpay.PlanDetails
	12, // 3: api.billpay.RechargeOrder.status:type_name -> api.billpay.enums.RechargeOrderStatus
	13, // 4: api.billpay.RechargeOrder.sub_status:type_name -> api.billpay.enums.RechargeOrderSubStatus
	14, // 5: api.billpay.RechargeOrder.created_at:type_name -> google.protobuf.Timestamp
	14, // 6: api.billpay.RechargeOrder.updated_at:type_name -> google.protobuf.Timestamp
	14, // 7: api.billpay.RechargeOrder.completed_at:type_name -> google.protobuf.Timestamp
	2,  // 8: api.billpay.PlanDetails.mobile_recharge_plan_details:type_name -> api.billpay.MobileRechargePlan
	15, // 9: api.billpay.MobileRechargePlan.amount:type_name -> google.type.Money
	11, // 10: api.billpay.MobileRechargePlan.operator:type_name -> api.billpay.enums.Operator
	16, // 11: api.billpay.RechargeOrderStage.stage:type_name -> api.billpay.enums.RechargeStage
	17, // 12: api.billpay.RechargeOrderStage.status:type_name -> api.billpay.enums.RechargeStageStatus
	14, // 13: api.billpay.RechargeOrderStage.expires_at:type_name -> google.protobuf.Timestamp
	4,  // 14: api.billpay.RechargeOrderStage.data:type_name -> api.billpay.RechargeStageData
	14, // 15: api.billpay.RechargeOrderStage.created_at:type_name -> google.protobuf.Timestamp
	14, // 16: api.billpay.RechargeOrderStage.updated_at:type_name -> google.protobuf.Timestamp
	14, // 17: api.billpay.RechargeOrderStage.completed_at:type_name -> google.protobuf.Timestamp
	5,  // 18: api.billpay.RechargeStageData.pool_account_payment_details:type_name -> api.billpay.RechargePoolAccountPaymentStageData
	7,  // 19: api.billpay.RechargeStageData.fulfillment_details:type_name -> api.billpay.RechargeFulfillmentStageData
	8,  // 20: api.billpay.RechargeStageData.refund_details:type_name -> api.billpay.RechargeRefundStageData
	9,  // 21: api.billpay.RechargeStageData.recon_details:type_name -> api.billpay.RechargeReconStageDetails
	18, // 22: api.billpay.RechargePoolAccountPaymentStageData.payment_mode:type_name -> api.billpay.enums.PaymentMode
	14, // 23: api.billpay.RechargePoolAccountPaymentStageData.payment_timestamp:type_name -> google.protobuf.Timestamp
	19, // 24: api.billpay.RechargeDetails.mobile_number:type_name -> api.typesv2.common.PhoneNumber
	11, // 25: api.billpay.RechargeDetails.provider:type_name -> api.billpay.enums.Operator
	15, // 26: api.billpay.RechargeDetails.amount:type_name -> google.type.Money
	12, // 27: api.billpay.RechargeDetails.status:type_name -> api.billpay.enums.RechargeOrderStatus
	6,  // 28: api.billpay.RechargeFulfillmentStageData.recharge_details:type_name -> api.billpay.RechargeDetails
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_api_billpay_recharge_proto_init() }
func file_api_billpay_recharge_proto_init() {
	if File_api_billpay_recharge_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_billpay_recharge_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RechargeOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_recharge_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlanDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_recharge_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MobileRechargePlan); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_recharge_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RechargeOrderStage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_recharge_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RechargeStageData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_recharge_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RechargePoolAccountPaymentStageData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_recharge_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RechargeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_recharge_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RechargeFulfillmentStageData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_recharge_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RechargeRefundStageData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_recharge_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RechargeReconStageDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_billpay_recharge_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*RechargeStageData_PoolAccountPaymentDetails)(nil),
		(*RechargeStageData_FulfillmentDetails)(nil),
		(*RechargeStageData_RefundDetails)(nil),
		(*RechargeStageData_ReconDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_billpay_recharge_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_billpay_recharge_proto_goTypes,
		DependencyIndexes: file_api_billpay_recharge_proto_depIdxs,
		MessageInfos:      file_api_billpay_recharge_proto_msgTypes,
	}.Build()
	File_api_billpay_recharge_proto = out.File
	file_api_billpay_recharge_proto_rawDesc = nil
	file_api_billpay_recharge_proto_goTypes = nil
	file_api_billpay_recharge_proto_depIdxs = nil
}
