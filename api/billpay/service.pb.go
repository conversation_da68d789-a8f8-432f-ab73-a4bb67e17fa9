// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/billpay/service.proto

package billpaypb

import (
	rpc "github.com/epifi/be-common/api/rpc"
	enums "github.com/epifi/gamma/api/billpay/enums"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FetchAndManageBillersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// optional filter:
	// fetches and updates all billers in the given category if there are any updates
	// if category is not provided, fetches and updates all categories.
	CategoryName string `protobuf:"bytes,1,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
}

func (x *FetchAndManageBillersRequest) Reset() {
	*x = FetchAndManageBillersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchAndManageBillersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchAndManageBillersRequest) ProtoMessage() {}

func (x *FetchAndManageBillersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchAndManageBillersRequest.ProtoReflect.Descriptor instead.
func (*FetchAndManageBillersRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{0}
}

func (x *FetchAndManageBillersRequest) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

type FetchAndManageBillersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// newly added categories
	AddedCategories   []string `protobuf:"bytes,2,rep,name=added_categories,json=addedCategories,proto3" json:"added_categories,omitempty"`
	RemovedCategories []string `protobuf:"bytes,3,rep,name=removed_categories,json=removedCategories,proto3" json:"removed_categories,omitempty"`
	// summary of the operations done in billers entity
	BillersSummaries []*FetchAndManageBillersResponse_BillersSummary `protobuf:"bytes,4,rep,name=billers_summaries,json=billersSummaries,proto3" json:"billers_summaries,omitempty"`
}

func (x *FetchAndManageBillersResponse) Reset() {
	*x = FetchAndManageBillersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchAndManageBillersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchAndManageBillersResponse) ProtoMessage() {}

func (x *FetchAndManageBillersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchAndManageBillersResponse.ProtoReflect.Descriptor instead.
func (*FetchAndManageBillersResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{1}
}

func (x *FetchAndManageBillersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchAndManageBillersResponse) GetAddedCategories() []string {
	if x != nil {
		return x.AddedCategories
	}
	return nil
}

func (x *FetchAndManageBillersResponse) GetRemovedCategories() []string {
	if x != nil {
		return x.RemovedCategories
	}
	return nil
}

func (x *FetchAndManageBillersResponse) GetBillersSummaries() []*FetchAndManageBillersResponse_BillersSummary {
	if x != nil {
		return x.BillersSummaries
	}
	return nil
}

type UpdateBillerStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// biller id
	BillerId string `protobuf:"bytes,1,opt,name=biller_id,json=billerId,proto3" json:"biller_id,omitempty"`
	// for each biller id, the status to be updated
	FromStatus BillerStatus `protobuf:"varint,2,opt,name=from_status,json=fromStatus,proto3,enum=api.billpay.BillerStatus" json:"from_status,omitempty"`
	ToStatus   BillerStatus `protobuf:"varint,3,opt,name=to_status,json=toStatus,proto3,enum=api.billpay.BillerStatus" json:"to_status,omitempty"`
}

func (x *UpdateBillerStatusRequest) Reset() {
	*x = UpdateBillerStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBillerStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBillerStatusRequest) ProtoMessage() {}

func (x *UpdateBillerStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBillerStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateBillerStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateBillerStatusRequest) GetBillerId() string {
	if x != nil {
		return x.BillerId
	}
	return ""
}

func (x *UpdateBillerStatusRequest) GetFromStatus() BillerStatus {
	if x != nil {
		return x.FromStatus
	}
	return BillerStatus_BILLER_STATUS_UNSPECIFIED
}

func (x *UpdateBillerStatusRequest) GetToStatus() BillerStatus {
	if x != nil {
		return x.ToStatus
	}
	return BillerStatus_BILLER_STATUS_UNSPECIFIED
}

type UpdateBillerStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateBillerStatusResponse) Reset() {
	*x = UpdateBillerStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBillerStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBillerStatusResponse) ProtoMessage() {}

func (x *UpdateBillerStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBillerStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateBillerStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateBillerStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Request/Response for FetchOrders
type FetchRechargeOrdersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId     string                  `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	PageContext *rpc.PageContextRequest `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *FetchRechargeOrdersRequest) Reset() {
	*x = FetchRechargeOrdersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchRechargeOrdersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchRechargeOrdersRequest) ProtoMessage() {}

func (x *FetchRechargeOrdersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchRechargeOrdersRequest.ProtoReflect.Descriptor instead.
func (*FetchRechargeOrdersRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{4}
}

func (x *FetchRechargeOrdersRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *FetchRechargeOrdersRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type FetchRechargeOrdersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status              *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Orders              []*RechargeOrder         `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders,omitempty"`
	PageContextResponse *rpc.PageContextResponse `protobuf:"bytes,3,opt,name=page_context_response,json=pageContextResponse,proto3" json:"page_context_response,omitempty"`
}

func (x *FetchRechargeOrdersResponse) Reset() {
	*x = FetchRechargeOrdersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchRechargeOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchRechargeOrdersResponse) ProtoMessage() {}

func (x *FetchRechargeOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchRechargeOrdersResponse.ProtoReflect.Descriptor instead.
func (*FetchRechargeOrdersResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{5}
}

func (x *FetchRechargeOrdersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchRechargeOrdersResponse) GetOrders() []*RechargeOrder {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *FetchRechargeOrdersResponse) GetPageContextResponse() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContextResponse
	}
	return nil
}

// Request/Response for GetLatestOrdersForUniqueAccounts
type GetLatestRechargeOrdersForUniqueAccountsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId     string                    `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AccountType enums.RechargeAccountType `protobuf:"varint,2,opt,name=account_type,json=accountType,proto3,enum=api.billpay.enums.RechargeAccountType" json:"account_type,omitempty"`
}

func (x *GetLatestRechargeOrdersForUniqueAccountsRequest) Reset() {
	*x = GetLatestRechargeOrdersForUniqueAccountsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLatestRechargeOrdersForUniqueAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestRechargeOrdersForUniqueAccountsRequest) ProtoMessage() {}

func (x *GetLatestRechargeOrdersForUniqueAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestRechargeOrdersForUniqueAccountsRequest.ProtoReflect.Descriptor instead.
func (*GetLatestRechargeOrdersForUniqueAccountsRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetLatestRechargeOrdersForUniqueAccountsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetLatestRechargeOrdersForUniqueAccountsRequest) GetAccountType() enums.RechargeAccountType {
	if x != nil {
		return x.AccountType
	}
	return enums.RechargeAccountType(0)
}

type GetLatestRechargeOrdersForUniqueAccountsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LatestOrders []*RechargeOrder `protobuf:"bytes,2,rep,name=latest_orders,json=latestOrders,proto3" json:"latest_orders,omitempty"`
}

func (x *GetLatestRechargeOrdersForUniqueAccountsResponse) Reset() {
	*x = GetLatestRechargeOrdersForUniqueAccountsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLatestRechargeOrdersForUniqueAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestRechargeOrdersForUniqueAccountsResponse) ProtoMessage() {}

func (x *GetLatestRechargeOrdersForUniqueAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestRechargeOrdersForUniqueAccountsResponse.ProtoReflect.Descriptor instead.
func (*GetLatestRechargeOrdersForUniqueAccountsResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetLatestRechargeOrdersForUniqueAccountsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLatestRechargeOrdersForUniqueAccountsResponse) GetLatestOrders() []*RechargeOrder {
	if x != nil {
		return x.LatestOrders
	}
	return nil
}

// Request/Response for FetchPlans
type FetchRechargePlansRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId           string                    `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AccountType       enums.RechargeAccountType `protobuf:"varint,2,opt,name=account_type,json=accountType,proto3,enum=api.billpay.enums.RechargeAccountType" json:"account_type,omitempty"`
	AccountIdentifier string                    `protobuf:"bytes,3,opt,name=account_identifier,json=accountIdentifier,proto3" json:"account_identifier,omitempty"`
}

func (x *FetchRechargePlansRequest) Reset() {
	*x = FetchRechargePlansRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchRechargePlansRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchRechargePlansRequest) ProtoMessage() {}

func (x *FetchRechargePlansRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchRechargePlansRequest.ProtoReflect.Descriptor instead.
func (*FetchRechargePlansRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{8}
}

func (x *FetchRechargePlansRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *FetchRechargePlansRequest) GetAccountType() enums.RechargeAccountType {
	if x != nil {
		return x.AccountType
	}
	return enums.RechargeAccountType(0)
}

func (x *FetchRechargePlansRequest) GetAccountIdentifier() string {
	if x != nil {
		return x.AccountIdentifier
	}
	return ""
}

type FetchRechargePlansResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Plans    []*MobileRechargePlan `protobuf:"bytes,2,rep,name=plans,proto3" json:"plans,omitempty"`
	Operator enums.Operator        `protobuf:"varint,3,opt,name=operator,proto3,enum=api.billpay.enums.Operator" json:"operator,omitempty"`
}

func (x *FetchRechargePlansResponse) Reset() {
	*x = FetchRechargePlansResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchRechargePlansResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchRechargePlansResponse) ProtoMessage() {}

func (x *FetchRechargePlansResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchRechargePlansResponse.ProtoReflect.Descriptor instead.
func (*FetchRechargePlansResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{9}
}

func (x *FetchRechargePlansResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchRechargePlansResponse) GetPlans() []*MobileRechargePlan {
	if x != nil {
		return x.Plans
	}
	return nil
}

func (x *FetchRechargePlansResponse) GetOperator() enums.Operator {
	if x != nil {
		return x.Operator
	}
	return enums.Operator(0)
}

// Request/Response for CreateRechargeOrder
type CreateRechargeOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId           string                    `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AccountType       enums.RechargeAccountType `protobuf:"varint,2,opt,name=account_type,json=accountType,proto3,enum=api.billpay.enums.RechargeAccountType" json:"account_type,omitempty"`
	AccountIdentifier string                    `protobuf:"bytes,3,opt,name=account_identifier,json=accountIdentifier,proto3" json:"account_identifier,omitempty"`
	Operator          enums.Operator            `protobuf:"varint,4,opt,name=operator,proto3,enum=api.billpay.enums.Operator" json:"operator,omitempty"`
	PlanId            string                    `protobuf:"bytes,5,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`
}

func (x *CreateRechargeOrderRequest) Reset() {
	*x = CreateRechargeOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRechargeOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRechargeOrderRequest) ProtoMessage() {}

func (x *CreateRechargeOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRechargeOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateRechargeOrderRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{10}
}

func (x *CreateRechargeOrderRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CreateRechargeOrderRequest) GetAccountType() enums.RechargeAccountType {
	if x != nil {
		return x.AccountType
	}
	return enums.RechargeAccountType(0)
}

func (x *CreateRechargeOrderRequest) GetAccountIdentifier() string {
	if x != nil {
		return x.AccountIdentifier
	}
	return ""
}

func (x *CreateRechargeOrderRequest) GetOperator() enums.Operator {
	if x != nil {
		return x.Operator
	}
	return enums.Operator(0)
}

func (x *CreateRechargeOrderRequest) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

type CreateRechargeOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ClientRequestId string      `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
}

func (x *CreateRechargeOrderResponse) Reset() {
	*x = CreateRechargeOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRechargeOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRechargeOrderResponse) ProtoMessage() {}

func (x *CreateRechargeOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRechargeOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateRechargeOrderResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{11}
}

func (x *CreateRechargeOrderResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateRechargeOrderResponse) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

// Request/Response for GetOrderStatus
type GetRechargeOrderStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientRequestId string `protobuf:"bytes,1,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
}

func (x *GetRechargeOrderStatusRequest) Reset() {
	*x = GetRechargeOrderStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRechargeOrderStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRechargeOrderStatusRequest) ProtoMessage() {}

func (x *GetRechargeOrderStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRechargeOrderStatusRequest.ProtoReflect.Descriptor instead.
func (*GetRechargeOrderStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetRechargeOrderStatusRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

type GetRechargeOrderStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status                  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	OrderStatus    enums.RechargeOrderStatus    `protobuf:"varint,2,opt,name=order_status,json=orderStatus,proto3,enum=api.billpay.enums.RechargeOrderStatus" json:"order_status,omitempty"`
	OrderSubStatus enums.RechargeOrderSubStatus `protobuf:"varint,3,opt,name=order_sub_status,json=orderSubStatus,proto3,enum=api.billpay.enums.RechargeOrderSubStatus" json:"order_sub_status,omitempty"`
	NextAction     *deeplink.Deeplink           `protobuf:"bytes,4,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *GetRechargeOrderStatusResponse) Reset() {
	*x = GetRechargeOrderStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRechargeOrderStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRechargeOrderStatusResponse) ProtoMessage() {}

func (x *GetRechargeOrderStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRechargeOrderStatusResponse.ProtoReflect.Descriptor instead.
func (*GetRechargeOrderStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetRechargeOrderStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRechargeOrderStatusResponse) GetOrderStatus() enums.RechargeOrderStatus {
	if x != nil {
		return x.OrderStatus
	}
	return enums.RechargeOrderStatus(0)
}

func (x *GetRechargeOrderStatusResponse) GetOrderSubStatus() enums.RechargeOrderSubStatus {
	if x != nil {
		return x.OrderSubStatus
	}
	return enums.RechargeOrderSubStatus(0)
}

func (x *GetRechargeOrderStatusResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type FetchAndManageBillersResponse_BillersSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BillerId     string       `protobuf:"bytes,1,opt,name=biller_id,json=billerId,proto3" json:"biller_id,omitempty"`
	BillerName   string       `protobuf:"bytes,2,opt,name=biller_name,json=billerName,proto3" json:"biller_name,omitempty"`
	CategoryId   string       `protobuf:"bytes,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	CategoryName string       `protobuf:"bytes,4,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	OldStatus    BillerStatus `protobuf:"varint,5,opt,name=old_status,json=oldStatus,proto3,enum=api.billpay.BillerStatus" json:"old_status,omitempty"`
	NewStatus    BillerStatus `protobuf:"varint,6,opt,name=new_status,json=newStatus,proto3,enum=api.billpay.BillerStatus" json:"new_status,omitempty"`
	IsUpdated    bool         `protobuf:"varint,7,opt,name=is_updated,json=isUpdated,proto3" json:"is_updated,omitempty"`
}

func (x *FetchAndManageBillersResponse_BillersSummary) Reset() {
	*x = FetchAndManageBillersResponse_BillersSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_billpay_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchAndManageBillersResponse_BillersSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchAndManageBillersResponse_BillersSummary) ProtoMessage() {}

func (x *FetchAndManageBillersResponse_BillersSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_billpay_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchAndManageBillersResponse_BillersSummary.ProtoReflect.Descriptor instead.
func (*FetchAndManageBillersResponse_BillersSummary) Descriptor() ([]byte, []int) {
	return file_api_billpay_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *FetchAndManageBillersResponse_BillersSummary) GetBillerId() string {
	if x != nil {
		return x.BillerId
	}
	return ""
}

func (x *FetchAndManageBillersResponse_BillersSummary) GetBillerName() string {
	if x != nil {
		return x.BillerName
	}
	return ""
}

func (x *FetchAndManageBillersResponse_BillersSummary) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *FetchAndManageBillersResponse_BillersSummary) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *FetchAndManageBillersResponse_BillersSummary) GetOldStatus() BillerStatus {
	if x != nil {
		return x.OldStatus
	}
	return BillerStatus_BILLER_STATUS_UNSPECIFIED
}

func (x *FetchAndManageBillersResponse_BillersSummary) GetNewStatus() BillerStatus {
	if x != nil {
		return x.NewStatus
	}
	return BillerStatus_BILLER_STATUS_UNSPECIFIED
}

func (x *FetchAndManageBillersResponse_BillersSummary) GetIsUpdated() bool {
	if x != nil {
		return x.IsUpdated
	}
	return false
}

var File_api_billpay_service_proto protoreflect.FileDescriptor

var file_api_billpay_service_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x69,
	0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x70, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61,
	0x70, 0x69, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x62,
	0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x43,
	0x0a, 0x1c, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x6e, 0x64, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x42, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0xb0, 0x04, 0x0a, 0x1d, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x6e, 0x64,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x42, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x64,
	0x64, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x64, 0x64, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x12, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x64,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x11, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x66, 0x0a, 0x11, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x73, 0x5f,
	0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x41, 0x6e, 0x64, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x42, 0x69, 0x6c, 0x6c,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x69, 0x6c, 0x6c,
	0x65, 0x72, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x10, 0x62, 0x69, 0x6c, 0x6c,
	0x65, 0x72, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x1a, 0xa7, 0x02, 0x0a,
	0x0e, 0x42, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12,
	0x1b, 0x0a, 0x09, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0a, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x09, 0x6f, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a,
	0x0a, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e,
	0x42, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x6e, 0x65,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x22, 0xac, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x3a, 0x0a, 0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x70, 0x61, 0x79, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a,
	0x09, 0x74, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x42,
	0x69, 0x6c, 0x6c, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x08, 0x74, 0x6f, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x41, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42,
	0x69, 0x6c, 0x6c, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x73, 0x0a, 0x1a, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0xc4, 0x01,
	0x0a, 0x1b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x32, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79,
	0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x4c, 0x0a, 0x15, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x13, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x97, 0x01, 0x0a, 0x2f, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x46, 0x6f, 0x72, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x98,
	0x01, 0x0a, 0x30, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x6e, 0x69,
	0x71, 0x75, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x0d, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x52, 0x65,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x0c, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x22, 0xb0, 0x01, 0x0a, 0x19, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x49, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a,
	0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xb1, 0x01, 0x0a,
	0x1a, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6c,
	0x61, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x35, 0x0a, 0x05, 0x70, 0x6c, 0x61, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x4d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6c, 0x61, 0x6e,
	0x52, 0x05, 0x70, 0x6c, 0x61, 0x6e, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x22, 0x83, 0x02, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x0c, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x17, 0x0a,
	0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x22, 0x6e, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x4b, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x22, 0xa3, 0x02, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x0c, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x53, 0x0a, 0x10, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x73, 0x75, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6e,
	0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e,
	0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0xb8, 0x06, 0x0a, 0x07, 0x42, 0x69,
	0x6c, 0x6c, 0x50, 0x61, 0x79, 0x12, 0x6e, 0x0a, 0x15, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x6e,
	0x64, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x42, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x73, 0x12, 0x29,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x41, 0x6e, 0x64, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x42, 0x69, 0x6c, 0x6c, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x6e, 0x64,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x42, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42,
	0x69, 0x6c, 0x6c, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61,
	0x79, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x13,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61,
	0x79, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa7, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x4c, 0x61,
	0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x12, 0x3c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61,
	0x79, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x6e, 0x69, 0x71,
	0x75, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x65, 0x0a, 0x12, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x70, 0x61, 0x79, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x27,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x71, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x52, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x3b, 0x62,
	0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_billpay_service_proto_rawDescOnce sync.Once
	file_api_billpay_service_proto_rawDescData = file_api_billpay_service_proto_rawDesc
)

func file_api_billpay_service_proto_rawDescGZIP() []byte {
	file_api_billpay_service_proto_rawDescOnce.Do(func() {
		file_api_billpay_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_billpay_service_proto_rawDescData)
	})
	return file_api_billpay_service_proto_rawDescData
}

var file_api_billpay_service_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_api_billpay_service_proto_goTypes = []interface{}{
	(*FetchAndManageBillersRequest)(nil),                     // 0: api.billpay.FetchAndManageBillersRequest
	(*FetchAndManageBillersResponse)(nil),                    // 1: api.billpay.FetchAndManageBillersResponse
	(*UpdateBillerStatusRequest)(nil),                        // 2: api.billpay.UpdateBillerStatusRequest
	(*UpdateBillerStatusResponse)(nil),                       // 3: api.billpay.UpdateBillerStatusResponse
	(*FetchRechargeOrdersRequest)(nil),                       // 4: api.billpay.FetchRechargeOrdersRequest
	(*FetchRechargeOrdersResponse)(nil),                      // 5: api.billpay.FetchRechargeOrdersResponse
	(*GetLatestRechargeOrdersForUniqueAccountsRequest)(nil),  // 6: api.billpay.GetLatestRechargeOrdersForUniqueAccountsRequest
	(*GetLatestRechargeOrdersForUniqueAccountsResponse)(nil), // 7: api.billpay.GetLatestRechargeOrdersForUniqueAccountsResponse
	(*FetchRechargePlansRequest)(nil),                        // 8: api.billpay.FetchRechargePlansRequest
	(*FetchRechargePlansResponse)(nil),                       // 9: api.billpay.FetchRechargePlansResponse
	(*CreateRechargeOrderRequest)(nil),                       // 10: api.billpay.CreateRechargeOrderRequest
	(*CreateRechargeOrderResponse)(nil),                      // 11: api.billpay.CreateRechargeOrderResponse
	(*GetRechargeOrderStatusRequest)(nil),                    // 12: api.billpay.GetRechargeOrderStatusRequest
	(*GetRechargeOrderStatusResponse)(nil),                   // 13: api.billpay.GetRechargeOrderStatusResponse
	(*FetchAndManageBillersResponse_BillersSummary)(nil),     // 14: api.billpay.FetchAndManageBillersResponse.BillersSummary
	(*rpc.Status)(nil),                                       // 15: rpc.Status
	(BillerStatus)(0),                                        // 16: api.billpay.BillerStatus
	(*rpc.PageContextRequest)(nil),                           // 17: rpc.PageContextRequest
	(*RechargeOrder)(nil),                                    // 18: api.billpay.RechargeOrder
	(*rpc.PageContextResponse)(nil),                          // 19: rpc.PageContextResponse
	(enums.RechargeAccountType)(0),                           // 20: api.billpay.enums.RechargeAccountType
	(*MobileRechargePlan)(nil),                               // 21: api.billpay.MobileRechargePlan
	(enums.Operator)(0),                                      // 22: api.billpay.enums.Operator
	(enums.RechargeOrderStatus)(0),                           // 23: api.billpay.enums.RechargeOrderStatus
	(enums.RechargeOrderSubStatus)(0),                        // 24: api.billpay.enums.RechargeOrderSubStatus
	(*deeplink.Deeplink)(nil),                                // 25: frontend.deeplink.Deeplink
}
var file_api_billpay_service_proto_depIdxs = []int32{
	15, // 0: api.billpay.FetchAndManageBillersResponse.status:type_name -> rpc.Status
	14, // 1: api.billpay.FetchAndManageBillersResponse.billers_summaries:type_name -> api.billpay.FetchAndManageBillersResponse.BillersSummary
	16, // 2: api.billpay.UpdateBillerStatusRequest.from_status:type_name -> api.billpay.BillerStatus
	16, // 3: api.billpay.UpdateBillerStatusRequest.to_status:type_name -> api.billpay.BillerStatus
	15, // 4: api.billpay.UpdateBillerStatusResponse.status:type_name -> rpc.Status
	17, // 5: api.billpay.FetchRechargeOrdersRequest.page_context:type_name -> rpc.PageContextRequest
	15, // 6: api.billpay.FetchRechargeOrdersResponse.status:type_name -> rpc.Status
	18, // 7: api.billpay.FetchRechargeOrdersResponse.orders:type_name -> api.billpay.RechargeOrder
	19, // 8: api.billpay.FetchRechargeOrdersResponse.page_context_response:type_name -> rpc.PageContextResponse
	20, // 9: api.billpay.GetLatestRechargeOrdersForUniqueAccountsRequest.account_type:type_name -> api.billpay.enums.RechargeAccountType
	15, // 10: api.billpay.GetLatestRechargeOrdersForUniqueAccountsResponse.status:type_name -> rpc.Status
	18, // 11: api.billpay.GetLatestRechargeOrdersForUniqueAccountsResponse.latest_orders:type_name -> api.billpay.RechargeOrder
	20, // 12: api.billpay.FetchRechargePlansRequest.account_type:type_name -> api.billpay.enums.RechargeAccountType
	15, // 13: api.billpay.FetchRechargePlansResponse.status:type_name -> rpc.Status
	21, // 14: api.billpay.FetchRechargePlansResponse.plans:type_name -> api.billpay.MobileRechargePlan
	22, // 15: api.billpay.FetchRechargePlansResponse.operator:type_name -> api.billpay.enums.Operator
	20, // 16: api.billpay.CreateRechargeOrderRequest.account_type:type_name -> api.billpay.enums.RechargeAccountType
	22, // 17: api.billpay.CreateRechargeOrderRequest.operator:type_name -> api.billpay.enums.Operator
	15, // 18: api.billpay.CreateRechargeOrderResponse.status:type_name -> rpc.Status
	15, // 19: api.billpay.GetRechargeOrderStatusResponse.status:type_name -> rpc.Status
	23, // 20: api.billpay.GetRechargeOrderStatusResponse.order_status:type_name -> api.billpay.enums.RechargeOrderStatus
	24, // 21: api.billpay.GetRechargeOrderStatusResponse.order_sub_status:type_name -> api.billpay.enums.RechargeOrderSubStatus
	25, // 22: api.billpay.GetRechargeOrderStatusResponse.next_action:type_name -> frontend.deeplink.Deeplink
	16, // 23: api.billpay.FetchAndManageBillersResponse.BillersSummary.old_status:type_name -> api.billpay.BillerStatus
	16, // 24: api.billpay.FetchAndManageBillersResponse.BillersSummary.new_status:type_name -> api.billpay.BillerStatus
	0,  // 25: api.billpay.BillPay.FetchAndManageBillers:input_type -> api.billpay.FetchAndManageBillersRequest
	2,  // 26: api.billpay.BillPay.UpdateBillerStatus:input_type -> api.billpay.UpdateBillerStatusRequest
	4,  // 27: api.billpay.BillPay.FetchRechargeOrders:input_type -> api.billpay.FetchRechargeOrdersRequest
	6,  // 28: api.billpay.BillPay.GetLatestRechargeOrdersForUniqueAccounts:input_type -> api.billpay.GetLatestRechargeOrdersForUniqueAccountsRequest
	8,  // 29: api.billpay.BillPay.FetchRechargePlans:input_type -> api.billpay.FetchRechargePlansRequest
	10, // 30: api.billpay.BillPay.CreateRechargeOrder:input_type -> api.billpay.CreateRechargeOrderRequest
	12, // 31: api.billpay.BillPay.GetRechargeOrderStatus:input_type -> api.billpay.GetRechargeOrderStatusRequest
	1,  // 32: api.billpay.BillPay.FetchAndManageBillers:output_type -> api.billpay.FetchAndManageBillersResponse
	3,  // 33: api.billpay.BillPay.UpdateBillerStatus:output_type -> api.billpay.UpdateBillerStatusResponse
	5,  // 34: api.billpay.BillPay.FetchRechargeOrders:output_type -> api.billpay.FetchRechargeOrdersResponse
	7,  // 35: api.billpay.BillPay.GetLatestRechargeOrdersForUniqueAccounts:output_type -> api.billpay.GetLatestRechargeOrdersForUniqueAccountsResponse
	9,  // 36: api.billpay.BillPay.FetchRechargePlans:output_type -> api.billpay.FetchRechargePlansResponse
	11, // 37: api.billpay.BillPay.CreateRechargeOrder:output_type -> api.billpay.CreateRechargeOrderResponse
	13, // 38: api.billpay.BillPay.GetRechargeOrderStatus:output_type -> api.billpay.GetRechargeOrderStatusResponse
	32, // [32:39] is the sub-list for method output_type
	25, // [25:32] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_api_billpay_service_proto_init() }
func file_api_billpay_service_proto_init() {
	if File_api_billpay_service_proto != nil {
		return
	}
	file_api_billpay_billpay_proto_init()
	file_api_billpay_recharge_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_billpay_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchAndManageBillersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchAndManageBillersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBillerStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBillerStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchRechargeOrdersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchRechargeOrdersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLatestRechargeOrdersForUniqueAccountsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLatestRechargeOrdersForUniqueAccountsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchRechargePlansRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchRechargePlansResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRechargeOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRechargeOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRechargeOrderStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRechargeOrderStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_billpay_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchAndManageBillersResponse_BillersSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_billpay_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_billpay_service_proto_goTypes,
		DependencyIndexes: file_api_billpay_service_proto_depIdxs,
		MessageInfos:      file_api_billpay_service_proto_msgTypes,
	}.Build()
	File_api_billpay_service_proto = out.File
	file_api_billpay_service_proto_rawDesc = nil
	file_api_billpay_service_proto_goTypes = nil
	file_api_billpay_service_proto_depIdxs = nil
}
