package billpaypb

import (
	"fmt"

	moneyPkg "github.com/epifi/be-common/pkg/money"
)

// GetPlanId creates a unique identifier for a plan
// Using amount units+nanos and operator as the unique identifier
func (plan *MobileRechargePlan) GetPlanId() string {
	if plan.GetAmount() == nil {
		return ""
	}
	return fmt.Sprintf("%s_%s", plan.GetOperator().String(), moneyPkg.ToDisplayString(plan.GetAmount()))
}
