//go:generate gen_sql -types=RechargeOrder,PlanDetails,RechargeOrderStage,RechargeStageData,RechargePoolAccountPaymentStageData,RechargeFulfillmentStageData,RechargeRefundStageData,RechargeReconStageDetails
syntax = "proto3";

package billpay;

import "api/billpay/enums.proto";
import "api/vendorgateway/recharge/enums.proto";
import "api/vendorgateway/recharge/service.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/billpay;billpaypb";
option java_package = "com.github.epifi.gamma.api.billpay";

// RechargeOrder entity
message RechargeOrder {
  string id = 1;
  string client_request_id = 2;
  string actor_id = 3;
  RechargeAccountType account_type = 4;
  // raw phone number will be the account identifier for Mobile Recharge account type
  string account_identifier = 5;
  vendorgateway.recharge.Operator account_operator = 6;
  PlanDetails plan_details = 7;
  // PlanDetails->MobileRechargePlanDetails->>Amount->>Units + nanos is the PlanIdentifier for Mobile Recharge account type
  string plan_identifier = 8;
  RechargeOrderStatus status = 9;
  RechargeOrderSubStatus sub_status = 10;
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
  google.protobuf.Timestamp completed_at = 13;
}

message PlanDetails {
  vendorgateway.recharge.Plan mobile_recharge_plan_details = 1; // for Mobile Recharge
}

// RechargeOrderStage entity
message RechargeOrderStage {
  string id = 1;
  string recharge_order_id = 2;
  string client_request_id = 3;
  RechargeStage stage = 4;
  RechargeStageStatus status = 5;
  google.protobuf.Timestamp expires_at = 6;
  RechargeStageData data = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp completed_at = 10;
}

// RechargeStageData message (to be extended with oneof fields later)
message RechargeStageData {
  oneof data {
    RechargePoolAccountPaymentStageData pool_account_payment_details = 1;
    RechargeFulfillmentStageData fulfillment_details = 2;
    RechargeRefundStageData refund_details = 3;
    RechargeReconStageDetails recon_details = 4;
  }
}

message RechargePoolAccountPaymentStageData {
  string order_id = 1;
}

message RechargeFulfillmentStageData {
  // recharge detail on successful fulfillment
  vendorgateway.recharge.RechargeDetails recharge_details = 1;
  // vendor responses for the recharge process with vendor
  string initiate_recharge_trace_id = 2;
  string enquire_recharge_status_trace_id = 3;
}

message RechargeRefundStageData {
  string order_id = 1;
}

message RechargeReconStageDetails {

}
