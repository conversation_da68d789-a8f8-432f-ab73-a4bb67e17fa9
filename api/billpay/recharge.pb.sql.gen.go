// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/billpay/recharge.pb.go

package billpaypb

import (
	"database/sql/driver"
	"fmt"
	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing RechargeOrder while reading from DB
func (a *RechargeOrder) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the RechargeOrder in string format in DB
func (a *RechargeOrder) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for RechargeOrder
func (a *RechargeOrder) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for RechargeOrder
func (a *RechargeOrder) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing PlanDetails while reading from DB
func (a *PlanDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the PlanDetails in string format in DB
func (a *PlanDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for PlanDetails
func (a *PlanDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for PlanDetails
func (a *PlanDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing MobileRechargePlan while reading from DB
func (a *MobileRechargePlan) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the MobileRechargePlan in string format in DB
func (a *MobileRechargePlan) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for MobileRechargePlan
func (a *MobileRechargePlan) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for MobileRechargePlan
func (a *MobileRechargePlan) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing RechargeOrderStage while reading from DB
func (a *RechargeOrderStage) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the RechargeOrderStage in string format in DB
func (a *RechargeOrderStage) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for RechargeOrderStage
func (a *RechargeOrderStage) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for RechargeOrderStage
func (a *RechargeOrderStage) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing RechargeStageData while reading from DB
func (a *RechargeStageData) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the RechargeStageData in string format in DB
func (a *RechargeStageData) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for RechargeStageData
func (a *RechargeStageData) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for RechargeStageData
func (a *RechargeStageData) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing RechargePoolAccountPaymentStageData while reading from DB
func (a *RechargePoolAccountPaymentStageData) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the RechargePoolAccountPaymentStageData in string format in DB
func (a *RechargePoolAccountPaymentStageData) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for RechargePoolAccountPaymentStageData
func (a *RechargePoolAccountPaymentStageData) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for RechargePoolAccountPaymentStageData
func (a *RechargePoolAccountPaymentStageData) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing RechargeDetails while reading from DB
func (a *RechargeDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the RechargeDetails in string format in DB
func (a *RechargeDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for RechargeDetails
func (a *RechargeDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for RechargeDetails
func (a *RechargeDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing RechargeFulfillmentStageData while reading from DB
func (a *RechargeFulfillmentStageData) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the RechargeFulfillmentStageData in string format in DB
func (a *RechargeFulfillmentStageData) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for RechargeFulfillmentStageData
func (a *RechargeFulfillmentStageData) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for RechargeFulfillmentStageData
func (a *RechargeFulfillmentStageData) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing RechargeRefundStageData while reading from DB
func (a *RechargeRefundStageData) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the RechargeRefundStageData in string format in DB
func (a *RechargeRefundStageData) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for RechargeRefundStageData
func (a *RechargeRefundStageData) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for RechargeRefundStageData
func (a *RechargeRefundStageData) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}

// Scanner interface implementation for parsing RechargeReconStageDetails while reading from DB
func (a *RechargeReconStageDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the RechargeReconStageDetails in string format in DB
func (a *RechargeReconStageDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for RechargeReconStageDetails
func (a *RechargeReconStageDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for RechargeReconStageDetails
func (a *RechargeReconStageDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
