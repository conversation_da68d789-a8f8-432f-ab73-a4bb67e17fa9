syntax = "proto3";

package api.investment.mutualfund;

import "api/investment/mutualfund/mutual_fund.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/investment/mutualfund";
option java_package = "com.github.epifi.gamma.api.investment.mutualfund";

// This map is used for marshaling the response of ParseAndGroupAumFile and store it in s3
// map is of the format map<actorId, map<amc, map<mutualFundId, map<folioId, FolioDetails>>>>
message ActorIdMap {
  map<string, AmcMap> actor_id_map = 1;
}

message AmcMap {
  map<string, MutualFundMap> amc_map = 1;
}

message MutualFundMap {
  map<string, FolioMap> mutual_fund_map = 1;
}

message FolioMap {
  map<string, FolioDetails> folio_map = 1;
}

// FolioDetails contains folio and mf details needed for CorrectFolioMismatch
message FolioDetails {
  string folio_number = 1;
  string mutual_fund_id = 2;
  string actor_id = 3;
  double rta_units = 4;
  api.investment.mutualfund.Amc amc = 5;
  double nav = 6;
  google.protobuf.Timestamp post_date = 7;
}

// FolioStatusInfo contains a list of FolioStatus that stores the folioId, status and remarks
// for CaptureMfAumFile workflow, new records are appended at the end and passed between CaptureMfAumFile activities
message FolioStatusInfo {
  repeated FolioInfo folio_status = 1;
}

message FolioInfo {
  enum Status {
    STATUS_UNSPECIFIED = 0;
    STATUS_SUCCESSFUL = 1;
    STATUS_IGNORED = 2;
    STATUS_FAILED = 3;
  }
  string folio_id = 1;
  Status status = 2;
  string remarks = 3;
}
