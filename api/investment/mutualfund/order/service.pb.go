// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/investment/mutualfund/order/service.proto

package order

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	mutualfund "github.com/epifi/gamma/api/investment/mutualfund"
	filegenerator "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	payment_handler "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	domain "github.com/epifi/gamma/api/order/domain"
	date "google.golang.org/genproto/googleapis/type/date"
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FilterFieldMask int32

const (
	FilterFieldMask_FilterFieldMask_UNSPECIFIED FilterFieldMask = 0
	FilterFieldMask_CREATED_AT                  FilterFieldMask = 1
	FilterFieldMask_EXTERNAL_ORDER_ID           FilterFieldMask = 2
	FilterFieldMask_ORDER_CLIENT                FilterFieldMask = 3
	FilterFieldMask_ORDER_TYPE                  FilterFieldMask = 4
	FilterFieldMask_STATUS                      FilterFieldMask = 5
)

// Enum value maps for FilterFieldMask.
var (
	FilterFieldMask_name = map[int32]string{
		0: "FilterFieldMask_UNSPECIFIED",
		1: "CREATED_AT",
		2: "EXTERNAL_ORDER_ID",
		3: "ORDER_CLIENT",
		4: "ORDER_TYPE",
		5: "STATUS",
	}
	FilterFieldMask_value = map[string]int32{
		"FilterFieldMask_UNSPECIFIED": 0,
		"CREATED_AT":                  1,
		"EXTERNAL_ORDER_ID":           2,
		"ORDER_CLIENT":                3,
		"ORDER_TYPE":                  4,
		"STATUS":                      5,
	}
)

func (x FilterFieldMask) Enum() *FilterFieldMask {
	p := new(FilterFieldMask)
	*p = x
	return p
}

func (x FilterFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FilterFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[0].Descriptor()
}

func (FilterFieldMask) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[0]
}

func (x FilterFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FilterFieldMask.Descriptor instead.
func (FilterFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{0}
}

type OrderIdentifier int32

const (
	OrderIdentifier_Order_IDENTIFIER_UNSPECIFIED        OrderIdentifier = 0
	OrderIdentifier_Order_IDENTIFIER_TRANSACTION_NUMBER OrderIdentifier = 1
	OrderIdentifier_Order_IDENTIFIER_VENDOR_ORDER_ID    OrderIdentifier = 2
	OrderIdentifier_ORDER_IDENTIFIER_ORDER_ID           OrderIdentifier = 3
)

// Enum value maps for OrderIdentifier.
var (
	OrderIdentifier_name = map[int32]string{
		0: "Order_IDENTIFIER_UNSPECIFIED",
		1: "Order_IDENTIFIER_TRANSACTION_NUMBER",
		2: "Order_IDENTIFIER_VENDOR_ORDER_ID",
		3: "ORDER_IDENTIFIER_ORDER_ID",
	}
	OrderIdentifier_value = map[string]int32{
		"Order_IDENTIFIER_UNSPECIFIED":        0,
		"Order_IDENTIFIER_TRANSACTION_NUMBER": 1,
		"Order_IDENTIFIER_VENDOR_ORDER_ID":    2,
		"ORDER_IDENTIFIER_ORDER_ID":           3,
	}
)

func (x OrderIdentifier) Enum() *OrderIdentifier {
	p := new(OrderIdentifier)
	*p = x
	return p
}

func (x OrderIdentifier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderIdentifier) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[1].Descriptor()
}

func (OrderIdentifier) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[1]
}

func (x OrderIdentifier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderIdentifier.Descriptor instead.
func (OrderIdentifier) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{1}
}

type IneligibilityReason int32

const (
	IneligibilityReason_INELIGIBILITY_REASON_UNSPECIFIED IneligibilityReason = 0
	// user's DOB is not same in EPIFI_WEALTH database and KRA vendor
	IneligibilityReason_DOB_MISMATCH IneligibilityReason = 1
	// user's docket with Aadhaar as proof of address is not validated by KRA
	IneligibilityReason_AADHAAR_POA_NOT_VALIDATED IneligibilityReason = 2
)

// Enum value maps for IneligibilityReason.
var (
	IneligibilityReason_name = map[int32]string{
		0: "INELIGIBILITY_REASON_UNSPECIFIED",
		1: "DOB_MISMATCH",
		2: "AADHAAR_POA_NOT_VALIDATED",
	}
	IneligibilityReason_value = map[string]int32{
		"INELIGIBILITY_REASON_UNSPECIFIED": 0,
		"DOB_MISMATCH":                     1,
		"AADHAAR_POA_NOT_VALIDATED":        2,
	}
)

func (x IneligibilityReason) Enum() *IneligibilityReason {
	p := new(IneligibilityReason)
	*p = x
	return p
}

func (x IneligibilityReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IneligibilityReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[2].Descriptor()
}

func (IneligibilityReason) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[2]
}

func (x IneligibilityReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IneligibilityReason.Descriptor instead.
func (IneligibilityReason) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{2}
}

type EligibilityStatus int32

const (
	EligibilityStatus_ELIGIBILITY_STATUS_UNSPECIFIED EligibilityStatus = 0
	EligibilityStatus_ELIGIBLE                       EligibilityStatus = 1
	EligibilityStatus_NOT_ELIGIBLE                   EligibilityStatus = 2
)

// Enum value maps for EligibilityStatus.
var (
	EligibilityStatus_name = map[int32]string{
		0: "ELIGIBILITY_STATUS_UNSPECIFIED",
		1: "ELIGIBLE",
		2: "NOT_ELIGIBLE",
	}
	EligibilityStatus_value = map[string]int32{
		"ELIGIBILITY_STATUS_UNSPECIFIED": 0,
		"ELIGIBLE":                       1,
		"NOT_ELIGIBLE":                   2,
	}
)

func (x EligibilityStatus) Enum() *EligibilityStatus {
	p := new(EligibilityStatus)
	*p = x
	return p
}

func (x EligibilityStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EligibilityStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[3].Descriptor()
}

func (EligibilityStatus) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[3]
}

func (x EligibilityStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EligibilityStatus.Descriptor instead.
func (EligibilityStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{3}
}

type ZipPreference int32

const (
	ZipPreference_UNSPECIFIED             ZipPreference = 0
	ZipPreference_ZIP_PREF_1_FILE_PER_RTA ZipPreference = 1
)

// Enum value maps for ZipPreference.
var (
	ZipPreference_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "ZIP_PREF_1_FILE_PER_RTA",
	}
	ZipPreference_value = map[string]int32{
		"UNSPECIFIED":             0,
		"ZIP_PREF_1_FILE_PER_RTA": 1,
	}
)

func (x ZipPreference) Enum() *ZipPreference {
	p := new(ZipPreference)
	*p = x
	return p
}

func (x ZipPreference) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ZipPreference) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[4].Descriptor()
}

func (ZipPreference) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[4]
}

func (x ZipPreference) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ZipPreference.Descriptor instead.
func (ZipPreference) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{4}
}

type HandleFitttSubscriptionUpdateResponse_Status int32

const (
	HandleFitttSubscriptionUpdateResponse_OK HandleFitttSubscriptionUpdateResponse_Status = 0
	// System faced internal errors while processing the request
	HandleFitttSubscriptionUpdateResponse_INTERNAL HandleFitttSubscriptionUpdateResponse_Status = 13
)

// Enum value maps for HandleFitttSubscriptionUpdateResponse_Status.
var (
	HandleFitttSubscriptionUpdateResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	HandleFitttSubscriptionUpdateResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x HandleFitttSubscriptionUpdateResponse_Status) Enum() *HandleFitttSubscriptionUpdateResponse_Status {
	p := new(HandleFitttSubscriptionUpdateResponse_Status)
	*p = x
	return p
}

func (x HandleFitttSubscriptionUpdateResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HandleFitttSubscriptionUpdateResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[5].Descriptor()
}

func (HandleFitttSubscriptionUpdateResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[5]
}

func (x HandleFitttSubscriptionUpdateResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HandleFitttSubscriptionUpdateResponse_Status.Descriptor instead.
func (HandleFitttSubscriptionUpdateResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{1, 0}
}

type CreateOrderResponse_Status int32

const (
	CreateOrderResponse_OK CreateOrderResponse_Status = 0
	// System faced internal errors while processing the request
	CreateOrderResponse_INTERNAL CreateOrderResponse_Status = 13
	// an order exists with same client request id
	CreateOrderResponse_DUPLICATE_ORDER CreateOrderResponse_Status = 101
	// Withdrawal order not allowed as one or more min-kyc checks failed
	CreateOrderResponse_NOT_ALLOWED_MIN_KYC_CHECK_FAILURE CreateOrderResponse_Status = 102
	// Withdrawal amount exceeds redeemable amount
	// (redeemable amount = amount user is holding - total amount of pending sell orders)
	CreateOrderResponse_WITHDRAWAL_AMOUNT_EXCEEDS_REDEEMABLE_AMOUNT CreateOrderResponse_Status = 103
	// Actor is not eligible to place an order
	// Use ineligibility reason specific enums
	//
	// Deprecated: Marked as deprecated in api/investment/mutualfund/order/service.proto.
	CreateOrderResponse_ACTOR_INELIGIBLE CreateOrderResponse_Status = 104
	// Transaction constraints validation failed
	CreateOrderResponse_TRANSACTION_CONSTRAINTS_FAILED CreateOrderResponse_Status = 105
	// AMC blocked new investments
	CreateOrderResponse_FUND_UNAVAILABLE_FOR_INVESTMENT_BY_AMC CreateOrderResponse_Status = 106
	// wealth account nominee is mandatory for new MF folios
	CreateOrderResponse_WEALTH_ACCOUNT_NOMINEE_NOT_DECLARED CreateOrderResponse_Status = 107
	// actor is ineligible to place MF order due to DOB mismatch b/w EPIFI_WEALTH database and KRA
	CreateOrderResponse_ACTOR_INELIGIBLE_DOB_MISMATCH CreateOrderResponse_Status = 108
	// actor is ineligible to place MF order due to their Aadhaar as proof of address being not validated by KRA
	CreateOrderResponse_ACTOR_INELIGIBLE_AADHAAR_POA_NOT_VALIDATED CreateOrderResponse_Status = 109
	// SIP constraint validations failed.
	CreateOrderResponse_SIP_CONSTRAINTS_FAILED CreateOrderResponse_Status = 110
	// actor is ineligible to place MF order because their pan is not linked with their aadhaar
	CreateOrderResponse_ACTOR_PAN_NOT_LINKED_WITH_AADHAAR CreateOrderResponse_Status = 111
)

// Enum value maps for CreateOrderResponse_Status.
var (
	CreateOrderResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		101: "DUPLICATE_ORDER",
		102: "NOT_ALLOWED_MIN_KYC_CHECK_FAILURE",
		103: "WITHDRAWAL_AMOUNT_EXCEEDS_REDEEMABLE_AMOUNT",
		104: "ACTOR_INELIGIBLE",
		105: "TRANSACTION_CONSTRAINTS_FAILED",
		106: "FUND_UNAVAILABLE_FOR_INVESTMENT_BY_AMC",
		107: "WEALTH_ACCOUNT_NOMINEE_NOT_DECLARED",
		108: "ACTOR_INELIGIBLE_DOB_MISMATCH",
		109: "ACTOR_INELIGIBLE_AADHAAR_POA_NOT_VALIDATED",
		110: "SIP_CONSTRAINTS_FAILED",
		111: "ACTOR_PAN_NOT_LINKED_WITH_AADHAAR",
	}
	CreateOrderResponse_Status_value = map[string]int32{
		"OK":                                0,
		"INTERNAL":                          13,
		"DUPLICATE_ORDER":                   101,
		"NOT_ALLOWED_MIN_KYC_CHECK_FAILURE": 102,
		"WITHDRAWAL_AMOUNT_EXCEEDS_REDEEMABLE_AMOUNT": 103,
		"ACTOR_INELIGIBLE":                            104,
		"TRANSACTION_CONSTRAINTS_FAILED":              105,
		"FUND_UNAVAILABLE_FOR_INVESTMENT_BY_AMC":      106,
		"WEALTH_ACCOUNT_NOMINEE_NOT_DECLARED":         107,
		"ACTOR_INELIGIBLE_DOB_MISMATCH":               108,
		"ACTOR_INELIGIBLE_AADHAAR_POA_NOT_VALIDATED":  109,
		"SIP_CONSTRAINTS_FAILED":                      110,
		"ACTOR_PAN_NOT_LINKED_WITH_AADHAAR":           111,
	}
)

func (x CreateOrderResponse_Status) Enum() *CreateOrderResponse_Status {
	p := new(CreateOrderResponse_Status)
	*p = x
	return p
}

func (x CreateOrderResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateOrderResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[6].Descriptor()
}

func (CreateOrderResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[6]
}

func (x CreateOrderResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateOrderResponse_Status.Descriptor instead.
func (CreateOrderResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{5, 0}
}

type GetOrdersRequest_OrderIdType int32

const (
	GetOrdersRequest_ORDER_ID_TYPE_UNSPECIFIED GetOrdersRequest_OrderIdType = 0
	GetOrdersRequest_ORDER_ID_TYPE_ID          GetOrdersRequest_OrderIdType = 1
	GetOrdersRequest_ORDER_ID_TYPE_CLIENT_ID   GetOrdersRequest_OrderIdType = 2
	GetOrdersRequest_ORDER_ID_TYPE_EXTERNAL_ID GetOrdersRequest_OrderIdType = 3
	GetOrdersRequest_ORDER_ID_TYPE_VENDOR_ID   GetOrdersRequest_OrderIdType = 4
)

// Enum value maps for GetOrdersRequest_OrderIdType.
var (
	GetOrdersRequest_OrderIdType_name = map[int32]string{
		0: "ORDER_ID_TYPE_UNSPECIFIED",
		1: "ORDER_ID_TYPE_ID",
		2: "ORDER_ID_TYPE_CLIENT_ID",
		3: "ORDER_ID_TYPE_EXTERNAL_ID",
		4: "ORDER_ID_TYPE_VENDOR_ID",
	}
	GetOrdersRequest_OrderIdType_value = map[string]int32{
		"ORDER_ID_TYPE_UNSPECIFIED": 0,
		"ORDER_ID_TYPE_ID":          1,
		"ORDER_ID_TYPE_CLIENT_ID":   2,
		"ORDER_ID_TYPE_EXTERNAL_ID": 3,
		"ORDER_ID_TYPE_VENDOR_ID":   4,
	}
)

func (x GetOrdersRequest_OrderIdType) Enum() *GetOrdersRequest_OrderIdType {
	p := new(GetOrdersRequest_OrderIdType)
	*p = x
	return p
}

func (x GetOrdersRequest_OrderIdType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetOrdersRequest_OrderIdType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[7].Descriptor()
}

func (GetOrdersRequest_OrderIdType) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[7]
}

func (x GetOrdersRequest_OrderIdType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetOrdersRequest_OrderIdType.Descriptor instead.
func (GetOrdersRequest_OrderIdType) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{6, 0}
}

type GetOrdersResponse_Status int32

const (
	GetOrdersResponse_OK GetOrdersResponse_Status = 0
	// System faced internal errors while processing the request
	GetOrdersResponse_INTERNAL GetOrdersResponse_Status = 13
)

// Enum value maps for GetOrdersResponse_Status.
var (
	GetOrdersResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetOrdersResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetOrdersResponse_Status) Enum() *GetOrdersResponse_Status {
	p := new(GetOrdersResponse_Status)
	*p = x
	return p
}

func (x GetOrdersResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetOrdersResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[8].Descriptor()
}

func (GetOrdersResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[8]
}

func (x GetOrdersResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetOrdersResponse_Status.Descriptor instead.
func (GetOrdersResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{8, 0}
}

type TriggerOrderProcessingResponse_Status int32

const (
	TriggerOrderProcessingResponse_OK TriggerOrderProcessingResponse_Status = 0
	// System faced internal errors while processing the request
	TriggerOrderProcessingResponse_INTERNAL TriggerOrderProcessingResponse_Status = 13
)

// Enum value maps for TriggerOrderProcessingResponse_Status.
var (
	TriggerOrderProcessingResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	TriggerOrderProcessingResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x TriggerOrderProcessingResponse_Status) Enum() *TriggerOrderProcessingResponse_Status {
	p := new(TriggerOrderProcessingResponse_Status)
	*p = x
	return p
}

func (x TriggerOrderProcessingResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TriggerOrderProcessingResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[9].Descriptor()
}

func (TriggerOrderProcessingResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[9]
}

func (x TriggerOrderProcessingResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TriggerOrderProcessingResponse_Status.Descriptor instead.
func (TriggerOrderProcessingResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{10, 0}
}

type GetOrderResponse_Status int32

const (
	// accepted by vendor after all the initial validations
	GetOrderResponse_OK GetOrderResponse_Status = 0
	// System faced internal errors while processing the request
	GetOrderResponse_INTERNAL GetOrderResponse_Status = 13
)

// Enum value maps for GetOrderResponse_Status.
var (
	GetOrderResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetOrderResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetOrderResponse_Status) Enum() *GetOrderResponse_Status {
	p := new(GetOrderResponse_Status)
	*p = x
	return p
}

func (x GetOrderResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetOrderResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[10].Descriptor()
}

func (GetOrderResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[10]
}

func (x GetOrderResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetOrderResponse_Status.Descriptor instead.
func (GetOrderResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{12, 0}
}

type UpdateOrderStatusWithCheckResponse_Status int32

const (
	// accepted by vendor after all the initial validations
	UpdateOrderStatusWithCheckResponse_OK UpdateOrderStatusWithCheckResponse_Status = 0
	// System faced internal errors while processing the request
	UpdateOrderStatusWithCheckResponse_INTERNAL UpdateOrderStatusWithCheckResponse_Status = 13
)

// Enum value maps for UpdateOrderStatusWithCheckResponse_Status.
var (
	UpdateOrderStatusWithCheckResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	UpdateOrderStatusWithCheckResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x UpdateOrderStatusWithCheckResponse_Status) Enum() *UpdateOrderStatusWithCheckResponse_Status {
	p := new(UpdateOrderStatusWithCheckResponse_Status)
	*p = x
	return p
}

func (x UpdateOrderStatusWithCheckResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateOrderStatusWithCheckResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[11].Descriptor()
}

func (UpdateOrderStatusWithCheckResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[11]
}

func (x UpdateOrderStatusWithCheckResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateOrderStatusWithCheckResponse_Status.Descriptor instead.
func (UpdateOrderStatusWithCheckResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{14, 0}
}

type CreateOrUpdateFileStateResponse_Status int32

const (
	CreateOrUpdateFileStateResponse_OK       CreateOrUpdateFileStateResponse_Status = 0
	CreateOrUpdateFileStateResponse_INTERNAL CreateOrUpdateFileStateResponse_Status = 13
)

// Enum value maps for CreateOrUpdateFileStateResponse_Status.
var (
	CreateOrUpdateFileStateResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	CreateOrUpdateFileStateResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x CreateOrUpdateFileStateResponse_Status) Enum() *CreateOrUpdateFileStateResponse_Status {
	p := new(CreateOrUpdateFileStateResponse_Status)
	*p = x
	return p
}

func (x CreateOrUpdateFileStateResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateOrUpdateFileStateResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[12].Descriptor()
}

func (CreateOrUpdateFileStateResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[12]
}

func (x CreateOrUpdateFileStateResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateOrUpdateFileStateResponse_Status.Descriptor instead.
func (CreateOrUpdateFileStateResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{16, 0}
}

type UploadVendorFileResponse_Status int32

const (
	UploadVendorFileResponse_OK       UploadVendorFileResponse_Status = 0
	UploadVendorFileResponse_INTERNAL UploadVendorFileResponse_Status = 13
)

// Enum value maps for UploadVendorFileResponse_Status.
var (
	UploadVendorFileResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	UploadVendorFileResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x UploadVendorFileResponse_Status) Enum() *UploadVendorFileResponse_Status {
	p := new(UploadVendorFileResponse_Status)
	*p = x
	return p
}

func (x UploadVendorFileResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UploadVendorFileResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[13].Descriptor()
}

func (UploadVendorFileResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[13]
}

func (x UploadVendorFileResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UploadVendorFileResponse_Status.Descriptor instead.
func (UploadVendorFileResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{18, 0}
}

type ProcessCreditMISReportConfirmationResponse_Status int32

const (
	ProcessCreditMISReportConfirmationResponse_OK       ProcessCreditMISReportConfirmationResponse_Status = 0
	ProcessCreditMISReportConfirmationResponse_INTERNAL ProcessCreditMISReportConfirmationResponse_Status = 13
)

// Enum value maps for ProcessCreditMISReportConfirmationResponse_Status.
var (
	ProcessCreditMISReportConfirmationResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	ProcessCreditMISReportConfirmationResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x ProcessCreditMISReportConfirmationResponse_Status) Enum() *ProcessCreditMISReportConfirmationResponse_Status {
	p := new(ProcessCreditMISReportConfirmationResponse_Status)
	*p = x
	return p
}

func (x ProcessCreditMISReportConfirmationResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessCreditMISReportConfirmationResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[14].Descriptor()
}

func (ProcessCreditMISReportConfirmationResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[14]
}

func (x ProcessCreditMISReportConfirmationResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessCreditMISReportConfirmationResponse_Status.Descriptor instead.
func (ProcessCreditMISReportConfirmationResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{20, 0}
}

type Filter_Comparator int32

const (
	Filter_EQUAL            Filter_Comparator = 0
	Filter_GREATER_OR_EQUAL Filter_Comparator = 1
	Filter_SMALLER_OR_EQUAL Filter_Comparator = 2
	Filter_GREATER          Filter_Comparator = 3
	Filter_SMALLER          Filter_Comparator = 4
)

// Enum value maps for Filter_Comparator.
var (
	Filter_Comparator_name = map[int32]string{
		0: "EQUAL",
		1: "GREATER_OR_EQUAL",
		2: "SMALLER_OR_EQUAL",
		3: "GREATER",
		4: "SMALLER",
	}
	Filter_Comparator_value = map[string]int32{
		"EQUAL":            0,
		"GREATER_OR_EQUAL": 1,
		"SMALLER_OR_EQUAL": 2,
		"GREATER":          3,
		"SMALLER":          4,
	}
)

func (x Filter_Comparator) Enum() *Filter_Comparator {
	p := new(Filter_Comparator)
	*p = x
	return p
}

func (x Filter_Comparator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Filter_Comparator) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[15].Descriptor()
}

func (Filter_Comparator) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[15]
}

func (x Filter_Comparator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Filter_Comparator.Descriptor instead.
func (Filter_Comparator) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{22, 0}
}

type GetOrdersByFundIDAndActorIDResponse_Status int32

const (
	GetOrdersByFundIDAndActorIDResponse_OK       GetOrdersByFundIDAndActorIDResponse_Status = 0
	GetOrdersByFundIDAndActorIDResponse_INTERNAL GetOrdersByFundIDAndActorIDResponse_Status = 13
)

// Enum value maps for GetOrdersByFundIDAndActorIDResponse_Status.
var (
	GetOrdersByFundIDAndActorIDResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetOrdersByFundIDAndActorIDResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetOrdersByFundIDAndActorIDResponse_Status) Enum() *GetOrdersByFundIDAndActorIDResponse_Status {
	p := new(GetOrdersByFundIDAndActorIDResponse_Status)
	*p = x
	return p
}

func (x GetOrdersByFundIDAndActorIDResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetOrdersByFundIDAndActorIDResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[16].Descriptor()
}

func (GetOrdersByFundIDAndActorIDResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[16]
}

func (x GetOrdersByFundIDAndActorIDResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetOrdersByFundIDAndActorIDResponse_Status.Descriptor instead.
func (GetOrdersByFundIDAndActorIDResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{23, 0}
}

type GetWithdrawalConstraintsResponse_Status int32

const (
	GetWithdrawalConstraintsResponse_OK GetWithdrawalConstraintsResponse_Status = 0
	// System faced internal errors while processing the request
	GetWithdrawalConstraintsResponse_INTERNAL GetWithdrawalConstraintsResponse_Status = 13
)

// Enum value maps for GetWithdrawalConstraintsResponse_Status.
var (
	GetWithdrawalConstraintsResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetWithdrawalConstraintsResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetWithdrawalConstraintsResponse_Status) Enum() *GetWithdrawalConstraintsResponse_Status {
	p := new(GetWithdrawalConstraintsResponse_Status)
	*p = x
	return p
}

func (x GetWithdrawalConstraintsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetWithdrawalConstraintsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[17].Descriptor()
}

func (GetWithdrawalConstraintsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[17]
}

func (x GetWithdrawalConstraintsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetWithdrawalConstraintsResponse_Status.Descriptor instead.
func (GetWithdrawalConstraintsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{25, 0}
}

type GetOrderDetailsResponse_Status int32

const (
	GetOrderDetailsResponse_OK       GetOrderDetailsResponse_Status = 0
	GetOrderDetailsResponse_INTERNAL GetOrderDetailsResponse_Status = 13
)

// Enum value maps for GetOrderDetailsResponse_Status.
var (
	GetOrderDetailsResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetOrderDetailsResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetOrderDetailsResponse_Status) Enum() *GetOrderDetailsResponse_Status {
	p := new(GetOrderDetailsResponse_Status)
	*p = x
	return p
}

func (x GetOrderDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetOrderDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[18].Descriptor()
}

func (GetOrderDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[18]
}

func (x GetOrderDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetOrderDetailsResponse_Status.Descriptor instead.
func (GetOrderDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{27, 0}
}

type PaymentConfirmationUpdateResult_Status int32

const (
	PaymentConfirmationUpdateResult_STATUS_UNSPECIFIED PaymentConfirmationUpdateResult_Status = 0
	// update was done successfully
	PaymentConfirmationUpdateResult_SUCCESS PaymentConfirmationUpdateResult_Status = 1
	// update failed
	PaymentConfirmationUpdateResult_FAILURE PaymentConfirmationUpdateResult_Status = 2
	// invalid transaction number, updated has failed
	PaymentConfirmationUpdateResult_INVALID_TRANSACTION_NUMBER PaymentConfirmationUpdateResult_Status = 3
	// invalid order id, updated has failed
	PaymentConfirmationUpdateResult_INVALID_ORDER_ID PaymentConfirmationUpdateResult_Status = 4
)

// Enum value maps for PaymentConfirmationUpdateResult_Status.
var (
	PaymentConfirmationUpdateResult_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "SUCCESS",
		2: "FAILURE",
		3: "INVALID_TRANSACTION_NUMBER",
		4: "INVALID_ORDER_ID",
	}
	PaymentConfirmationUpdateResult_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED":         0,
		"SUCCESS":                    1,
		"FAILURE":                    2,
		"INVALID_TRANSACTION_NUMBER": 3,
		"INVALID_ORDER_ID":           4,
	}
)

func (x PaymentConfirmationUpdateResult_Status) Enum() *PaymentConfirmationUpdateResult_Status {
	p := new(PaymentConfirmationUpdateResult_Status)
	*p = x
	return p
}

func (x PaymentConfirmationUpdateResult_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentConfirmationUpdateResult_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[19].Descriptor()
}

func (PaymentConfirmationUpdateResult_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[19]
}

func (x PaymentConfirmationUpdateResult_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentConfirmationUpdateResult_Status.Descriptor instead.
func (PaymentConfirmationUpdateResult_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{29, 0}
}

type ProcessPaymentCreditFromVendorResponse_Status int32

const (
	// accepted by vendor after all the initial validations
	ProcessPaymentCreditFromVendorResponse_OK ProcessPaymentCreditFromVendorResponse_Status = 0
	// System faced internal errors while processing the request
	ProcessPaymentCreditFromVendorResponse_INTERNAL ProcessPaymentCreditFromVendorResponse_Status = 13
)

// Enum value maps for ProcessPaymentCreditFromVendorResponse_Status.
var (
	ProcessPaymentCreditFromVendorResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	ProcessPaymentCreditFromVendorResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x ProcessPaymentCreditFromVendorResponse_Status) Enum() *ProcessPaymentCreditFromVendorResponse_Status {
	p := new(ProcessPaymentCreditFromVendorResponse_Status)
	*p = x
	return p
}

func (x ProcessPaymentCreditFromVendorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessPaymentCreditFromVendorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[20].Descriptor()
}

func (ProcessPaymentCreditFromVendorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[20]
}

func (x ProcessPaymentCreditFromVendorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessPaymentCreditFromVendorResponse_Status.Descriptor instead.
func (ProcessPaymentCreditFromVendorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{31, 0}
}

type OrderRejectionUpdateResult_Status int32

const (
	OrderRejectionUpdateResult_STATUS_UNSPECIFIED OrderRejectionUpdateResult_Status = 0
	// update was done successfully
	OrderRejectionUpdateResult_SUCCESS OrderRejectionUpdateResult_Status = 1
	// update failed
	OrderRejectionUpdateResult_FAILURE OrderRejectionUpdateResult_Status = 2
	// invalid order id, updated has failed
	OrderRejectionUpdateResult_INVALID_VENDOR_ORDER_ID OrderRejectionUpdateResult_Status = 3
)

// Enum value maps for OrderRejectionUpdateResult_Status.
var (
	OrderRejectionUpdateResult_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "SUCCESS",
		2: "FAILURE",
		3: "INVALID_VENDOR_ORDER_ID",
	}
	OrderRejectionUpdateResult_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED":      0,
		"SUCCESS":                 1,
		"FAILURE":                 2,
		"INVALID_VENDOR_ORDER_ID": 3,
	}
)

func (x OrderRejectionUpdateResult_Status) Enum() *OrderRejectionUpdateResult_Status {
	p := new(OrderRejectionUpdateResult_Status)
	*p = x
	return p
}

func (x OrderRejectionUpdateResult_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderRejectionUpdateResult_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[21].Descriptor()
}

func (OrderRejectionUpdateResult_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[21]
}

func (x OrderRejectionUpdateResult_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderRejectionUpdateResult_Status.Descriptor instead.
func (OrderRejectionUpdateResult_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{32, 0}
}

type ProcessOrderRejectionFromVendorResponse_Status int32

const (
	// accepted by vendor after all the initial validations
	ProcessOrderRejectionFromVendorResponse_OK ProcessOrderRejectionFromVendorResponse_Status = 0
	// System faced internal errors while processing the request
	ProcessOrderRejectionFromVendorResponse_INTERNAL ProcessOrderRejectionFromVendorResponse_Status = 13
)

// Enum value maps for ProcessOrderRejectionFromVendorResponse_Status.
var (
	ProcessOrderRejectionFromVendorResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	ProcessOrderRejectionFromVendorResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x ProcessOrderRejectionFromVendorResponse_Status) Enum() *ProcessOrderRejectionFromVendorResponse_Status {
	p := new(ProcessOrderRejectionFromVendorResponse_Status)
	*p = x
	return p
}

func (x ProcessOrderRejectionFromVendorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessOrderRejectionFromVendorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[22].Descriptor()
}

func (ProcessOrderRejectionFromVendorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[22]
}

func (x ProcessOrderRejectionFromVendorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessOrderRejectionFromVendorResponse_Status.Descriptor instead.
func (ProcessOrderRejectionFromVendorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{34, 0}
}

type GetBuyConstraintsResponse_Status int32

const (
	GetBuyConstraintsResponse_OK GetBuyConstraintsResponse_Status = 0
	// System faced internal errors while processing the request
	GetBuyConstraintsResponse_INTERNAL GetBuyConstraintsResponse_Status = 13
	// requested FIT rule frequency (daily/weekly/monthly) is not supported by fund house
	GetBuyConstraintsResponse_SIP_FREQUENCY_NOT_SUPPORTED GetBuyConstraintsResponse_Status = 101
)

// Enum value maps for GetBuyConstraintsResponse_Status.
var (
	GetBuyConstraintsResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		101: "SIP_FREQUENCY_NOT_SUPPORTED",
	}
	GetBuyConstraintsResponse_Status_value = map[string]int32{
		"OK":                          0,
		"INTERNAL":                    13,
		"SIP_FREQUENCY_NOT_SUPPORTED": 101,
	}
)

func (x GetBuyConstraintsResponse_Status) Enum() *GetBuyConstraintsResponse_Status {
	p := new(GetBuyConstraintsResponse_Status)
	*p = x
	return p
}

func (x GetBuyConstraintsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetBuyConstraintsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[23].Descriptor()
}

func (GetBuyConstraintsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[23]
}

func (x GetBuyConstraintsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetBuyConstraintsResponse_Status.Descriptor instead.
func (GetBuyConstraintsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{36, 0}
}

type GetOrdersInCreditMISFileResponse_Status int32

const (
	GetOrdersInCreditMISFileResponse_OK GetOrdersInCreditMISFileResponse_Status = 0
	// System faced internal errors while processing the request
	GetOrdersInCreditMISFileResponse_INTERNAL GetOrdersInCreditMISFileResponse_Status = 13
)

// Enum value maps for GetOrdersInCreditMISFileResponse_Status.
var (
	GetOrdersInCreditMISFileResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetOrdersInCreditMISFileResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetOrdersInCreditMISFileResponse_Status) Enum() *GetOrdersInCreditMISFileResponse_Status {
	p := new(GetOrdersInCreditMISFileResponse_Status)
	*p = x
	return p
}

func (x GetOrdersInCreditMISFileResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetOrdersInCreditMISFileResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[24].Descriptor()
}

func (GetOrdersInCreditMISFileResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[24]
}

func (x GetOrdersInCreditMISFileResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetOrdersInCreditMISFileResponse_Status.Descriptor instead.
func (GetOrdersInCreditMISFileResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{40, 0}
}

type GetMFInvestmentsForActorResponse_Status int32

const (
	GetMFInvestmentsForActorResponse_OK       GetMFInvestmentsForActorResponse_Status = 0
	GetMFInvestmentsForActorResponse_INTERNAL GetMFInvestmentsForActorResponse_Status = 13
)

// Enum value maps for GetMFInvestmentsForActorResponse_Status.
var (
	GetMFInvestmentsForActorResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetMFInvestmentsForActorResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetMFInvestmentsForActorResponse_Status) Enum() *GetMFInvestmentsForActorResponse_Status {
	p := new(GetMFInvestmentsForActorResponse_Status)
	*p = x
	return p
}

func (x GetMFInvestmentsForActorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetMFInvestmentsForActorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[25].Descriptor()
}

func (GetMFInvestmentsForActorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[25]
}

func (x GetMFInvestmentsForActorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetMFInvestmentsForActorResponse_Status.Descriptor instead.
func (GetMFInvestmentsForActorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{43, 0}
}

type CheckKYCStatusResponse_Status int32

const (
	CheckKYCStatusResponse_OK       CheckKYCStatusResponse_Status = 0
	CheckKYCStatusResponse_INTERNAL CheckKYCStatusResponse_Status = 13
)

// Enum value maps for CheckKYCStatusResponse_Status.
var (
	CheckKYCStatusResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	CheckKYCStatusResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x CheckKYCStatusResponse_Status) Enum() *CheckKYCStatusResponse_Status {
	p := new(CheckKYCStatusResponse_Status)
	*p = x
	return p
}

func (x CheckKYCStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckKYCStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[26].Descriptor()
}

func (CheckKYCStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[26]
}

func (x CheckKYCStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckKYCStatusResponse_Status.Descriptor instead.
func (CheckKYCStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{45, 0}
}

type OrderUpdateResult_Status int32

const (
	OrderUpdateResult_STATUS_UNSPECIFIED OrderUpdateResult_Status = 0
	// update was done successfully
	OrderUpdateResult_SUCCESS OrderUpdateResult_Status = 1
	// update failed
	OrderUpdateResult_FAILURE OrderUpdateResult_Status = 2
	// invalid order id, updated had failed
	OrderUpdateResult_INVALID_ORDER_ID OrderUpdateResult_Status = 3
)

// Enum value maps for OrderUpdateResult_Status.
var (
	OrderUpdateResult_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "SUCCESS",
		2: "FAILURE",
		3: "INVALID_ORDER_ID",
	}
	OrderUpdateResult_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"SUCCESS":            1,
		"FAILURE":            2,
		"INVALID_ORDER_ID":   3,
	}
)

func (x OrderUpdateResult_Status) Enum() *OrderUpdateResult_Status {
	p := new(OrderUpdateResult_Status)
	*p = x
	return p
}

func (x OrderUpdateResult_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderUpdateResult_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[27].Descriptor()
}

func (OrderUpdateResult_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[27]
}

func (x OrderUpdateResult_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderUpdateResult_Status.Descriptor instead.
func (OrderUpdateResult_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{46, 0}
}

type ProcessOrderConfirmationFromVendorResponse_Status int32

const (
	ProcessOrderConfirmationFromVendorResponse_OK ProcessOrderConfirmationFromVendorResponse_Status = 0
	// System faced internal errors while processing the request
	ProcessOrderConfirmationFromVendorResponse_INTERNAL ProcessOrderConfirmationFromVendorResponse_Status = 13
)

// Enum value maps for ProcessOrderConfirmationFromVendorResponse_Status.
var (
	ProcessOrderConfirmationFromVendorResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	ProcessOrderConfirmationFromVendorResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x ProcessOrderConfirmationFromVendorResponse_Status) Enum() *ProcessOrderConfirmationFromVendorResponse_Status {
	p := new(ProcessOrderConfirmationFromVendorResponse_Status)
	*p = x
	return p
}

func (x ProcessOrderConfirmationFromVendorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessOrderConfirmationFromVendorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[28].Descriptor()
}

func (ProcessOrderConfirmationFromVendorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[28]
}

func (x ProcessOrderConfirmationFromVendorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessOrderConfirmationFromVendorResponse_Status.Descriptor instead.
func (ProcessOrderConfirmationFromVendorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{49, 0}
}

type ProcessExternalOrderConfirmationFromVendorResponse_Status int32

const (
	ProcessExternalOrderConfirmationFromVendorResponse_OK       ProcessExternalOrderConfirmationFromVendorResponse_Status = 0
	ProcessExternalOrderConfirmationFromVendorResponse_INTERNAL ProcessExternalOrderConfirmationFromVendorResponse_Status = 13
)

// Enum value maps for ProcessExternalOrderConfirmationFromVendorResponse_Status.
var (
	ProcessExternalOrderConfirmationFromVendorResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	ProcessExternalOrderConfirmationFromVendorResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x ProcessExternalOrderConfirmationFromVendorResponse_Status) Enum() *ProcessExternalOrderConfirmationFromVendorResponse_Status {
	p := new(ProcessExternalOrderConfirmationFromVendorResponse_Status)
	*p = x
	return p
}

func (x ProcessExternalOrderConfirmationFromVendorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessExternalOrderConfirmationFromVendorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[29].Descriptor()
}

func (ProcessExternalOrderConfirmationFromVendorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[29]
}

func (x ProcessExternalOrderConfirmationFromVendorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessExternalOrderConfirmationFromVendorResponse_Status.Descriptor instead.
func (ProcessExternalOrderConfirmationFromVendorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{51, 0}
}

type ProcessExternalOrderPaymentCreditFromVendorRequest_Status int32

const (
	ProcessExternalOrderPaymentCreditFromVendorRequest_OK       ProcessExternalOrderPaymentCreditFromVendorRequest_Status = 0
	ProcessExternalOrderPaymentCreditFromVendorRequest_INTERNAL ProcessExternalOrderPaymentCreditFromVendorRequest_Status = 13
)

// Enum value maps for ProcessExternalOrderPaymentCreditFromVendorRequest_Status.
var (
	ProcessExternalOrderPaymentCreditFromVendorRequest_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	ProcessExternalOrderPaymentCreditFromVendorRequest_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x ProcessExternalOrderPaymentCreditFromVendorRequest_Status) Enum() *ProcessExternalOrderPaymentCreditFromVendorRequest_Status {
	p := new(ProcessExternalOrderPaymentCreditFromVendorRequest_Status)
	*p = x
	return p
}

func (x ProcessExternalOrderPaymentCreditFromVendorRequest_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessExternalOrderPaymentCreditFromVendorRequest_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[30].Descriptor()
}

func (ProcessExternalOrderPaymentCreditFromVendorRequest_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[30]
}

func (x ProcessExternalOrderPaymentCreditFromVendorRequest_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessExternalOrderPaymentCreditFromVendorRequest_Status.Descriptor instead.
func (ProcessExternalOrderPaymentCreditFromVendorRequest_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{52, 0}
}

type ProcessExternalOrderPaymentCreditFromVendorResponse_Status int32

const (
	ProcessExternalOrderPaymentCreditFromVendorResponse_OK       ProcessExternalOrderPaymentCreditFromVendorResponse_Status = 0
	ProcessExternalOrderPaymentCreditFromVendorResponse_INTERNAL ProcessExternalOrderPaymentCreditFromVendorResponse_Status = 13
)

// Enum value maps for ProcessExternalOrderPaymentCreditFromVendorResponse_Status.
var (
	ProcessExternalOrderPaymentCreditFromVendorResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	ProcessExternalOrderPaymentCreditFromVendorResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x ProcessExternalOrderPaymentCreditFromVendorResponse_Status) Enum() *ProcessExternalOrderPaymentCreditFromVendorResponse_Status {
	p := new(ProcessExternalOrderPaymentCreditFromVendorResponse_Status)
	*p = x
	return p
}

func (x ProcessExternalOrderPaymentCreditFromVendorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessExternalOrderPaymentCreditFromVendorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[31].Descriptor()
}

func (ProcessExternalOrderPaymentCreditFromVendorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[31]
}

func (x ProcessExternalOrderPaymentCreditFromVendorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessExternalOrderPaymentCreditFromVendorResponse_Status.Descriptor instead.
func (ProcessExternalOrderPaymentCreditFromVendorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{53, 0}
}

type CheckOrderEligibilityResponse_Status int32

const (
	// accepted by vendor after all the initial validations
	CheckOrderEligibilityResponse_OK CheckOrderEligibilityResponse_Status = 0
	// System faced internal errors while processing the request
	CheckOrderEligibilityResponse_INTERNAL CheckOrderEligibilityResponse_Status = 13
)

// Enum value maps for CheckOrderEligibilityResponse_Status.
var (
	CheckOrderEligibilityResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	CheckOrderEligibilityResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x CheckOrderEligibilityResponse_Status) Enum() *CheckOrderEligibilityResponse_Status {
	p := new(CheckOrderEligibilityResponse_Status)
	*p = x
	return p
}

func (x CheckOrderEligibilityResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckOrderEligibilityResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[32].Descriptor()
}

func (CheckOrderEligibilityResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[32]
}

func (x CheckOrderEligibilityResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckOrderEligibilityResponse_Status.Descriptor instead.
func (CheckOrderEligibilityResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{55, 0}
}

type MockCreditMISReportProcessingResponse_Status int32

const (
	MockCreditMISReportProcessingResponse_OK       MockCreditMISReportProcessingResponse_Status = 0
	MockCreditMISReportProcessingResponse_INTERNAL MockCreditMISReportProcessingResponse_Status = 13
)

// Enum value maps for MockCreditMISReportProcessingResponse_Status.
var (
	MockCreditMISReportProcessingResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	MockCreditMISReportProcessingResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x MockCreditMISReportProcessingResponse_Status) Enum() *MockCreditMISReportProcessingResponse_Status {
	p := new(MockCreditMISReportProcessingResponse_Status)
	*p = x
	return p
}

func (x MockCreditMISReportProcessingResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MockCreditMISReportProcessingResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[33].Descriptor()
}

func (MockCreditMISReportProcessingResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[33]
}

func (x MockCreditMISReportProcessingResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MockCreditMISReportProcessingResponse_Status.Descriptor instead.
func (MockCreditMISReportProcessingResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{57, 0}
}

type UpdateOrdersResponse_Status int32

const (
	UpdateOrdersResponse_OK       UpdateOrdersResponse_Status = 0
	UpdateOrdersResponse_INTERNAL UpdateOrdersResponse_Status = 13
)

// Enum value maps for UpdateOrdersResponse_Status.
var (
	UpdateOrdersResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	UpdateOrdersResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x UpdateOrdersResponse_Status) Enum() *UpdateOrdersResponse_Status {
	p := new(UpdateOrdersResponse_Status)
	*p = x
	return p
}

func (x UpdateOrdersResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateOrdersResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[34].Descriptor()
}

func (UpdateOrdersResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[34]
}

func (x UpdateOrdersResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateOrdersResponse_Status.Descriptor instead.
func (UpdateOrdersResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{59, 0}
}

type UpdateOrderConfirmationInfoResult_Status int32

const (
	UpdateOrderConfirmationInfoResult_STATUS_UNSPECIFIED UpdateOrderConfirmationInfoResult_Status = 0
	// update was done successfully
	UpdateOrderConfirmationInfoResult_SUCCESS UpdateOrderConfirmationInfoResult_Status = 1
	// update failed
	UpdateOrderConfirmationInfoResult_FAILURE UpdateOrderConfirmationInfoResult_Status = 2
	// invalid order id, updated had failed
	UpdateOrderConfirmationInfoResult_INVALID_ORDER_ID UpdateOrderConfirmationInfoResult_Status = 3
)

// Enum value maps for UpdateOrderConfirmationInfoResult_Status.
var (
	UpdateOrderConfirmationInfoResult_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "SUCCESS",
		2: "FAILURE",
		3: "INVALID_ORDER_ID",
	}
	UpdateOrderConfirmationInfoResult_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"SUCCESS":            1,
		"FAILURE":            2,
		"INVALID_ORDER_ID":   3,
	}
)

func (x UpdateOrderConfirmationInfoResult_Status) Enum() *UpdateOrderConfirmationInfoResult_Status {
	p := new(UpdateOrderConfirmationInfoResult_Status)
	*p = x
	return p
}

func (x UpdateOrderConfirmationInfoResult_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateOrderConfirmationInfoResult_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[35].Descriptor()
}

func (UpdateOrderConfirmationInfoResult_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[35]
}

func (x UpdateOrderConfirmationInfoResult_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateOrderConfirmationInfoResult_Status.Descriptor instead.
func (UpdateOrderConfirmationInfoResult_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{61, 0}
}

type UpdateOrderConfirmationInfoResponse_Status int32

const (
	UpdateOrderConfirmationInfoResponse_OK       UpdateOrderConfirmationInfoResponse_Status = 0
	UpdateOrderConfirmationInfoResponse_INTERNAL UpdateOrderConfirmationInfoResponse_Status = 13
)

// Enum value maps for UpdateOrderConfirmationInfoResponse_Status.
var (
	UpdateOrderConfirmationInfoResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	UpdateOrderConfirmationInfoResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x UpdateOrderConfirmationInfoResponse_Status) Enum() *UpdateOrderConfirmationInfoResponse_Status {
	p := new(UpdateOrderConfirmationInfoResponse_Status)
	*p = x
	return p
}

func (x UpdateOrderConfirmationInfoResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateOrderConfirmationInfoResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[36].Descriptor()
}

func (UpdateOrderConfirmationInfoResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[36]
}

func (x UpdateOrderConfirmationInfoResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateOrderConfirmationInfoResponse_Status.Descriptor instead.
func (UpdateOrderConfirmationInfoResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{63, 0}
}

type GetMisFilesResponse_Status int32

const (
	GetMisFilesResponse_OK GetMisFilesResponse_Status = 0
	// No record found for given startDate and endDate
	GetMisFilesResponse_NOT_FOUND GetMisFilesResponse_Status = 5
	GetMisFilesResponse_INTERNAL  GetMisFilesResponse_Status = 13
)

// Enum value maps for GetMisFilesResponse_Status.
var (
	GetMisFilesResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetMisFilesResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetMisFilesResponse_Status) Enum() *GetMisFilesResponse_Status {
	p := new(GetMisFilesResponse_Status)
	*p = x
	return p
}

func (x GetMisFilesResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetMisFilesResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_mutualfund_order_service_proto_enumTypes[37].Descriptor()
}

func (GetMisFilesResponse_Status) Type() protoreflect.EnumType {
	return &file_api_investment_mutualfund_order_service_proto_enumTypes[37]
}

func (x GetMisFilesResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetMisFilesResponse_Status.Descriptor instead.
func (GetMisFilesResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{67, 0}
}

type HandleFitttSubscriptionUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// only process sip update if order sub type is BUY_SIP
	OrderSubType OrderSubType `protobuf:"varint,1,opt,name=order_sub_type,json=orderSubType,proto3,enum=api.investment.mutualfund.order.OrderSubType" json:"order_sub_type,omitempty"`
	// fittt rule subscription id
	FitttSubscriptionId string `protobuf:"bytes,2,opt,name=fittt_subscription_id,json=fitttSubscriptionId,proto3" json:"fittt_subscription_id,omitempty"`
	// updated amount for sip
	// must be nil if amount is not modified for subscription
	UpdatedAmount *money.Money `protobuf:"bytes,3,opt,name=updated_amount,json=updatedAmount,proto3" json:"updated_amount,omitempty"`
	// mutual fund id in case if mutual fund is changed for a subscription
	// would be empty if mutual fund is not updated
	MutualFundId string `protobuf:"bytes,4,opt,name=mutual_fund_id,json=mutualFundId,proto3" json:"mutual_fund_id,omitempty"`
	// updated sip time
	// would be nil if time is not updated
	//
	// Types that are assignable to UpdatedSipExecution:
	//
	//	*HandleFitttSubscriptionUpdateRequest_SipDate
	//	*HandleFitttSubscriptionUpdateRequest_SipDay
	UpdatedSipExecution isHandleFitttSubscriptionUpdateRequest_UpdatedSipExecution `protobuf_oneof:"updated_sip_execution"`
}

func (x *HandleFitttSubscriptionUpdateRequest) Reset() {
	*x = HandleFitttSubscriptionUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleFitttSubscriptionUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleFitttSubscriptionUpdateRequest) ProtoMessage() {}

func (x *HandleFitttSubscriptionUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleFitttSubscriptionUpdateRequest.ProtoReflect.Descriptor instead.
func (*HandleFitttSubscriptionUpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{0}
}

func (x *HandleFitttSubscriptionUpdateRequest) GetOrderSubType() OrderSubType {
	if x != nil {
		return x.OrderSubType
	}
	return OrderSubType_ORDER_SUB_TYPE_UNSPECIFIED
}

func (x *HandleFitttSubscriptionUpdateRequest) GetFitttSubscriptionId() string {
	if x != nil {
		return x.FitttSubscriptionId
	}
	return ""
}

func (x *HandleFitttSubscriptionUpdateRequest) GetUpdatedAmount() *money.Money {
	if x != nil {
		return x.UpdatedAmount
	}
	return nil
}

func (x *HandleFitttSubscriptionUpdateRequest) GetMutualFundId() string {
	if x != nil {
		return x.MutualFundId
	}
	return ""
}

func (m *HandleFitttSubscriptionUpdateRequest) GetUpdatedSipExecution() isHandleFitttSubscriptionUpdateRequest_UpdatedSipExecution {
	if m != nil {
		return m.UpdatedSipExecution
	}
	return nil
}

func (x *HandleFitttSubscriptionUpdateRequest) GetSipDate() *date.Date {
	if x, ok := x.GetUpdatedSipExecution().(*HandleFitttSubscriptionUpdateRequest_SipDate); ok {
		return x.SipDate
	}
	return nil
}

func (x *HandleFitttSubscriptionUpdateRequest) GetSipDay() dayofweek.DayOfWeek {
	if x, ok := x.GetUpdatedSipExecution().(*HandleFitttSubscriptionUpdateRequest_SipDay); ok {
		return x.SipDay
	}
	return dayofweek.DayOfWeek(0)
}

type isHandleFitttSubscriptionUpdateRequest_UpdatedSipExecution interface {
	isHandleFitttSubscriptionUpdateRequest_UpdatedSipExecution()
}

type HandleFitttSubscriptionUpdateRequest_SipDate struct {
	// updated sip date if any
	SipDate *date.Date `protobuf:"bytes,5,opt,name=sip_date,json=sipDate,proto3,oneof"`
}

type HandleFitttSubscriptionUpdateRequest_SipDay struct {
	// updated sip day of the week if any
	SipDay dayofweek.DayOfWeek `protobuf:"varint,6,opt,name=sip_day,json=sipDay,proto3,enum=google.type.DayOfWeek,oneof"`
}

func (*HandleFitttSubscriptionUpdateRequest_SipDate) isHandleFitttSubscriptionUpdateRequest_UpdatedSipExecution() {
}

func (*HandleFitttSubscriptionUpdateRequest_SipDay) isHandleFitttSubscriptionUpdateRequest_UpdatedSipExecution() {
}

type HandleFitttSubscriptionUpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *HandleFitttSubscriptionUpdateResponse) Reset() {
	*x = HandleFitttSubscriptionUpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleFitttSubscriptionUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleFitttSubscriptionUpdateResponse) ProtoMessage() {}

func (x *HandleFitttSubscriptionUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleFitttSubscriptionUpdateResponse.ProtoReflect.Descriptor instead.
func (*HandleFitttSubscriptionUpdateResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{1}
}

func (x *HandleFitttSubscriptionUpdateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// identifier to the chosen fund.
	MutualFundId string `protobuf:"bytes,2,opt,name=mutual_fund_id,json=mutualFundId,proto3" json:"mutual_fund_id,omitempty"`
	// possible values are only : PAYOUT_ONLY and REINVESTMENT_ONY
	// denote which reinvestment option the user chose at the time of order placement
	//
	// Deprecated: Marked as deprecated in api/investment/mutualfund/order/service.proto.
	ReinvType mutualfund.DividendReinvestmentOptionType `protobuf:"varint,3,opt,name=reinv_type,json=reinvType,proto3,enum=api.investment.mutualfund.DividendReinvestmentOptionType" json:"reinv_type,omitempty"`
	// quantity the user is willing to purchase/redeem
	Units float64 `protobuf:"fixed64,4,opt,name=units,proto3" json:"units,omitempty"`
	// amount of purchase/redemption
	Amount *money.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// type of order
	OrderType OrderType `protobuf:"varint,6,opt,name=order_type,json=orderType,proto3,enum=api.investment.mutualfund.order.OrderType" json:"order_type,omitempty"`
	// client who initiates the order
	Client OrderClient `protobuf:"varint,7,opt,name=client,proto3,enum=api.investment.mutualfund.order.OrderClient" json:"client,omitempty"`
	// unique identifier for the order sent by the client. For example: FIT create an identifier to track
	// the order life cycle at its end.
	ClientOrderId string `protobuf:"bytes,8,opt,name=client_order_id,json=clientOrderId,proto3" json:"client_order_id,omitempty"`
	// the mode of payment for the order
	PaymentMode PaymentMode `protobuf:"varint,9,opt,name=payment_mode,json=paymentMode,proto3,enum=api.investment.mutualfund.order.PaymentMode" json:"payment_mode,omitempty"`
	// rta which processes the selected fund. This is optional.
	//
	// Deprecated: Marked as deprecated in api/investment/mutualfund/order/service.proto.
	Rta vendorgateway.Vendor `protobuf:"varint,10,opt,name=rta,proto3,enum=vendorgateway.Vendor" json:"rta,omitempty"`
	// Investment goal or Jar type for which the investment is being made for. Should be "NO_GOAL" by default
	InvestmentGoal string `protobuf:"bytes,11,opt,name=investment_goal,json=investmentGoal,proto3" json:"investment_goal,omitempty"`
	// payment related metadata required to execute the payment
	PayInfo *PaymentRequestInfo `protobuf:"bytes,12,opt,name=pay_info,json=payInfo,proto3" json:"pay_info,omitempty"`
	// subtype of the order
	OrderSubType OrderSubType `protobuf:"varint,13,opt,name=order_sub_type,json=orderSubType,proto3,enum=api.investment.mutualfund.order.OrderSubType" json:"order_sub_type,omitempty"`
	// Setting this would create the order in a dormant state
	// i.e it would not be picked up for processing and wont be returned in queries unless specifically requested
	CreateInDormantState bool `protobuf:"varint,14,opt,name=create_in_dormant_state,json=createInDormantState,proto3" json:"create_in_dormant_state,omitempty"`
	// Identifier for auth service
	// auth_id will be empty string for orders which are not authenticated by user
	// For now we are only supporting authentication for sell orders
	AuthId string `protobuf:"bytes,15,opt,name=auth_id,json=authId,proto3" json:"auth_id,omitempty"`
	// is_withdraw_all is applicable only for sell orders. if is_withdraw_all is passed as true, then the amount/units passed in the
	// order is ignored and the entire amount/units invested in the fund will be withdrawn.
	IsWithdrawAll bool `protobuf:"varint,16,opt,name=is_withdraw_all,json=isWithdrawAll,proto3" json:"is_withdraw_all,omitempty"`
	// present only for OrderSubType = BUY_SIP
	// metadata required for executing SIP orders
	SipExecutionInfo *SIPExecutionInfo `protobuf:"bytes,17,opt,name=sip_execution_info,json=sipExecutionInfo,proto3" json:"sip_execution_info,omitempty"`
}

func (x *CreateOrderRequest) Reset() {
	*x = CreateOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderRequest) ProtoMessage() {}

func (x *CreateOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateOrderRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateOrderRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CreateOrderRequest) GetMutualFundId() string {
	if x != nil {
		return x.MutualFundId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/investment/mutualfund/order/service.proto.
func (x *CreateOrderRequest) GetReinvType() mutualfund.DividendReinvestmentOptionType {
	if x != nil {
		return x.ReinvType
	}
	return mutualfund.DividendReinvestmentOptionType(0)
}

func (x *CreateOrderRequest) GetUnits() float64 {
	if x != nil {
		return x.Units
	}
	return 0
}

func (x *CreateOrderRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *CreateOrderRequest) GetOrderType() OrderType {
	if x != nil {
		return x.OrderType
	}
	return OrderType_ORDER_TYPE_UNSPECIFIED
}

func (x *CreateOrderRequest) GetClient() OrderClient {
	if x != nil {
		return x.Client
	}
	return OrderClient_ORDER_CLIENT_UNSPECIFIED
}

func (x *CreateOrderRequest) GetClientOrderId() string {
	if x != nil {
		return x.ClientOrderId
	}
	return ""
}

func (x *CreateOrderRequest) GetPaymentMode() PaymentMode {
	if x != nil {
		return x.PaymentMode
	}
	return PaymentMode_PAYMENT_MODE_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/investment/mutualfund/order/service.proto.
func (x *CreateOrderRequest) GetRta() vendorgateway.Vendor {
	if x != nil {
		return x.Rta
	}
	return vendorgateway.Vendor(0)
}

func (x *CreateOrderRequest) GetInvestmentGoal() string {
	if x != nil {
		return x.InvestmentGoal
	}
	return ""
}

func (x *CreateOrderRequest) GetPayInfo() *PaymentRequestInfo {
	if x != nil {
		return x.PayInfo
	}
	return nil
}

func (x *CreateOrderRequest) GetOrderSubType() OrderSubType {
	if x != nil {
		return x.OrderSubType
	}
	return OrderSubType_ORDER_SUB_TYPE_UNSPECIFIED
}

func (x *CreateOrderRequest) GetCreateInDormantState() bool {
	if x != nil {
		return x.CreateInDormantState
	}
	return false
}

func (x *CreateOrderRequest) GetAuthId() string {
	if x != nil {
		return x.AuthId
	}
	return ""
}

func (x *CreateOrderRequest) GetIsWithdrawAll() bool {
	if x != nil {
		return x.IsWithdrawAll
	}
	return false
}

func (x *CreateOrderRequest) GetSipExecutionInfo() *SIPExecutionInfo {
	if x != nil {
		return x.SipExecutionInfo
	}
	return nil
}

type PaymentRequestInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory when payment mode is SI
	RecurringPaymentId string `protobuf:"bytes,1,opt,name=recurring_payment_id,json=recurringPaymentId,proto3" json:"recurring_payment_id,omitempty"`
}

func (x *PaymentRequestInfo) Reset() {
	*x = PaymentRequestInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentRequestInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentRequestInfo) ProtoMessage() {}

func (x *PaymentRequestInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentRequestInfo.ProtoReflect.Descriptor instead.
func (*PaymentRequestInfo) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{3}
}

func (x *PaymentRequestInfo) GetRecurringPaymentId() string {
	if x != nil {
		return x.RecurringPaymentId
	}
	return ""
}

type SIPExecutionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// determines the granularity of the SIP order. Eg: DAILY, WEEKLY and MONTHLY
	SipGranularity mutualfund.SIPGranularity `protobuf:"varint,1,opt,name=sip_granularity,json=sipGranularity,proto3,enum=api.investment.mutualfund.SIPGranularity" json:"sip_granularity,omitempty"`
	// determines subscription for which this SIP order needs to be placed
	FitttSubscriptionId string `protobuf:"bytes,2,opt,name=fittt_subscription_id,json=fitttSubscriptionId,proto3" json:"fittt_subscription_id,omitempty"`
	// used as the 'sip start date' when sip is getting created for the first time
	// This has to coincide with the values provided by the user rather than the actual execution time
	// eg:
	// Monthly sip: if user has selected 15th of every month, then this should be 15th of the month
	// of execution irrespective of when the actual call to 'createOrder' is made
	// Weekly Sip: if user has selected 'TUESDAY', then this should coincide with the 'Tuesday' of the week
	// of execution.
	ExecutionDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=execution_date,json=executionDate,proto3" json:"execution_date,omitempty"`
	// investment_auth_id is the authentication-id generated after verifying the otp during sip registration.
	InvestmentAuthId string `protobuf:"bytes,4,opt,name=investment_auth_id,json=investmentAuthId,proto3" json:"investment_auth_id,omitempty"`
}

func (x *SIPExecutionInfo) Reset() {
	*x = SIPExecutionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SIPExecutionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPExecutionInfo) ProtoMessage() {}

func (x *SIPExecutionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPExecutionInfo.ProtoReflect.Descriptor instead.
func (*SIPExecutionInfo) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{4}
}

func (x *SIPExecutionInfo) GetSipGranularity() mutualfund.SIPGranularity {
	if x != nil {
		return x.SipGranularity
	}
	return mutualfund.SIPGranularity(0)
}

func (x *SIPExecutionInfo) GetFitttSubscriptionId() string {
	if x != nil {
		return x.FitttSubscriptionId
	}
	return ""
}

func (x *SIPExecutionInfo) GetExecutionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ExecutionDate
	}
	return nil
}

func (x *SIPExecutionInfo) GetInvestmentAuthId() string {
	if x != nil {
		return x.InvestmentAuthId
	}
	return ""
}

type CreateOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Order  *Order      `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"`
	// only valid for status: ACTOR_INELIGIBLE
	// for other statuses, this field is not applicable
	NextOnboardingStep *deeplink.Deeplink `protobuf:"bytes,3,opt,name=next_onboarding_step,json=nextOnboardingStep,proto3" json:"next_onboarding_step,omitempty"`
}

func (x *CreateOrderResponse) Reset() {
	*x = CreateOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderResponse) ProtoMessage() {}

func (x *CreateOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateOrderResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateOrderResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateOrderResponse) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *CreateOrderResponse) GetNextOnboardingStep() *deeplink.Deeplink {
	if x != nil {
		return x.NextOnboardingStep
	}
	return nil
}

type GetOrdersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderIds    []string                     `protobuf:"bytes,1,rep,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	OrderIdType GetOrdersRequest_OrderIdType `protobuf:"varint,2,opt,name=order_id_type,json=orderIdType,proto3,enum=api.investment.mutualfund.order.GetOrdersRequest_OrderIdType" json:"order_id_type,omitempty"`
}

func (x *GetOrdersRequest) Reset() {
	*x = GetOrdersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrdersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrdersRequest) ProtoMessage() {}

func (x *GetOrdersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrdersRequest.ProtoReflect.Descriptor instead.
func (*GetOrdersRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetOrdersRequest) GetOrderIds() []string {
	if x != nil {
		return x.OrderIds
	}
	return nil
}

func (x *GetOrdersRequest) GetOrderIdType() GetOrdersRequest_OrderIdType {
	if x != nil {
		return x.OrderIdType
	}
	return GetOrdersRequest_ORDER_ID_TYPE_UNSPECIFIED
}

type OrderData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Order *Order `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *OrderData) Reset() {
	*x = OrderData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderData) ProtoMessage() {}

func (x *OrderData) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderData.ProtoReflect.Descriptor instead.
func (*OrderData) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{7}
}

func (x *OrderData) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

type GetOrdersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Orders map[string]*OrderData `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetOrdersResponse) Reset() {
	*x = GetOrdersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrdersResponse) ProtoMessage() {}

func (x *GetOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrdersResponse.ProtoReflect.Descriptor instead.
func (*GetOrdersResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetOrdersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOrdersResponse) GetOrders() map[string]*OrderData {
	if x != nil {
		return x.Orders
	}
	return nil
}

type TriggerOrderProcessingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// trigger processing of all orders that were received till this timestamp
	ReceivedTill *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=received_till,json=receivedTill,proto3" json:"received_till,omitempty"`
}

func (x *TriggerOrderProcessingRequest) Reset() {
	*x = TriggerOrderProcessingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerOrderProcessingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerOrderProcessingRequest) ProtoMessage() {}

func (x *TriggerOrderProcessingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerOrderProcessingRequest.ProtoReflect.Descriptor instead.
func (*TriggerOrderProcessingRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{9}
}

func (x *TriggerOrderProcessingRequest) GetReceivedTill() *timestamppb.Timestamp {
	if x != nil {
		return x.ReceivedTill
	}
	return nil
}

type TriggerOrderProcessingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *TriggerOrderProcessingResponse) Reset() {
	*x = TriggerOrderProcessingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerOrderProcessingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerOrderProcessingResponse) ProtoMessage() {}

func (x *TriggerOrderProcessingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerOrderProcessingResponse.ProtoReflect.Descriptor instead.
func (*TriggerOrderProcessingResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{10}
}

func (x *TriggerOrderProcessingResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Id:
	//
	//	*GetOrderRequest_OrderId
	//	*GetOrderRequest_ClientOrderId
	//	*GetOrderRequest_ExternalOrderId
	//	*GetOrderRequest_VendorOrderId
	Id isGetOrderRequest_Id `protobuf_oneof:"id"`
}

func (x *GetOrderRequest) Reset() {
	*x = GetOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderRequest) ProtoMessage() {}

func (x *GetOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderRequest.ProtoReflect.Descriptor instead.
func (*GetOrderRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{11}
}

func (m *GetOrderRequest) GetId() isGetOrderRequest_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

func (x *GetOrderRequest) GetOrderId() string {
	if x, ok := x.GetId().(*GetOrderRequest_OrderId); ok {
		return x.OrderId
	}
	return ""
}

func (x *GetOrderRequest) GetClientOrderId() string {
	if x, ok := x.GetId().(*GetOrderRequest_ClientOrderId); ok {
		return x.ClientOrderId
	}
	return ""
}

func (x *GetOrderRequest) GetExternalOrderId() string {
	if x, ok := x.GetId().(*GetOrderRequest_ExternalOrderId); ok {
		return x.ExternalOrderId
	}
	return ""
}

func (x *GetOrderRequest) GetVendorOrderId() string {
	if x, ok := x.GetId().(*GetOrderRequest_VendorOrderId); ok {
		return x.VendorOrderId
	}
	return ""
}

type isGetOrderRequest_Id interface {
	isGetOrderRequest_Id()
}

type GetOrderRequest_OrderId struct {
	OrderId string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3,oneof"`
}

type GetOrderRequest_ClientOrderId struct {
	ClientOrderId string `protobuf:"bytes,2,opt,name=client_order_id,json=clientOrderId,proto3,oneof"`
}

type GetOrderRequest_ExternalOrderId struct {
	ExternalOrderId string `protobuf:"bytes,3,opt,name=external_order_id,json=externalOrderId,proto3,oneof"`
}

type GetOrderRequest_VendorOrderId struct {
	VendorOrderId string `protobuf:"bytes,4,opt,name=vendor_order_id,json=vendorOrderId,proto3,oneof"`
}

func (*GetOrderRequest_OrderId) isGetOrderRequest_Id() {}

func (*GetOrderRequest_ClientOrderId) isGetOrderRequest_Id() {}

func (*GetOrderRequest_ExternalOrderId) isGetOrderRequest_Id() {}

func (*GetOrderRequest_VendorOrderId) isGetOrderRequest_Id() {}

type GetOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Order  *Order      `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *GetOrderResponse) Reset() {
	*x = GetOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderResponse) ProtoMessage() {}

func (x *GetOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderResponse.ProtoReflect.Descriptor instead.
func (*GetOrderResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetOrderResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOrderResponse) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

type UpdateOrderStatusWithCheckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/investment/mutualfund/order/service.proto.
	OrderId              string               `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	NewOrderStatus       OrderStatus          `protobuf:"varint,2,opt,name=new_order_status,json=newOrderStatus,proto3,enum=api.investment.mutualfund.order.OrderStatus" json:"new_order_status,omitempty"`
	CurrentOrderStatus   OrderStatus          `protobuf:"varint,3,opt,name=current_order_status,json=currentOrderStatus,proto3,enum=api.investment.mutualfund.order.OrderStatus" json:"current_order_status,omitempty"`
	FailureReason        FailureReason        `protobuf:"varint,4,opt,name=failure_reason,json=failureReason,proto3,enum=api.investment.mutualfund.order.FailureReason" json:"failure_reason,omitempty"`
	PaymentFailureReason PaymentFailureReason `protobuf:"varint,5,opt,name=payment_failure_reason,json=paymentFailureReason,proto3,enum=api.investment.mutualfund.order.PaymentFailureReason" json:"payment_failure_reason,omitempty"`
	// Types that are assignable to OrderIdentifier:
	//
	//	*UpdateOrderStatusWithCheckRequest_VendorOrderId
	//	*UpdateOrderStatusWithCheckRequest_Id
	OrderIdentifier isUpdateOrderStatusWithCheckRequest_OrderIdentifier `protobuf_oneof:"order_identifier"`
}

func (x *UpdateOrderStatusWithCheckRequest) Reset() {
	*x = UpdateOrderStatusWithCheckRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrderStatusWithCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderStatusWithCheckRequest) ProtoMessage() {}

func (x *UpdateOrderStatusWithCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderStatusWithCheckRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrderStatusWithCheckRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{13}
}

// Deprecated: Marked as deprecated in api/investment/mutualfund/order/service.proto.
func (x *UpdateOrderStatusWithCheckRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *UpdateOrderStatusWithCheckRequest) GetNewOrderStatus() OrderStatus {
	if x != nil {
		return x.NewOrderStatus
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

func (x *UpdateOrderStatusWithCheckRequest) GetCurrentOrderStatus() OrderStatus {
	if x != nil {
		return x.CurrentOrderStatus
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

func (x *UpdateOrderStatusWithCheckRequest) GetFailureReason() FailureReason {
	if x != nil {
		return x.FailureReason
	}
	return FailureReason_FAILURE_REASON_UNSPECIFIED
}

func (x *UpdateOrderStatusWithCheckRequest) GetPaymentFailureReason() PaymentFailureReason {
	if x != nil {
		return x.PaymentFailureReason
	}
	return PaymentFailureReason_PAYMENT_FAILURE_REASON_UNSPECIFIED
}

func (m *UpdateOrderStatusWithCheckRequest) GetOrderIdentifier() isUpdateOrderStatusWithCheckRequest_OrderIdentifier {
	if m != nil {
		return m.OrderIdentifier
	}
	return nil
}

func (x *UpdateOrderStatusWithCheckRequest) GetVendorOrderId() string {
	if x, ok := x.GetOrderIdentifier().(*UpdateOrderStatusWithCheckRequest_VendorOrderId); ok {
		return x.VendorOrderId
	}
	return ""
}

func (x *UpdateOrderStatusWithCheckRequest) GetId() string {
	if x, ok := x.GetOrderIdentifier().(*UpdateOrderStatusWithCheckRequest_Id); ok {
		return x.Id
	}
	return ""
}

type isUpdateOrderStatusWithCheckRequest_OrderIdentifier interface {
	isUpdateOrderStatusWithCheckRequest_OrderIdentifier()
}

type UpdateOrderStatusWithCheckRequest_VendorOrderId struct {
	VendorOrderId string `protobuf:"bytes,6,opt,name=vendor_order_id,json=vendorOrderId,proto3,oneof"`
}

type UpdateOrderStatusWithCheckRequest_Id struct {
	Id string `protobuf:"bytes,7,opt,name=id,proto3,oneof"`
}

func (*UpdateOrderStatusWithCheckRequest_VendorOrderId) isUpdateOrderStatusWithCheckRequest_OrderIdentifier() {
}

func (*UpdateOrderStatusWithCheckRequest_Id) isUpdateOrderStatusWithCheckRequest_OrderIdentifier() {}

type UpdateOrderStatusWithCheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// represents the latest order details. If order was updated, then updated order is returned, other wise existing order is returned.
	Order *Order `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *UpdateOrderStatusWithCheckResponse) Reset() {
	*x = UpdateOrderStatusWithCheckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrderStatusWithCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderStatusWithCheckResponse) ProtoMessage() {}

func (x *UpdateOrderStatusWithCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderStatusWithCheckResponse.ProtoReflect.Descriptor instead.
func (*UpdateOrderStatusWithCheckResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateOrderStatusWithCheckResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateOrderStatusWithCheckResponse) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

type CreateOrUpdateFileStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileState *FileState `protobuf:"bytes,1,opt,name=file_state,json=fileState,proto3" json:"file_state,omitempty"`
}

func (x *CreateOrUpdateFileStateRequest) Reset() {
	*x = CreateOrUpdateFileStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateFileStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateFileStateRequest) ProtoMessage() {}

func (x *CreateOrUpdateFileStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateFileStateRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateFileStateRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{15}
}

func (x *CreateOrUpdateFileStateRequest) GetFileState() *FileState {
	if x != nil {
		return x.FileState
	}
	return nil
}

type CreateOrUpdateFileStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateOrUpdateFileStateResponse) Reset() {
	*x = CreateOrUpdateFileStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrUpdateFileStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateFileStateResponse) ProtoMessage() {}

func (x *CreateOrUpdateFileStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateFileStateResponse.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateFileStateResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{16}
}

func (x *CreateOrUpdateFileStateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UploadVendorFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName    string               `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileContent []byte               `protobuf:"bytes,2,opt,name=file_content,json=fileContent,proto3" json:"file_content,omitempty"`
	Vendor      vendorgateway.Vendor `protobuf:"varint,3,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
}

func (x *UploadVendorFileRequest) Reset() {
	*x = UploadVendorFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadVendorFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadVendorFileRequest) ProtoMessage() {}

func (x *UploadVendorFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadVendorFileRequest.ProtoReflect.Descriptor instead.
func (*UploadVendorFileRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{17}
}

func (x *UploadVendorFileRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *UploadVendorFileRequest) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

func (x *UploadVendorFileRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

type UploadVendorFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// signed_url returns the url using which the uploaded file can be accessed. This url expires after a pre-defined time
	SignedUrl string `protobuf:"bytes,2,opt,name=signed_url,json=signedUrl,proto3" json:"signed_url,omitempty"`
}

func (x *UploadVendorFileResponse) Reset() {
	*x = UploadVendorFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadVendorFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadVendorFileResponse) ProtoMessage() {}

func (x *UploadVendorFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadVendorFileResponse.ProtoReflect.Descriptor instead.
func (*UploadVendorFileResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{18}
}

func (x *UploadVendorFileResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UploadVendorFileResponse) GetSignedUrl() string {
	if x != nil {
		return x.SignedUrl
	}
	return ""
}

type ProcessCreditMISReportConfirmationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName        string               `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	Rta             vendorgateway.Vendor `protobuf:"varint,2,opt,name=rta,proto3,enum=vendorgateway.Vendor" json:"rta,omitempty"`
	ReferenceNumber string               `protobuf:"bytes,3,opt,name=reference_number,json=referenceNumber,proto3" json:"reference_number,omitempty"`
}

func (x *ProcessCreditMISReportConfirmationRequest) Reset() {
	*x = ProcessCreditMISReportConfirmationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCreditMISReportConfirmationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCreditMISReportConfirmationRequest) ProtoMessage() {}

func (x *ProcessCreditMISReportConfirmationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCreditMISReportConfirmationRequest.ProtoReflect.Descriptor instead.
func (*ProcessCreditMISReportConfirmationRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{19}
}

func (x *ProcessCreditMISReportConfirmationRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ProcessCreditMISReportConfirmationRequest) GetRta() vendorgateway.Vendor {
	if x != nil {
		return x.Rta
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessCreditMISReportConfirmationRequest) GetReferenceNumber() string {
	if x != nil {
		return x.ReferenceNumber
	}
	return ""
}

type ProcessCreditMISReportConfirmationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ProcessCreditMISReportConfirmationResponse) Reset() {
	*x = ProcessCreditMISReportConfirmationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCreditMISReportConfirmationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCreditMISReportConfirmationResponse) ProtoMessage() {}

func (x *ProcessCreditMISReportConfirmationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCreditMISReportConfirmationResponse.ProtoReflect.Descriptor instead.
func (*ProcessCreditMISReportConfirmationResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{20}
}

func (x *ProcessCreditMISReportConfirmationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetOrdersByFundIDAndActorIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId      string                  `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	MutualFundId string                  `protobuf:"bytes,2,opt,name=mutual_fund_id,json=mutualFundId,proto3" json:"mutual_fund_id,omitempty"`
	PageContext  *rpc.PageContextRequest `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	// key in this map will be FilterField enum
	// all the filters will be applied with "AND" operations
	Filters []*Filter `protobuf:"bytes,4,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *GetOrdersByFundIDAndActorIDRequest) Reset() {
	*x = GetOrdersByFundIDAndActorIDRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrdersByFundIDAndActorIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrdersByFundIDAndActorIDRequest) ProtoMessage() {}

func (x *GetOrdersByFundIDAndActorIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrdersByFundIDAndActorIDRequest.ProtoReflect.Descriptor instead.
func (*GetOrdersByFundIDAndActorIDRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetOrdersByFundIDAndActorIDRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetOrdersByFundIDAndActorIDRequest) GetMutualFundId() string {
	if x != nil {
		return x.MutualFundId
	}
	return ""
}

func (x *GetOrdersByFundIDAndActorIDRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetOrdersByFundIDAndActorIDRequest) GetFilters() []*Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

type Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Comparator  Filter_Comparator `protobuf:"varint,1,opt,name=comparator,proto3,enum=api.investment.mutualfund.order.Filter_Comparator" json:"comparator,omitempty"`
	FilterField FilterFieldMask   `protobuf:"varint,2,opt,name=filter_field,json=filterField,proto3,enum=api.investment.mutualfund.order.FilterFieldMask" json:"filter_field,omitempty"`
	// Types that are assignable to FilterValue:
	//
	//	*Filter_NumericVal
	//	*Filter_StringVal
	//	*Filter_TimeVal
	//	*Filter_OrderClientVal
	//	*Filter_OrderTypeVal
	//	*Filter_OrderStatusVal
	FilterValue isFilter_FilterValue `protobuf_oneof:"filter_value"`
}

func (x *Filter) Reset() {
	*x = Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filter) ProtoMessage() {}

func (x *Filter) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filter.ProtoReflect.Descriptor instead.
func (*Filter) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{22}
}

func (x *Filter) GetComparator() Filter_Comparator {
	if x != nil {
		return x.Comparator
	}
	return Filter_EQUAL
}

func (x *Filter) GetFilterField() FilterFieldMask {
	if x != nil {
		return x.FilterField
	}
	return FilterFieldMask_FilterFieldMask_UNSPECIFIED
}

func (m *Filter) GetFilterValue() isFilter_FilterValue {
	if m != nil {
		return m.FilterValue
	}
	return nil
}

func (x *Filter) GetNumericVal() float64 {
	if x, ok := x.GetFilterValue().(*Filter_NumericVal); ok {
		return x.NumericVal
	}
	return 0
}

func (x *Filter) GetStringVal() string {
	if x, ok := x.GetFilterValue().(*Filter_StringVal); ok {
		return x.StringVal
	}
	return ""
}

func (x *Filter) GetTimeVal() *timestamppb.Timestamp {
	if x, ok := x.GetFilterValue().(*Filter_TimeVal); ok {
		return x.TimeVal
	}
	return nil
}

func (x *Filter) GetOrderClientVal() OrderClient {
	if x, ok := x.GetFilterValue().(*Filter_OrderClientVal); ok {
		return x.OrderClientVal
	}
	return OrderClient_ORDER_CLIENT_UNSPECIFIED
}

func (x *Filter) GetOrderTypeVal() OrderType {
	if x, ok := x.GetFilterValue().(*Filter_OrderTypeVal); ok {
		return x.OrderTypeVal
	}
	return OrderType_ORDER_TYPE_UNSPECIFIED
}

func (x *Filter) GetOrderStatusVal() OrderStatus {
	if x, ok := x.GetFilterValue().(*Filter_OrderStatusVal); ok {
		return x.OrderStatusVal
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

type isFilter_FilterValue interface {
	isFilter_FilterValue()
}

type Filter_NumericVal struct {
	NumericVal float64 `protobuf:"fixed64,3,opt,name=numeric_val,json=numericVal,proto3,oneof"`
}

type Filter_StringVal struct {
	StringVal string `protobuf:"bytes,4,opt,name=string_val,json=stringVal,proto3,oneof"`
}

type Filter_TimeVal struct {
	TimeVal *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=time_val,json=timeVal,proto3,oneof"`
}

type Filter_OrderClientVal struct {
	OrderClientVal OrderClient `protobuf:"varint,6,opt,name=order_client_val,json=orderClientVal,proto3,enum=api.investment.mutualfund.order.OrderClient,oneof"`
}

type Filter_OrderTypeVal struct {
	OrderTypeVal OrderType `protobuf:"varint,7,opt,name=order_type_val,json=orderTypeVal,proto3,enum=api.investment.mutualfund.order.OrderType,oneof"`
}

type Filter_OrderStatusVal struct {
	OrderStatusVal OrderStatus `protobuf:"varint,8,opt,name=order_status_val,json=orderStatusVal,proto3,enum=api.investment.mutualfund.order.OrderStatus,oneof"`
}

func (*Filter_NumericVal) isFilter_FilterValue() {}

func (*Filter_StringVal) isFilter_FilterValue() {}

func (*Filter_TimeVal) isFilter_FilterValue() {}

func (*Filter_OrderClientVal) isFilter_FilterValue() {}

func (*Filter_OrderTypeVal) isFilter_FilterValue() {}

func (*Filter_OrderStatusVal) isFilter_FilterValue() {}

type GetOrdersByFundIDAndActorIDResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PageContext *rpc.PageContextResponse `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	Orders      []*Order                 `protobuf:"bytes,3,rep,name=orders,proto3" json:"orders,omitempty"`
	TotalOrders int32                    `protobuf:"varint,4,opt,name=total_orders,json=totalOrders,proto3" json:"total_orders,omitempty"`
	MutualFund  *mutualfund.MutualFund   `protobuf:"bytes,5,opt,name=mutual_fund,json=mutualFund,proto3" json:"mutual_fund,omitempty"`
}

func (x *GetOrdersByFundIDAndActorIDResponse) Reset() {
	*x = GetOrdersByFundIDAndActorIDResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrdersByFundIDAndActorIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrdersByFundIDAndActorIDResponse) ProtoMessage() {}

func (x *GetOrdersByFundIDAndActorIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrdersByFundIDAndActorIDResponse.ProtoReflect.Descriptor instead.
func (*GetOrdersByFundIDAndActorIDResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetOrdersByFundIDAndActorIDResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOrdersByFundIDAndActorIDResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetOrdersByFundIDAndActorIDResponse) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *GetOrdersByFundIDAndActorIDResponse) GetTotalOrders() int32 {
	if x != nil {
		return x.TotalOrders
	}
	return 0
}

func (x *GetOrdersByFundIDAndActorIDResponse) GetMutualFund() *mutualfund.MutualFund {
	if x != nil {
		return x.MutualFund
	}
	return nil
}

type GetWithdrawalConstraintsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MfId    string `protobuf:"bytes,1,opt,name=mf_id,json=mfId,proto3" json:"mf_id,omitempty"`
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetWithdrawalConstraintsRequest) Reset() {
	*x = GetWithdrawalConstraintsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWithdrawalConstraintsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWithdrawalConstraintsRequest) ProtoMessage() {}

func (x *GetWithdrawalConstraintsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWithdrawalConstraintsRequest.ProtoReflect.Descriptor instead.
func (*GetWithdrawalConstraintsRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetWithdrawalConstraintsRequest) GetMfId() string {
	if x != nil {
		return x.MfId
	}
	return ""
}

func (x *GetWithdrawalConstraintsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetWithdrawalConstraintsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status                      *rpc.Status  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TotalAmount                 *money.Money `protobuf:"bytes,2,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	MinimumWithdrawalAmount     *money.Money `protobuf:"bytes,3,opt,name=minimum_withdrawal_amount,json=minimumWithdrawalAmount,proto3" json:"minimum_withdrawal_amount,omitempty"`
	MaximumWithdrawalAmount     *money.Money `protobuf:"bytes,4,opt,name=maximum_withdrawal_amount,json=maximumWithdrawalAmount,proto3" json:"maximum_withdrawal_amount,omitempty"`
	IncrementalWithdrawalAmount *money.Money `protobuf:"bytes,5,opt,name=incremental_withdrawal_amount,json=incrementalWithdrawalAmount,proto3" json:"incremental_withdrawal_amount,omitempty"`
	// Deprecated: Marked as deprecated in api/investment/mutualfund/order/service.proto.
	AmountsLike        []string                          `protobuf:"bytes,6,rep,name=amounts_like,json=amountsLike,proto3" json:"amounts_like,omitempty"`
	AssetClass         mutualfund.AssetClass             `protobuf:"varint,7,opt,name=asset_class,json=assetClass,proto3,enum=api.investment.mutualfund.AssetClass" json:"asset_class,omitempty"`
	WithdrawableAmount *money.Money                      `protobuf:"bytes,8,opt,name=withdrawable_amount,json=withdrawableAmount,proto3" json:"withdrawable_amount,omitempty"`
	Category           mutualfund.MutualFundCategoryName `protobuf:"varint,9,opt,name=category,proto3,enum=api.investment.mutualfund.MutualFundCategoryName" json:"category,omitempty"`
}

func (x *GetWithdrawalConstraintsResponse) Reset() {
	*x = GetWithdrawalConstraintsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWithdrawalConstraintsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWithdrawalConstraintsResponse) ProtoMessage() {}

func (x *GetWithdrawalConstraintsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWithdrawalConstraintsResponse.ProtoReflect.Descriptor instead.
func (*GetWithdrawalConstraintsResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetWithdrawalConstraintsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetWithdrawalConstraintsResponse) GetTotalAmount() *money.Money {
	if x != nil {
		return x.TotalAmount
	}
	return nil
}

func (x *GetWithdrawalConstraintsResponse) GetMinimumWithdrawalAmount() *money.Money {
	if x != nil {
		return x.MinimumWithdrawalAmount
	}
	return nil
}

func (x *GetWithdrawalConstraintsResponse) GetMaximumWithdrawalAmount() *money.Money {
	if x != nil {
		return x.MaximumWithdrawalAmount
	}
	return nil
}

func (x *GetWithdrawalConstraintsResponse) GetIncrementalWithdrawalAmount() *money.Money {
	if x != nil {
		return x.IncrementalWithdrawalAmount
	}
	return nil
}

// Deprecated: Marked as deprecated in api/investment/mutualfund/order/service.proto.
func (x *GetWithdrawalConstraintsResponse) GetAmountsLike() []string {
	if x != nil {
		return x.AmountsLike
	}
	return nil
}

func (x *GetWithdrawalConstraintsResponse) GetAssetClass() mutualfund.AssetClass {
	if x != nil {
		return x.AssetClass
	}
	return mutualfund.AssetClass(0)
}

func (x *GetWithdrawalConstraintsResponse) GetWithdrawableAmount() *money.Money {
	if x != nil {
		return x.WithdrawableAmount
	}
	return nil
}

func (x *GetWithdrawalConstraintsResponse) GetCategory() mutualfund.MutualFundCategoryName {
	if x != nil {
		return x.Category
	}
	return mutualfund.MutualFundCategoryName(0)
}

type GetOrderDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Id:
	//
	//	*GetOrderDetailsRequest_OrderId
	//	*GetOrderDetailsRequest_ExternalOrderId
	//	*GetOrderDetailsRequest_ClientOrderId
	//	*GetOrderDetailsRequest_VendorOrderId
	Id isGetOrderDetailsRequest_Id `protobuf_oneof:"id"`
}

func (x *GetOrderDetailsRequest) Reset() {
	*x = GetOrderDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderDetailsRequest) ProtoMessage() {}

func (x *GetOrderDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetOrderDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{26}
}

func (m *GetOrderDetailsRequest) GetId() isGetOrderDetailsRequest_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

func (x *GetOrderDetailsRequest) GetOrderId() string {
	if x, ok := x.GetId().(*GetOrderDetailsRequest_OrderId); ok {
		return x.OrderId
	}
	return ""
}

func (x *GetOrderDetailsRequest) GetExternalOrderId() string {
	if x, ok := x.GetId().(*GetOrderDetailsRequest_ExternalOrderId); ok {
		return x.ExternalOrderId
	}
	return ""
}

func (x *GetOrderDetailsRequest) GetClientOrderId() string {
	if x, ok := x.GetId().(*GetOrderDetailsRequest_ClientOrderId); ok {
		return x.ClientOrderId
	}
	return ""
}

func (x *GetOrderDetailsRequest) GetVendorOrderId() string {
	if x, ok := x.GetId().(*GetOrderDetailsRequest_VendorOrderId); ok {
		return x.VendorOrderId
	}
	return ""
}

type isGetOrderDetailsRequest_Id interface {
	isGetOrderDetailsRequest_Id()
}

type GetOrderDetailsRequest_OrderId struct {
	OrderId string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3,oneof"`
}

type GetOrderDetailsRequest_ExternalOrderId struct {
	ExternalOrderId string `protobuf:"bytes,2,opt,name=external_order_id,json=externalOrderId,proto3,oneof"`
}

type GetOrderDetailsRequest_ClientOrderId struct {
	ClientOrderId string `protobuf:"bytes,3,opt,name=client_order_id,json=clientOrderId,proto3,oneof"`
}

type GetOrderDetailsRequest_VendorOrderId struct {
	VendorOrderId string `protobuf:"bytes,4,opt,name=vendor_order_id,json=vendorOrderId,proto3,oneof"`
}

func (*GetOrderDetailsRequest_OrderId) isGetOrderDetailsRequest_Id() {}

func (*GetOrderDetailsRequest_ExternalOrderId) isGetOrderDetailsRequest_Id() {}

func (*GetOrderDetailsRequest_ClientOrderId) isGetOrderDetailsRequest_Id() {}

func (*GetOrderDetailsRequest_VendorOrderId) isGetOrderDetailsRequest_Id() {}

type GetOrderDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status              *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Order               *Order                 `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"`
	MutualFund          *mutualfund.MutualFund `protobuf:"bytes,3,opt,name=mutual_fund,json=mutualFund,proto3" json:"mutual_fund,omitempty"`
	AmcName             string                 `protobuf:"bytes,4,opt,name=amc_name,json=amcName,proto3" json:"amc_name,omitempty"`
	OrderStatusTimeline []*OrderStatusUpdate   `protobuf:"bytes,5,rep,name=order_status_timeline,json=orderStatusTimeline,proto3" json:"order_status_timeline,omitempty"`
	// Order payment info
	PaymentStatus   payment_handler.PaymentStatus `protobuf:"varint,6,opt,name=payment_status,json=paymentStatus,proto3,enum=api.investment.mutualfund.payment_handler.PaymentStatus" json:"payment_status,omitempty"`
	UtrRefNumber    string                        `protobuf:"bytes,7,opt,name=utr_ref_number,json=utrRefNumber,proto3" json:"utr_ref_number,omitempty"`
	TransactionTime *timestamppb.Timestamp        `protobuf:"bytes,8,opt,name=transaction_time,json=transactionTime,proto3" json:"transaction_time,omitempty"`
	// returns order confirmation details if order was successful
	OrderConfirmationInfo *OrderConfirmationInfo `protobuf:"bytes,9,opt,name=order_confirmation_info,json=orderConfirmationInfo,proto3" json:"order_confirmation_info,omitempty"`
}

func (x *GetOrderDetailsResponse) Reset() {
	*x = GetOrderDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderDetailsResponse) ProtoMessage() {}

func (x *GetOrderDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetOrderDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetOrderDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOrderDetailsResponse) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *GetOrderDetailsResponse) GetMutualFund() *mutualfund.MutualFund {
	if x != nil {
		return x.MutualFund
	}
	return nil
}

func (x *GetOrderDetailsResponse) GetAmcName() string {
	if x != nil {
		return x.AmcName
	}
	return ""
}

func (x *GetOrderDetailsResponse) GetOrderStatusTimeline() []*OrderStatusUpdate {
	if x != nil {
		return x.OrderStatusTimeline
	}
	return nil
}

func (x *GetOrderDetailsResponse) GetPaymentStatus() payment_handler.PaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return payment_handler.PaymentStatus(0)
}

func (x *GetOrderDetailsResponse) GetUtrRefNumber() string {
	if x != nil {
		return x.UtrRefNumber
	}
	return ""
}

func (x *GetOrderDetailsResponse) GetTransactionTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionTime
	}
	return nil
}

func (x *GetOrderDetailsResponse) GetOrderConfirmationInfo() *OrderConfirmationInfo {
	if x != nil {
		return x.OrderConfirmationInfo
	}
	return nil
}

type OrderStatusUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderStatus OrderStatus            `protobuf:"varint,1,opt,name=order_status,json=orderStatus,proto3,enum=api.investment.mutualfund.order.OrderStatus" json:"order_status,omitempty"`
	UpdatedAt   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *OrderStatusUpdate) Reset() {
	*x = OrderStatusUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderStatusUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderStatusUpdate) ProtoMessage() {}

func (x *OrderStatusUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderStatusUpdate.ProtoReflect.Descriptor instead.
func (*OrderStatusUpdate) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{28}
}

func (x *OrderStatusUpdate) GetOrderStatus() OrderStatus {
	if x != nil {
		return x.OrderStatus
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

func (x *OrderStatusUpdate) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type PaymentConfirmationUpdateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdatedStatus     PaymentConfirmationUpdateResult_Status `protobuf:"varint,1,opt,name=updated_status,json=updatedStatus,proto3,enum=api.investment.mutualfund.order.PaymentConfirmationUpdateResult_Status" json:"updated_status,omitempty"`
	OrderId           string                                 `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	TransactionNumber string                                 `protobuf:"bytes,3,opt,name=transaction_number,json=transactionNumber,proto3" json:"transaction_number,omitempty"`
}

func (x *PaymentConfirmationUpdateResult) Reset() {
	*x = PaymentConfirmationUpdateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentConfirmationUpdateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentConfirmationUpdateResult) ProtoMessage() {}

func (x *PaymentConfirmationUpdateResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentConfirmationUpdateResult.ProtoReflect.Descriptor instead.
func (*PaymentConfirmationUpdateResult) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{29}
}

func (x *PaymentConfirmationUpdateResult) GetUpdatedStatus() PaymentConfirmationUpdateResult_Status {
	if x != nil {
		return x.UpdatedStatus
	}
	return PaymentConfirmationUpdateResult_STATUS_UNSPECIFIED
}

func (x *PaymentConfirmationUpdateResult) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *PaymentConfirmationUpdateResult) GetTransactionNumber() string {
	if x != nil {
		return x.TransactionNumber
	}
	return ""
}

type ProcessPaymentCreditFromVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderIdentifier OrderIdentifier `protobuf:"varint,1,opt,name=order_identifier,json=orderIdentifier,proto3,enum=api.investment.mutualfund.order.OrderIdentifier" json:"order_identifier,omitempty"`
	// key of the map represents the id of the PaymentCreditConfirmationIdentifier
	PaymentInfoMap map[string]*PaymentConfirmationMetadata `protobuf:"bytes,2,rep,name=payment_info_map,json=paymentInfoMap,proto3" json:"payment_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Rta            vendorgateway.Vendor                    `protobuf:"varint,3,opt,name=rta,proto3,enum=vendorgateway.Vendor" json:"rta,omitempty"`
}

func (x *ProcessPaymentCreditFromVendorRequest) Reset() {
	*x = ProcessPaymentCreditFromVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessPaymentCreditFromVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessPaymentCreditFromVendorRequest) ProtoMessage() {}

func (x *ProcessPaymentCreditFromVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessPaymentCreditFromVendorRequest.ProtoReflect.Descriptor instead.
func (*ProcessPaymentCreditFromVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{30}
}

func (x *ProcessPaymentCreditFromVendorRequest) GetOrderIdentifier() OrderIdentifier {
	if x != nil {
		return x.OrderIdentifier
	}
	return OrderIdentifier_Order_IDENTIFIER_UNSPECIFIED
}

func (x *ProcessPaymentCreditFromVendorRequest) GetPaymentInfoMap() map[string]*PaymentConfirmationMetadata {
	if x != nil {
		return x.PaymentInfoMap
	}
	return nil
}

func (x *ProcessPaymentCreditFromVendorRequest) GetRta() vendorgateway.Vendor {
	if x != nil {
		return x.Rta
	}
	return vendorgateway.Vendor(0)
}

type ProcessPaymentCreditFromVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// key of the map represents the identifier for the order that was passed in the request.
	UpdatedResults map[string]*PaymentConfirmationUpdateResult `protobuf:"bytes,2,rep,name=updated_results,json=updatedResults,proto3" json:"updated_results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ProcessPaymentCreditFromVendorResponse) Reset() {
	*x = ProcessPaymentCreditFromVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessPaymentCreditFromVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessPaymentCreditFromVendorResponse) ProtoMessage() {}

func (x *ProcessPaymentCreditFromVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessPaymentCreditFromVendorResponse.ProtoReflect.Descriptor instead.
func (*ProcessPaymentCreditFromVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{31}
}

func (x *ProcessPaymentCreditFromVendorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ProcessPaymentCreditFromVendorResponse) GetUpdatedResults() map[string]*PaymentConfirmationUpdateResult {
	if x != nil {
		return x.UpdatedResults
	}
	return nil
}

type OrderRejectionUpdateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdatedStatus OrderRejectionUpdateResult_Status `protobuf:"varint,1,opt,name=updated_status,json=updatedStatus,proto3,enum=api.investment.mutualfund.order.OrderRejectionUpdateResult_Status" json:"updated_status,omitempty"`
	OrderId       string                            `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *OrderRejectionUpdateResult) Reset() {
	*x = OrderRejectionUpdateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderRejectionUpdateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderRejectionUpdateResult) ProtoMessage() {}

func (x *OrderRejectionUpdateResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderRejectionUpdateResult.ProtoReflect.Descriptor instead.
func (*OrderRejectionUpdateResult) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{32}
}

func (x *OrderRejectionUpdateResult) GetUpdatedStatus() OrderRejectionUpdateResult_Status {
	if x != nil {
		return x.UpdatedStatus
	}
	return OrderRejectionUpdateResult_STATUS_UNSPECIFIED
}

func (x *OrderRejectionUpdateResult) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type ProcessOrderRejectionFromVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the key of the map represents the vendor order id
	RejectedOrdersInfo map[string]*OrderRejectionMetaData `protobuf:"bytes,2,rep,name=rejected_orders_info,json=rejectedOrdersInfo,proto3" json:"rejected_orders_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ProcessOrderRejectionFromVendorRequest) Reset() {
	*x = ProcessOrderRejectionFromVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessOrderRejectionFromVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessOrderRejectionFromVendorRequest) ProtoMessage() {}

func (x *ProcessOrderRejectionFromVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessOrderRejectionFromVendorRequest.ProtoReflect.Descriptor instead.
func (*ProcessOrderRejectionFromVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{33}
}

func (x *ProcessOrderRejectionFromVendorRequest) GetRejectedOrdersInfo() map[string]*OrderRejectionMetaData {
	if x != nil {
		return x.RejectedOrdersInfo
	}
	return nil
}

type ProcessOrderRejectionFromVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// key of the map represents the identifier for the order that was passed in the request.
	UpdatedResults map[string]*OrderRejectionUpdateResult `protobuf:"bytes,2,rep,name=updated_results,json=updatedResults,proto3" json:"updated_results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ProcessOrderRejectionFromVendorResponse) Reset() {
	*x = ProcessOrderRejectionFromVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessOrderRejectionFromVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessOrderRejectionFromVendorResponse) ProtoMessage() {}

func (x *ProcessOrderRejectionFromVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessOrderRejectionFromVendorResponse.ProtoReflect.Descriptor instead.
func (*ProcessOrderRejectionFromVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{34}
}

func (x *ProcessOrderRejectionFromVendorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ProcessOrderRejectionFromVendorResponse) GetUpdatedResults() map[string]*OrderRejectionUpdateResult {
	if x != nil {
		return x.UpdatedResults
	}
	return nil
}

type GetBuyConstraintsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MfId    string `protobuf:"bytes,1,opt,name=mf_id,json=mfId,proto3" json:"mf_id,omitempty"`
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Optional - would be present only if the call is from Fittt
	FitRuleId string `protobuf:"bytes,4,opt,name=fitRuleId,proto3" json:"fitRuleId,omitempty"`
	// feature flag if sip is enabled
	// flag will be corresponds to MF_SIP feature flag
	SipEnabled bool `protobuf:"varint,5,opt,name=sip_enabled,json=sipEnabled,proto3" json:"sip_enabled,omitempty"`
}

func (x *GetBuyConstraintsRequest) Reset() {
	*x = GetBuyConstraintsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBuyConstraintsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBuyConstraintsRequest) ProtoMessage() {}

func (x *GetBuyConstraintsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBuyConstraintsRequest.ProtoReflect.Descriptor instead.
func (*GetBuyConstraintsRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetBuyConstraintsRequest) GetMfId() string {
	if x != nil {
		return x.MfId
	}
	return ""
}

func (x *GetBuyConstraintsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetBuyConstraintsRequest) GetFitRuleId() string {
	if x != nil {
		return x.FitRuleId
	}
	return ""
}

func (x *GetBuyConstraintsRequest) GetSipEnabled() bool {
	if x != nil {
		return x.SipEnabled
	}
	return false
}

type GetBuyConstraintsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// order amount can not be less than minimum_purchase_amount
	MinimumPurchaseAmount *money.Money `protobuf:"bytes,2,opt,name=minimum_purchase_amount,json=minimumPurchaseAmount,proto3" json:"minimum_purchase_amount,omitempty"`
	// order amount can not be more than maximum_purchase_amount
	MaximumPurchaseAmount *money.Money `protobuf:"bytes,3,opt,name=maximum_purchase_amount,json=maximumPurchaseAmount,proto3" json:"maximum_purchase_amount,omitempty"`
	// order amount - minimum_purchase_amount should be in steps of incremental_buy_amount
	// For example: if min_buy_amt = 100 and incremental_buy_amt = 50, the permitted values should be 100, 150, 200, 250 etc
	IncrementalPurchaseAmount *money.Money `protobuf:"bytes,4,opt,name=incremental_purchase_amount,json=incrementalPurchaseAmount,proto3" json:"incremental_purchase_amount,omitempty"`
	// list of possible purchase values which conform to the above constraints.
	// used for showing list of default amounts the user can pick either to place an one time order or subscribe to a fit rule
	// Based on the whether this is called by Fitt possible amounts can vary
	// For eg: possible values for a 'Daily' fitt rule would be much less than possible values for 'Monthly' fitt rule
	PossiblePurchaseAmounts []*money.Money `protobuf:"bytes,5,rep,name=possible_purchase_amounts,json=possiblePurchaseAmounts,proto3" json:"possible_purchase_amounts,omitempty"`
	// possible values when an SIP order can be placed
	//
	// Types that are assignable to ScheduleConstraints:
	//
	//	*GetBuyConstraintsResponse_PossibleDates
	//	*GetBuyConstraintsResponse_PossibleDays
	ScheduleConstraints isGetBuyConstraintsResponse_ScheduleConstraints `protobuf_oneof:"schedule_constraints"`
	// order sub type that needs to be created for the order
	OrderSubType OrderSubType `protobuf:"varint,8,opt,name=order_sub_type,json=orderSubType,proto3,enum=api.investment.mutualfund.order.OrderSubType" json:"order_sub_type,omitempty"`
	// frequencies for which sip is allowed in our system
	// note: fund may support multiple SIP frequencies but we will only support sip frequencies shared by product team for a fund
	AllowedSipFrequencies []mutualfund.AipFrequency `protobuf:"varint,9,rep,packed,name=allowed_sip_frequencies,json=allowedSipFrequencies,proto3,enum=api.investment.mutualfund.AipFrequency" json:"allowed_sip_frequencies,omitempty"`
}

func (x *GetBuyConstraintsResponse) Reset() {
	*x = GetBuyConstraintsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBuyConstraintsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBuyConstraintsResponse) ProtoMessage() {}

func (x *GetBuyConstraintsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBuyConstraintsResponse.ProtoReflect.Descriptor instead.
func (*GetBuyConstraintsResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetBuyConstraintsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetBuyConstraintsResponse) GetMinimumPurchaseAmount() *money.Money {
	if x != nil {
		return x.MinimumPurchaseAmount
	}
	return nil
}

func (x *GetBuyConstraintsResponse) GetMaximumPurchaseAmount() *money.Money {
	if x != nil {
		return x.MaximumPurchaseAmount
	}
	return nil
}

func (x *GetBuyConstraintsResponse) GetIncrementalPurchaseAmount() *money.Money {
	if x != nil {
		return x.IncrementalPurchaseAmount
	}
	return nil
}

func (x *GetBuyConstraintsResponse) GetPossiblePurchaseAmounts() []*money.Money {
	if x != nil {
		return x.PossiblePurchaseAmounts
	}
	return nil
}

func (m *GetBuyConstraintsResponse) GetScheduleConstraints() isGetBuyConstraintsResponse_ScheduleConstraints {
	if m != nil {
		return m.ScheduleConstraints
	}
	return nil
}

func (x *GetBuyConstraintsResponse) GetPossibleDates() *RepeatedInt {
	if x, ok := x.GetScheduleConstraints().(*GetBuyConstraintsResponse_PossibleDates); ok {
		return x.PossibleDates
	}
	return nil
}

func (x *GetBuyConstraintsResponse) GetPossibleDays() *RepeatedAipDay {
	if x, ok := x.GetScheduleConstraints().(*GetBuyConstraintsResponse_PossibleDays); ok {
		return x.PossibleDays
	}
	return nil
}

func (x *GetBuyConstraintsResponse) GetOrderSubType() OrderSubType {
	if x != nil {
		return x.OrderSubType
	}
	return OrderSubType_ORDER_SUB_TYPE_UNSPECIFIED
}

func (x *GetBuyConstraintsResponse) GetAllowedSipFrequencies() []mutualfund.AipFrequency {
	if x != nil {
		return x.AllowedSipFrequencies
	}
	return nil
}

type isGetBuyConstraintsResponse_ScheduleConstraints interface {
	isGetBuyConstraintsResponse_ScheduleConstraints()
}

type GetBuyConstraintsResponse_PossibleDates struct {
	// possible dates will contain values in range 1-31
	PossibleDates *RepeatedInt `protobuf:"bytes,6,opt,name=possible_dates,json=possibleDates,proto3,oneof"`
}

type GetBuyConstraintsResponse_PossibleDays struct {
	// possible days will contain values from Weekdays Monday to Sunday
	PossibleDays *RepeatedAipDay `protobuf:"bytes,7,opt,name=possible_days,json=possibleDays,proto3,oneof"`
}

func (*GetBuyConstraintsResponse_PossibleDates) isGetBuyConstraintsResponse_ScheduleConstraints() {}

func (*GetBuyConstraintsResponse_PossibleDays) isGetBuyConstraintsResponse_ScheduleConstraints() {}

type RepeatedInt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IntArray []int32 `protobuf:"varint,1,rep,packed,name=int_array,json=intArray,proto3" json:"int_array,omitempty"`
}

func (x *RepeatedInt) Reset() {
	*x = RepeatedInt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedInt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedInt) ProtoMessage() {}

func (x *RepeatedInt) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedInt.ProtoReflect.Descriptor instead.
func (*RepeatedInt) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{37}
}

func (x *RepeatedInt) GetIntArray() []int32 {
	if x != nil {
		return x.IntArray
	}
	return nil
}

type RepeatedAipDay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DaysArray []dayofweek.DayOfWeek `protobuf:"varint,2,rep,packed,name=days_array,json=daysArray,proto3,enum=google.type.DayOfWeek" json:"days_array,omitempty"`
}

func (x *RepeatedAipDay) Reset() {
	*x = RepeatedAipDay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedAipDay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedAipDay) ProtoMessage() {}

func (x *RepeatedAipDay) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedAipDay.ProtoReflect.Descriptor instead.
func (*RepeatedAipDay) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{38}
}

func (x *RepeatedAipDay) GetDaysArray() []dayofweek.DayOfWeek {
	if x != nil {
		return x.DaysArray
	}
	return nil
}

type GetOrdersInCreditMISFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rta      vendorgateway.Vendor `protobuf:"varint,1,opt,name=rta,proto3,enum=vendorgateway.Vendor" json:"rta,omitempty"`
	FileName string               `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
}

func (x *GetOrdersInCreditMISFileRequest) Reset() {
	*x = GetOrdersInCreditMISFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrdersInCreditMISFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrdersInCreditMISFileRequest) ProtoMessage() {}

func (x *GetOrdersInCreditMISFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrdersInCreditMISFileRequest.ProtoReflect.Descriptor instead.
func (*GetOrdersInCreditMISFileRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{39}
}

func (x *GetOrdersInCreditMISFileRequest) GetRta() vendorgateway.Vendor {
	if x != nil {
		return x.Rta
	}
	return vendorgateway.Vendor(0)
}

func (x *GetOrdersInCreditMISFileRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type GetOrdersInCreditMISFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Orders []*Order    `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders,omitempty"`
}

func (x *GetOrdersInCreditMISFileResponse) Reset() {
	*x = GetOrdersInCreditMISFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrdersInCreditMISFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrdersInCreditMISFileResponse) ProtoMessage() {}

func (x *GetOrdersInCreditMISFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrdersInCreditMISFileResponse.ProtoReflect.Descriptor instead.
func (*GetOrdersInCreditMISFileResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{40}
}

func (x *GetOrdersInCreditMISFileResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOrdersInCreditMISFileResponse) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

type GetMFInvestmentsForActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string   `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	MfIds   []string `protobuf:"bytes,2,rep,name=mf_ids,json=mfIds,proto3" json:"mf_ids,omitempty"`
}

func (x *GetMFInvestmentsForActorRequest) Reset() {
	*x = GetMFInvestmentsForActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMFInvestmentsForActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMFInvestmentsForActorRequest) ProtoMessage() {}

func (x *GetMFInvestmentsForActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMFInvestmentsForActorRequest.ProtoReflect.Descriptor instead.
func (*GetMFInvestmentsForActorRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{41}
}

func (x *GetMFInvestmentsForActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetMFInvestmentsForActorRequest) GetMfIds() []string {
	if x != nil {
		return x.MfIds
	}
	return nil
}

type InvestmentsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Invested bool `protobuf:"varint,1,opt,name=invested,proto3" json:"invested,omitempty"`
}

func (x *InvestmentsInfo) Reset() {
	*x = InvestmentsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvestmentsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvestmentsInfo) ProtoMessage() {}

func (x *InvestmentsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvestmentsInfo.ProtoReflect.Descriptor instead.
func (*InvestmentsInfo) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{42}
}

func (x *InvestmentsInfo) GetInvested() bool {
	if x != nil {
		return x.Invested
	}
	return false
}

type GetMFInvestmentsForActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Maps an MF ID to user's investments in that MF
	Investments map[string]*InvestmentsInfo `protobuf:"bytes,2,rep,name=investments,proto3" json:"investments,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetMFInvestmentsForActorResponse) Reset() {
	*x = GetMFInvestmentsForActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMFInvestmentsForActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMFInvestmentsForActorResponse) ProtoMessage() {}

func (x *GetMFInvestmentsForActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMFInvestmentsForActorResponse.ProtoReflect.Descriptor instead.
func (*GetMFInvestmentsForActorResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{43}
}

func (x *GetMFInvestmentsForActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetMFInvestmentsForActorResponse) GetInvestments() map[string]*InvestmentsInfo {
	if x != nil {
		return x.Investments
	}
	return nil
}

type CheckKYCStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *CheckKYCStatusRequest) Reset() {
	*x = CheckKYCStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckKYCStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckKYCStatusRequest) ProtoMessage() {}

func (x *CheckKYCStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckKYCStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckKYCStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{44}
}

func (x *CheckKYCStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type CheckKYCStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                  *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	IsKycVerificationNeeded bool        `protobuf:"varint,2,opt,name=is_kyc_verification_needed,json=isKycVerificationNeeded,proto3" json:"is_kyc_verification_needed,omitempty"`
}

func (x *CheckKYCStatusResponse) Reset() {
	*x = CheckKYCStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckKYCStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckKYCStatusResponse) ProtoMessage() {}

func (x *CheckKYCStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckKYCStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckKYCStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{45}
}

func (x *CheckKYCStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckKYCStatusResponse) GetIsKycVerificationNeeded() bool {
	if x != nil {
		return x.IsKycVerificationNeeded
	}
	return false
}

type OrderUpdateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId       string                   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	UpdateStatus  OrderUpdateResult_Status `protobuf:"varint,2,opt,name=update_status,json=updateStatus,proto3,enum=api.investment.mutualfund.order.OrderUpdateResult_Status" json:"update_status,omitempty"`
	VendorOrderId string                   `protobuf:"bytes,3,opt,name=vendor_order_id,json=vendorOrderId,proto3" json:"vendor_order_id,omitempty"`
}

func (x *OrderUpdateResult) Reset() {
	*x = OrderUpdateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderUpdateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderUpdateResult) ProtoMessage() {}

func (x *OrderUpdateResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderUpdateResult.ProtoReflect.Descriptor instead.
func (*OrderUpdateResult) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{46}
}

func (x *OrderUpdateResult) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *OrderUpdateResult) GetUpdateStatus() OrderUpdateResult_Status {
	if x != nil {
		return x.UpdateStatus
	}
	return OrderUpdateResult_STATUS_UNSPECIFIED
}

func (x *OrderUpdateResult) GetVendorOrderId() string {
	if x != nil {
		return x.VendorOrderId
	}
	return ""
}

type OrderConfirmationDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId                   string                     `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Amount                    *money.Money               `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Nav                       *money.Money               `protobuf:"bytes,3,opt,name=nav,proto3" json:"nav,omitempty"`
	UnitsAllocated            float64                    `protobuf:"fixed64,4,opt,name=units_allocated,json=unitsAllocated,proto3" json:"units_allocated,omitempty"`
	FolioId                   string                     `protobuf:"bytes,5,opt,name=folio_id,json=folioId,proto3" json:"folio_id,omitempty"`
	OrderConfirmationMetaData *OrderConfirmationMetadata `protobuf:"bytes,6,opt,name=order_confirmation_meta_data,json=orderConfirmationMetaData,proto3" json:"order_confirmation_meta_data,omitempty"`
	VendorOrderId             string                     `protobuf:"bytes,7,opt,name=vendor_order_id,json=vendorOrderId,proto3" json:"vendor_order_id,omitempty"`
	RtaTransactionNumber      string                     `protobuf:"bytes,8,opt,name=rta_transaction_number,json=rtaTransactionNumber,proto3" json:"rta_transaction_number,omitempty"`
	UserCode                  string                     `protobuf:"bytes,9,opt,name=user_code,json=userCode,proto3" json:"user_code,omitempty"`
	SchemeCode                string                     `protobuf:"bytes,10,opt,name=scheme_code,json=schemeCode,proto3" json:"scheme_code,omitempty"`
	OrderAllocatedDate        *date.Date                 `protobuf:"bytes,11,opt,name=order_allocated_date,json=orderAllocatedDate,proto3" json:"order_allocated_date,omitempty"`
	Amc                       mutualfund.Amc             `protobuf:"varint,12,opt,name=amc,proto3,enum=api.investment.mutualfund.Amc" json:"amc,omitempty"`
	OrderType                 OrderType                  `protobuf:"varint,13,opt,name=order_type,json=orderType,proto3,enum=api.investment.mutualfund.order.OrderType" json:"order_type,omitempty"`
	PostDate                  *date.Date                 `protobuf:"bytes,14,opt,name=post_date,json=postDate,proto3" json:"post_date,omitempty"`
}

func (x *OrderConfirmationDetail) Reset() {
	*x = OrderConfirmationDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderConfirmationDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderConfirmationDetail) ProtoMessage() {}

func (x *OrderConfirmationDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderConfirmationDetail.ProtoReflect.Descriptor instead.
func (*OrderConfirmationDetail) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{47}
}

func (x *OrderConfirmationDetail) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *OrderConfirmationDetail) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *OrderConfirmationDetail) GetNav() *money.Money {
	if x != nil {
		return x.Nav
	}
	return nil
}

func (x *OrderConfirmationDetail) GetUnitsAllocated() float64 {
	if x != nil {
		return x.UnitsAllocated
	}
	return 0
}

func (x *OrderConfirmationDetail) GetFolioId() string {
	if x != nil {
		return x.FolioId
	}
	return ""
}

func (x *OrderConfirmationDetail) GetOrderConfirmationMetaData() *OrderConfirmationMetadata {
	if x != nil {
		return x.OrderConfirmationMetaData
	}
	return nil
}

func (x *OrderConfirmationDetail) GetVendorOrderId() string {
	if x != nil {
		return x.VendorOrderId
	}
	return ""
}

func (x *OrderConfirmationDetail) GetRtaTransactionNumber() string {
	if x != nil {
		return x.RtaTransactionNumber
	}
	return ""
}

func (x *OrderConfirmationDetail) GetUserCode() string {
	if x != nil {
		return x.UserCode
	}
	return ""
}

func (x *OrderConfirmationDetail) GetSchemeCode() string {
	if x != nil {
		return x.SchemeCode
	}
	return ""
}

func (x *OrderConfirmationDetail) GetOrderAllocatedDate() *date.Date {
	if x != nil {
		return x.OrderAllocatedDate
	}
	return nil
}

func (x *OrderConfirmationDetail) GetAmc() mutualfund.Amc {
	if x != nil {
		return x.Amc
	}
	return mutualfund.Amc(0)
}

func (x *OrderConfirmationDetail) GetOrderType() OrderType {
	if x != nil {
		return x.OrderType
	}
	return OrderType_ORDER_TYPE_UNSPECIFIED
}

func (x *OrderConfirmationDetail) GetPostDate() *date.Date {
	if x != nil {
		return x.PostDate
	}
	return nil
}

type ProcessOrderConfirmationFromVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientRequestId          string                     `protobuf:"bytes,1,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	OrderConfirmationDetails []*OrderConfirmationDetail `protobuf:"bytes,2,rep,name=order_confirmation_details,json=orderConfirmationDetails,proto3" json:"order_confirmation_details,omitempty"`
}

func (x *ProcessOrderConfirmationFromVendorRequest) Reset() {
	*x = ProcessOrderConfirmationFromVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessOrderConfirmationFromVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessOrderConfirmationFromVendorRequest) ProtoMessage() {}

func (x *ProcessOrderConfirmationFromVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessOrderConfirmationFromVendorRequest.ProtoReflect.Descriptor instead.
func (*ProcessOrderConfirmationFromVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{48}
}

func (x *ProcessOrderConfirmationFromVendorRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *ProcessOrderConfirmationFromVendorRequest) GetOrderConfirmationDetails() []*OrderConfirmationDetail {
	if x != nil {
		return x.OrderConfirmationDetails
	}
	return nil
}

type ProcessOrderConfirmationFromVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status                   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	UpdateResults map[string]*OrderUpdateResult `protobuf:"bytes,2,rep,name=update_results,json=updateResults,proto3" json:"update_results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ProcessOrderConfirmationFromVendorResponse) Reset() {
	*x = ProcessOrderConfirmationFromVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessOrderConfirmationFromVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessOrderConfirmationFromVendorResponse) ProtoMessage() {}

func (x *ProcessOrderConfirmationFromVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessOrderConfirmationFromVendorResponse.ProtoReflect.Descriptor instead.
func (*ProcessOrderConfirmationFromVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{49}
}

func (x *ProcessOrderConfirmationFromVendorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ProcessOrderConfirmationFromVendorResponse) GetUpdateResults() map[string]*OrderUpdateResult {
	if x != nil {
		return x.UpdateResults
	}
	return nil
}

type ProcessExternalOrderConfirmationFromVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientRequestId          string                     `protobuf:"bytes,1,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	OrderConfirmationDetails []*OrderConfirmationDetail `protobuf:"bytes,2,rep,name=order_confirmation_details,json=orderConfirmationDetails,proto3" json:"order_confirmation_details,omitempty"`
	Rta                      vendorgateway.Vendor       `protobuf:"varint,3,opt,name=rta,proto3,enum=vendorgateway.Vendor" json:"rta,omitempty"`
	Amc                      mutualfund.Amc             `protobuf:"varint,4,opt,name=amc,proto3,enum=api.investment.mutualfund.Amc" json:"amc,omitempty"`
	OrderType                OrderType                  `protobuf:"varint,5,opt,name=order_type,json=orderType,proto3,enum=api.investment.mutualfund.order.OrderType" json:"order_type,omitempty"`
	OrderSubType             OrderSubType               `protobuf:"varint,6,opt,name=order_sub_type,json=orderSubType,proto3,enum=api.investment.mutualfund.order.OrderSubType" json:"order_sub_type,omitempty"`
}

func (x *ProcessExternalOrderConfirmationFromVendorRequest) Reset() {
	*x = ProcessExternalOrderConfirmationFromVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessExternalOrderConfirmationFromVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessExternalOrderConfirmationFromVendorRequest) ProtoMessage() {}

func (x *ProcessExternalOrderConfirmationFromVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessExternalOrderConfirmationFromVendorRequest.ProtoReflect.Descriptor instead.
func (*ProcessExternalOrderConfirmationFromVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{50}
}

func (x *ProcessExternalOrderConfirmationFromVendorRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *ProcessExternalOrderConfirmationFromVendorRequest) GetOrderConfirmationDetails() []*OrderConfirmationDetail {
	if x != nil {
		return x.OrderConfirmationDetails
	}
	return nil
}

func (x *ProcessExternalOrderConfirmationFromVendorRequest) GetRta() vendorgateway.Vendor {
	if x != nil {
		return x.Rta
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessExternalOrderConfirmationFromVendorRequest) GetAmc() mutualfund.Amc {
	if x != nil {
		return x.Amc
	}
	return mutualfund.Amc(0)
}

func (x *ProcessExternalOrderConfirmationFromVendorRequest) GetOrderType() OrderType {
	if x != nil {
		return x.OrderType
	}
	return OrderType_ORDER_TYPE_UNSPECIFIED
}

func (x *ProcessExternalOrderConfirmationFromVendorRequest) GetOrderSubType() OrderSubType {
	if x != nil {
		return x.OrderSubType
	}
	return OrderSubType_ORDER_SUB_TYPE_UNSPECIFIED
}

type ProcessExternalOrderConfirmationFromVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status                   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	UpdateResults map[string]*OrderUpdateResult `protobuf:"bytes,2,rep,name=update_results,json=updateResults,proto3" json:"update_results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ProcessExternalOrderConfirmationFromVendorResponse) Reset() {
	*x = ProcessExternalOrderConfirmationFromVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessExternalOrderConfirmationFromVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessExternalOrderConfirmationFromVendorResponse) ProtoMessage() {}

func (x *ProcessExternalOrderConfirmationFromVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessExternalOrderConfirmationFromVendorResponse.ProtoReflect.Descriptor instead.
func (*ProcessExternalOrderConfirmationFromVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{51}
}

func (x *ProcessExternalOrderConfirmationFromVendorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ProcessExternalOrderConfirmationFromVendorResponse) GetUpdateResults() map[string]*OrderUpdateResult {
	if x != nil {
		return x.UpdateResults
	}
	return nil
}

type ProcessExternalOrderPaymentCreditFromVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ProcessExternalOrderPaymentCreditFromVendorRequest) Reset() {
	*x = ProcessExternalOrderPaymentCreditFromVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessExternalOrderPaymentCreditFromVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessExternalOrderPaymentCreditFromVendorRequest) ProtoMessage() {}

func (x *ProcessExternalOrderPaymentCreditFromVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessExternalOrderPaymentCreditFromVendorRequest.ProtoReflect.Descriptor instead.
func (*ProcessExternalOrderPaymentCreditFromVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{52}
}

func (x *ProcessExternalOrderPaymentCreditFromVendorRequest) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ProcessExternalOrderPaymentCreditFromVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ProcessExternalOrderPaymentCreditFromVendorResponse) Reset() {
	*x = ProcessExternalOrderPaymentCreditFromVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessExternalOrderPaymentCreditFromVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessExternalOrderPaymentCreditFromVendorResponse) ProtoMessage() {}

func (x *ProcessExternalOrderPaymentCreditFromVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessExternalOrderPaymentCreditFromVendorResponse.ProtoReflect.Descriptor instead.
func (*ProcessExternalOrderPaymentCreditFromVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{53}
}

func (x *ProcessExternalOrderPaymentCreditFromVendorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CheckOrderEligibilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Order *Order `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *CheckOrderEligibilityRequest) Reset() {
	*x = CheckOrderEligibilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckOrderEligibilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOrderEligibilityRequest) ProtoMessage() {}

func (x *CheckOrderEligibilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOrderEligibilityRequest.ProtoReflect.Descriptor instead.
func (*CheckOrderEligibilityRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{54}
}

func (x *CheckOrderEligibilityRequest) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

type CheckOrderEligibilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status              *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	EligibilityStatus   EligibilityStatus   `protobuf:"varint,2,opt,name=eligibility_status,json=eligibilityStatus,proto3,enum=api.investment.mutualfund.order.EligibilityStatus" json:"eligibility_status,omitempty"`
	IneligibilityReason IneligibilityReason `protobuf:"varint,3,opt,name=ineligibility_reason,json=ineligibilityReason,proto3,enum=api.investment.mutualfund.order.IneligibilityReason" json:"ineligibility_reason,omitempty"`
	// relevant only when eligibility_status is NOT_ELIGIBLE
	// in other cases this field is not applicable
	NextStep *deeplink.Deeplink `protobuf:"bytes,4,opt,name=next_step,json=nextStep,proto3" json:"next_step,omitempty"`
}

func (x *CheckOrderEligibilityResponse) Reset() {
	*x = CheckOrderEligibilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckOrderEligibilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOrderEligibilityResponse) ProtoMessage() {}

func (x *CheckOrderEligibilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOrderEligibilityResponse.ProtoReflect.Descriptor instead.
func (*CheckOrderEligibilityResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{55}
}

func (x *CheckOrderEligibilityResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckOrderEligibilityResponse) GetEligibilityStatus() EligibilityStatus {
	if x != nil {
		return x.EligibilityStatus
	}
	return EligibilityStatus_ELIGIBILITY_STATUS_UNSPECIFIED
}

func (x *CheckOrderEligibilityResponse) GetIneligibilityReason() IneligibilityReason {
	if x != nil {
		return x.IneligibilityReason
	}
	return IneligibilityReason_INELIGIBILITY_REASON_UNSPECIFIED
}

func (x *CheckOrderEligibilityResponse) GetNextStep() *deeplink.Deeplink {
	if x != nil {
		return x.NextStep
	}
	return nil
}

type MockCreditMISReportProcessingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName  string               `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	Rta       vendorgateway.Vendor `protobuf:"varint,2,opt,name=rta,proto3,enum=vendorgateway.Vendor" json:"rta,omitempty"`
	OrderIds  []string             `protobuf:"bytes,3,rep,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	OrderType OrderType            `protobuf:"varint,4,opt,name=order_type,json=orderType,proto3,enum=api.investment.mutualfund.order.OrderType" json:"order_type,omitempty"`
}

func (x *MockCreditMISReportProcessingRequest) Reset() {
	*x = MockCreditMISReportProcessingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MockCreditMISReportProcessingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MockCreditMISReportProcessingRequest) ProtoMessage() {}

func (x *MockCreditMISReportProcessingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MockCreditMISReportProcessingRequest.ProtoReflect.Descriptor instead.
func (*MockCreditMISReportProcessingRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{56}
}

func (x *MockCreditMISReportProcessingRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *MockCreditMISReportProcessingRequest) GetRta() vendorgateway.Vendor {
	if x != nil {
		return x.Rta
	}
	return vendorgateway.Vendor(0)
}

func (x *MockCreditMISReportProcessingRequest) GetOrderIds() []string {
	if x != nil {
		return x.OrderIds
	}
	return nil
}

func (x *MockCreditMISReportProcessingRequest) GetOrderType() OrderType {
	if x != nil {
		return x.OrderType
	}
	return OrderType_ORDER_TYPE_UNSPECIFIED
}

type MockCreditMISReportProcessingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *MockCreditMISReportProcessingResponse) Reset() {
	*x = MockCreditMISReportProcessingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MockCreditMISReportProcessingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MockCreditMISReportProcessingResponse) ProtoMessage() {}

func (x *MockCreditMISReportProcessingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MockCreditMISReportProcessingResponse.ProtoReflect.Descriptor instead.
func (*MockCreditMISReportProcessingResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{57}
}

func (x *MockCreditMISReportProcessingResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UpdateOrdersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderUpdateDetails []*OrderUpdateDetail `protobuf:"bytes,1,rep,name=order_update_details,json=orderUpdateDetails,proto3" json:"order_update_details,omitempty"`
}

func (x *UpdateOrdersRequest) Reset() {
	*x = UpdateOrdersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrdersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrdersRequest) ProtoMessage() {}

func (x *UpdateOrdersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrdersRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrdersRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{58}
}

func (x *UpdateOrdersRequest) GetOrderUpdateDetails() []*OrderUpdateDetail {
	if x != nil {
		return x.OrderUpdateDetails
	}
	return nil
}

type UpdateOrdersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// key: order_id
	UpdateResults map[string]*OrderUpdateResult `protobuf:"bytes,2,rep,name=update_results,json=updateResults,proto3" json:"update_results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateOrdersResponse) Reset() {
	*x = UpdateOrdersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrdersResponse) ProtoMessage() {}

func (x *UpdateOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrdersResponse.ProtoReflect.Descriptor instead.
func (*UpdateOrdersResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{59}
}

func (x *UpdateOrdersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateOrdersResponse) GetUpdateResults() map[string]*OrderUpdateResult {
	if x != nil {
		return x.UpdateResults
	}
	return nil
}

type OrderUpdateDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Order *Order           `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	Masks []OrderFieldMask `protobuf:"varint,2,rep,packed,name=masks,proto3,enum=api.investment.mutualfund.order.OrderFieldMask" json:"masks,omitempty"`
}

func (x *OrderUpdateDetail) Reset() {
	*x = OrderUpdateDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderUpdateDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderUpdateDetail) ProtoMessage() {}

func (x *OrderUpdateDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderUpdateDetail.ProtoReflect.Descriptor instead.
func (*OrderUpdateDetail) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{60}
}

func (x *OrderUpdateDetail) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *OrderUpdateDetail) GetMasks() []OrderFieldMask {
	if x != nil {
		return x.Masks
	}
	return nil
}

type UpdateOrderConfirmationInfoResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId      string                                   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	UpdateStatus UpdateOrderConfirmationInfoResult_Status `protobuf:"varint,2,opt,name=update_status,json=updateStatus,proto3,enum=api.investment.mutualfund.order.UpdateOrderConfirmationInfoResult_Status" json:"update_status,omitempty"`
}

func (x *UpdateOrderConfirmationInfoResult) Reset() {
	*x = UpdateOrderConfirmationInfoResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrderConfirmationInfoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderConfirmationInfoResult) ProtoMessage() {}

func (x *UpdateOrderConfirmationInfoResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderConfirmationInfoResult.ProtoReflect.Descriptor instead.
func (*UpdateOrderConfirmationInfoResult) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{61}
}

func (x *UpdateOrderConfirmationInfoResult) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *UpdateOrderConfirmationInfoResult) GetUpdateStatus() UpdateOrderConfirmationInfoResult_Status {
	if x != nil {
		return x.UpdateStatus
	}
	return UpdateOrderConfirmationInfoResult_STATUS_UNSPECIFIED
}

type UpdateOrderConfirmationInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderConfirmationInfos []*OrderConfirmationInfo         `protobuf:"bytes,1,rep,name=order_confirmation_infos,json=orderConfirmationInfos,proto3" json:"order_confirmation_infos,omitempty"`
	Masks                  []OrderConfirmationInfoFieldMASK `protobuf:"varint,2,rep,packed,name=masks,proto3,enum=api.investment.mutualfund.order.OrderConfirmationInfoFieldMASK" json:"masks,omitempty"`
}

func (x *UpdateOrderConfirmationInfoRequest) Reset() {
	*x = UpdateOrderConfirmationInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrderConfirmationInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderConfirmationInfoRequest) ProtoMessage() {}

func (x *UpdateOrderConfirmationInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderConfirmationInfoRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrderConfirmationInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{62}
}

func (x *UpdateOrderConfirmationInfoRequest) GetOrderConfirmationInfos() []*OrderConfirmationInfo {
	if x != nil {
		return x.OrderConfirmationInfos
	}
	return nil
}

func (x *UpdateOrderConfirmationInfoRequest) GetMasks() []OrderConfirmationInfoFieldMASK {
	if x != nil {
		return x.Masks
	}
	return nil
}

type UpdateOrderConfirmationInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// key: order_id
	UpdateResults map[string]*UpdateOrderConfirmationInfoResult `protobuf:"bytes,2,rep,name=update_results,json=updateResults,proto3" json:"update_results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateOrderConfirmationInfoResponse) Reset() {
	*x = UpdateOrderConfirmationInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrderConfirmationInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderConfirmationInfoResponse) ProtoMessage() {}

func (x *UpdateOrderConfirmationInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderConfirmationInfoResponse.ProtoReflect.Descriptor instead.
func (*UpdateOrderConfirmationInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{63}
}

func (x *UpdateOrderConfirmationInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateOrderConfirmationInfoResponse) GetUpdateResults() map[string]*UpdateOrderConfirmationInfoResult {
	if x != nil {
		return x.UpdateResults
	}
	return nil
}

type ProcessInternalSwitchOrdersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SwitchInOrder   *ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload `protobuf:"bytes,1,opt,name=switch_in_order,json=switchInOrder,proto3" json:"switch_in_order,omitempty"`
	SwitchOutOrder  *ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload `protobuf:"bytes,2,opt,name=switch_out_order,json=switchOutOrder,proto3" json:"switch_out_order,omitempty"`
	ClientRequestId string                                                       `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
}

func (x *ProcessInternalSwitchOrdersRequest) Reset() {
	*x = ProcessInternalSwitchOrdersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessInternalSwitchOrdersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessInternalSwitchOrdersRequest) ProtoMessage() {}

func (x *ProcessInternalSwitchOrdersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessInternalSwitchOrdersRequest.ProtoReflect.Descriptor instead.
func (*ProcessInternalSwitchOrdersRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{64}
}

func (x *ProcessInternalSwitchOrdersRequest) GetSwitchInOrder() *ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload {
	if x != nil {
		return x.SwitchInOrder
	}
	return nil
}

func (x *ProcessInternalSwitchOrdersRequest) GetSwitchOutOrder() *ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload {
	if x != nil {
		return x.SwitchOutOrder
	}
	return nil
}

func (x *ProcessInternalSwitchOrdersRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

type ProcessInternalSwitchOrdersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status                   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	UpdateResults map[string]*OrderUpdateResult `protobuf:"bytes,2,rep,name=update_results,json=updateResults,proto3" json:"update_results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ProcessInternalSwitchOrdersResponse) Reset() {
	*x = ProcessInternalSwitchOrdersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessInternalSwitchOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessInternalSwitchOrdersResponse) ProtoMessage() {}

func (x *ProcessInternalSwitchOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessInternalSwitchOrdersResponse.ProtoReflect.Descriptor instead.
func (*ProcessInternalSwitchOrdersResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{65}
}

func (x *ProcessInternalSwitchOrdersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ProcessInternalSwitchOrdersResponse) GetUpdateResults() map[string]*OrderUpdateResult {
	if x != nil {
		return x.UpdateResults
	}
	return nil
}

type GetMisFilesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartDate *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// define how to handle(zipped/unzipped) multiple files per RTA
	ZipPreference ZipPreference `protobuf:"varint,3,opt,name=zip_preference,json=zipPreference,proto3,enum=api.investment.mutualfund.order.ZipPreference" json:"zip_preference,omitempty"`
}

func (x *GetMisFilesRequest) Reset() {
	*x = GetMisFilesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMisFilesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMisFilesRequest) ProtoMessage() {}

func (x *GetMisFilesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMisFilesRequest.ProtoReflect.Descriptor instead.
func (*GetMisFilesRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{66}
}

func (x *GetMisFilesRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetMisFilesRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *GetMisFilesRequest) GetZipPreference() ZipPreference {
	if x != nil {
		return x.ZipPreference
	}
	return ZipPreference_UNSPECIFIED
}

type GetMisFilesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of Amc files with FileData(Url) and fileName
	AmcFiles []*GetMisFilesResponse_AmcFiles `protobuf:"bytes,2,rep,name=amc_files,json=amcFiles,proto3" json:"amc_files,omitempty"`
	// Rta wise zip file with fileName and url
	RtaFiles []*GetMisFilesResponse_RtaFile `protobuf:"bytes,3,rep,name=rta_files,json=rtaFiles,proto3" json:"rta_files,omitempty"`
}

func (x *GetMisFilesResponse) Reset() {
	*x = GetMisFilesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMisFilesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMisFilesResponse) ProtoMessage() {}

func (x *GetMisFilesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMisFilesResponse.ProtoReflect.Descriptor instead.
func (*GetMisFilesResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{67}
}

func (x *GetMisFilesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetMisFilesResponse) GetAmcFiles() []*GetMisFilesResponse_AmcFiles {
	if x != nil {
		return x.AmcFiles
	}
	return nil
}

func (x *GetMisFilesResponse) GetRtaFiles() []*GetMisFilesResponse_RtaFile {
	if x != nil {
		return x.RtaFiles
	}
	return nil
}

type ProcessExternalSwitchOrdersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SwitchInOrder   *ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload `protobuf:"bytes,1,opt,name=switch_in_order,json=switchInOrder,proto3" json:"switch_in_order,omitempty"`
	SwitchOutOrder  *ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload `protobuf:"bytes,2,opt,name=switch_out_order,json=switchOutOrder,proto3" json:"switch_out_order,omitempty"`
	ClientRequestId string                                                       `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
}

func (x *ProcessExternalSwitchOrdersRequest) Reset() {
	*x = ProcessExternalSwitchOrdersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessExternalSwitchOrdersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessExternalSwitchOrdersRequest) ProtoMessage() {}

func (x *ProcessExternalSwitchOrdersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessExternalSwitchOrdersRequest.ProtoReflect.Descriptor instead.
func (*ProcessExternalSwitchOrdersRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{68}
}

func (x *ProcessExternalSwitchOrdersRequest) GetSwitchInOrder() *ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload {
	if x != nil {
		return x.SwitchInOrder
	}
	return nil
}

func (x *ProcessExternalSwitchOrdersRequest) GetSwitchOutOrder() *ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload {
	if x != nil {
		return x.SwitchOutOrder
	}
	return nil
}

func (x *ProcessExternalSwitchOrdersRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

type ProcessExternalSwitchOrdersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status                   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	UpdateResults map[string]*OrderUpdateResult `protobuf:"bytes,2,rep,name=update_results,json=updateResults,proto3" json:"update_results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ProcessExternalSwitchOrdersResponse) Reset() {
	*x = ProcessExternalSwitchOrdersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessExternalSwitchOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessExternalSwitchOrdersResponse) ProtoMessage() {}

func (x *ProcessExternalSwitchOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessExternalSwitchOrdersResponse.ProtoReflect.Descriptor instead.
func (*ProcessExternalSwitchOrdersResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{69}
}

func (x *ProcessExternalSwitchOrdersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ProcessExternalSwitchOrdersResponse) GetUpdateResults() map[string]*OrderUpdateResult {
	if x != nil {
		return x.UpdateResults
	}
	return nil
}

type ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderConfirmationDetail *OrderConfirmationDetail `protobuf:"bytes,1,opt,name=order_confirmation_detail,json=orderConfirmationDetail,proto3" json:"order_confirmation_detail,omitempty"`
	Rta                     vendorgateway.Vendor     `protobuf:"varint,2,opt,name=rta,proto3,enum=vendorgateway.Vendor" json:"rta,omitempty"`
	OrderType               OrderType                `protobuf:"varint,3,opt,name=order_type,json=orderType,proto3,enum=api.investment.mutualfund.order.OrderType" json:"order_type,omitempty"`
	OrderSubType            OrderSubType             `protobuf:"varint,4,opt,name=order_sub_type,json=orderSubType,proto3,enum=api.investment.mutualfund.order.OrderSubType" json:"order_sub_type,omitempty"`
}

func (x *ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload) Reset() {
	*x = ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload) ProtoMessage() {}

func (x *ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload.ProtoReflect.Descriptor instead.
func (*ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{64, 0}
}

func (x *ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload) GetOrderConfirmationDetail() *OrderConfirmationDetail {
	if x != nil {
		return x.OrderConfirmationDetail
	}
	return nil
}

func (x *ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload) GetRta() vendorgateway.Vendor {
	if x != nil {
		return x.Rta
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload) GetOrderType() OrderType {
	if x != nil {
		return x.OrderType
	}
	return OrderType_ORDER_TYPE_UNSPECIFIED
}

func (x *ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload) GetOrderSubType() OrderSubType {
	if x != nil {
		return x.OrderSubType
	}
	return OrderSubType_ORDER_SUB_TYPE_UNSPECIFIED
}

type GetMisFilesResponse_AmcFiles struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amc         mutualfund.Amc                              `protobuf:"varint,1,opt,name=amc,proto3,enum=api.investment.mutualfund.Amc" json:"amc,omitempty"`
	AmcFileData []*GetMisFilesResponse_AmcFiles_AmcFileData `protobuf:"bytes,2,rep,name=amc_file_data,json=amcFileData,proto3" json:"amc_file_data,omitempty"`
}

func (x *GetMisFilesResponse_AmcFiles) Reset() {
	*x = GetMisFilesResponse_AmcFiles{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMisFilesResponse_AmcFiles) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMisFilesResponse_AmcFiles) ProtoMessage() {}

func (x *GetMisFilesResponse_AmcFiles) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMisFilesResponse_AmcFiles.ProtoReflect.Descriptor instead.
func (*GetMisFilesResponse_AmcFiles) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{67, 0}
}

func (x *GetMisFilesResponse_AmcFiles) GetAmc() mutualfund.Amc {
	if x != nil {
		return x.Amc
	}
	return mutualfund.Amc(0)
}

func (x *GetMisFilesResponse_AmcFiles) GetAmcFileData() []*GetMisFilesResponse_AmcFiles_AmcFileData {
	if x != nil {
		return x.AmcFileData
	}
	return nil
}

type GetMisFilesResponse_RtaFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rta         vendorgateway.Vendor `protobuf:"varint,1,opt,name=rta,proto3,enum=vendorgateway.Vendor" json:"rta,omitempty"`
	ZipUrl      string               `protobuf:"bytes,2,opt,name=zip_url,json=zipUrl,proto3" json:"zip_url,omitempty"`
	ZipFilename string               `protobuf:"bytes,3,opt,name=zip_filename,json=zipFilename,proto3" json:"zip_filename,omitempty"`
}

func (x *GetMisFilesResponse_RtaFile) Reset() {
	*x = GetMisFilesResponse_RtaFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMisFilesResponse_RtaFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMisFilesResponse_RtaFile) ProtoMessage() {}

func (x *GetMisFilesResponse_RtaFile) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMisFilesResponse_RtaFile.ProtoReflect.Descriptor instead.
func (*GetMisFilesResponse_RtaFile) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{67, 1}
}

func (x *GetMisFilesResponse_RtaFile) GetRta() vendorgateway.Vendor {
	if x != nil {
		return x.Rta
	}
	return vendorgateway.Vendor(0)
}

func (x *GetMisFilesResponse_RtaFile) GetZipUrl() string {
	if x != nil {
		return x.ZipUrl
	}
	return ""
}

func (x *GetMisFilesResponse_RtaFile) GetZipFilename() string {
	if x != nil {
		return x.ZipFilename
	}
	return ""
}

type GetMisFilesResponse_AmcFiles_AmcFileData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileData *filegenerator.FileData `protobuf:"bytes,1,opt,name=file_data,json=fileData,proto3" json:"file_data,omitempty"`
	Filename string                  `protobuf:"bytes,2,opt,name=filename,proto3" json:"filename,omitempty"`
}

func (x *GetMisFilesResponse_AmcFiles_AmcFileData) Reset() {
	*x = GetMisFilesResponse_AmcFiles_AmcFileData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMisFilesResponse_AmcFiles_AmcFileData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMisFilesResponse_AmcFiles_AmcFileData) ProtoMessage() {}

func (x *GetMisFilesResponse_AmcFiles_AmcFileData) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMisFilesResponse_AmcFiles_AmcFileData.ProtoReflect.Descriptor instead.
func (*GetMisFilesResponse_AmcFiles_AmcFileData) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{67, 0, 0}
}

func (x *GetMisFilesResponse_AmcFiles_AmcFileData) GetFileData() *filegenerator.FileData {
	if x != nil {
		return x.FileData
	}
	return nil
}

func (x *GetMisFilesResponse_AmcFiles_AmcFileData) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

type ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderConfirmationDetail *OrderConfirmationDetail `protobuf:"bytes,1,opt,name=order_confirmation_detail,json=orderConfirmationDetail,proto3" json:"order_confirmation_detail,omitempty"`
	Rta                     vendorgateway.Vendor     `protobuf:"varint,2,opt,name=rta,proto3,enum=vendorgateway.Vendor" json:"rta,omitempty"`
	OrderType               OrderType                `protobuf:"varint,3,opt,name=order_type,json=orderType,proto3,enum=api.investment.mutualfund.order.OrderType" json:"order_type,omitempty"`
	OrderSubType            OrderSubType             `protobuf:"varint,4,opt,name=order_sub_type,json=orderSubType,proto3,enum=api.investment.mutualfund.order.OrderSubType" json:"order_sub_type,omitempty"`
}

func (x *ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload) Reset() {
	*x = ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload) ProtoMessage() {}

func (x *ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_order_service_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload.ProtoReflect.Descriptor instead.
func (*ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_order_service_proto_rawDescGZIP(), []int{68, 0}
}

func (x *ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload) GetOrderConfirmationDetail() *OrderConfirmationDetail {
	if x != nil {
		return x.OrderConfirmationDetail
	}
	return nil
}

func (x *ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload) GetRta() vendorgateway.Vendor {
	if x != nil {
		return x.Rta
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload) GetOrderType() OrderType {
	if x != nil {
		return x.OrderType
	}
	return OrderType_ORDER_TYPE_UNSPECIFIED
}

func (x *ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload) GetOrderSubType() OrderSubType {
	if x != nil {
		return x.OrderSubType
	}
	return OrderSubType_ORDER_SUB_TYPE_UNSPECIFIED
}

var File_api_investment_mutualfund_order_service_proto protoreflect.FileDescriptor

var file_api_investment_mutualfund_order_service_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1f, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x3d, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37,
	0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75,
	0x6e, 0x64, 0x2f, 0x73, 0x69, 0x70, 0x5f, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61, 0x67,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x79, 0x6f, 0x66, 0x77, 0x65, 0x65, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8c, 0x03, 0x0a, 0x24, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x46, 0x69, 0x74, 0x74, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53,
	0x0a, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75,
	0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x75,
	0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x75, 0x62, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x66, 0x69, 0x74, 0x74, 0x74, 0x5f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x66, 0x69, 0x74, 0x74, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x08, 0x73, 0x69, 0x70, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52,
	0x07, 0x73, 0x69, 0x70, 0x44, 0x61, 0x74, 0x65, 0x12, 0x31, 0x0a, 0x07, 0x73, 0x69, 0x70, 0x5f,
	0x64, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65,
	0x6b, 0x48, 0x00, 0x52, 0x06, 0x73, 0x69, 0x70, 0x44, 0x61, 0x79, 0x42, 0x17, 0x0a, 0x15, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x70, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x6c, 0x0a, 0x25, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x46, 0x69,
	0x74, 0x74, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c,
	0x10, 0x0d, 0x22, 0x88, 0x08, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x2f, 0x0a, 0x0e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04,
	0x18, 0x64, 0x52, 0x0c, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x64,
	0x12, 0x5c, 0x0a, 0x0a, 0x72, 0x65, 0x69, 0x6e, 0x76, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x44, 0x69, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x09, 0x72, 0x65, 0x69, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x75,
	0x6e, 0x69, 0x74, 0x73, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x53, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4e, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x06, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x03, 0x72, 0x74, 0x61,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x03, 0x72, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x6f, 0x61, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x6f, 0x61, 0x6c, 0x12,
	0x4e, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x70, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x53, 0x0a, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x75, 0x62,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x69,
	0x6e, 0x5f, 0x64, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x44,
	0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x75,
	0x74, 0x68, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x64,
	0x72, 0x61, 0x77, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69,
	0x73, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x41, 0x6c, 0x6c, 0x12, 0x5f, 0x0a, 0x12,
	0x73, 0x69, 0x70, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x53, 0x49, 0x50, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x73, 0x69, 0x70,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x46, 0x0a,
	0x12, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x8b, 0x02, 0x0a, 0x10, 0x53, 0x49, 0x50, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x52, 0x0a, 0x0f, 0x73, 0x69,
	0x70, 0x5f, 0x67, 0x72, 0x61, 0x6e, 0x75, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x53, 0x49, 0x50, 0x47, 0x72, 0x61, 0x6e, 0x75, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x52, 0x0e,
	0x73, 0x69, 0x70, 0x47, 0x72, 0x61, 0x6e, 0x75, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x12, 0x32,
	0x0a, 0x15, 0x66, 0x69, 0x74, 0x74, 0x74, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x66,
	0x69, 0x74, 0x74, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x41, 0x0a, 0x0e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74,
	0x68, 0x49, 0x64, 0x22, 0xfe, 0x04, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x3c, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x4d,
	0x0a, 0x14, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x12, 0x6e, 0x65, 0x78, 0x74, 0x4f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x65, 0x70, 0x22, 0xb4, 0x03,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x13,
	0x0a, 0x0f, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x10, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57,
	0x45, 0x44, 0x5f, 0x4d, 0x49, 0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x66, 0x12, 0x2f, 0x0a, 0x2b, 0x57, 0x49,
	0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x4c, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x53, 0x5f, 0x52, 0x45, 0x44, 0x45, 0x45, 0x4d, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x67, 0x12, 0x18, 0x0a, 0x10, 0x41,
	0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x10,
	0x68, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x54, 0x53,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x69, 0x12, 0x2a, 0x0a, 0x26, 0x46, 0x55, 0x4e,
	0x44, 0x5f, 0x55, 0x4e, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x42, 0x59, 0x5f,
	0x41, 0x4d, 0x43, 0x10, 0x6a, 0x12, 0x27, 0x0a, 0x23, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x4d, 0x49, 0x4e, 0x45, 0x45, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x41, 0x52, 0x45, 0x44, 0x10, 0x6b, 0x12, 0x21,
	0x0a, 0x1d, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42,
	0x4c, 0x45, 0x5f, 0x44, 0x4f, 0x42, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10,
	0x6c, 0x12, 0x2e, 0x0a, 0x2a, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49,
	0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x50, 0x4f,
	0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x6d, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x49, 0x50, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x41,
	0x49, 0x4e, 0x54, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x6e, 0x12, 0x25, 0x0a,
	0x21, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4c,
	0x49, 0x4e, 0x4b, 0x45, 0x44, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x41, 0x41, 0x44, 0x48, 0x41,
	0x41, 0x52, 0x10, 0x6f, 0x22, 0xb0, 0x02, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x61, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x54, 0x79, 0x70, 0x65, 0x22, 0x9b, 0x01, 0x0a, 0x0b, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x49, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x5f, 0x49, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x1b,
	0x0a, 0x17, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x58, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44,
	0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x22, 0x49, 0x0a, 0x09, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x3c, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x22, 0x97, 0x02, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x56, 0x0a,
	0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x1a, 0x65, 0x0a, 0x0b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x40, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1e, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x60, 0x0a, 0x1d,
	0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a,
	0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x54, 0x69, 0x6c, 0x6c, 0x22, 0x65,
	0x0a, 0x1e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xb6, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x11, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0f, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x42, 0x04, 0x0a, 0x02, 0x69, 0x64, 0x22, 0x95,
	0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x8e, 0x04, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x57, 0x69, 0x74, 0x68,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x56, 0x0a, 0x10, 0x6e,
	0x65, 0x77, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0e, 0x6e, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x5e, 0x0a, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x55, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x6b, 0x0a, 0x16, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x52, 0x14, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x10, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xa7, 0x01, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x57, 0x69, 0x74,
	0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x0d, 0x22, 0x6b, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x66,
	0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x88, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x22, 0x7e, 0x0a, 0x18, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x55, 0x72,
	0x6c, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x0d, 0x22, 0x9c, 0x01, 0x0a, 0x29, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x4d, 0x49, 0x53, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x03,
	0x72, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x52, 0x03, 0x72, 0x74, 0x61, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x22, 0x71, 0x0a, 0x2a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x4d, 0x49, 0x53, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x0d, 0x22, 0xe4, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x42, 0x79, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x41, 0x6e, 0x64, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f,
	0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0c, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x41, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0xa5, 0x05, 0x0a, 0x06, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x53, 0x0a, 0x0c, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x21,
	0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x56, 0x61,
	0x6c, 0x12, 0x1f, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x12, 0x37, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x48, 0x00, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x56, 0x61, 0x6c, 0x12, 0x58, 0x0a, 0x10, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x56, 0x61, 0x6c, 0x12, 0x52, 0x0a, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x12, 0x58, 0x0a, 0x10, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x48, 0x00, 0x52, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x56, 0x61, 0x6c, 0x22, 0x5d, 0x0a, 0x0a, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10,
	0x47, 0x52, 0x45, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x4f, 0x52, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c,
	0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x4d, 0x41, 0x4c, 0x4c, 0x45, 0x52, 0x5f, 0x4f, 0x52,
	0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x47, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x52, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x4d, 0x41, 0x4c, 0x4c, 0x45, 0x52,
	0x10, 0x04, 0x42, 0x0e, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0xd2, 0x02, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x42, 0x79, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x41, 0x6e, 0x64, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x3b, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x3e, 0x0a, 0x06,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12,
	0x46, 0x0a, 0x0b, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x52, 0x0a, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x51, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x57, 0x69,
	0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x13, 0x0a, 0x05, 0x6d, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x66, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x99, 0x05, 0x0a, 0x20, 0x47,
	0x65, 0x74, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x73,
	0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x19, 0x6d,
	0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61,
	0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x17, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x57, 0x69, 0x74, 0x68, 0x64,
	0x72, 0x61, 0x77, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x19, 0x6d,
	0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61,
	0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x17, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x57, 0x69, 0x74, 0x68, 0x64,
	0x72, 0x61, 0x77, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x56, 0x0a, 0x1d, 0x69,
	0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x64,
	0x72, 0x61, 0x77, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x1b, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x61, 0x6c, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0c, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x5f, 0x6c,
	0x69, 0x6b, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x4c, 0x69, 0x6b, 0x65, 0x12, 0x46, 0x0a, 0x0b, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x12, 0x43, 0x0a, 0x13, 0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x12, 0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x62, 0x6c,
	0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xbd, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2c,
	0x0a, 0x11, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0f, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x42, 0x04, 0x0a, 0x02, 0x69, 0x64, 0x22, 0xa5, 0x05, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75,
	0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x05,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x46, 0x0a, 0x0b, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f,
	0x66, 0x75, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e,
	0x64, 0x52, 0x0a, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x6d, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x6d, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x66, 0x0a, 0x15, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x13, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x12, 0x5f, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x74, 0x72, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x74, 0x72, 0x52, 0x65,
	0x66, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x45, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x6e,
	0x0a, 0x17, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x15, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x1e,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x9f,
	0x01, 0x0a, 0x11, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x4f, 0x0a, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x22, 0xcd, 0x02, 0x0a, 0x1f, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x6e, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x2d, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x70,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x04,
	0x22, 0xb5, 0x03, 0x0a, 0x25, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5b, 0x0a, 0x10, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x84, 0x01, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x5a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x61, 0x70, 0x12, 0x27,
	0x0a, 0x03, 0x72, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x52, 0x03, 0x72, 0x74, 0x61, 0x1a, 0x7f, 0x0a, 0x13, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x52, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xfa, 0x02, 0x0a, 0x26, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x84, 0x01, 0x0a, 0x0f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a,
	0x83, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x56, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xfb, 0x01, 0x0a, 0x1a, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x69, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x57, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x41, 0x49,
	0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x49,
	0x44, 0x10, 0x03, 0x22, 0xbc, 0x02, 0x0a, 0x26, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f,
	0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x91,
	0x01, 0x0a, 0x14, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12,
	0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x1a, 0x7e, 0x0a, 0x17, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x4d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xf6, 0x02, 0x0a, 0x27, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x85, 0x01, 0x0a, 0x0f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x7e, 0x0a, 0x13, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x51, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x6a, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1e, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x89, 0x01, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x42, 0x75, 0x79, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x13, 0x0a, 0x05, 0x6d, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x66, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x74, 0x52,
	0x75, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x74,
	0x52, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x69, 0x70, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x73, 0x69, 0x70,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0xba, 0x06, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x42,
	0x75, 0x79, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a, 0x17, 0x6d, 0x69,
	0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x15, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x17, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75,
	0x6d, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x15, 0x6d, 0x61, 0x78,
	0x69, 0x6d, 0x75, 0x6d, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x52, 0x0a, 0x1b, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x6c, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x19, 0x69, 0x6e, 0x63,
	0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x19, 0x70, 0x6f, 0x73, 0x73, 0x69, 0x62,
	0x6c, 0x65, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x17, 0x70,
	0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x55, 0x0a, 0x0e, 0x70, 0x6f, 0x73, 0x73, 0x69, 0x62,
	0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0d,
	0x70, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x56, 0x0a,
	0x0d, 0x70, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x69, 0x70, 0x44, 0x61, 0x79, 0x48, 0x00, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x44, 0x61, 0x79, 0x73, 0x12, 0x53, 0x0a, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x73,
	0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5f, 0x0a, 0x17, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x70, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x69, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x41, 0x69, 0x70, 0x46, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x52, 0x15, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x53, 0x69, 0x70,
	0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x22, 0x3f, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x1f, 0x0a, 0x1b, 0x53,
	0x49, 0x50, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x10, 0x65, 0x42, 0x16, 0x0a, 0x14,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61,
	0x69, 0x6e, 0x74, 0x73, 0x22, 0x2a, 0x0a, 0x0b, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x49, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x5f, 0x61, 0x72, 0x72, 0x61, 0x79,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x41, 0x72, 0x72, 0x61, 0x79,
	0x22, 0x47, 0x0a, 0x0e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x69, 0x70, 0x44,
	0x61, 0x79, 0x12, 0x35, 0x0a, 0x0a, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x61, 0x72, 0x72, 0x61, 0x79,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x09,
	0x64, 0x61, 0x79, 0x73, 0x41, 0x72, 0x72, 0x61, 0x79, 0x22, 0x67, 0x0a, 0x1f, 0x47, 0x65, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4d, 0x49,
	0x53, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x03,
	0x72, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x52, 0x03, 0x72, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0xa7, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x49, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4d, 0x49, 0x53, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x06,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x22, 0x1e, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x53, 0x0a, 0x1f,
	0x47, 0x65, 0x74, 0x4d, 0x46, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6d, 0x66,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x66, 0x49, 0x64,
	0x73, 0x22, 0x2d, 0x0a, 0x0f, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x22, 0xcf, 0x02, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4d, 0x46, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x74, 0x0a, 0x0b, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x52, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x46, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0b, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x1a, 0x70, 0x0a, 0x10, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x46, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c,
	0x10, 0x0d, 0x22, 0x32, 0x0a, 0x15, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4b, 0x59, 0x43, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x9a, 0x01, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x4b, 0x59, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x6b, 0x79, 0x63,
	0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x65,
	0x65, 0x64, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x69, 0x73, 0x4b, 0x79,
	0x63, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x65,
	0x64, 0x65, 0x64, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x0d, 0x22, 0x88, 0x02, 0x0a, 0x11, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x5e, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x46,
	0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x22, 0xd5,
	0x05, 0x0a, 0x17, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x24, 0x0a, 0x03, 0x6e, 0x61, 0x76, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x03, 0x6e, 0x61, 0x76, 0x12, 0x27, 0x0a, 0x0f, 0x75, 0x6e, 0x69, 0x74, 0x73,
	0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0e, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x49, 0x64, 0x12, 0x7b, 0x0a, 0x1c, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x19, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x34, 0x0a, 0x16, 0x72, 0x74, 0x61, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x72, 0x74, 0x61, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x43, 0x0a, 0x14, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x6c,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x12, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x03, 0x61, 0x6d, 0x63,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75,
	0x6e, 0x64, 0x2e, 0x41, 0x6d, 0x63, 0x52, 0x03, 0x61, 0x6d, 0x63, 0x12, 0x49, 0x0a, 0x0a, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x08, 0x70, 0x6f,
	0x73, 0x74, 0x44, 0x61, 0x74, 0x65, 0x22, 0xcf, 0x01, 0x0a, 0x29, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x76, 0x0a, 0x1a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x18,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xef, 0x02, 0x0a, 0x2a, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x85, 0x01, 0x0a,
	0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x1a, 0x74, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x48, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xd2, 0x03, 0x0a, 0x31, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x76, 0x0a, 0x1a,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x18, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x27, 0x0a, 0x03, 0x72, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x03, 0x72, 0x74, 0x61, 0x12, 0x30, 0x0a,
	0x03, 0x61, 0x6d, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x41, 0x6d, 0x63, 0x52, 0x03, 0x61, 0x6d, 0x63, 0x12,
	0x49, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x0e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x22,
	0xff, 0x02, 0x0a, 0x32, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x8d, 0x01, 0x0a, 0x0e,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x66, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x74, 0x0a, 0x12, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x48, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x0d, 0x22, 0x79, 0x0a, 0x32, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x1e, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x7a, 0x0a, 0x33,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x5c, 0x0a, 0x1c, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0xea, 0x02, 0x0a, 0x1d, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x61, 0x0a,
	0x12, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x45, 0x6c, 0x69, 0x67,
	0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x65,
	0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x67, 0x0a, 0x14, 0x69, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x49, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x52, 0x13, 0x69, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x09, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x6e, 0x65, 0x78, 0x74, 0x53,
	0x74, 0x65, 0x70, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x0d, 0x22, 0xd4, 0x01, 0x0a, 0x24, 0x4d, 0x6f, 0x63, 0x6b, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x4d, 0x49, 0x53, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x03, 0x72, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x03, 0x72,
	0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12,
	0x49, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x6c, 0x0a, 0x25, 0x4d, 0x6f,
	0x63, 0x6b, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4d, 0x49, 0x53, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x7b, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x64, 0x0a, 0x14, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x12, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xc2, 0x02, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x6f, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x1a, 0x74, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x48, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x98, 0x01, 0x0a, 0x11, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x3c, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x45,
	0x0a, 0x05, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x05,
	0x6d, 0x61, 0x73, 0x6b, 0x73, 0x22, 0x80, 0x02, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x6e, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x49, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x50, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x22, 0xed, 0x01, 0x0a, 0x22, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x70, 0x0a, 0x18, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x16, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x12, 0x55, 0x0a, 0x05, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x41, 0x53,
	0x4b, 0x52, 0x05, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x22, 0xf1, 0x02, 0x0a, 0x23, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x7e, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x57, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x84, 0x01, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x58,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1e, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xbc, 0x05, 0x0a,
	0x22, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x84, 0x01, 0x0a, 0x0f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x69,
	0x6e, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x0d, 0x73, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x49, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x86, 0x01, 0x0a, 0x10, 0x73,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x0e, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x75, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x1a,
	0xd9, 0x02, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x74, 0x0a, 0x19,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x17, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x27, 0x0a, 0x03, 0x72, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x03, 0x72, 0x74, 0x61, 0x12, 0x49, 0x0a, 0x0a, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x22, 0xc0, 0x02, 0x0a, 0x23,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x7e, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x57, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x74, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x48, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xdd,
	0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4d, 0x69, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x55, 0x0a, 0x0e, 0x7a, 0x69, 0x70, 0x5f, 0x70,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x5a, 0x69, 0x70, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52,
	0x0d, 0x7a, 0x69, 0x70, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x22, 0xb9,
	0x05, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x69, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5a, 0x0a, 0x09, 0x61,
	0x6d, 0x63, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x69, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x6d, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x08, 0x61,
	0x6d, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x59, 0x0a, 0x09, 0x72, 0x74, 0x61, 0x5f, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x69, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x52, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x08, 0x72, 0x74, 0x61, 0x46, 0x69, 0x6c,
	0x65, 0x73, 0x1a, 0xa6, 0x02, 0x0a, 0x08, 0x41, 0x6d, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12,
	0x30, 0x0a, 0x03, 0x61, 0x6d, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x41, 0x6d, 0x63, 0x52, 0x03, 0x61, 0x6d,
	0x63, 0x12, 0x6d, 0x0a, 0x0d, 0x61, 0x6d, 0x63, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x69,
	0x73, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41,
	0x6d, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x2e, 0x41, 0x6d, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x0b, 0x61, 0x6d, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x1a, 0x79, 0x0a, 0x0b, 0x41, 0x6d, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x4e, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x66,
	0x69, 0x6c, 0x65, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x46, 0x69, 0x6c,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x1a, 0x6e, 0x0a, 0x07, 0x52,
	0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x03, 0x72, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x03, 0x72, 0x74, 0x61, 0x12,
	0x17, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x7a, 0x69, 0x70, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x7a, 0x69, 0x70, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x7a, 0x69, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x2d, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xbc, 0x05, 0x0a, 0x22, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x84, 0x01, 0x0a, 0x0f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x6e, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x0d, 0x73, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x49, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x86, 0x01, 0x0a, 0x10, 0x73, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x5c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x52, 0x0e, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x75, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x1a, 0xd9, 0x02,
	0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x74, 0x0a, 0x19, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x17, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x27, 0x0a, 0x03, 0x72, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x52, 0x03, 0x72, 0x74, 0x61, 0x12, 0x49, 0x0a, 0x0a, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x75,
	0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x22, 0xc0, 0x02, 0x0a, 0x23, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x7e, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x57,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x74, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x48,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0x87, 0x01, 0x0a,
	0x0f, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d,
	0x61, 0x73, 0x6b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10,
	0x01, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0x05, 0x2a, 0xa1, 0x01, 0x0a, 0x0f, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x27, 0x0a, 0x23,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x55, 0x4d,
	0x42, 0x45, 0x52, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x2a, 0x6c, 0x0a, 0x13, 0x49, 0x6e,
	0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x24, 0x0a, 0x20, 0x49, 0x4e, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49,
	0x54, 0x59, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x4f, 0x42, 0x5f, 0x4d,
	0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x41, 0x44,
	0x48, 0x41, 0x41, 0x52, 0x5f, 0x50, 0x4f, 0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x57, 0x0a, 0x11, 0x45, 0x6c, 0x69, 0x67,
	0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a,
	0x1e, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12,
	0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x10,
	0x02, 0x2a, 0x3d, 0x0a, 0x0d, 0x5a, 0x69, 0x70, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x5a, 0x49, 0x50, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x5f,
	0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x5f, 0x52, 0x54, 0x41, 0x10, 0x01,
	0x32, 0xeb, 0x23, 0x0a, 0x0c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x12, 0x7a, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x74, 0x0a,
	0x09, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x9b, 0x01, 0x0a, 0x16, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x12, 0x3e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x66, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x6f,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x26, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xbf, 0x01, 0x0a, 0x22, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x12, 0x4a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x08, 0x47,
	0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xa7,
	0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x57, 0x69, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x42, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x57, 0x69, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x43, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x57, 0x69, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9e, 0x01, 0x0a, 0x17, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8a, 0x01, 0x0a, 0x11, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12,
	0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xbf, 0x01, 0x0a, 0x22, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4d, 0x49, 0x53, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4d, 0x49, 0x53,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4d, 0x49, 0x53, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xaa, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x41, 0x6e,
	0x64, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x44, 0x12, 0x43, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x41, 0x6e, 0x64, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x75, 0x6e, 0x64, 0x49,
	0x44, 0x41, 0x6e, 0x64, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xa1, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x57, 0x69, 0x74,
	0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e,
	0x74, 0x73, 0x12, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77,
	0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72,
	0x61, 0x77, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x37, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75,
	0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0xb3, 0x01, 0x0a, 0x1e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x46, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x72, 0x6f, 0x6d,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xb6, 0x01, 0x0a, 0x1f, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x47, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x6a, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x48, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f,
	0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x8c, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x42, 0x75, 0x79, 0x43, 0x6f, 0x6e, 0x73,
	0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x79,
	0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x79, 0x43, 0x6f, 0x6e, 0x73, 0x74,
	0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0xa1, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x49, 0x6e,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4d, 0x49, 0x53, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x40, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x4d, 0x49, 0x53, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x41, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x4d, 0x49, 0x53, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0xa1, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4d, 0x46, 0x49, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x12, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x46, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x46, 0x49, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x83, 0x01, 0x0a, 0x0e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x4b, 0x59, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x4b, 0x59, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4b, 0x59, 0x43, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xd7,
	0x01, 0x0a, 0x2a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x52, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x53, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x98, 0x01, 0x0a, 0x15, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x12, 0x3d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45,
	0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x6c,
	0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0xb0, 0x01, 0x0a, 0x1d, 0x4d, 0x6f, 0x63, 0x6b, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x4d, 0x49, 0x53, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x12, 0x45, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x63, 0x6b, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x4d, 0x49, 0x53, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4d,
	0x6f, 0x63, 0x6b, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4d, 0x49, 0x53, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x46, 0x69,
	0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x0c, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x34, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xaa, 0x01, 0x0a, 0x1b, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x43, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x44, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xb0, 0x01, 0x0a, 0x1d, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x46, 0x69, 0x74, 0x74, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x45, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x46, 0x69, 0x74, 0x74, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x46, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x46, 0x69, 0x74, 0x74, 0x74, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xaa, 0x01, 0x0a, 0x1b, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x43, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x44, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4d, 0x69,
	0x73, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75,
	0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x69, 0x73, 0x46,
	0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x4d, 0x69, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0xa8, 0x01, 0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x12, 0x43, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c,
	0x66, 0x75, 0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x70,
	0x0a, 0x36, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75,
	0x6e, 0x64, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_investment_mutualfund_order_service_proto_rawDescOnce sync.Once
	file_api_investment_mutualfund_order_service_proto_rawDescData = file_api_investment_mutualfund_order_service_proto_rawDesc
)

func file_api_investment_mutualfund_order_service_proto_rawDescGZIP() []byte {
	file_api_investment_mutualfund_order_service_proto_rawDescOnce.Do(func() {
		file_api_investment_mutualfund_order_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_investment_mutualfund_order_service_proto_rawDescData)
	})
	return file_api_investment_mutualfund_order_service_proto_rawDescData
}

var file_api_investment_mutualfund_order_service_proto_enumTypes = make([]protoimpl.EnumInfo, 38)
var file_api_investment_mutualfund_order_service_proto_msgTypes = make([]protoimpl.MessageInfo, 87)
var file_api_investment_mutualfund_order_service_proto_goTypes = []interface{}{
	(FilterFieldMask)(0),                                            // 0: api.investment.mutualfund.order.FilterFieldMask
	(OrderIdentifier)(0),                                            // 1: api.investment.mutualfund.order.OrderIdentifier
	(IneligibilityReason)(0),                                        // 2: api.investment.mutualfund.order.IneligibilityReason
	(EligibilityStatus)(0),                                          // 3: api.investment.mutualfund.order.EligibilityStatus
	(ZipPreference)(0),                                              // 4: api.investment.mutualfund.order.ZipPreference
	(HandleFitttSubscriptionUpdateResponse_Status)(0),               // 5: api.investment.mutualfund.order.HandleFitttSubscriptionUpdateResponse.Status
	(CreateOrderResponse_Status)(0),                                 // 6: api.investment.mutualfund.order.CreateOrderResponse.Status
	(GetOrdersRequest_OrderIdType)(0),                               // 7: api.investment.mutualfund.order.GetOrdersRequest.OrderIdType
	(GetOrdersResponse_Status)(0),                                   // 8: api.investment.mutualfund.order.GetOrdersResponse.Status
	(TriggerOrderProcessingResponse_Status)(0),                      // 9: api.investment.mutualfund.order.TriggerOrderProcessingResponse.Status
	(GetOrderResponse_Status)(0),                                    // 10: api.investment.mutualfund.order.GetOrderResponse.Status
	(UpdateOrderStatusWithCheckResponse_Status)(0),                  // 11: api.investment.mutualfund.order.UpdateOrderStatusWithCheckResponse.Status
	(CreateOrUpdateFileStateResponse_Status)(0),                     // 12: api.investment.mutualfund.order.CreateOrUpdateFileStateResponse.Status
	(UploadVendorFileResponse_Status)(0),                            // 13: api.investment.mutualfund.order.UploadVendorFileResponse.Status
	(ProcessCreditMISReportConfirmationResponse_Status)(0),          // 14: api.investment.mutualfund.order.ProcessCreditMISReportConfirmationResponse.Status
	(Filter_Comparator)(0),                                          // 15: api.investment.mutualfund.order.Filter.Comparator
	(GetOrdersByFundIDAndActorIDResponse_Status)(0),                 // 16: api.investment.mutualfund.order.GetOrdersByFundIDAndActorIDResponse.Status
	(GetWithdrawalConstraintsResponse_Status)(0),                    // 17: api.investment.mutualfund.order.GetWithdrawalConstraintsResponse.Status
	(GetOrderDetailsResponse_Status)(0),                             // 18: api.investment.mutualfund.order.GetOrderDetailsResponse.Status
	(PaymentConfirmationUpdateResult_Status)(0),                     // 19: api.investment.mutualfund.order.PaymentConfirmationUpdateResult.Status
	(ProcessPaymentCreditFromVendorResponse_Status)(0),              // 20: api.investment.mutualfund.order.ProcessPaymentCreditFromVendorResponse.Status
	(OrderRejectionUpdateResult_Status)(0),                          // 21: api.investment.mutualfund.order.OrderRejectionUpdateResult.Status
	(ProcessOrderRejectionFromVendorResponse_Status)(0),             // 22: api.investment.mutualfund.order.ProcessOrderRejectionFromVendorResponse.Status
	(GetBuyConstraintsResponse_Status)(0),                           // 23: api.investment.mutualfund.order.GetBuyConstraintsResponse.Status
	(GetOrdersInCreditMISFileResponse_Status)(0),                    // 24: api.investment.mutualfund.order.GetOrdersInCreditMISFileResponse.Status
	(GetMFInvestmentsForActorResponse_Status)(0),                    // 25: api.investment.mutualfund.order.GetMFInvestmentsForActorResponse.Status
	(CheckKYCStatusResponse_Status)(0),                              // 26: api.investment.mutualfund.order.CheckKYCStatusResponse.Status
	(OrderUpdateResult_Status)(0),                                   // 27: api.investment.mutualfund.order.OrderUpdateResult.Status
	(ProcessOrderConfirmationFromVendorResponse_Status)(0),          // 28: api.investment.mutualfund.order.ProcessOrderConfirmationFromVendorResponse.Status
	(ProcessExternalOrderConfirmationFromVendorResponse_Status)(0),  // 29: api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorResponse.Status
	(ProcessExternalOrderPaymentCreditFromVendorRequest_Status)(0),  // 30: api.investment.mutualfund.order.ProcessExternalOrderPaymentCreditFromVendorRequest.Status
	(ProcessExternalOrderPaymentCreditFromVendorResponse_Status)(0), // 31: api.investment.mutualfund.order.ProcessExternalOrderPaymentCreditFromVendorResponse.Status
	(CheckOrderEligibilityResponse_Status)(0),                       // 32: api.investment.mutualfund.order.CheckOrderEligibilityResponse.Status
	(MockCreditMISReportProcessingResponse_Status)(0),               // 33: api.investment.mutualfund.order.MockCreditMISReportProcessingResponse.Status
	(UpdateOrdersResponse_Status)(0),                                // 34: api.investment.mutualfund.order.UpdateOrdersResponse.Status
	(UpdateOrderConfirmationInfoResult_Status)(0),                   // 35: api.investment.mutualfund.order.UpdateOrderConfirmationInfoResult.Status
	(UpdateOrderConfirmationInfoResponse_Status)(0),                 // 36: api.investment.mutualfund.order.UpdateOrderConfirmationInfoResponse.Status
	(GetMisFilesResponse_Status)(0),                                 // 37: api.investment.mutualfund.order.GetMisFilesResponse.Status
	(*HandleFitttSubscriptionUpdateRequest)(nil),                    // 38: api.investment.mutualfund.order.HandleFitttSubscriptionUpdateRequest
	(*HandleFitttSubscriptionUpdateResponse)(nil),                   // 39: api.investment.mutualfund.order.HandleFitttSubscriptionUpdateResponse
	(*CreateOrderRequest)(nil),                                      // 40: api.investment.mutualfund.order.CreateOrderRequest
	(*PaymentRequestInfo)(nil),                                      // 41: api.investment.mutualfund.order.PaymentRequestInfo
	(*SIPExecutionInfo)(nil),                                        // 42: api.investment.mutualfund.order.SIPExecutionInfo
	(*CreateOrderResponse)(nil),                                     // 43: api.investment.mutualfund.order.CreateOrderResponse
	(*GetOrdersRequest)(nil),                                        // 44: api.investment.mutualfund.order.GetOrdersRequest
	(*OrderData)(nil),                                               // 45: api.investment.mutualfund.order.OrderData
	(*GetOrdersResponse)(nil),                                       // 46: api.investment.mutualfund.order.GetOrdersResponse
	(*TriggerOrderProcessingRequest)(nil),                           // 47: api.investment.mutualfund.order.TriggerOrderProcessingRequest
	(*TriggerOrderProcessingResponse)(nil),                          // 48: api.investment.mutualfund.order.TriggerOrderProcessingResponse
	(*GetOrderRequest)(nil),                                         // 49: api.investment.mutualfund.order.GetOrderRequest
	(*GetOrderResponse)(nil),                                        // 50: api.investment.mutualfund.order.GetOrderResponse
	(*UpdateOrderStatusWithCheckRequest)(nil),                       // 51: api.investment.mutualfund.order.UpdateOrderStatusWithCheckRequest
	(*UpdateOrderStatusWithCheckResponse)(nil),                      // 52: api.investment.mutualfund.order.UpdateOrderStatusWithCheckResponse
	(*CreateOrUpdateFileStateRequest)(nil),                          // 53: api.investment.mutualfund.order.CreateOrUpdateFileStateRequest
	(*CreateOrUpdateFileStateResponse)(nil),                         // 54: api.investment.mutualfund.order.CreateOrUpdateFileStateResponse
	(*UploadVendorFileRequest)(nil),                                 // 55: api.investment.mutualfund.order.UploadVendorFileRequest
	(*UploadVendorFileResponse)(nil),                                // 56: api.investment.mutualfund.order.UploadVendorFileResponse
	(*ProcessCreditMISReportConfirmationRequest)(nil),               // 57: api.investment.mutualfund.order.ProcessCreditMISReportConfirmationRequest
	(*ProcessCreditMISReportConfirmationResponse)(nil),              // 58: api.investment.mutualfund.order.ProcessCreditMISReportConfirmationResponse
	(*GetOrdersByFundIDAndActorIDRequest)(nil),                      // 59: api.investment.mutualfund.order.GetOrdersByFundIDAndActorIDRequest
	(*Filter)(nil), // 60: api.investment.mutualfund.order.Filter
	(*GetOrdersByFundIDAndActorIDResponse)(nil),                 // 61: api.investment.mutualfund.order.GetOrdersByFundIDAndActorIDResponse
	(*GetWithdrawalConstraintsRequest)(nil),                     // 62: api.investment.mutualfund.order.GetWithdrawalConstraintsRequest
	(*GetWithdrawalConstraintsResponse)(nil),                    // 63: api.investment.mutualfund.order.GetWithdrawalConstraintsResponse
	(*GetOrderDetailsRequest)(nil),                              // 64: api.investment.mutualfund.order.GetOrderDetailsRequest
	(*GetOrderDetailsResponse)(nil),                             // 65: api.investment.mutualfund.order.GetOrderDetailsResponse
	(*OrderStatusUpdate)(nil),                                   // 66: api.investment.mutualfund.order.OrderStatusUpdate
	(*PaymentConfirmationUpdateResult)(nil),                     // 67: api.investment.mutualfund.order.PaymentConfirmationUpdateResult
	(*ProcessPaymentCreditFromVendorRequest)(nil),               // 68: api.investment.mutualfund.order.ProcessPaymentCreditFromVendorRequest
	(*ProcessPaymentCreditFromVendorResponse)(nil),              // 69: api.investment.mutualfund.order.ProcessPaymentCreditFromVendorResponse
	(*OrderRejectionUpdateResult)(nil),                          // 70: api.investment.mutualfund.order.OrderRejectionUpdateResult
	(*ProcessOrderRejectionFromVendorRequest)(nil),              // 71: api.investment.mutualfund.order.ProcessOrderRejectionFromVendorRequest
	(*ProcessOrderRejectionFromVendorResponse)(nil),             // 72: api.investment.mutualfund.order.ProcessOrderRejectionFromVendorResponse
	(*GetBuyConstraintsRequest)(nil),                            // 73: api.investment.mutualfund.order.GetBuyConstraintsRequest
	(*GetBuyConstraintsResponse)(nil),                           // 74: api.investment.mutualfund.order.GetBuyConstraintsResponse
	(*RepeatedInt)(nil),                                         // 75: api.investment.mutualfund.order.RepeatedInt
	(*RepeatedAipDay)(nil),                                      // 76: api.investment.mutualfund.order.RepeatedAipDay
	(*GetOrdersInCreditMISFileRequest)(nil),                     // 77: api.investment.mutualfund.order.GetOrdersInCreditMISFileRequest
	(*GetOrdersInCreditMISFileResponse)(nil),                    // 78: api.investment.mutualfund.order.GetOrdersInCreditMISFileResponse
	(*GetMFInvestmentsForActorRequest)(nil),                     // 79: api.investment.mutualfund.order.GetMFInvestmentsForActorRequest
	(*InvestmentsInfo)(nil),                                     // 80: api.investment.mutualfund.order.InvestmentsInfo
	(*GetMFInvestmentsForActorResponse)(nil),                    // 81: api.investment.mutualfund.order.GetMFInvestmentsForActorResponse
	(*CheckKYCStatusRequest)(nil),                               // 82: api.investment.mutualfund.order.CheckKYCStatusRequest
	(*CheckKYCStatusResponse)(nil),                              // 83: api.investment.mutualfund.order.CheckKYCStatusResponse
	(*OrderUpdateResult)(nil),                                   // 84: api.investment.mutualfund.order.OrderUpdateResult
	(*OrderConfirmationDetail)(nil),                             // 85: api.investment.mutualfund.order.OrderConfirmationDetail
	(*ProcessOrderConfirmationFromVendorRequest)(nil),           // 86: api.investment.mutualfund.order.ProcessOrderConfirmationFromVendorRequest
	(*ProcessOrderConfirmationFromVendorResponse)(nil),          // 87: api.investment.mutualfund.order.ProcessOrderConfirmationFromVendorResponse
	(*ProcessExternalOrderConfirmationFromVendorRequest)(nil),   // 88: api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorRequest
	(*ProcessExternalOrderConfirmationFromVendorResponse)(nil),  // 89: api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorResponse
	(*ProcessExternalOrderPaymentCreditFromVendorRequest)(nil),  // 90: api.investment.mutualfund.order.ProcessExternalOrderPaymentCreditFromVendorRequest
	(*ProcessExternalOrderPaymentCreditFromVendorResponse)(nil), // 91: api.investment.mutualfund.order.ProcessExternalOrderPaymentCreditFromVendorResponse
	(*CheckOrderEligibilityRequest)(nil),                        // 92: api.investment.mutualfund.order.CheckOrderEligibilityRequest
	(*CheckOrderEligibilityResponse)(nil),                       // 93: api.investment.mutualfund.order.CheckOrderEligibilityResponse
	(*MockCreditMISReportProcessingRequest)(nil),                // 94: api.investment.mutualfund.order.MockCreditMISReportProcessingRequest
	(*MockCreditMISReportProcessingResponse)(nil),               // 95: api.investment.mutualfund.order.MockCreditMISReportProcessingResponse
	(*UpdateOrdersRequest)(nil),                                 // 96: api.investment.mutualfund.order.UpdateOrdersRequest
	(*UpdateOrdersResponse)(nil),                                // 97: api.investment.mutualfund.order.UpdateOrdersResponse
	(*OrderUpdateDetail)(nil),                                   // 98: api.investment.mutualfund.order.OrderUpdateDetail
	(*UpdateOrderConfirmationInfoResult)(nil),                   // 99: api.investment.mutualfund.order.UpdateOrderConfirmationInfoResult
	(*UpdateOrderConfirmationInfoRequest)(nil),                  // 100: api.investment.mutualfund.order.UpdateOrderConfirmationInfoRequest
	(*UpdateOrderConfirmationInfoResponse)(nil),                 // 101: api.investment.mutualfund.order.UpdateOrderConfirmationInfoResponse
	(*ProcessInternalSwitchOrdersRequest)(nil),                  // 102: api.investment.mutualfund.order.ProcessInternalSwitchOrdersRequest
	(*ProcessInternalSwitchOrdersResponse)(nil),                 // 103: api.investment.mutualfund.order.ProcessInternalSwitchOrdersResponse
	(*GetMisFilesRequest)(nil),                                  // 104: api.investment.mutualfund.order.GetMisFilesRequest
	(*GetMisFilesResponse)(nil),                                 // 105: api.investment.mutualfund.order.GetMisFilesResponse
	(*ProcessExternalSwitchOrdersRequest)(nil),                  // 106: api.investment.mutualfund.order.ProcessExternalSwitchOrdersRequest
	(*ProcessExternalSwitchOrdersResponse)(nil),                 // 107: api.investment.mutualfund.order.ProcessExternalSwitchOrdersResponse
	nil, // 108: api.investment.mutualfund.order.GetOrdersResponse.OrdersEntry
	nil, // 109: api.investment.mutualfund.order.ProcessPaymentCreditFromVendorRequest.PaymentInfoMapEntry
	nil, // 110: api.investment.mutualfund.order.ProcessPaymentCreditFromVendorResponse.UpdatedResultsEntry
	nil, // 111: api.investment.mutualfund.order.ProcessOrderRejectionFromVendorRequest.RejectedOrdersInfoEntry
	nil, // 112: api.investment.mutualfund.order.ProcessOrderRejectionFromVendorResponse.UpdatedResultsEntry
	nil, // 113: api.investment.mutualfund.order.GetMFInvestmentsForActorResponse.InvestmentsEntry
	nil, // 114: api.investment.mutualfund.order.ProcessOrderConfirmationFromVendorResponse.UpdateResultsEntry
	nil, // 115: api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorResponse.UpdateResultsEntry
	nil, // 116: api.investment.mutualfund.order.UpdateOrdersResponse.UpdateResultsEntry
	nil, // 117: api.investment.mutualfund.order.UpdateOrderConfirmationInfoResponse.UpdateResultsEntry
	(*ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload)(nil), // 118: api.investment.mutualfund.order.ProcessInternalSwitchOrdersRequest.CreateSwitchOrderPayload
	nil,                                  // 119: api.investment.mutualfund.order.ProcessInternalSwitchOrdersResponse.UpdateResultsEntry
	(*GetMisFilesResponse_AmcFiles)(nil), // 120: api.investment.mutualfund.order.GetMisFilesResponse.AmcFiles
	(*GetMisFilesResponse_RtaFile)(nil),  // 121: api.investment.mutualfund.order.GetMisFilesResponse.RtaFile
	(*GetMisFilesResponse_AmcFiles_AmcFileData)(nil),                    // 122: api.investment.mutualfund.order.GetMisFilesResponse.AmcFiles.AmcFileData
	(*ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload)(nil), // 123: api.investment.mutualfund.order.ProcessExternalSwitchOrdersRequest.CreateSwitchOrderPayload
	nil,                      // 124: api.investment.mutualfund.order.ProcessExternalSwitchOrdersResponse.UpdateResultsEntry
	(OrderSubType)(0),        // 125: api.investment.mutualfund.order.OrderSubType
	(*money.Money)(nil),      // 126: google.type.Money
	(*date.Date)(nil),        // 127: google.type.Date
	(dayofweek.DayOfWeek)(0), // 128: google.type.DayOfWeek
	(*rpc.Status)(nil),       // 129: rpc.Status
	(mutualfund.DividendReinvestmentOptionType)(0), // 130: api.investment.mutualfund.DividendReinvestmentOptionType
	(OrderType)(0),                           // 131: api.investment.mutualfund.order.OrderType
	(OrderClient)(0),                         // 132: api.investment.mutualfund.order.OrderClient
	(PaymentMode)(0),                         // 133: api.investment.mutualfund.order.PaymentMode
	(vendorgateway.Vendor)(0),                // 134: vendorgateway.Vendor
	(mutualfund.SIPGranularity)(0),           // 135: api.investment.mutualfund.SIPGranularity
	(*timestamppb.Timestamp)(nil),            // 136: google.protobuf.Timestamp
	(*Order)(nil),                            // 137: api.investment.mutualfund.order.Order
	(*deeplink.Deeplink)(nil),                // 138: frontend.deeplink.Deeplink
	(OrderStatus)(0),                         // 139: api.investment.mutualfund.order.OrderStatus
	(FailureReason)(0),                       // 140: api.investment.mutualfund.order.FailureReason
	(PaymentFailureReason)(0),                // 141: api.investment.mutualfund.order.PaymentFailureReason
	(*FileState)(nil),                        // 142: api.investment.mutualfund.order.FileState
	(*rpc.PageContextRequest)(nil),           // 143: rpc.PageContextRequest
	(*rpc.PageContextResponse)(nil),          // 144: rpc.PageContextResponse
	(*mutualfund.MutualFund)(nil),            // 145: api.investment.mutualfund.MutualFund
	(mutualfund.AssetClass)(0),               // 146: api.investment.mutualfund.AssetClass
	(mutualfund.MutualFundCategoryName)(0),   // 147: api.investment.mutualfund.MutualFundCategoryName
	(payment_handler.PaymentStatus)(0),       // 148: api.investment.mutualfund.payment_handler.PaymentStatus
	(*OrderConfirmationInfo)(nil),            // 149: api.investment.mutualfund.order.OrderConfirmationInfo
	(mutualfund.AipFrequency)(0),             // 150: api.investment.mutualfund.AipFrequency
	(*OrderConfirmationMetadata)(nil),        // 151: api.investment.mutualfund.order.OrderConfirmationMetadata
	(mutualfund.Amc)(0),                      // 152: api.investment.mutualfund.Amc
	(OrderFieldMask)(0),                      // 153: api.investment.mutualfund.order.OrderFieldMask
	(OrderConfirmationInfoFieldMASK)(0),      // 154: api.investment.mutualfund.order.OrderConfirmationInfoFieldMASK
	(*PaymentConfirmationMetadata)(nil),      // 155: api.investment.mutualfund.order.PaymentConfirmationMetadata
	(*OrderRejectionMetaData)(nil),           // 156: api.investment.mutualfund.order.OrderRejectionMetaData
	(*filegenerator.FileData)(nil),           // 157: api.investment.mutualfund.filegenerator.FileData
	(*domain.ProcessFulfilmentRequest)(nil),  // 158: order.domain.ProcessFulfilmentRequest
	(*GetFileStateDetailsRequest)(nil),       // 159: api.investment.mutualfund.order.GetFileStateDetailsRequest
	(*domain.ProcessFulfilmentResponse)(nil), // 160: order.domain.ProcessFulfilmentResponse
	(*GetFileStateDetailsResponse)(nil),      // 161: api.investment.mutualfund.order.GetFileStateDetailsResponse
}
var file_api_investment_mutualfund_order_service_proto_depIdxs = []int32{
	125, // 0: api.investment.mutualfund.order.HandleFitttSubscriptionUpdateRequest.order_sub_type:type_name -> api.investment.mutualfund.order.OrderSubType
	126, // 1: api.investment.mutualfund.order.HandleFitttSubscriptionUpdateRequest.updated_amount:type_name -> google.type.Money
	127, // 2: api.investment.mutualfund.order.HandleFitttSubscriptionUpdateRequest.sip_date:type_name -> google.type.Date
	128, // 3: api.investment.mutualfund.order.HandleFitttSubscriptionUpdateRequest.sip_day:type_name -> google.type.DayOfWeek
	129, // 4: api.investment.mutualfund.order.HandleFitttSubscriptionUpdateResponse.status:type_name -> rpc.Status
	130, // 5: api.investment.mutualfund.order.CreateOrderRequest.reinv_type:type_name -> api.investment.mutualfund.DividendReinvestmentOptionType
	126, // 6: api.investment.mutualfund.order.CreateOrderRequest.amount:type_name -> google.type.Money
	131, // 7: api.investment.mutualfund.order.CreateOrderRequest.order_type:type_name -> api.investment.mutualfund.order.OrderType
	132, // 8: api.investment.mutualfund.order.CreateOrderRequest.client:type_name -> api.investment.mutualfund.order.OrderClient
	133, // 9: api.investment.mutualfund.order.CreateOrderRequest.payment_mode:type_name -> api.investment.mutualfund.order.PaymentMode
	134, // 10: api.investment.mutualfund.order.CreateOrderRequest.rta:type_name -> vendorgateway.Vendor
	41,  // 11: api.investment.mutualfund.order.CreateOrderRequest.pay_info:type_name -> api.investment.mutualfund.order.PaymentRequestInfo
	125, // 12: api.investment.mutualfund.order.CreateOrderRequest.order_sub_type:type_name -> api.investment.mutualfund.order.OrderSubType
	42,  // 13: api.investment.mutualfund.order.CreateOrderRequest.sip_execution_info:type_name -> api.investment.mutualfund.order.SIPExecutionInfo
	135, // 14: api.investment.mutualfund.order.SIPExecutionInfo.sip_granularity:type_name -> api.investment.mutualfund.SIPGranularity
	136, // 15: api.investment.mutualfund.order.SIPExecutionInfo.execution_date:type_name -> google.protobuf.Timestamp
	129, // 16: api.investment.mutualfund.order.CreateOrderResponse.status:type_name -> rpc.Status
	137, // 17: api.investment.mutualfund.order.CreateOrderResponse.order:type_name -> api.investment.mutualfund.order.Order
	138, // 18: api.investment.mutualfund.order.CreateOrderResponse.next_onboarding_step:type_name -> frontend.deeplink.Deeplink
	7,   // 19: api.investment.mutualfund.order.GetOrdersRequest.order_id_type:type_name -> api.investment.mutualfund.order.GetOrdersRequest.OrderIdType
	137, // 20: api.investment.mutualfund.order.OrderData.order:type_name -> api.investment.mutualfund.order.Order
	129, // 21: api.investment.mutualfund.order.GetOrdersResponse.status:type_name -> rpc.Status
	108, // 22: api.investment.mutualfund.order.GetOrdersResponse.orders:type_name -> api.investment.mutualfund.order.GetOrdersResponse.OrdersEntry
	136, // 23: api.investment.mutualfund.order.TriggerOrderProcessingRequest.received_till:type_name -> google.protobuf.Timestamp
	129, // 24: api.investment.mutualfund.order.TriggerOrderProcessingResponse.status:type_name -> rpc.Status
	129, // 25: api.investment.mutualfund.order.GetOrderResponse.status:type_name -> rpc.Status
	137, // 26: api.investment.mutualfund.order.GetOrderResponse.order:type_name -> api.investment.mutualfund.order.Order
	139, // 27: api.investment.mutualfund.order.UpdateOrderStatusWithCheckRequest.new_order_status:type_name -> api.investment.mutualfund.order.OrderStatus
	139, // 28: api.investment.mutualfund.order.UpdateOrderStatusWithCheckRequest.current_order_status:type_name -> api.investment.mutualfund.order.OrderStatus
	140, // 29: api.investment.mutualfund.order.UpdateOrderStatusWithCheckRequest.failure_reason:type_name -> api.investment.mutualfund.order.FailureReason
	141, // 30: api.investment.mutualfund.order.UpdateOrderStatusWithCheckRequest.payment_failure_reason:type_name -> api.investment.mutualfund.order.PaymentFailureReason
	129, // 31: api.investment.mutualfund.order.UpdateOrderStatusWithCheckResponse.status:type_name -> rpc.Status
	137, // 32: api.investment.mutualfund.order.UpdateOrderStatusWithCheckResponse.order:type_name -> api.investment.mutualfund.order.Order
	142, // 33: api.investment.mutualfund.order.CreateOrUpdateFileStateRequest.file_state:type_name -> api.investment.mutualfund.order.FileState
	129, // 34: api.investment.mutualfund.order.CreateOrUpdateFileStateResponse.status:type_name -> rpc.Status
	134, // 35: api.investment.mutualfund.order.UploadVendorFileRequest.vendor:type_name -> vendorgateway.Vendor
	129, // 36: api.investment.mutualfund.order.UploadVendorFileResponse.status:type_name -> rpc.Status
	134, // 37: api.investment.mutualfund.order.ProcessCreditMISReportConfirmationRequest.rta:type_name -> vendorgateway.Vendor
	129, // 38: api.investment.mutualfund.order.ProcessCreditMISReportConfirmationResponse.status:type_name -> rpc.Status
	143, // 39: api.investment.mutualfund.order.GetOrdersByFundIDAndActorIDRequest.page_context:type_name -> rpc.PageContextRequest
	60,  // 40: api.investment.mutualfund.order.GetOrdersByFundIDAndActorIDRequest.filters:type_name -> api.investment.mutualfund.order.Filter
	15,  // 41: api.investment.mutualfund.order.Filter.comparator:type_name -> api.investment.mutualfund.order.Filter.Comparator
	0,   // 42: api.investment.mutualfund.order.Filter.filter_field:type_name -> api.investment.mutualfund.order.FilterFieldMask
	136, // 43: api.investment.mutualfund.order.Filter.time_val:type_name -> google.protobuf.Timestamp
	132, // 44: api.investment.mutualfund.order.Filter.order_client_val:type_name -> api.investment.mutualfund.order.OrderClient
	131, // 45: api.investment.mutualfund.order.Filter.order_type_val:type_name -> api.investment.mutualfund.order.OrderType
	139, // 46: api.investment.mutualfund.order.Filter.order_status_val:type_name -> api.investment.mutualfund.order.OrderStatus
	129, // 47: api.investment.mutualfund.order.GetOrdersByFundIDAndActorIDResponse.status:type_name -> rpc.Status
	144, // 48: api.investment.mutualfund.order.GetOrdersByFundIDAndActorIDResponse.page_context:type_name -> rpc.PageContextResponse
	137, // 49: api.investment.mutualfund.order.GetOrdersByFundIDAndActorIDResponse.orders:type_name -> api.investment.mutualfund.order.Order
	145, // 50: api.investment.mutualfund.order.GetOrdersByFundIDAndActorIDResponse.mutual_fund:type_name -> api.investment.mutualfund.MutualFund
	129, // 51: api.investment.mutualfund.order.GetWithdrawalConstraintsResponse.status:type_name -> rpc.Status
	126, // 52: api.investment.mutualfund.order.GetWithdrawalConstraintsResponse.total_amount:type_name -> google.type.Money
	126, // 53: api.investment.mutualfund.order.GetWithdrawalConstraintsResponse.minimum_withdrawal_amount:type_name -> google.type.Money
	126, // 54: api.investment.mutualfund.order.GetWithdrawalConstraintsResponse.maximum_withdrawal_amount:type_name -> google.type.Money
	126, // 55: api.investment.mutualfund.order.GetWithdrawalConstraintsResponse.incremental_withdrawal_amount:type_name -> google.type.Money
	146, // 56: api.investment.mutualfund.order.GetWithdrawalConstraintsResponse.asset_class:type_name -> api.investment.mutualfund.AssetClass
	126, // 57: api.investment.mutualfund.order.GetWithdrawalConstraintsResponse.withdrawable_amount:type_name -> google.type.Money
	147, // 58: api.investment.mutualfund.order.GetWithdrawalConstraintsResponse.category:type_name -> api.investment.mutualfund.MutualFundCategoryName
	129, // 59: api.investment.mutualfund.order.GetOrderDetailsResponse.status:type_name -> rpc.Status
	137, // 60: api.investment.mutualfund.order.GetOrderDetailsResponse.order:type_name -> api.investment.mutualfund.order.Order
	145, // 61: api.investment.mutualfund.order.GetOrderDetailsResponse.mutual_fund:type_name -> api.investment.mutualfund.MutualFund
	66,  // 62: api.investment.mutualfund.order.GetOrderDetailsResponse.order_status_timeline:type_name -> api.investment.mutualfund.order.OrderStatusUpdate
	148, // 63: api.investment.mutualfund.order.GetOrderDetailsResponse.payment_status:type_name -> api.investment.mutualfund.payment_handler.PaymentStatus
	136, // 64: api.investment.mutualfund.order.GetOrderDetailsResponse.transaction_time:type_name -> google.protobuf.Timestamp
	149, // 65: api.investment.mutualfund.order.GetOrderDetailsResponse.order_confirmation_info:type_name -> api.investment.mutualfund.order.OrderConfirmationInfo
	139, // 66: api.investment.mutualfund.order.OrderStatusUpdate.order_status:type_name -> api.investment.mutualfund.order.OrderStatus
	136, // 67: api.investment.mutualfund.order.OrderStatusUpdate.updated_at:type_name -> google.protobuf.Timestamp
	19,  // 68: api.investment.mutualfund.order.PaymentConfirmationUpdateResult.updated_status:type_name -> api.investment.mutualfund.order.PaymentConfirmationUpdateResult.Status
	1,   // 69: api.investment.mutualfund.order.ProcessPaymentCreditFromVendorRequest.order_identifier:type_name -> api.investment.mutualfund.order.OrderIdentifier
	109, // 70: api.investment.mutualfund.order.ProcessPaymentCreditFromVendorRequest.payment_info_map:type_name -> api.investment.mutualfund.order.ProcessPaymentCreditFromVendorRequest.PaymentInfoMapEntry
	134, // 71: api.investment.mutualfund.order.ProcessPaymentCreditFromVendorRequest.rta:type_name -> vendorgateway.Vendor
	129, // 72: api.investment.mutualfund.order.ProcessPaymentCreditFromVendorResponse.status:type_name -> rpc.Status
	110, // 73: api.investment.mutualfund.order.ProcessPaymentCreditFromVendorResponse.updated_results:type_name -> api.investment.mutualfund.order.ProcessPaymentCreditFromVendorResponse.UpdatedResultsEntry
	21,  // 74: api.investment.mutualfund.order.OrderRejectionUpdateResult.updated_status:type_name -> api.investment.mutualfund.order.OrderRejectionUpdateResult.Status
	111, // 75: api.investment.mutualfund.order.ProcessOrderRejectionFromVendorRequest.rejected_orders_info:type_name -> api.investment.mutualfund.order.ProcessOrderRejectionFromVendorRequest.RejectedOrdersInfoEntry
	129, // 76: api.investment.mutualfund.order.ProcessOrderRejectionFromVendorResponse.status:type_name -> rpc.Status
	112, // 77: api.investment.mutualfund.order.ProcessOrderRejectionFromVendorResponse.updated_results:type_name -> api.investment.mutualfund.order.ProcessOrderRejectionFromVendorResponse.UpdatedResultsEntry
	129, // 78: api.investment.mutualfund.order.GetBuyConstraintsResponse.status:type_name -> rpc.Status
	126, // 79: api.investment.mutualfund.order.GetBuyConstraintsResponse.minimum_purchase_amount:type_name -> google.type.Money
	126, // 80: api.investment.mutualfund.order.GetBuyConstraintsResponse.maximum_purchase_amount:type_name -> google.type.Money
	126, // 81: api.investment.mutualfund.order.GetBuyConstraintsResponse.incremental_purchase_amount:type_name -> google.type.Money
	126, // 82: api.investment.mutualfund.order.GetBuyConstraintsResponse.possible_purchase_amounts:type_name -> google.type.Money
	75,  // 83: api.investment.mutualfund.order.GetBuyConstraintsResponse.possible_dates:type_name -> api.investment.mutualfund.order.RepeatedInt
	76,  // 84: api.investment.mutualfund.order.GetBuyConstraintsResponse.possible_days:type_name -> api.investment.mutualfund.order.RepeatedAipDay
	125, // 85: api.investment.mutualfund.order.GetBuyConstraintsResponse.order_sub_type:type_name -> api.investment.mutualfund.order.OrderSubType
	150, // 86: api.investment.mutualfund.order.GetBuyConstraintsResponse.allowed_sip_frequencies:type_name -> api.investment.mutualfund.AipFrequency
	128, // 87: api.investment.mutualfund.order.RepeatedAipDay.days_array:type_name -> google.type.DayOfWeek
	134, // 88: api.investment.mutualfund.order.GetOrdersInCreditMISFileRequest.rta:type_name -> vendorgateway.Vendor
	129, // 89: api.investment.mutualfund.order.GetOrdersInCreditMISFileResponse.status:type_name -> rpc.Status
	137, // 90: api.investment.mutualfund.order.GetOrdersInCreditMISFileResponse.orders:type_name -> api.investment.mutualfund.order.Order
	129, // 91: api.investment.mutualfund.order.GetMFInvestmentsForActorResponse.status:type_name -> rpc.Status
	113, // 92: api.investment.mutualfund.order.GetMFInvestmentsForActorResponse.investments:type_name -> api.investment.mutualfund.order.GetMFInvestmentsForActorResponse.InvestmentsEntry
	129, // 93: api.investment.mutualfund.order.CheckKYCStatusResponse.status:type_name -> rpc.Status
	27,  // 94: api.investment.mutualfund.order.OrderUpdateResult.update_status:type_name -> api.investment.mutualfund.order.OrderUpdateResult.Status
	126, // 95: api.investment.mutualfund.order.OrderConfirmationDetail.amount:type_name -> google.type.Money
	126, // 96: api.investment.mutualfund.order.OrderConfirmationDetail.nav:type_name -> google.type.Money
	151, // 97: api.investment.mutualfund.order.OrderConfirmationDetail.order_confirmation_meta_data:type_name -> api.investment.mutualfund.order.OrderConfirmationMetadata
	127, // 98: api.investment.mutualfund.order.OrderConfirmationDetail.order_allocated_date:type_name -> google.type.Date
	152, // 99: api.investment.mutualfund.order.OrderConfirmationDetail.amc:type_name -> api.investment.mutualfund.Amc
	131, // 100: api.investment.mutualfund.order.OrderConfirmationDetail.order_type:type_name -> api.investment.mutualfund.order.OrderType
	127, // 101: api.investment.mutualfund.order.OrderConfirmationDetail.post_date:type_name -> google.type.Date
	85,  // 102: api.investment.mutualfund.order.ProcessOrderConfirmationFromVendorRequest.order_confirmation_details:type_name -> api.investment.mutualfund.order.OrderConfirmationDetail
	129, // 103: api.investment.mutualfund.order.ProcessOrderConfirmationFromVendorResponse.status:type_name -> rpc.Status
	114, // 104: api.investment.mutualfund.order.ProcessOrderConfirmationFromVendorResponse.update_results:type_name -> api.investment.mutualfund.order.ProcessOrderConfirmationFromVendorResponse.UpdateResultsEntry
	85,  // 105: api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorRequest.order_confirmation_details:type_name -> api.investment.mutualfund.order.OrderConfirmationDetail
	134, // 106: api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorRequest.rta:type_name -> vendorgateway.Vendor
	152, // 107: api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorRequest.amc:type_name -> api.investment.mutualfund.Amc
	131, // 108: api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorRequest.order_type:type_name -> api.investment.mutualfund.order.OrderType
	125, // 109: api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorRequest.order_sub_type:type_name -> api.investment.mutualfund.order.OrderSubType
	129, // 110: api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorResponse.status:type_name -> rpc.Status
	115, // 111: api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorResponse.update_results:type_name -> api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorResponse.UpdateResultsEntry
	129, // 112: api.investment.mutualfund.order.ProcessExternalOrderPaymentCreditFromVendorRequest.status:type_name -> rpc.Status
	129, // 113: api.investment.mutualfund.order.ProcessExternalOrderPaymentCreditFromVendorResponse.status:type_name -> rpc.Status
	137, // 114: api.investment.mutualfund.order.CheckOrderEligibilityRequest.order:type_name -> api.investment.mutualfund.order.Order
	129, // 115: api.investment.mutualfund.order.CheckOrderEligibilityResponse.status:type_name -> rpc.Status
	3,   // 116: api.investment.mutualfund.order.CheckOrderEligibilityResponse.eligibility_status:type_name -> api.investment.mutualfund.order.EligibilityStatus
	2,   // 117: api.investment.mutualfund.order.CheckOrderEligibilityResponse.ineligibility_reason:type_name -> api.investment.mutualfund.order.IneligibilityReason
	138, // 118: api.investment.mutualfund.order.CheckOrderEligibilityResponse.next_step:type_name -> frontend.deeplink.Deeplink
	134, // 119: api.investment.mutualfund.order.MockCreditMISReportProcessingRequest.rta:type_name -> vendorgateway.Vendor
	131, // 120: api.investment.mutualfund.order.MockCreditMISReportProcessingRequest.order_type:type_name -> api.investment.mutualfund.order.OrderType
	129, // 121: api.investment.mutualfund.order.MockCreditMISReportProcessingResponse.status:type_name -> rpc.Status
	98,  // 122: api.investment.mutualfund.order.UpdateOrdersRequest.order_update_details:type_name -> api.investment.mutualfund.order.OrderUpdateDetail
	129, // 123: api.investment.mutualfund.order.UpdateOrdersResponse.status:type_name -> rpc.Status
	116, // 124: api.investment.mutualfund.order.UpdateOrdersResponse.update_results:type_name -> api.investment.mutualfund.order.UpdateOrdersResponse.UpdateResultsEntry
	137, // 125: api.investment.mutualfund.order.OrderUpdateDetail.order:type_name -> api.investment.mutualfund.order.Order
	153, // 126: api.investment.mutualfund.order.OrderUpdateDetail.masks:type_name -> api.investment.mutualfund.order.OrderFieldMask
	35,  // 127: api.investment.mutualfund.order.UpdateOrderConfirmationInfoResult.update_status:type_name -> api.investment.mutualfund.order.UpdateOrderConfirmationInfoResult.Status
	149, // 128: api.investment.mutualfund.order.UpdateOrderConfirmationInfoRequest.order_confirmation_infos:type_name -> api.investment.mutualfund.order.OrderConfirmationInfo
	154, // 129: api.investment.mutualfund.order.UpdateOrderConfirmationInfoRequest.masks:type_name -> api.investment.mutualfund.order.OrderConfirmationInfoFieldMASK
	129, // 130: api.investment.mutualfund.order.UpdateOrderConfirmationInfoResponse.status:type_name -> rpc.Status
	117, // 131: api.investment.mutualfund.order.UpdateOrderConfirmationInfoResponse.update_results:type_name -> api.investment.mutualfund.order.UpdateOrderConfirmationInfoResponse.UpdateResultsEntry
	118, // 132: api.investment.mutualfund.order.ProcessInternalSwitchOrdersRequest.switch_in_order:type_name -> api.investment.mutualfund.order.ProcessInternalSwitchOrdersRequest.CreateSwitchOrderPayload
	118, // 133: api.investment.mutualfund.order.ProcessInternalSwitchOrdersRequest.switch_out_order:type_name -> api.investment.mutualfund.order.ProcessInternalSwitchOrdersRequest.CreateSwitchOrderPayload
	129, // 134: api.investment.mutualfund.order.ProcessInternalSwitchOrdersResponse.status:type_name -> rpc.Status
	119, // 135: api.investment.mutualfund.order.ProcessInternalSwitchOrdersResponse.update_results:type_name -> api.investment.mutualfund.order.ProcessInternalSwitchOrdersResponse.UpdateResultsEntry
	136, // 136: api.investment.mutualfund.order.GetMisFilesRequest.start_date:type_name -> google.protobuf.Timestamp
	136, // 137: api.investment.mutualfund.order.GetMisFilesRequest.end_date:type_name -> google.protobuf.Timestamp
	4,   // 138: api.investment.mutualfund.order.GetMisFilesRequest.zip_preference:type_name -> api.investment.mutualfund.order.ZipPreference
	129, // 139: api.investment.mutualfund.order.GetMisFilesResponse.status:type_name -> rpc.Status
	120, // 140: api.investment.mutualfund.order.GetMisFilesResponse.amc_files:type_name -> api.investment.mutualfund.order.GetMisFilesResponse.AmcFiles
	121, // 141: api.investment.mutualfund.order.GetMisFilesResponse.rta_files:type_name -> api.investment.mutualfund.order.GetMisFilesResponse.RtaFile
	123, // 142: api.investment.mutualfund.order.ProcessExternalSwitchOrdersRequest.switch_in_order:type_name -> api.investment.mutualfund.order.ProcessExternalSwitchOrdersRequest.CreateSwitchOrderPayload
	123, // 143: api.investment.mutualfund.order.ProcessExternalSwitchOrdersRequest.switch_out_order:type_name -> api.investment.mutualfund.order.ProcessExternalSwitchOrdersRequest.CreateSwitchOrderPayload
	129, // 144: api.investment.mutualfund.order.ProcessExternalSwitchOrdersResponse.status:type_name -> rpc.Status
	124, // 145: api.investment.mutualfund.order.ProcessExternalSwitchOrdersResponse.update_results:type_name -> api.investment.mutualfund.order.ProcessExternalSwitchOrdersResponse.UpdateResultsEntry
	45,  // 146: api.investment.mutualfund.order.GetOrdersResponse.OrdersEntry.value:type_name -> api.investment.mutualfund.order.OrderData
	155, // 147: api.investment.mutualfund.order.ProcessPaymentCreditFromVendorRequest.PaymentInfoMapEntry.value:type_name -> api.investment.mutualfund.order.PaymentConfirmationMetadata
	67,  // 148: api.investment.mutualfund.order.ProcessPaymentCreditFromVendorResponse.UpdatedResultsEntry.value:type_name -> api.investment.mutualfund.order.PaymentConfirmationUpdateResult
	156, // 149: api.investment.mutualfund.order.ProcessOrderRejectionFromVendorRequest.RejectedOrdersInfoEntry.value:type_name -> api.investment.mutualfund.order.OrderRejectionMetaData
	70,  // 150: api.investment.mutualfund.order.ProcessOrderRejectionFromVendorResponse.UpdatedResultsEntry.value:type_name -> api.investment.mutualfund.order.OrderRejectionUpdateResult
	80,  // 151: api.investment.mutualfund.order.GetMFInvestmentsForActorResponse.InvestmentsEntry.value:type_name -> api.investment.mutualfund.order.InvestmentsInfo
	84,  // 152: api.investment.mutualfund.order.ProcessOrderConfirmationFromVendorResponse.UpdateResultsEntry.value:type_name -> api.investment.mutualfund.order.OrderUpdateResult
	84,  // 153: api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorResponse.UpdateResultsEntry.value:type_name -> api.investment.mutualfund.order.OrderUpdateResult
	84,  // 154: api.investment.mutualfund.order.UpdateOrdersResponse.UpdateResultsEntry.value:type_name -> api.investment.mutualfund.order.OrderUpdateResult
	99,  // 155: api.investment.mutualfund.order.UpdateOrderConfirmationInfoResponse.UpdateResultsEntry.value:type_name -> api.investment.mutualfund.order.UpdateOrderConfirmationInfoResult
	85,  // 156: api.investment.mutualfund.order.ProcessInternalSwitchOrdersRequest.CreateSwitchOrderPayload.order_confirmation_detail:type_name -> api.investment.mutualfund.order.OrderConfirmationDetail
	134, // 157: api.investment.mutualfund.order.ProcessInternalSwitchOrdersRequest.CreateSwitchOrderPayload.rta:type_name -> vendorgateway.Vendor
	131, // 158: api.investment.mutualfund.order.ProcessInternalSwitchOrdersRequest.CreateSwitchOrderPayload.order_type:type_name -> api.investment.mutualfund.order.OrderType
	125, // 159: api.investment.mutualfund.order.ProcessInternalSwitchOrdersRequest.CreateSwitchOrderPayload.order_sub_type:type_name -> api.investment.mutualfund.order.OrderSubType
	84,  // 160: api.investment.mutualfund.order.ProcessInternalSwitchOrdersResponse.UpdateResultsEntry.value:type_name -> api.investment.mutualfund.order.OrderUpdateResult
	152, // 161: api.investment.mutualfund.order.GetMisFilesResponse.AmcFiles.amc:type_name -> api.investment.mutualfund.Amc
	122, // 162: api.investment.mutualfund.order.GetMisFilesResponse.AmcFiles.amc_file_data:type_name -> api.investment.mutualfund.order.GetMisFilesResponse.AmcFiles.AmcFileData
	134, // 163: api.investment.mutualfund.order.GetMisFilesResponse.RtaFile.rta:type_name -> vendorgateway.Vendor
	157, // 164: api.investment.mutualfund.order.GetMisFilesResponse.AmcFiles.AmcFileData.file_data:type_name -> api.investment.mutualfund.filegenerator.FileData
	85,  // 165: api.investment.mutualfund.order.ProcessExternalSwitchOrdersRequest.CreateSwitchOrderPayload.order_confirmation_detail:type_name -> api.investment.mutualfund.order.OrderConfirmationDetail
	134, // 166: api.investment.mutualfund.order.ProcessExternalSwitchOrdersRequest.CreateSwitchOrderPayload.rta:type_name -> vendorgateway.Vendor
	131, // 167: api.investment.mutualfund.order.ProcessExternalSwitchOrdersRequest.CreateSwitchOrderPayload.order_type:type_name -> api.investment.mutualfund.order.OrderType
	125, // 168: api.investment.mutualfund.order.ProcessExternalSwitchOrdersRequest.CreateSwitchOrderPayload.order_sub_type:type_name -> api.investment.mutualfund.order.OrderSubType
	84,  // 169: api.investment.mutualfund.order.ProcessExternalSwitchOrdersResponse.UpdateResultsEntry.value:type_name -> api.investment.mutualfund.order.OrderUpdateResult
	40,  // 170: api.investment.mutualfund.order.OrderManager.CreateOrder:input_type -> api.investment.mutualfund.order.CreateOrderRequest
	44,  // 171: api.investment.mutualfund.order.OrderManager.GetOrders:input_type -> api.investment.mutualfund.order.GetOrdersRequest
	47,  // 172: api.investment.mutualfund.order.OrderManager.TriggerOrderProcessing:input_type -> api.investment.mutualfund.order.TriggerOrderProcessingRequest
	158, // 173: api.investment.mutualfund.order.OrderManager.SendOrderToVendor:input_type -> order.domain.ProcessFulfilmentRequest
	86,  // 174: api.investment.mutualfund.order.OrderManager.ProcessOrderConfirmationFromVendor:input_type -> api.investment.mutualfund.order.ProcessOrderConfirmationFromVendorRequest
	49,  // 175: api.investment.mutualfund.order.OrderManager.GetOrder:input_type -> api.investment.mutualfund.order.GetOrderRequest
	51,  // 176: api.investment.mutualfund.order.OrderManager.UpdateOrderStatusWithCheck:input_type -> api.investment.mutualfund.order.UpdateOrderStatusWithCheckRequest
	53,  // 177: api.investment.mutualfund.order.OrderManager.CreateOrUpdateFileState:input_type -> api.investment.mutualfund.order.CreateOrUpdateFileStateRequest
	55,  // 178: api.investment.mutualfund.order.OrderManager.UploadVendorFiles:input_type -> api.investment.mutualfund.order.UploadVendorFileRequest
	57,  // 179: api.investment.mutualfund.order.OrderManager.ProcessCreditMISReportConfirmation:input_type -> api.investment.mutualfund.order.ProcessCreditMISReportConfirmationRequest
	59,  // 180: api.investment.mutualfund.order.OrderManager.GetOrdersByFundIDAndActorID:input_type -> api.investment.mutualfund.order.GetOrdersByFundIDAndActorIDRequest
	62,  // 181: api.investment.mutualfund.order.OrderManager.GetWithdrawalConstraints:input_type -> api.investment.mutualfund.order.GetWithdrawalConstraintsRequest
	64,  // 182: api.investment.mutualfund.order.OrderManager.GetOrderDetails:input_type -> api.investment.mutualfund.order.GetOrderDetailsRequest
	68,  // 183: api.investment.mutualfund.order.OrderManager.ProcessPaymentCreditFromVendor:input_type -> api.investment.mutualfund.order.ProcessPaymentCreditFromVendorRequest
	71,  // 184: api.investment.mutualfund.order.OrderManager.ProcessOrderRejectionFromVendor:input_type -> api.investment.mutualfund.order.ProcessOrderRejectionFromVendorRequest
	73,  // 185: api.investment.mutualfund.order.OrderManager.GetBuyConstraints:input_type -> api.investment.mutualfund.order.GetBuyConstraintsRequest
	77,  // 186: api.investment.mutualfund.order.OrderManager.GetOrdersInCreditMISFile:input_type -> api.investment.mutualfund.order.GetOrdersInCreditMISFileRequest
	79,  // 187: api.investment.mutualfund.order.OrderManager.GetMFInvestmentsForActor:input_type -> api.investment.mutualfund.order.GetMFInvestmentsForActorRequest
	82,  // 188: api.investment.mutualfund.order.OrderManager.CheckKYCStatus:input_type -> api.investment.mutualfund.order.CheckKYCStatusRequest
	88,  // 189: api.investment.mutualfund.order.OrderManager.ProcessExternalOrderConfirmationFromVendor:input_type -> api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorRequest
	92,  // 190: api.investment.mutualfund.order.OrderManager.CheckOrderEligibility:input_type -> api.investment.mutualfund.order.CheckOrderEligibilityRequest
	94,  // 191: api.investment.mutualfund.order.OrderManager.MockCreditMISReportProcessing:input_type -> api.investment.mutualfund.order.MockCreditMISReportProcessingRequest
	159, // 192: api.investment.mutualfund.order.OrderManager.GetFileStateDetails:input_type -> api.investment.mutualfund.order.GetFileStateDetailsRequest
	96,  // 193: api.investment.mutualfund.order.OrderManager.UpdateOrders:input_type -> api.investment.mutualfund.order.UpdateOrdersRequest
	100, // 194: api.investment.mutualfund.order.OrderManager.UpdateOrderConfirmationInfo:input_type -> api.investment.mutualfund.order.UpdateOrderConfirmationInfoRequest
	38,  // 195: api.investment.mutualfund.order.OrderManager.HandleFitttSubscriptionUpdate:input_type -> api.investment.mutualfund.order.HandleFitttSubscriptionUpdateRequest
	102, // 196: api.investment.mutualfund.order.OrderManager.ProcessInternalSwitchOrders:input_type -> api.investment.mutualfund.order.ProcessInternalSwitchOrdersRequest
	104, // 197: api.investment.mutualfund.order.OrderManager.GetMisFiles:input_type -> api.investment.mutualfund.order.GetMisFilesRequest
	106, // 198: api.investment.mutualfund.order.OrderManager.ProcessExternalSwitchOrders:input_type -> api.investment.mutualfund.order.ProcessExternalSwitchOrdersRequest
	43,  // 199: api.investment.mutualfund.order.OrderManager.CreateOrder:output_type -> api.investment.mutualfund.order.CreateOrderResponse
	46,  // 200: api.investment.mutualfund.order.OrderManager.GetOrders:output_type -> api.investment.mutualfund.order.GetOrdersResponse
	48,  // 201: api.investment.mutualfund.order.OrderManager.TriggerOrderProcessing:output_type -> api.investment.mutualfund.order.TriggerOrderProcessingResponse
	160, // 202: api.investment.mutualfund.order.OrderManager.SendOrderToVendor:output_type -> order.domain.ProcessFulfilmentResponse
	87,  // 203: api.investment.mutualfund.order.OrderManager.ProcessOrderConfirmationFromVendor:output_type -> api.investment.mutualfund.order.ProcessOrderConfirmationFromVendorResponse
	50,  // 204: api.investment.mutualfund.order.OrderManager.GetOrder:output_type -> api.investment.mutualfund.order.GetOrderResponse
	52,  // 205: api.investment.mutualfund.order.OrderManager.UpdateOrderStatusWithCheck:output_type -> api.investment.mutualfund.order.UpdateOrderStatusWithCheckResponse
	54,  // 206: api.investment.mutualfund.order.OrderManager.CreateOrUpdateFileState:output_type -> api.investment.mutualfund.order.CreateOrUpdateFileStateResponse
	56,  // 207: api.investment.mutualfund.order.OrderManager.UploadVendorFiles:output_type -> api.investment.mutualfund.order.UploadVendorFileResponse
	58,  // 208: api.investment.mutualfund.order.OrderManager.ProcessCreditMISReportConfirmation:output_type -> api.investment.mutualfund.order.ProcessCreditMISReportConfirmationResponse
	61,  // 209: api.investment.mutualfund.order.OrderManager.GetOrdersByFundIDAndActorID:output_type -> api.investment.mutualfund.order.GetOrdersByFundIDAndActorIDResponse
	63,  // 210: api.investment.mutualfund.order.OrderManager.GetWithdrawalConstraints:output_type -> api.investment.mutualfund.order.GetWithdrawalConstraintsResponse
	65,  // 211: api.investment.mutualfund.order.OrderManager.GetOrderDetails:output_type -> api.investment.mutualfund.order.GetOrderDetailsResponse
	69,  // 212: api.investment.mutualfund.order.OrderManager.ProcessPaymentCreditFromVendor:output_type -> api.investment.mutualfund.order.ProcessPaymentCreditFromVendorResponse
	72,  // 213: api.investment.mutualfund.order.OrderManager.ProcessOrderRejectionFromVendor:output_type -> api.investment.mutualfund.order.ProcessOrderRejectionFromVendorResponse
	74,  // 214: api.investment.mutualfund.order.OrderManager.GetBuyConstraints:output_type -> api.investment.mutualfund.order.GetBuyConstraintsResponse
	78,  // 215: api.investment.mutualfund.order.OrderManager.GetOrdersInCreditMISFile:output_type -> api.investment.mutualfund.order.GetOrdersInCreditMISFileResponse
	81,  // 216: api.investment.mutualfund.order.OrderManager.GetMFInvestmentsForActor:output_type -> api.investment.mutualfund.order.GetMFInvestmentsForActorResponse
	83,  // 217: api.investment.mutualfund.order.OrderManager.CheckKYCStatus:output_type -> api.investment.mutualfund.order.CheckKYCStatusResponse
	89,  // 218: api.investment.mutualfund.order.OrderManager.ProcessExternalOrderConfirmationFromVendor:output_type -> api.investment.mutualfund.order.ProcessExternalOrderConfirmationFromVendorResponse
	93,  // 219: api.investment.mutualfund.order.OrderManager.CheckOrderEligibility:output_type -> api.investment.mutualfund.order.CheckOrderEligibilityResponse
	95,  // 220: api.investment.mutualfund.order.OrderManager.MockCreditMISReportProcessing:output_type -> api.investment.mutualfund.order.MockCreditMISReportProcessingResponse
	161, // 221: api.investment.mutualfund.order.OrderManager.GetFileStateDetails:output_type -> api.investment.mutualfund.order.GetFileStateDetailsResponse
	97,  // 222: api.investment.mutualfund.order.OrderManager.UpdateOrders:output_type -> api.investment.mutualfund.order.UpdateOrdersResponse
	101, // 223: api.investment.mutualfund.order.OrderManager.UpdateOrderConfirmationInfo:output_type -> api.investment.mutualfund.order.UpdateOrderConfirmationInfoResponse
	39,  // 224: api.investment.mutualfund.order.OrderManager.HandleFitttSubscriptionUpdate:output_type -> api.investment.mutualfund.order.HandleFitttSubscriptionUpdateResponse
	103, // 225: api.investment.mutualfund.order.OrderManager.ProcessInternalSwitchOrders:output_type -> api.investment.mutualfund.order.ProcessInternalSwitchOrdersResponse
	105, // 226: api.investment.mutualfund.order.OrderManager.GetMisFiles:output_type -> api.investment.mutualfund.order.GetMisFilesResponse
	107, // 227: api.investment.mutualfund.order.OrderManager.ProcessExternalSwitchOrders:output_type -> api.investment.mutualfund.order.ProcessExternalSwitchOrdersResponse
	199, // [199:228] is the sub-list for method output_type
	170, // [170:199] is the sub-list for method input_type
	170, // [170:170] is the sub-list for extension type_name
	170, // [170:170] is the sub-list for extension extendee
	0,   // [0:170] is the sub-list for field type_name
}

func init() { file_api_investment_mutualfund_order_service_proto_init() }
func file_api_investment_mutualfund_order_service_proto_init() {
	if File_api_investment_mutualfund_order_service_proto != nil {
		return
	}
	file_api_investment_mutualfund_order_file_state_proto_init()
	file_api_investment_mutualfund_order_order_proto_init()
	file_api_investment_mutualfund_order_order_confirmation_info_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_investment_mutualfund_order_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleFitttSubscriptionUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleFitttSubscriptionUpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentRequestInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SIPExecutionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrdersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrdersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerOrderProcessingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerOrderProcessingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrderStatusWithCheckRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrderStatusWithCheckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrUpdateFileStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrUpdateFileStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadVendorFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadVendorFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCreditMISReportConfirmationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCreditMISReportConfirmationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrdersByFundIDAndActorIDRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrdersByFundIDAndActorIDResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWithdrawalConstraintsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWithdrawalConstraintsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderStatusUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentConfirmationUpdateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessPaymentCreditFromVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessPaymentCreditFromVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderRejectionUpdateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessOrderRejectionFromVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessOrderRejectionFromVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBuyConstraintsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBuyConstraintsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepeatedInt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepeatedAipDay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrdersInCreditMISFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrdersInCreditMISFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMFInvestmentsForActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvestmentsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMFInvestmentsForActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckKYCStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckKYCStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderUpdateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderConfirmationDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessOrderConfirmationFromVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessOrderConfirmationFromVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessExternalOrderConfirmationFromVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessExternalOrderConfirmationFromVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessExternalOrderPaymentCreditFromVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessExternalOrderPaymentCreditFromVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckOrderEligibilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckOrderEligibilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MockCreditMISReportProcessingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MockCreditMISReportProcessingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrdersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrdersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderUpdateDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrderConfirmationInfoResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrderConfirmationInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrderConfirmationInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessInternalSwitchOrdersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessInternalSwitchOrdersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMisFilesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMisFilesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessExternalSwitchOrdersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessExternalSwitchOrdersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMisFilesResponse_AmcFiles); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMisFilesResponse_RtaFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMisFilesResponse_AmcFiles_AmcFileData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_order_service_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_investment_mutualfund_order_service_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*HandleFitttSubscriptionUpdateRequest_SipDate)(nil),
		(*HandleFitttSubscriptionUpdateRequest_SipDay)(nil),
	}
	file_api_investment_mutualfund_order_service_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*GetOrderRequest_OrderId)(nil),
		(*GetOrderRequest_ClientOrderId)(nil),
		(*GetOrderRequest_ExternalOrderId)(nil),
		(*GetOrderRequest_VendorOrderId)(nil),
	}
	file_api_investment_mutualfund_order_service_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*UpdateOrderStatusWithCheckRequest_VendorOrderId)(nil),
		(*UpdateOrderStatusWithCheckRequest_Id)(nil),
	}
	file_api_investment_mutualfund_order_service_proto_msgTypes[22].OneofWrappers = []interface{}{
		(*Filter_NumericVal)(nil),
		(*Filter_StringVal)(nil),
		(*Filter_TimeVal)(nil),
		(*Filter_OrderClientVal)(nil),
		(*Filter_OrderTypeVal)(nil),
		(*Filter_OrderStatusVal)(nil),
	}
	file_api_investment_mutualfund_order_service_proto_msgTypes[26].OneofWrappers = []interface{}{
		(*GetOrderDetailsRequest_OrderId)(nil),
		(*GetOrderDetailsRequest_ExternalOrderId)(nil),
		(*GetOrderDetailsRequest_ClientOrderId)(nil),
		(*GetOrderDetailsRequest_VendorOrderId)(nil),
	}
	file_api_investment_mutualfund_order_service_proto_msgTypes[36].OneofWrappers = []interface{}{
		(*GetBuyConstraintsResponse_PossibleDates)(nil),
		(*GetBuyConstraintsResponse_PossibleDays)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_investment_mutualfund_order_service_proto_rawDesc,
			NumEnums:      38,
			NumMessages:   87,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_investment_mutualfund_order_service_proto_goTypes,
		DependencyIndexes: file_api_investment_mutualfund_order_service_proto_depIdxs,
		EnumInfos:         file_api_investment_mutualfund_order_service_proto_enumTypes,
		MessageInfos:      file_api_investment_mutualfund_order_service_proto_msgTypes,
	}.Build()
	File_api_investment_mutualfund_order_service_proto = out.File
	file_api_investment_mutualfund_order_service_proto_rawDesc = nil
	file_api_investment_mutualfund_order_service_proto_goTypes = nil
	file_api_investment_mutualfund_order_service_proto_depIdxs = nil
}
