// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/investment/mutualfund/order/scheduler/service.proto

package scheduler

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	mutualfund "github.com/epifi/gamma/api/investment/mutualfund"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = mutualfund.Amc(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on TriggerCreditMISReportGenerationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *TriggerCreditMISReportGenerationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// TriggerCreditMISReportGenerationRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// TriggerCreditMISReportGenerationRequestMultiError, or nil if none found.
func (m *TriggerCreditMISReportGenerationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerCreditMISReportGenerationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vendor

	// no validation rules for Amc

	if all {
		switch v := interface{}(m.GetReceivedTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TriggerCreditMISReportGenerationRequestValidationError{
					field:  "ReceivedTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TriggerCreditMISReportGenerationRequestValidationError{
					field:  "ReceivedTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReceivedTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TriggerCreditMISReportGenerationRequestValidationError{
				field:  "ReceivedTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TriggerCreditMISReportGenerationRequestMultiError(errors)
	}

	return nil
}

// TriggerCreditMISReportGenerationRequestMultiError is an error wrapping
// multiple validation errors returned by
// TriggerCreditMISReportGenerationRequest.ValidateAll() if the designated
// constraints aren't met.
type TriggerCreditMISReportGenerationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerCreditMISReportGenerationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerCreditMISReportGenerationRequestMultiError) AllErrors() []error { return m }

// TriggerCreditMISReportGenerationRequestValidationError is the validation
// error returned by TriggerCreditMISReportGenerationRequest.Validate if the
// designated constraints aren't met.
type TriggerCreditMISReportGenerationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerCreditMISReportGenerationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerCreditMISReportGenerationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerCreditMISReportGenerationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerCreditMISReportGenerationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerCreditMISReportGenerationRequestValidationError) ErrorName() string {
	return "TriggerCreditMISReportGenerationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerCreditMISReportGenerationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerCreditMISReportGenerationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerCreditMISReportGenerationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerCreditMISReportGenerationRequestValidationError{}

// Validate checks the field values on TriggerCreditMISReportGenerationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *TriggerCreditMISReportGenerationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// TriggerCreditMISReportGenerationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// TriggerCreditMISReportGenerationResponseMultiError, or nil if none found.
func (m *TriggerCreditMISReportGenerationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerCreditMISReportGenerationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TriggerCreditMISReportGenerationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TriggerCreditMISReportGenerationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TriggerCreditMISReportGenerationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TriggerCreditMISReportGenerationResponseMultiError(errors)
	}

	return nil
}

// TriggerCreditMISReportGenerationResponseMultiError is an error wrapping
// multiple validation errors returned by
// TriggerCreditMISReportGenerationResponse.ValidateAll() if the designated
// constraints aren't met.
type TriggerCreditMISReportGenerationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerCreditMISReportGenerationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerCreditMISReportGenerationResponseMultiError) AllErrors() []error { return m }

// TriggerCreditMISReportGenerationResponseValidationError is the validation
// error returned by TriggerCreditMISReportGenerationResponse.Validate if the
// designated constraints aren't met.
type TriggerCreditMISReportGenerationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerCreditMISReportGenerationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerCreditMISReportGenerationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerCreditMISReportGenerationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerCreditMISReportGenerationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerCreditMISReportGenerationResponseValidationError) ErrorName() string {
	return "TriggerCreditMISReportGenerationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerCreditMISReportGenerationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerCreditMISReportGenerationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerCreditMISReportGenerationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerCreditMISReportGenerationResponseValidationError{}

// Validate checks the field values on TriggerOrderProcessingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerOrderProcessingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerOrderProcessingRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerOrderProcessingRequestMultiError, or nil if none found.
func (m *TriggerOrderProcessingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerOrderProcessingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReceivedTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TriggerOrderProcessingRequestValidationError{
					field:  "ReceivedTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TriggerOrderProcessingRequestValidationError{
					field:  "ReceivedTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReceivedTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TriggerOrderProcessingRequestValidationError{
				field:  "ReceivedTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	if len(errors) > 0 {
		return TriggerOrderProcessingRequestMultiError(errors)
	}

	return nil
}

// TriggerOrderProcessingRequestMultiError is an error wrapping multiple
// validation errors returned by TriggerOrderProcessingRequest.ValidateAll()
// if the designated constraints aren't met.
type TriggerOrderProcessingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerOrderProcessingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerOrderProcessingRequestMultiError) AllErrors() []error { return m }

// TriggerOrderProcessingRequestValidationError is the validation error
// returned by TriggerOrderProcessingRequest.Validate if the designated
// constraints aren't met.
type TriggerOrderProcessingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerOrderProcessingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerOrderProcessingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerOrderProcessingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerOrderProcessingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerOrderProcessingRequestValidationError) ErrorName() string {
	return "TriggerOrderProcessingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerOrderProcessingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerOrderProcessingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerOrderProcessingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerOrderProcessingRequestValidationError{}

// Validate checks the field values on TriggerOrderProcessingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerOrderProcessingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerOrderProcessingResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TriggerOrderProcessingResponseMultiError, or nil if none found.
func (m *TriggerOrderProcessingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerOrderProcessingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TriggerOrderProcessingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TriggerOrderProcessingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TriggerOrderProcessingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TriggerOrderProcessingResponseMultiError(errors)
	}

	return nil
}

// TriggerOrderProcessingResponseMultiError is an error wrapping multiple
// validation errors returned by TriggerOrderProcessingResponse.ValidateAll()
// if the designated constraints aren't met.
type TriggerOrderProcessingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerOrderProcessingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerOrderProcessingResponseMultiError) AllErrors() []error { return m }

// TriggerOrderProcessingResponseValidationError is the validation error
// returned by TriggerOrderProcessingResponse.Validate if the designated
// constraints aren't met.
type TriggerOrderProcessingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerOrderProcessingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerOrderProcessingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerOrderProcessingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerOrderProcessingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerOrderProcessingResponseValidationError) ErrorName() string {
	return "TriggerOrderProcessingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerOrderProcessingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerOrderProcessingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerOrderProcessingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerOrderProcessingResponseValidationError{}

// Validate checks the field values on TriggerNFTProcessingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerNFTProcessingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerNFTProcessingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TriggerNFTProcessingRequestMultiError, or nil if none found.
func (m *TriggerNFTProcessingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerNFTProcessingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TriggerNFTProcessingRequestMultiError(errors)
	}

	return nil
}

// TriggerNFTProcessingRequestMultiError is an error wrapping multiple
// validation errors returned by TriggerNFTProcessingRequest.ValidateAll() if
// the designated constraints aren't met.
type TriggerNFTProcessingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerNFTProcessingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerNFTProcessingRequestMultiError) AllErrors() []error { return m }

// TriggerNFTProcessingRequestValidationError is the validation error returned
// by TriggerNFTProcessingRequest.Validate if the designated constraints
// aren't met.
type TriggerNFTProcessingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerNFTProcessingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerNFTProcessingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerNFTProcessingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerNFTProcessingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerNFTProcessingRequestValidationError) ErrorName() string {
	return "TriggerNFTProcessingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerNFTProcessingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerNFTProcessingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerNFTProcessingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerNFTProcessingRequestValidationError{}

// Validate checks the field values on TriggerNFTProcessingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TriggerNFTProcessingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TriggerNFTProcessingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TriggerNFTProcessingResponseMultiError, or nil if none found.
func (m *TriggerNFTProcessingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerNFTProcessingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TriggerNFTProcessingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TriggerNFTProcessingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TriggerNFTProcessingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TriggerNFTProcessingResponseMultiError(errors)
	}

	return nil
}

// TriggerNFTProcessingResponseMultiError is an error wrapping multiple
// validation errors returned by TriggerNFTProcessingResponse.ValidateAll() if
// the designated constraints aren't met.
type TriggerNFTProcessingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerNFTProcessingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerNFTProcessingResponseMultiError) AllErrors() []error { return m }

// TriggerNFTProcessingResponseValidationError is the validation error returned
// by TriggerNFTProcessingResponse.Validate if the designated constraints
// aren't met.
type TriggerNFTProcessingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerNFTProcessingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerNFTProcessingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerNFTProcessingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerNFTProcessingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerNFTProcessingResponseValidationError) ErrorName() string {
	return "TriggerNFTProcessingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerNFTProcessingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerNFTProcessingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerNFTProcessingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerNFTProcessingResponseValidationError{}

// Validate checks the field values on
// TriggerCreditMISReportGenerationV2Request with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TriggerCreditMISReportGenerationV2Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// TriggerCreditMISReportGenerationV2Request with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// TriggerCreditMISReportGenerationV2RequestMultiError, or nil if none found.
func (m *TriggerCreditMISReportGenerationV2Request) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerCreditMISReportGenerationV2Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vendor

	// no validation rules for Amc

	if all {
		switch v := interface{}(m.GetReceivedTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TriggerCreditMISReportGenerationV2RequestValidationError{
					field:  "ReceivedTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TriggerCreditMISReportGenerationV2RequestValidationError{
					field:  "ReceivedTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReceivedTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TriggerCreditMISReportGenerationV2RequestValidationError{
				field:  "ReceivedTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TriggerCreditMISReportGenerationV2RequestMultiError(errors)
	}

	return nil
}

// TriggerCreditMISReportGenerationV2RequestMultiError is an error wrapping
// multiple validation errors returned by
// TriggerCreditMISReportGenerationV2Request.ValidateAll() if the designated
// constraints aren't met.
type TriggerCreditMISReportGenerationV2RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerCreditMISReportGenerationV2RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerCreditMISReportGenerationV2RequestMultiError) AllErrors() []error { return m }

// TriggerCreditMISReportGenerationV2RequestValidationError is the validation
// error returned by TriggerCreditMISReportGenerationV2Request.Validate if the
// designated constraints aren't met.
type TriggerCreditMISReportGenerationV2RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerCreditMISReportGenerationV2RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerCreditMISReportGenerationV2RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerCreditMISReportGenerationV2RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerCreditMISReportGenerationV2RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerCreditMISReportGenerationV2RequestValidationError) ErrorName() string {
	return "TriggerCreditMISReportGenerationV2RequestValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerCreditMISReportGenerationV2RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerCreditMISReportGenerationV2Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerCreditMISReportGenerationV2RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerCreditMISReportGenerationV2RequestValidationError{}

// Validate checks the field values on
// TriggerCreditMISReportGenerationV2Response with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TriggerCreditMISReportGenerationV2Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// TriggerCreditMISReportGenerationV2Response with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// TriggerCreditMISReportGenerationV2ResponseMultiError, or nil if none found.
func (m *TriggerCreditMISReportGenerationV2Response) ValidateAll() error {
	return m.validate(true)
}

func (m *TriggerCreditMISReportGenerationV2Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TriggerCreditMISReportGenerationV2ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TriggerCreditMISReportGenerationV2ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TriggerCreditMISReportGenerationV2ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TriggerCreditMISReportGenerationV2ResponseMultiError(errors)
	}

	return nil
}

// TriggerCreditMISReportGenerationV2ResponseMultiError is an error wrapping
// multiple validation errors returned by
// TriggerCreditMISReportGenerationV2Response.ValidateAll() if the designated
// constraints aren't met.
type TriggerCreditMISReportGenerationV2ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TriggerCreditMISReportGenerationV2ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TriggerCreditMISReportGenerationV2ResponseMultiError) AllErrors() []error { return m }

// TriggerCreditMISReportGenerationV2ResponseValidationError is the validation
// error returned by TriggerCreditMISReportGenerationV2Response.Validate if
// the designated constraints aren't met.
type TriggerCreditMISReportGenerationV2ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TriggerCreditMISReportGenerationV2ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TriggerCreditMISReportGenerationV2ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TriggerCreditMISReportGenerationV2ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TriggerCreditMISReportGenerationV2ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TriggerCreditMISReportGenerationV2ResponseValidationError) ErrorName() string {
	return "TriggerCreditMISReportGenerationV2ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TriggerCreditMISReportGenerationV2ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTriggerCreditMISReportGenerationV2Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TriggerCreditMISReportGenerationV2ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TriggerCreditMISReportGenerationV2ResponseValidationError{}
