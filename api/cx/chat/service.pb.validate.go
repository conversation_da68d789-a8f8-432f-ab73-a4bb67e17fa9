// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/chat/service.proto

package chat

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = typesv2.InAppChatViewType(0)
)

// Validate checks the field values on SenseforthChatInitInformation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SenseforthChatInitInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SenseforthChatInitInformation with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SenseforthChatInitInformationMultiError, or nil if none found.
func (m *SenseforthChatInitInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *SenseforthChatInitInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WebViewUrl

	// no validation rules for ShortToken

	// no validation rules for ReuseCacheData

	// no validation rules for BotContextCode

	if len(errors) > 0 {
		return SenseforthChatInitInformationMultiError(errors)
	}

	return nil
}

// SenseforthChatInitInformationMultiError is an error wrapping multiple
// validation errors returned by SenseforthChatInitInformation.ValidateAll()
// if the designated constraints aren't met.
type SenseforthChatInitInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SenseforthChatInitInformationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SenseforthChatInitInformationMultiError) AllErrors() []error { return m }

// SenseforthChatInitInformationValidationError is the validation error
// returned by SenseforthChatInitInformation.Validate if the designated
// constraints aren't met.
type SenseforthChatInitInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SenseforthChatInitInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SenseforthChatInitInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SenseforthChatInitInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SenseforthChatInitInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SenseforthChatInitInformationValidationError) ErrorName() string {
	return "SenseforthChatInitInformationValidationError"
}

// Error satisfies the builtin error interface
func (e SenseforthChatInitInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSenseforthChatInitInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SenseforthChatInitInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SenseforthChatInitInformationValidationError{}

// Validate checks the field values on GetReferenceIdForActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetReferenceIdForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReferenceIdForActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetReferenceIdForActorRequestMultiError, or nil if none found.
func (m *GetReferenceIdForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReferenceIdForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetReferenceIdForActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetReferenceIdForActorRequestMultiError(errors)
	}

	return nil
}

// GetReferenceIdForActorRequestMultiError is an error wrapping multiple
// validation errors returned by GetReferenceIdForActorRequest.ValidateAll()
// if the designated constraints aren't met.
type GetReferenceIdForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReferenceIdForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReferenceIdForActorRequestMultiError) AllErrors() []error { return m }

// GetReferenceIdForActorRequestValidationError is the validation error
// returned by GetReferenceIdForActorRequest.Validate if the designated
// constraints aren't met.
type GetReferenceIdForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReferenceIdForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReferenceIdForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReferenceIdForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReferenceIdForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReferenceIdForActorRequestValidationError) ErrorName() string {
	return "GetReferenceIdForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetReferenceIdForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReferenceIdForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReferenceIdForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReferenceIdForActorRequestValidationError{}

// Validate checks the field values on GetReferenceIdForActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetReferenceIdForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReferenceIdForActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetReferenceIdForActorResponseMultiError, or nil if none found.
func (m *GetReferenceIdForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReferenceIdForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReferenceIdForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReferenceIdForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReferenceIdForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for ReferenceId

	// no validation rules for AppId

	// no validation rules for AppKey

	// no validation rules for Domain

	if len(errors) > 0 {
		return GetReferenceIdForActorResponseMultiError(errors)
	}

	return nil
}

// GetReferenceIdForActorResponseMultiError is an error wrapping multiple
// validation errors returned by GetReferenceIdForActorResponse.ValidateAll()
// if the designated constraints aren't met.
type GetReferenceIdForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReferenceIdForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReferenceIdForActorResponseMultiError) AllErrors() []error { return m }

// GetReferenceIdForActorResponseValidationError is the validation error
// returned by GetReferenceIdForActorResponse.Validate if the designated
// constraints aren't met.
type GetReferenceIdForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReferenceIdForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReferenceIdForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReferenceIdForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReferenceIdForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReferenceIdForActorResponseValidationError) ErrorName() string {
	return "GetReferenceIdForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetReferenceIdForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReferenceIdForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReferenceIdForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReferenceIdForActorResponseValidationError{}

// Validate checks the field values on ClientSideChatFailureInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClientSideChatFailureInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClientSideChatFailureInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClientSideChatFailureInfoMultiError, or nil if none found.
func (m *ClientSideChatFailureInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ClientSideChatFailureInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LastTriedChatView

	// no validation rules for FailureCount

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return ClientSideChatFailureInfoMultiError(errors)
	}

	return nil
}

// ClientSideChatFailureInfoMultiError is an error wrapping multiple validation
// errors returned by ClientSideChatFailureInfo.ValidateAll() if the
// designated constraints aren't met.
type ClientSideChatFailureInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClientSideChatFailureInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClientSideChatFailureInfoMultiError) AllErrors() []error { return m }

// ClientSideChatFailureInfoValidationError is the validation error returned by
// ClientSideChatFailureInfo.Validate if the designated constraints aren't met.
type ClientSideChatFailureInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClientSideChatFailureInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClientSideChatFailureInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClientSideChatFailureInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClientSideChatFailureInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClientSideChatFailureInfoValidationError) ErrorName() string {
	return "ClientSideChatFailureInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ClientSideChatFailureInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClientSideChatFailureInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClientSideChatFailureInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClientSideChatFailureInfoValidationError{}

// Validate checks the field values on GetChatInitInformationForActorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetChatInitInformationForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChatInitInformationForActorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetChatInitInformationForActorRequestMultiError, or nil if none found.
func (m *GetChatInitInformationForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatInitInformationForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetChatInitInformationForActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for LastSuccessfullyLoadedChatView

	if all {
		switch v := interface{}(m.GetLastSuccessfulSessionTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatInitInformationForActorRequestValidationError{
					field:  "LastSuccessfulSessionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatInitInformationForActorRequestValidationError{
					field:  "LastSuccessfulSessionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastSuccessfulSessionTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatInitInformationForActorRequestValidationError{
				field:  "LastSuccessfulSessionTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClientSideChatFailureInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatInitInformationForActorRequestValidationError{
					field:  "ClientSideChatFailureInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatInitInformationForActorRequestValidationError{
					field:  "ClientSideChatFailureInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientSideChatFailureInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatInitInformationForActorRequestValidationError{
				field:  "ClientSideChatFailureInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ForceNewSession

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatInitInformationForActorRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatInitInformationForActorRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatInitInformationForActorRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SenseforthBotContextCode

	// no validation rules for Metadata

	// no validation rules for ScreenMetadata

	if len(errors) > 0 {
		return GetChatInitInformationForActorRequestMultiError(errors)
	}

	return nil
}

// GetChatInitInformationForActorRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetChatInitInformationForActorRequest.ValidateAll() if the designated
// constraints aren't met.
type GetChatInitInformationForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatInitInformationForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatInitInformationForActorRequestMultiError) AllErrors() []error { return m }

// GetChatInitInformationForActorRequestValidationError is the validation error
// returned by GetChatInitInformationForActorRequest.Validate if the
// designated constraints aren't met.
type GetChatInitInformationForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatInitInformationForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatInitInformationForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatInitInformationForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatInitInformationForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatInitInformationForActorRequestValidationError) ErrorName() string {
	return "GetChatInitInformationForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetChatInitInformationForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatInitInformationForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatInitInformationForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatInitInformationForActorRequestValidationError{}

// Validate checks the field values on GetChatInitInformationForActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetChatInitInformationForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetChatInitInformationForActorResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetChatInitInformationForActorResponseMultiError, or nil if none found.
func (m *GetChatInitInformationForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatInitInformationForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatInitInformationForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatInitInformationForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatInitInformationForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReferenceId

	// no validation rules for AppId

	// no validation rules for AppKey

	// no validation rules for Domain

	// no validation rules for CustomUserProperties

	// no validation rules for Email

	// no validation rules for ChatViewToBeLoaded

	if all {
		switch v := interface{}(m.GetSenseforthChatInitInformation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatInitInformationForActorResponseValidationError{
					field:  "SenseforthChatInitInformation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatInitInformationForActorResponseValidationError{
					field:  "SenseforthChatInitInformation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSenseforthChatInitInformation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatInitInformationForActorResponseValidationError{
				field:  "SenseforthChatInitInformation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShouldAutoRetry

	if all {
		switch v := interface{}(m.GetChatbotInitInformation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatInitInformationForActorResponseValidationError{
					field:  "ChatbotInitInformation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatInitInformationForActorResponseValidationError{
					field:  "ChatbotInitInformation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChatbotInitInformation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatInitInformationForActorResponseValidationError{
				field:  "ChatbotInitInformation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetChatInitInformationForActorResponseMultiError(errors)
	}

	return nil
}

// GetChatInitInformationForActorResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetChatInitInformationForActorResponse.ValidateAll() if the designated
// constraints aren't met.
type GetChatInitInformationForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatInitInformationForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatInitInformationForActorResponseMultiError) AllErrors() []error { return m }

// GetChatInitInformationForActorResponseValidationError is the validation
// error returned by GetChatInitInformationForActorResponse.Validate if the
// designated constraints aren't met.
type GetChatInitInformationForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatInitInformationForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatInitInformationForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatInitInformationForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatInitInformationForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatInitInformationForActorResponseValidationError) ErrorName() string {
	return "GetChatInitInformationForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetChatInitInformationForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatInitInformationForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatInitInformationForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatInitInformationForActorResponseValidationError{}

// Validate checks the field values on UpdateTicketForChatRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTicketForChatRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTicketForChatRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTicketForChatRequestMultiError, or nil if none found.
func (m *UpdateTicketForChatRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTicketForChatRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TicketId

	// no validation rules for ReferenceId

	if len(errors) > 0 {
		return UpdateTicketForChatRequestMultiError(errors)
	}

	return nil
}

// UpdateTicketForChatRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateTicketForChatRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateTicketForChatRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTicketForChatRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTicketForChatRequestMultiError) AllErrors() []error { return m }

// UpdateTicketForChatRequestValidationError is the validation error returned
// by UpdateTicketForChatRequest.Validate if the designated constraints aren't met.
type UpdateTicketForChatRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTicketForChatRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTicketForChatRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTicketForChatRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTicketForChatRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTicketForChatRequestValidationError) ErrorName() string {
	return "UpdateTicketForChatRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTicketForChatRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTicketForChatRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTicketForChatRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTicketForChatRequestValidationError{}

// Validate checks the field values on UpdateTicketForChatResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTicketForChatResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTicketForChatResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTicketForChatResponseMultiError, or nil if none found.
func (m *UpdateTicketForChatResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTicketForChatResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTicketForChatResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTicketForChatResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTicketForChatResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateTicketForChatResponseMultiError(errors)
	}

	return nil
}

// UpdateTicketForChatResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateTicketForChatResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateTicketForChatResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTicketForChatResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTicketForChatResponseMultiError) AllErrors() []error { return m }

// UpdateTicketForChatResponseValidationError is the validation error returned
// by UpdateTicketForChatResponse.Validate if the designated constraints
// aren't met.
type UpdateTicketForChatResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTicketForChatResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTicketForChatResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTicketForChatResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTicketForChatResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTicketForChatResponseValidationError) ErrorName() string {
	return "UpdateTicketForChatResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTicketForChatResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTicketForChatResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTicketForChatResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTicketForChatResponseValidationError{}

// Validate checks the field values on GetActorIdFromReferenceIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetActorIdFromReferenceIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorIdFromReferenceIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetActorIdFromReferenceIdRequestMultiError, or nil if none found.
func (m *GetActorIdFromReferenceIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorIdFromReferenceIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetReferenceId()) < 1 {
		err := GetActorIdFromReferenceIdRequestValidationError{
			field:  "ReferenceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetActorIdFromReferenceIdRequestMultiError(errors)
	}

	return nil
}

// GetActorIdFromReferenceIdRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetActorIdFromReferenceIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetActorIdFromReferenceIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorIdFromReferenceIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorIdFromReferenceIdRequestMultiError) AllErrors() []error { return m }

// GetActorIdFromReferenceIdRequestValidationError is the validation error
// returned by GetActorIdFromReferenceIdRequest.Validate if the designated
// constraints aren't met.
type GetActorIdFromReferenceIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorIdFromReferenceIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorIdFromReferenceIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorIdFromReferenceIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorIdFromReferenceIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorIdFromReferenceIdRequestValidationError) ErrorName() string {
	return "GetActorIdFromReferenceIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorIdFromReferenceIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorIdFromReferenceIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorIdFromReferenceIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorIdFromReferenceIdRequestValidationError{}

// Validate checks the field values on GetActorIdFromReferenceIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetActorIdFromReferenceIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorIdFromReferenceIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetActorIdFromReferenceIdResponseMultiError, or nil if none found.
func (m *GetActorIdFromReferenceIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorIdFromReferenceIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActorIdFromReferenceIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActorIdFromReferenceIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActorIdFromReferenceIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetActorIdFromReferenceIdResponseMultiError(errors)
	}

	return nil
}

// GetActorIdFromReferenceIdResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetActorIdFromReferenceIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetActorIdFromReferenceIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorIdFromReferenceIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorIdFromReferenceIdResponseMultiError) AllErrors() []error { return m }

// GetActorIdFromReferenceIdResponseValidationError is the validation error
// returned by GetActorIdFromReferenceIdResponse.Validate if the designated
// constraints aren't met.
type GetActorIdFromReferenceIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorIdFromReferenceIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorIdFromReferenceIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorIdFromReferenceIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorIdFromReferenceIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorIdFromReferenceIdResponseValidationError) ErrorName() string {
	return "GetActorIdFromReferenceIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorIdFromReferenceIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorIdFromReferenceIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorIdFromReferenceIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorIdFromReferenceIdResponseValidationError{}

// Validate checks the field values on UpdateTicketForSourceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTicketForSourceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTicketForSourceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTicketForSourceRequestMultiError, or nil if none found.
func (m *UpdateTicketForSourceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTicketForSourceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TicketId

	// no validation rules for PhoneNumber

	// no validation rules for Email

	// no validation rules for Source

	if len(errors) > 0 {
		return UpdateTicketForSourceRequestMultiError(errors)
	}

	return nil
}

// UpdateTicketForSourceRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateTicketForSourceRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateTicketForSourceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTicketForSourceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTicketForSourceRequestMultiError) AllErrors() []error { return m }

// UpdateTicketForSourceRequestValidationError is the validation error returned
// by UpdateTicketForSourceRequest.Validate if the designated constraints
// aren't met.
type UpdateTicketForSourceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTicketForSourceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTicketForSourceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTicketForSourceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTicketForSourceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTicketForSourceRequestValidationError) ErrorName() string {
	return "UpdateTicketForSourceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTicketForSourceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTicketForSourceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTicketForSourceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTicketForSourceRequestValidationError{}

// Validate checks the field values on UpdateTicketForSourceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTicketForSourceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTicketForSourceResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateTicketForSourceResponseMultiError, or nil if none found.
func (m *UpdateTicketForSourceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTicketForSourceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTicketForSourceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTicketForSourceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTicketForSourceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateTicketForSourceResponseMultiError(errors)
	}

	return nil
}

// UpdateTicketForSourceResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateTicketForSourceResponse.ValidateAll()
// if the designated constraints aren't met.
type UpdateTicketForSourceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTicketForSourceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTicketForSourceResponseMultiError) AllErrors() []error { return m }

// UpdateTicketForSourceResponseValidationError is the validation error
// returned by UpdateTicketForSourceResponse.Validate if the designated
// constraints aren't met.
type UpdateTicketForSourceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTicketForSourceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTicketForSourceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTicketForSourceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTicketForSourceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTicketForSourceResponseValidationError) ErrorName() string {
	return "UpdateTicketForSourceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTicketForSourceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTicketForSourceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTicketForSourceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTicketForSourceResponseValidationError{}

// Validate checks the field values on FetchAccessTokenForNuggetChatbotRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FetchAccessTokenForNuggetChatbotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchAccessTokenForNuggetChatbotRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// FetchAccessTokenForNuggetChatbotRequestMultiError, or nil if none found.
func (m *FetchAccessTokenForNuggetChatbotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchAccessTokenForNuggetChatbotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := FetchAccessTokenForNuggetChatbotRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FetchAccessTokenForNuggetChatbotRequestMultiError(errors)
	}

	return nil
}

// FetchAccessTokenForNuggetChatbotRequestMultiError is an error wrapping
// multiple validation errors returned by
// FetchAccessTokenForNuggetChatbotRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchAccessTokenForNuggetChatbotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchAccessTokenForNuggetChatbotRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchAccessTokenForNuggetChatbotRequestMultiError) AllErrors() []error { return m }

// FetchAccessTokenForNuggetChatbotRequestValidationError is the validation
// error returned by FetchAccessTokenForNuggetChatbotRequest.Validate if the
// designated constraints aren't met.
type FetchAccessTokenForNuggetChatbotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchAccessTokenForNuggetChatbotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchAccessTokenForNuggetChatbotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchAccessTokenForNuggetChatbotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchAccessTokenForNuggetChatbotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchAccessTokenForNuggetChatbotRequestValidationError) ErrorName() string {
	return "FetchAccessTokenForNuggetChatbotRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchAccessTokenForNuggetChatbotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchAccessTokenForNuggetChatbotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchAccessTokenForNuggetChatbotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchAccessTokenForNuggetChatbotRequestValidationError{}

// Validate checks the field values on FetchAccessTokenForNuggetChatbotResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FetchAccessTokenForNuggetChatbotResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchAccessTokenForNuggetChatbotResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FetchAccessTokenForNuggetChatbotResponseMultiError, or nil if none found.
func (m *FetchAccessTokenForNuggetChatbotResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchAccessTokenForNuggetChatbotResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAccessTokenForNuggetChatbotResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAccessTokenForNuggetChatbotResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAccessTokenForNuggetChatbotResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccessToken

	if len(errors) > 0 {
		return FetchAccessTokenForNuggetChatbotResponseMultiError(errors)
	}

	return nil
}

// FetchAccessTokenForNuggetChatbotResponseMultiError is an error wrapping
// multiple validation errors returned by
// FetchAccessTokenForNuggetChatbotResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchAccessTokenForNuggetChatbotResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchAccessTokenForNuggetChatbotResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchAccessTokenForNuggetChatbotResponseMultiError) AllErrors() []error { return m }

// FetchAccessTokenForNuggetChatbotResponseValidationError is the validation
// error returned by FetchAccessTokenForNuggetChatbotResponse.Validate if the
// designated constraints aren't met.
type FetchAccessTokenForNuggetChatbotResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchAccessTokenForNuggetChatbotResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchAccessTokenForNuggetChatbotResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchAccessTokenForNuggetChatbotResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchAccessTokenForNuggetChatbotResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchAccessTokenForNuggetChatbotResponseValidationError) ErrorName() string {
	return "FetchAccessTokenForNuggetChatbotResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchAccessTokenForNuggetChatbotResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchAccessTokenForNuggetChatbotResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchAccessTokenForNuggetChatbotResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchAccessTokenForNuggetChatbotResponseValidationError{}
