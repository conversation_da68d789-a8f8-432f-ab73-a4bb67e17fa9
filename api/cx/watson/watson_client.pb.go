// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/watson/watson_client.proto

package watson

import (
	rpc "github.com/epifi/be-common/api/rpc"
	comms "github.com/epifi/gamma/api/comms"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for IsIncidentValid method to be implemented by a Watson client service
type IsIncidentValidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Incident *IncidentDetailsForClient `protobuf:"bytes,1,opt,name=incident,proto3" json:"incident,omitempty"`
}

func (x *IsIncidentValidRequest) Reset() {
	*x = IsIncidentValidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_watson_watson_client_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsIncidentValidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsIncidentValidRequest) ProtoMessage() {}

func (x *IsIncidentValidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_watson_watson_client_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsIncidentValidRequest.ProtoReflect.Descriptor instead.
func (*IsIncidentValidRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_watson_watson_client_proto_rawDescGZIP(), []int{0}
}

func (x *IsIncidentValidRequest) GetIncident() *IncidentDetailsForClient {
	if x != nil {
		return x.Incident
	}
	return nil
}

// Response of IsIncidentValid method to be implemented by a Watson client service
type IsIncidentValidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	IsIncidentValid bool        `protobuf:"varint,2,opt,name=is_incident_valid,json=isIncidentValid,proto3" json:"is_incident_valid,omitempty"`
}

func (x *IsIncidentValidResponse) Reset() {
	*x = IsIncidentValidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_watson_watson_client_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsIncidentValidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsIncidentValidResponse) ProtoMessage() {}

func (x *IsIncidentValidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_watson_watson_client_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsIncidentValidResponse.ProtoReflect.Descriptor instead.
func (*IsIncidentValidResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_watson_watson_client_proto_rawDescGZIP(), []int{1}
}

func (x *IsIncidentValidResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *IsIncidentValidResponse) GetIsIncidentValid() bool {
	if x != nil {
		return x.IsIncidentValid
	}
	return false
}

// Request for GetTicketDetails method to be implemented by a Watson client service
type GetTicketDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Incident *IncidentDetailsForClient `protobuf:"bytes,1,opt,name=incident,proto3" json:"incident,omitempty"`
	// GetTicketDetailsActionType signifies what action Watson will perform after the current GetTicketDetails call
	ActionType GetTicketDetailsActionType `protobuf:"varint,2,opt,name=action_type,json=actionType,proto3,enum=cx.watson.GetTicketDetailsActionType" json:"action_type,omitempty"`
}

func (x *GetTicketDetailsRequest) Reset() {
	*x = GetTicketDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_watson_watson_client_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTicketDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicketDetailsRequest) ProtoMessage() {}

func (x *GetTicketDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_watson_watson_client_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicketDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetTicketDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_watson_watson_client_proto_rawDescGZIP(), []int{2}
}

func (x *GetTicketDetailsRequest) GetIncident() *IncidentDetailsForClient {
	if x != nil {
		return x.Incident
	}
	return nil
}

func (x *GetTicketDetailsRequest) GetActionType() GetTicketDetailsActionType {
	if x != nil {
		return x.ActionType
	}
	return GetTicketDetailsActionType_GET_TICKET_DETAILS_ACTION_TYPE_UNSPECIFIED
}

// Response of GetTicketDetails method to be implemented by a Watson client service
type GetTicketDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TicketDetails *TicketDetails `protobuf:"bytes,2,opt,name=ticket_details,json=ticketDetails,proto3" json:"ticket_details,omitempty"`
}

func (x *GetTicketDetailsResponse) Reset() {
	*x = GetTicketDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_watson_watson_client_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTicketDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicketDetailsResponse) ProtoMessage() {}

func (x *GetTicketDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_watson_watson_client_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicketDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetTicketDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_watson_watson_client_proto_rawDescGZIP(), []int{3}
}

func (x *GetTicketDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTicketDetailsResponse) GetTicketDetails() *TicketDetails {
	if x != nil {
		return x.TicketDetails
	}
	return nil
}

// Request for IsIncidentResolved method to be implemented by a Watson client service
type IsIncidentResolvedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Incident *IncidentDetailsForClient `protobuf:"bytes,1,opt,name=incident,proto3" json:"incident,omitempty"`
}

func (x *IsIncidentResolvedRequest) Reset() {
	*x = IsIncidentResolvedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_watson_watson_client_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsIncidentResolvedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsIncidentResolvedRequest) ProtoMessage() {}

func (x *IsIncidentResolvedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_watson_watson_client_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsIncidentResolvedRequest.ProtoReflect.Descriptor instead.
func (*IsIncidentResolvedRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_watson_watson_client_proto_rawDescGZIP(), []int{4}
}

func (x *IsIncidentResolvedRequest) GetIncident() *IncidentDetailsForClient {
	if x != nil {
		return x.Incident
	}
	return nil
}

// Response of IsIncidentResolved method to be implemented by a Watson client service
type IsIncidentResolvedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status             *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	IsIncidentResolved bool        `protobuf:"varint,2,opt,name=is_incident_resolved,json=isIncidentResolved,proto3" json:"is_incident_resolved,omitempty"`
}

func (x *IsIncidentResolvedResponse) Reset() {
	*x = IsIncidentResolvedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_watson_watson_client_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsIncidentResolvedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsIncidentResolvedResponse) ProtoMessage() {}

func (x *IsIncidentResolvedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_watson_watson_client_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsIncidentResolvedResponse.ProtoReflect.Descriptor instead.
func (*IsIncidentResolvedResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_watson_watson_client_proto_rawDescGZIP(), []int{5}
}

func (x *IsIncidentResolvedResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *IsIncidentResolvedResponse) GetIsIncidentResolved() bool {
	if x != nil {
		return x.IsIncidentResolved
	}
	return false
}

// Request for GetCommsDetails method to be implemented by a Watson client service
type GetCommsDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Incident *IncidentDetailsForClient `protobuf:"bytes,1,opt,name=incident,proto3" json:"incident,omitempty"`
	// The metadata which may be used by the client to decide what comms to send. It may be dependent on the Comms Type
	CommsMetadata *CommsMetadataForClient `protobuf:"bytes,2,opt,name=comms_metadata,json=commsMetadata,proto3" json:"comms_metadata,omitempty"`
	CommsType     CommsType               `protobuf:"varint,3,opt,name=comms_type,json=commsType,proto3,enum=cx.watson.CommsType" json:"comms_type,omitempty"`
}

func (x *GetCommsDetailsRequest) Reset() {
	*x = GetCommsDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_watson_watson_client_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommsDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommsDetailsRequest) ProtoMessage() {}

func (x *GetCommsDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_watson_watson_client_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommsDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetCommsDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_watson_watson_client_proto_rawDescGZIP(), []int{6}
}

func (x *GetCommsDetailsRequest) GetIncident() *IncidentDetailsForClient {
	if x != nil {
		return x.Incident
	}
	return nil
}

func (x *GetCommsDetailsRequest) GetCommsMetadata() *CommsMetadataForClient {
	if x != nil {
		return x.CommsMetadata
	}
	return nil
}

func (x *GetCommsDetailsRequest) GetCommsType() CommsType {
	if x != nil {
		return x.CommsType
	}
	return CommsType_COMMS_TYPE_UNSPECIFIED
}

// CommsDetail contains the message sent by client for sending custom comms
type CommsDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Detail:
	//
	//	*CommsDetail_Sms
	//	*CommsDetail_Email
	//	*CommsDetail_Notification
	//	*CommsDetail_Whatsapp
	Detail isCommsDetail_Detail `protobuf_oneof:"detail"`
}

func (x *CommsDetail) Reset() {
	*x = CommsDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_watson_watson_client_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommsDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommsDetail) ProtoMessage() {}

func (x *CommsDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_watson_watson_client_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommsDetail.ProtoReflect.Descriptor instead.
func (*CommsDetail) Descriptor() ([]byte, []int) {
	return file_api_cx_watson_watson_client_proto_rawDescGZIP(), []int{7}
}

func (m *CommsDetail) GetDetail() isCommsDetail_Detail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (x *CommsDetail) GetSms() *comms.SMSMessage {
	if x, ok := x.GetDetail().(*CommsDetail_Sms); ok {
		return x.Sms
	}
	return nil
}

func (x *CommsDetail) GetEmail() *comms.EmailMessage {
	if x, ok := x.GetDetail().(*CommsDetail_Email); ok {
		return x.Email
	}
	return nil
}

func (x *CommsDetail) GetNotification() *comms.NotificationMessage {
	if x, ok := x.GetDetail().(*CommsDetail_Notification); ok {
		return x.Notification
	}
	return nil
}

func (x *CommsDetail) GetWhatsapp() *comms.WhatsappMessage {
	if x, ok := x.GetDetail().(*CommsDetail_Whatsapp); ok {
		return x.Whatsapp
	}
	return nil
}

type isCommsDetail_Detail interface {
	isCommsDetail_Detail()
}

type CommsDetail_Sms struct {
	Sms *comms.SMSMessage `protobuf:"bytes,1,opt,name=sms,proto3,oneof"`
}

type CommsDetail_Email struct {
	Email *comms.EmailMessage `protobuf:"bytes,2,opt,name=email,proto3,oneof"`
}

type CommsDetail_Notification struct {
	Notification *comms.NotificationMessage `protobuf:"bytes,3,opt,name=notification,proto3,oneof"`
}

type CommsDetail_Whatsapp struct {
	Whatsapp *comms.WhatsappMessage `protobuf:"bytes,4,opt,name=whatsapp,proto3,oneof"`
}

func (*CommsDetail_Sms) isCommsDetail_Detail() {}

func (*CommsDetail_Email) isCommsDetail_Detail() {}

func (*CommsDetail_Notification) isCommsDetail_Detail() {}

func (*CommsDetail_Whatsapp) isCommsDetail_Detail() {}

// Response of GetCommsDetails method to be implemented by a Watson client service
type GetCommsDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CommsDetails []*CommsDetail `protobuf:"bytes,2,rep,name=comms_details,json=commsDetails,proto3" json:"comms_details,omitempty"`
}

func (x *GetCommsDetailsResponse) Reset() {
	*x = GetCommsDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_watson_watson_client_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommsDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommsDetailsResponse) ProtoMessage() {}

func (x *GetCommsDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_watson_watson_client_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommsDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetCommsDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_watson_watson_client_proto_rawDescGZIP(), []int{8}
}

func (x *GetCommsDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCommsDetailsResponse) GetCommsDetails() []*CommsDetail {
	if x != nil {
		return x.CommsDetails
	}
	return nil
}

var File_api_cx_watson_watson_client_proto protoreflect.FileDescriptor

var file_api_cx_watson_watson_client_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x2f,
	0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x09, 0x63, 0x78, 0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x1a, 0x15,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73,
	0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2f, 0x73,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x73, 0x2f, 0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x77, 0x61, 0x74, 0x73, 0x6f,
	0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x2f, 0x77, 0x61, 0x74,
	0x73, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x59, 0x0a, 0x16, 0x49, 0x73, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x08, 0x69, 0x6e, 0x63,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x78,
	0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x08, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x22, 0x6a, 0x0a, 0x17, 0x49, 0x73,
	0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x73,
	0x5f, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x22, 0xa2, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3f, 0x0a, 0x08, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x78, 0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e,
	0x2e, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x46, 0x6f, 0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x69, 0x6e, 0x63, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x63, 0x78, 0x2e, 0x77, 0x61,
	0x74, 0x73, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x80, 0x01, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a,
	0x0e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x78, 0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f,
	0x6e, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x0d, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x5c,
	0x0a, 0x19, 0x49, 0x73, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x6f,
	0x6c, 0x76, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x08, 0x69,
	0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x63, 0x78, 0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x52, 0x08, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x22, 0x73, 0x0a, 0x1a,
	0x49, 0x73, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76,
	0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x30, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69,
	0x73, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65,
	0x64, 0x22, 0xd8, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x08,
	0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x63, 0x78, 0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x63, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x08, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x48, 0x0a,
	0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x78, 0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f,
	0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x46,
	0x6f, 0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x33, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x73,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x78,
	0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0xe3, 0x01, 0x0a,
	0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x0a, 0x03,
	0x73, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x73, 0x2e, 0x53, 0x4d, 0x53, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x03,
	0x73, 0x6d, 0x73, 0x12, 0x2b, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x40, 0x0a, 0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x08, 0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x57, 0x68, 0x61,
	0x74, 0x73, 0x61, 0x70, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x08,
	0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x42, 0x08, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x22, 0x7b, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x78, 0x2e, 0x77,
	0x61, 0x74, 0x73, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42,
	0x4c, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78,
	0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x5a, 0x24, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_watson_watson_client_proto_rawDescOnce sync.Once
	file_api_cx_watson_watson_client_proto_rawDescData = file_api_cx_watson_watson_client_proto_rawDesc
)

func file_api_cx_watson_watson_client_proto_rawDescGZIP() []byte {
	file_api_cx_watson_watson_client_proto_rawDescOnce.Do(func() {
		file_api_cx_watson_watson_client_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_watson_watson_client_proto_rawDescData)
	})
	return file_api_cx_watson_watson_client_proto_rawDescData
}

var file_api_cx_watson_watson_client_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_cx_watson_watson_client_proto_goTypes = []interface{}{
	(*IsIncidentValidRequest)(nil),     // 0: cx.watson.IsIncidentValidRequest
	(*IsIncidentValidResponse)(nil),    // 1: cx.watson.IsIncidentValidResponse
	(*GetTicketDetailsRequest)(nil),    // 2: cx.watson.GetTicketDetailsRequest
	(*GetTicketDetailsResponse)(nil),   // 3: cx.watson.GetTicketDetailsResponse
	(*IsIncidentResolvedRequest)(nil),  // 4: cx.watson.IsIncidentResolvedRequest
	(*IsIncidentResolvedResponse)(nil), // 5: cx.watson.IsIncidentResolvedResponse
	(*GetCommsDetailsRequest)(nil),     // 6: cx.watson.GetCommsDetailsRequest
	(*CommsDetail)(nil),                // 7: cx.watson.CommsDetail
	(*GetCommsDetailsResponse)(nil),    // 8: cx.watson.GetCommsDetailsResponse
	(*IncidentDetailsForClient)(nil),   // 9: cx.watson.IncidentDetailsForClient
	(*rpc.Status)(nil),                 // 10: rpc.Status
	(GetTicketDetailsActionType)(0),    // 11: cx.watson.GetTicketDetailsActionType
	(*TicketDetails)(nil),              // 12: cx.watson.TicketDetails
	(*CommsMetadataForClient)(nil),     // 13: cx.watson.CommsMetadataForClient
	(CommsType)(0),                     // 14: cx.watson.CommsType
	(*comms.SMSMessage)(nil),           // 15: comms.SMSMessage
	(*comms.EmailMessage)(nil),         // 16: comms.EmailMessage
	(*comms.NotificationMessage)(nil),  // 17: comms.NotificationMessage
	(*comms.WhatsappMessage)(nil),      // 18: comms.WhatsappMessage
}
var file_api_cx_watson_watson_client_proto_depIdxs = []int32{
	9,  // 0: cx.watson.IsIncidentValidRequest.incident:type_name -> cx.watson.IncidentDetailsForClient
	10, // 1: cx.watson.IsIncidentValidResponse.status:type_name -> rpc.Status
	9,  // 2: cx.watson.GetTicketDetailsRequest.incident:type_name -> cx.watson.IncidentDetailsForClient
	11, // 3: cx.watson.GetTicketDetailsRequest.action_type:type_name -> cx.watson.GetTicketDetailsActionType
	10, // 4: cx.watson.GetTicketDetailsResponse.status:type_name -> rpc.Status
	12, // 5: cx.watson.GetTicketDetailsResponse.ticket_details:type_name -> cx.watson.TicketDetails
	9,  // 6: cx.watson.IsIncidentResolvedRequest.incident:type_name -> cx.watson.IncidentDetailsForClient
	10, // 7: cx.watson.IsIncidentResolvedResponse.status:type_name -> rpc.Status
	9,  // 8: cx.watson.GetCommsDetailsRequest.incident:type_name -> cx.watson.IncidentDetailsForClient
	13, // 9: cx.watson.GetCommsDetailsRequest.comms_metadata:type_name -> cx.watson.CommsMetadataForClient
	14, // 10: cx.watson.GetCommsDetailsRequest.comms_type:type_name -> cx.watson.CommsType
	15, // 11: cx.watson.CommsDetail.sms:type_name -> comms.SMSMessage
	16, // 12: cx.watson.CommsDetail.email:type_name -> comms.EmailMessage
	17, // 13: cx.watson.CommsDetail.notification:type_name -> comms.NotificationMessage
	18, // 14: cx.watson.CommsDetail.whatsapp:type_name -> comms.WhatsappMessage
	10, // 15: cx.watson.GetCommsDetailsResponse.status:type_name -> rpc.Status
	7,  // 16: cx.watson.GetCommsDetailsResponse.comms_details:type_name -> cx.watson.CommsDetail
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_api_cx_watson_watson_client_proto_init() }
func file_api_cx_watson_watson_client_proto_init() {
	if File_api_cx_watson_watson_client_proto != nil {
		return
	}
	file_api_cx_watson_enums_proto_init()
	file_api_cx_watson_watson_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_cx_watson_watson_client_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsIncidentValidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_watson_watson_client_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsIncidentValidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_watson_watson_client_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTicketDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_watson_watson_client_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTicketDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_watson_watson_client_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsIncidentResolvedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_watson_watson_client_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsIncidentResolvedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_watson_watson_client_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommsDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_watson_watson_client_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommsDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_watson_watson_client_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommsDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_cx_watson_watson_client_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*CommsDetail_Sms)(nil),
		(*CommsDetail_Email)(nil),
		(*CommsDetail_Notification)(nil),
		(*CommsDetail_Whatsapp)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_watson_watson_client_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_watson_watson_client_proto_goTypes,
		DependencyIndexes: file_api_cx_watson_watson_client_proto_depIdxs,
		MessageInfos:      file_api_cx_watson_watson_client_proto_msgTypes,
	}.Build()
	File_api_cx_watson_watson_client_proto = out.File
	file_api_cx_watson_watson_client_proto_rawDesc = nil
	file_api_cx_watson_watson_client_proto_goTypes = nil
	file_api_cx_watson_watson_client_proto_depIdxs = nil
}
