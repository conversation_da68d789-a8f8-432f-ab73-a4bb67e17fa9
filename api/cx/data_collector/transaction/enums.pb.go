// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/data_collector/transaction/enums.proto

package transaction

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Replicate Order side payment protocol
// PaymentProtocol defines list of protocols at a bank through which a transaction can be sent
type PaymentProtocol int32

const (
	PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED PaymentProtocol = 0
	PaymentProtocol_INTRA_BANK                   PaymentProtocol = 1
	PaymentProtocol_NEFT                         PaymentProtocol = 2
	PaymentProtocol_IMPS                         PaymentProtocol = 3
	PaymentProtocol_RTGS                         PaymentProtocol = 4
	PaymentProtocol_UPI                          PaymentProtocol = 5
	PaymentProtocol_CARD                         PaymentProtocol = 6
	PaymentProtocol_CREDIT_CARD                  PaymentProtocol = 7
	// RBI is considering replacement of the existing system of settlement of payment on the basis of physical cheques by a new procedure called “ Cheque Truncation System” (CTS).
	// It is an online image-based cheque clearing system where cheque images and Magnetic Ink Character Recognition (MICR) data are captured at the collecting  bank branch and transmitted electronically eliminating the actual cheque movement.
	PaymentProtocol_CTS   PaymentProtocol = 8
	PaymentProtocol_SWIFT PaymentProtocol = 9
	PaymentProtocol_ENACH PaymentProtocol = 10
)

// Enum value maps for PaymentProtocol.
var (
	PaymentProtocol_name = map[int32]string{
		0:  "PAYMENT_PROTOCOL_UNSPECIFIED",
		1:  "INTRA_BANK",
		2:  "NEFT",
		3:  "IMPS",
		4:  "RTGS",
		5:  "UPI",
		6:  "CARD",
		7:  "CREDIT_CARD",
		8:  "CTS",
		9:  "SWIFT",
		10: "ENACH",
	}
	PaymentProtocol_value = map[string]int32{
		"PAYMENT_PROTOCOL_UNSPECIFIED": 0,
		"INTRA_BANK":                   1,
		"NEFT":                         2,
		"IMPS":                         3,
		"RTGS":                         4,
		"UPI":                          5,
		"CARD":                         6,
		"CREDIT_CARD":                  7,
		"CTS":                          8,
		"SWIFT":                        9,
		"ENACH":                        10,
	}
)

func (x PaymentProtocol) Enum() *PaymentProtocol {
	p := new(PaymentProtocol)
	*p = x
	return p
}

func (x PaymentProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_data_collector_transaction_enums_proto_enumTypes[0].Descriptor()
}

func (PaymentProtocol) Type() protoreflect.EnumType {
	return &file_api_cx_data_collector_transaction_enums_proto_enumTypes[0]
}

func (x PaymentProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentProtocol.Descriptor instead.
func (PaymentProtocol) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_data_collector_transaction_enums_proto_rawDescGZIP(), []int{0}
}

var File_api_cx_data_collector_transaction_enums_proto protoreflect.FileDescriptor

var file_api_cx_data_collector_transaction_enums_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1d, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2a, 0xa4,
	0x01, 0x0a, 0x0f, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52,
	0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x4e, 0x54, 0x52, 0x41, 0x5f, 0x42, 0x41,
	0x4e, 0x4b, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x45, 0x46, 0x54, 0x10, 0x02, 0x12, 0x08,
	0x0a, 0x04, 0x49, 0x4d, 0x50, 0x53, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x52, 0x54, 0x47, 0x53,
	0x10, 0x04, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x50, 0x49, 0x10, 0x05, 0x12, 0x08, 0x0a, 0x04, 0x43,
	0x41, 0x52, 0x44, 0x10, 0x06, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x10, 0x07, 0x12, 0x07, 0x0a, 0x03, 0x43, 0x54, 0x53, 0x10, 0x08, 0x12,
	0x09, 0x0a, 0x05, 0x53, 0x57, 0x49, 0x46, 0x54, 0x10, 0x09, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4e,
	0x41, 0x43, 0x48, 0x10, 0x0a, 0x42, 0x74, 0x0a, 0x38, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78,
	0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_cx_data_collector_transaction_enums_proto_rawDescOnce sync.Once
	file_api_cx_data_collector_transaction_enums_proto_rawDescData = file_api_cx_data_collector_transaction_enums_proto_rawDesc
)

func file_api_cx_data_collector_transaction_enums_proto_rawDescGZIP() []byte {
	file_api_cx_data_collector_transaction_enums_proto_rawDescOnce.Do(func() {
		file_api_cx_data_collector_transaction_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_data_collector_transaction_enums_proto_rawDescData)
	})
	return file_api_cx_data_collector_transaction_enums_proto_rawDescData
}

var file_api_cx_data_collector_transaction_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_cx_data_collector_transaction_enums_proto_goTypes = []interface{}{
	(PaymentProtocol)(0), // 0: cx.data_collector.transaction.PaymentProtocol
}
var file_api_cx_data_collector_transaction_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_cx_data_collector_transaction_enums_proto_init() }
func file_api_cx_data_collector_transaction_enums_proto_init() {
	if File_api_cx_data_collector_transaction_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_data_collector_transaction_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_data_collector_transaction_enums_proto_goTypes,
		DependencyIndexes: file_api_cx_data_collector_transaction_enums_proto_depIdxs,
		EnumInfos:         file_api_cx_data_collector_transaction_enums_proto_enumTypes,
	}.Build()
	File_api_cx_data_collector_transaction_enums_proto = out.File
	file_api_cx_data_collector_transaction_enums_proto_rawDesc = nil
	file_api_cx_data_collector_transaction_enums_proto_goTypes = nil
	file_api_cx_data_collector_transaction_enums_proto_depIdxs = nil
}
