// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/dispute/tree.proto

package dispute

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TreeNode with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TreeNode) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TreeNode with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TreeNodeMultiError, or nil
// if none found.
func (m *TreeNode) ValidateAll() error {
	return m.validate(true)
}

func (m *TreeNode) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for NodeType

	// no validation rules for EdgeType

	{
		sorted_keys := make([]string, len(m.GetChildList()))
		i := 0
		for key := range m.GetChildList() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetChildList()[key]
			_ = val

			// no validation rules for ChildList[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, TreeNodeValidationError{
							field:  fmt.Sprintf("ChildList[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, TreeNodeValidationError{
							field:  fmt.Sprintf("ChildList[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return TreeNodeValidationError{
						field:  fmt.Sprintf("ChildList[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetChild()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TreeNodeValidationError{
					field:  "Child",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TreeNodeValidationError{
					field:  "Child",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChild()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TreeNodeValidationError{
				field:  "Child",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsChildOptional

	// no validation rules for ChildOptionalFlagMapping

	// no validation rules for SftpDisputeType

	// no validation rules for SftpDisputeTypeMapping

	// no validation rules for ProductCategoryMapping

	// no validation rules for ProductCategoryDetailsMapping

	// no validation rules for SubcategoryMapping

	switch v := m.Meta.(type) {
	case *TreeNode_QuestionMeta:
		if v == nil {
			err := TreeNodeValidationError{
				field:  "Meta",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetQuestionMeta()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TreeNodeValidationError{
						field:  "QuestionMeta",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TreeNodeValidationError{
						field:  "QuestionMeta",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetQuestionMeta()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TreeNodeValidationError{
					field:  "QuestionMeta",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *TreeNode_ChannelMeta:
		if v == nil {
			err := TreeNodeValidationError{
				field:  "Meta",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetChannelMeta()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TreeNodeValidationError{
						field:  "ChannelMeta",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TreeNodeValidationError{
						field:  "ChannelMeta",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetChannelMeta()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TreeNodeValidationError{
					field:  "ChannelMeta",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return TreeNodeMultiError(errors)
	}

	return nil
}

// TreeNodeMultiError is an error wrapping multiple validation errors returned
// by TreeNode.ValidateAll() if the designated constraints aren't met.
type TreeNodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TreeNodeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TreeNodeMultiError) AllErrors() []error { return m }

// TreeNodeValidationError is the validation error returned by
// TreeNode.Validate if the designated constraints aren't met.
type TreeNodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TreeNodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TreeNodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TreeNodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TreeNodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TreeNodeValidationError) ErrorName() string { return "TreeNodeValidationError" }

// Error satisfies the builtin error interface
func (e TreeNodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTreeNode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TreeNodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TreeNodeValidationError{}

// Validate checks the field values on QuestionMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QuestionMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionMeta with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QuestionMetaMultiError, or
// nil if none found.
func (m *QuestionMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Channel

	// no validation rules for QuestionCode

	// no validation rules for ActualQuestion

	// no validation rules for AnswerDataType

	// no validation rules for IsOptional

	// no validation rules for PlaceholderText

	// no validation rules for ExpectedAnswerDataType

	if len(errors) > 0 {
		return QuestionMetaMultiError(errors)
	}

	return nil
}

// QuestionMetaMultiError is an error wrapping multiple validation errors
// returned by QuestionMeta.ValidateAll() if the designated constraints aren't met.
type QuestionMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionMetaMultiError) AllErrors() []error { return m }

// QuestionMetaValidationError is the validation error returned by
// QuestionMeta.Validate if the designated constraints aren't met.
type QuestionMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionMetaValidationError) ErrorName() string { return "QuestionMetaValidationError" }

// Error satisfies the builtin error interface
func (e QuestionMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionMetaValidationError{}

// Validate checks the field values on ChannelMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ChannelMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChannelMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ChannelMetaMultiError, or
// nil if none found.
func (m *ChannelMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *ChannelMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Channel

	if len(errors) > 0 {
		return ChannelMetaMultiError(errors)
	}

	return nil
}

// ChannelMetaMultiError is an error wrapping multiple validation errors
// returned by ChannelMeta.ValidateAll() if the designated constraints aren't met.
type ChannelMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChannelMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChannelMetaMultiError) AllErrors() []error { return m }

// ChannelMetaValidationError is the validation error returned by
// ChannelMeta.Validate if the designated constraints aren't met.
type ChannelMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChannelMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChannelMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChannelMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChannelMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChannelMetaValidationError) ErrorName() string { return "ChannelMetaValidationError" }

// Error satisfies the builtin error interface
func (e ChannelMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChannelMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChannelMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChannelMetaValidationError{}

// Validate checks the field values on QuestionnaireResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionnaireResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionnaireResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionnaireResultMultiError, or nil if none found.
func (m *QuestionnaireResult) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionnaireResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResultList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QuestionnaireResultValidationError{
						field:  fmt.Sprintf("ResultList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QuestionnaireResultValidationError{
						field:  fmt.Sprintf("ResultList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QuestionnaireResultValidationError{
					field:  fmt.Sprintf("ResultList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SftpDisputeType

	// no validation rules for ProductCategory

	// no validation rules for ProductCategoryDetails

	// no validation rules for Subcategory

	if len(errors) > 0 {
		return QuestionnaireResultMultiError(errors)
	}

	return nil
}

// QuestionnaireResultMultiError is an error wrapping multiple validation
// errors returned by QuestionnaireResult.ValidateAll() if the designated
// constraints aren't met.
type QuestionnaireResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionnaireResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionnaireResultMultiError) AllErrors() []error { return m }

// QuestionnaireResultValidationError is the validation error returned by
// QuestionnaireResult.Validate if the designated constraints aren't met.
type QuestionnaireResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionnaireResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionnaireResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionnaireResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionnaireResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionnaireResultValidationError) ErrorName() string {
	return "QuestionnaireResultValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionnaireResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionnaireResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionnaireResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionnaireResultValidationError{}

// Validate checks the field values on QuestionnaireResultResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionnaireResultResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionnaireResultResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionnaireResultResultMultiError, or nil if none found.
func (m *QuestionnaireResultResult) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionnaireResultResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for QuestionCode

	// no validation rules for Question

	// no validation rules for Answer

	// no validation rules for AnswerDataType

	// no validation rules for ExpectedAnswerDataType

	if len(errors) > 0 {
		return QuestionnaireResultResultMultiError(errors)
	}

	return nil
}

// QuestionnaireResultResultMultiError is an error wrapping multiple validation
// errors returned by QuestionnaireResultResult.ValidateAll() if the
// designated constraints aren't met.
type QuestionnaireResultResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionnaireResultResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionnaireResultResultMultiError) AllErrors() []error { return m }

// QuestionnaireResultResultValidationError is the validation error returned by
// QuestionnaireResultResult.Validate if the designated constraints aren't met.
type QuestionnaireResultResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionnaireResultResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionnaireResultResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionnaireResultResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionnaireResultResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionnaireResultResultValidationError) ErrorName() string {
	return "QuestionnaireResultResultValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionnaireResultResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionnaireResultResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionnaireResultResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionnaireResultResultValidationError{}
