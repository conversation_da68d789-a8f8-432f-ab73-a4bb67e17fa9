// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/crm_issue_tracker_integration/service.proto

package crm_issue_tracker_integration

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateIssueTrackerTicketRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateIssueTrackerTicketRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateIssueTrackerTicketRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateIssueTrackerTicketRequestMultiError, or nil if none found.
func (m *CreateIssueTrackerTicketRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateIssueTrackerTicketRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CrmTool

	if all {
		switch v := interface{}(m.GetCrmTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateIssueTrackerTicketRequestValidationError{
					field:  "CrmTicket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateIssueTrackerTicketRequestValidationError{
					field:  "CrmTicket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCrmTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateIssueTrackerTicketRequestValidationError{
				field:  "CrmTicket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IssueTrackerTool

	if len(errors) > 0 {
		return CreateIssueTrackerTicketRequestMultiError(errors)
	}

	return nil
}

// CreateIssueTrackerTicketRequestMultiError is an error wrapping multiple
// validation errors returned by CreateIssueTrackerTicketRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateIssueTrackerTicketRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateIssueTrackerTicketRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateIssueTrackerTicketRequestMultiError) AllErrors() []error { return m }

// CreateIssueTrackerTicketRequestValidationError is the validation error
// returned by CreateIssueTrackerTicketRequest.Validate if the designated
// constraints aren't met.
type CreateIssueTrackerTicketRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateIssueTrackerTicketRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateIssueTrackerTicketRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateIssueTrackerTicketRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateIssueTrackerTicketRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateIssueTrackerTicketRequestValidationError) ErrorName() string {
	return "CreateIssueTrackerTicketRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateIssueTrackerTicketRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateIssueTrackerTicketRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateIssueTrackerTicketRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateIssueTrackerTicketRequestValidationError{}

// Validate checks the field values on CreateIssueTrackerTicketResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateIssueTrackerTicketResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateIssueTrackerTicketResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateIssueTrackerTicketResponseMultiError, or nil if none found.
func (m *CreateIssueTrackerTicketResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateIssueTrackerTicketResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateIssueTrackerTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateIssueTrackerTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateIssueTrackerTicketResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIssueTrackerTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateIssueTrackerTicketResponseValidationError{
					field:  "IssueTrackerTicket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateIssueTrackerTicketResponseValidationError{
					field:  "IssueTrackerTicket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIssueTrackerTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateIssueTrackerTicketResponseValidationError{
				field:  "IssueTrackerTicket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateIssueTrackerTicketResponseMultiError(errors)
	}

	return nil
}

// CreateIssueTrackerTicketResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateIssueTrackerTicketResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateIssueTrackerTicketResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateIssueTrackerTicketResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateIssueTrackerTicketResponseMultiError) AllErrors() []error { return m }

// CreateIssueTrackerTicketResponseValidationError is the validation error
// returned by CreateIssueTrackerTicketResponse.Validate if the designated
// constraints aren't met.
type CreateIssueTrackerTicketResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateIssueTrackerTicketResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateIssueTrackerTicketResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateIssueTrackerTicketResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateIssueTrackerTicketResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateIssueTrackerTicketResponseValidationError) ErrorName() string {
	return "CreateIssueTrackerTicketResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateIssueTrackerTicketResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateIssueTrackerTicketResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateIssueTrackerTicketResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateIssueTrackerTicketResponseValidationError{}

// Validate checks the field values on UpdateIssueTrackerTicketRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateIssueTrackerTicketRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateIssueTrackerTicketRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateIssueTrackerTicketRequestMultiError, or nil if none found.
func (m *UpdateIssueTrackerTicketRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateIssueTrackerTicketRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CrmTool

	if all {
		switch v := interface{}(m.GetCrmTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateIssueTrackerTicketRequestValidationError{
					field:  "CrmTicket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateIssueTrackerTicketRequestValidationError{
					field:  "CrmTicket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCrmTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateIssueTrackerTicketRequestValidationError{
				field:  "CrmTicket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IssueTrackerTool

	// no validation rules for Note

	if len(errors) > 0 {
		return UpdateIssueTrackerTicketRequestMultiError(errors)
	}

	return nil
}

// UpdateIssueTrackerTicketRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateIssueTrackerTicketRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateIssueTrackerTicketRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateIssueTrackerTicketRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateIssueTrackerTicketRequestMultiError) AllErrors() []error { return m }

// UpdateIssueTrackerTicketRequestValidationError is the validation error
// returned by UpdateIssueTrackerTicketRequest.Validate if the designated
// constraints aren't met.
type UpdateIssueTrackerTicketRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateIssueTrackerTicketRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateIssueTrackerTicketRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateIssueTrackerTicketRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateIssueTrackerTicketRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateIssueTrackerTicketRequestValidationError) ErrorName() string {
	return "UpdateIssueTrackerTicketRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateIssueTrackerTicketRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateIssueTrackerTicketRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateIssueTrackerTicketRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateIssueTrackerTicketRequestValidationError{}

// Validate checks the field values on UpdateIssueTrackerTicketResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateIssueTrackerTicketResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateIssueTrackerTicketResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateIssueTrackerTicketResponseMultiError, or nil if none found.
func (m *UpdateIssueTrackerTicketResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateIssueTrackerTicketResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateIssueTrackerTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateIssueTrackerTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateIssueTrackerTicketResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateIssueTrackerTicketResponseMultiError(errors)
	}

	return nil
}

// UpdateIssueTrackerTicketResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateIssueTrackerTicketResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateIssueTrackerTicketResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateIssueTrackerTicketResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateIssueTrackerTicketResponseMultiError) AllErrors() []error { return m }

// UpdateIssueTrackerTicketResponseValidationError is the validation error
// returned by UpdateIssueTrackerTicketResponse.Validate if the designated
// constraints aren't met.
type UpdateIssueTrackerTicketResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateIssueTrackerTicketResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateIssueTrackerTicketResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateIssueTrackerTicketResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateIssueTrackerTicketResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateIssueTrackerTicketResponseValidationError) ErrorName() string {
	return "UpdateIssueTrackerTicketResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateIssueTrackerTicketResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateIssueTrackerTicketResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateIssueTrackerTicketResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateIssueTrackerTicketResponseValidationError{}

// Validate checks the field values on UpdateCrmTicketRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCrmTicketRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCrmTicketRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCrmTicketRequestMultiError, or nil if none found.
func (m *UpdateCrmTicketRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCrmTicketRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IssueTrackerTool

	if all {
		switch v := interface{}(m.GetIssueTrackerTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCrmTicketRequestValidationError{
					field:  "IssueTrackerTicket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCrmTicketRequestValidationError{
					field:  "IssueTrackerTicket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIssueTrackerTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCrmTicketRequestValidationError{
				field:  "IssueTrackerTicket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CrmTool

	// no validation rules for Note

	// no validation rules for IsTicketAttributeUpdate

	if len(errors) > 0 {
		return UpdateCrmTicketRequestMultiError(errors)
	}

	return nil
}

// UpdateCrmTicketRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateCrmTicketRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateCrmTicketRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCrmTicketRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCrmTicketRequestMultiError) AllErrors() []error { return m }

// UpdateCrmTicketRequestValidationError is the validation error returned by
// UpdateCrmTicketRequest.Validate if the designated constraints aren't met.
type UpdateCrmTicketRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCrmTicketRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCrmTicketRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCrmTicketRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCrmTicketRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCrmTicketRequestValidationError) ErrorName() string {
	return "UpdateCrmTicketRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCrmTicketRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCrmTicketRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCrmTicketRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCrmTicketRequestValidationError{}

// Validate checks the field values on UpdateCrmTicketResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCrmTicketResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCrmTicketResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCrmTicketResponseMultiError, or nil if none found.
func (m *UpdateCrmTicketResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCrmTicketResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCrmTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCrmTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCrmTicketResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateCrmTicketResponseMultiError(errors)
	}

	return nil
}

// UpdateCrmTicketResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateCrmTicketResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateCrmTicketResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCrmTicketResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCrmTicketResponseMultiError) AllErrors() []error { return m }

// UpdateCrmTicketResponseValidationError is the validation error returned by
// UpdateCrmTicketResponse.Validate if the designated constraints aren't met.
type UpdateCrmTicketResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCrmTicketResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCrmTicketResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCrmTicketResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCrmTicketResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCrmTicketResponseValidationError) ErrorName() string {
	return "UpdateCrmTicketResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCrmTicketResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCrmTicketResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCrmTicketResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCrmTicketResponseValidationError{}

// Validate checks the field values on
// AppendCrmTicketToIssueTrackerTicketRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AppendCrmTicketToIssueTrackerTicketRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AppendCrmTicketToIssueTrackerTicketRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// AppendCrmTicketToIssueTrackerTicketRequestMultiError, or nil if none found.
func (m *AppendCrmTicketToIssueTrackerTicketRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AppendCrmTicketToIssueTrackerTicketRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CrmTool

	if all {
		switch v := interface{}(m.GetCrmTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AppendCrmTicketToIssueTrackerTicketRequestValidationError{
					field:  "CrmTicket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AppendCrmTicketToIssueTrackerTicketRequestValidationError{
					field:  "CrmTicket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCrmTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AppendCrmTicketToIssueTrackerTicketRequestValidationError{
				field:  "CrmTicket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IssueTrackerTool

	if len(errors) > 0 {
		return AppendCrmTicketToIssueTrackerTicketRequestMultiError(errors)
	}

	return nil
}

// AppendCrmTicketToIssueTrackerTicketRequestMultiError is an error wrapping
// multiple validation errors returned by
// AppendCrmTicketToIssueTrackerTicketRequest.ValidateAll() if the designated
// constraints aren't met.
type AppendCrmTicketToIssueTrackerTicketRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppendCrmTicketToIssueTrackerTicketRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppendCrmTicketToIssueTrackerTicketRequestMultiError) AllErrors() []error { return m }

// AppendCrmTicketToIssueTrackerTicketRequestValidationError is the validation
// error returned by AppendCrmTicketToIssueTrackerTicketRequest.Validate if
// the designated constraints aren't met.
type AppendCrmTicketToIssueTrackerTicketRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppendCrmTicketToIssueTrackerTicketRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppendCrmTicketToIssueTrackerTicketRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppendCrmTicketToIssueTrackerTicketRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppendCrmTicketToIssueTrackerTicketRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppendCrmTicketToIssueTrackerTicketRequestValidationError) ErrorName() string {
	return "AppendCrmTicketToIssueTrackerTicketRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AppendCrmTicketToIssueTrackerTicketRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppendCrmTicketToIssueTrackerTicketRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppendCrmTicketToIssueTrackerTicketRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppendCrmTicketToIssueTrackerTicketRequestValidationError{}

// Validate checks the field values on
// AppendCrmTicketToIssueTrackerTicketResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AppendCrmTicketToIssueTrackerTicketResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AppendCrmTicketToIssueTrackerTicketResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// AppendCrmTicketToIssueTrackerTicketResponseMultiError, or nil if none found.
func (m *AppendCrmTicketToIssueTrackerTicketResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AppendCrmTicketToIssueTrackerTicketResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AppendCrmTicketToIssueTrackerTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AppendCrmTicketToIssueTrackerTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AppendCrmTicketToIssueTrackerTicketResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AppendCrmTicketToIssueTrackerTicketResponseMultiError(errors)
	}

	return nil
}

// AppendCrmTicketToIssueTrackerTicketResponseMultiError is an error wrapping
// multiple validation errors returned by
// AppendCrmTicketToIssueTrackerTicketResponse.ValidateAll() if the designated
// constraints aren't met.
type AppendCrmTicketToIssueTrackerTicketResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppendCrmTicketToIssueTrackerTicketResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppendCrmTicketToIssueTrackerTicketResponseMultiError) AllErrors() []error { return m }

// AppendCrmTicketToIssueTrackerTicketResponseValidationError is the validation
// error returned by AppendCrmTicketToIssueTrackerTicketResponse.Validate if
// the designated constraints aren't met.
type AppendCrmTicketToIssueTrackerTicketResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppendCrmTicketToIssueTrackerTicketResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppendCrmTicketToIssueTrackerTicketResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppendCrmTicketToIssueTrackerTicketResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppendCrmTicketToIssueTrackerTicketResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppendCrmTicketToIssueTrackerTicketResponseValidationError) ErrorName() string {
	return "AppendCrmTicketToIssueTrackerTicketResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AppendCrmTicketToIssueTrackerTicketResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppendCrmTicketToIssueTrackerTicketResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppendCrmTicketToIssueTrackerTicketResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppendCrmTicketToIssueTrackerTicketResponseValidationError{}
