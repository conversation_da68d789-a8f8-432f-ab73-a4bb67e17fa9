// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/ticket/service.proto

package ticket

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	cx "github.com/epifi/gamma/api/cx"
	webui "github.com/epifi/gamma/api/typesv2/webui"
	freshdesk "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetMergedTicketsResponse_Status int32

const (
	// successfully returned ticket ids of merged tickets
	GetMergedTicketsResponse_OK GetMergedTicketsResponse_Status = 0
	// if there are no tickets merged
	GetMergedTicketsResponse_RECORD_NOT_FOUND GetMergedTicketsResponse_Status = 5
	// system faced internal errors while processing the request
	GetMergedTicketsResponse_INTERNAL GetMergedTicketsResponse_Status = 13
)

// Enum value maps for GetMergedTicketsResponse_Status.
var (
	GetMergedTicketsResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetMergedTicketsResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetMergedTicketsResponse_Status) Enum() *GetMergedTicketsResponse_Status {
	p := new(GetMergedTicketsResponse_Status)
	*p = x
	return p
}

func (x GetMergedTicketsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetMergedTicketsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_ticket_service_proto_enumTypes[0].Descriptor()
}

func (GetMergedTicketsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_cx_ticket_service_proto_enumTypes[0]
}

func (x GetMergedTicketsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetMergedTicketsResponse_Status.Descriptor instead.
func (GetMergedTicketsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{60, 0}
}

type FetchLatestResolvedTicketIdForCSATRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *FetchLatestResolvedTicketIdForCSATRequest) Reset() {
	*x = FetchLatestResolvedTicketIdForCSATRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchLatestResolvedTicketIdForCSATRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchLatestResolvedTicketIdForCSATRequest) ProtoMessage() {}

func (x *FetchLatestResolvedTicketIdForCSATRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchLatestResolvedTicketIdForCSATRequest.ProtoReflect.Descriptor instead.
func (*FetchLatestResolvedTicketIdForCSATRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{0}
}

func (x *FetchLatestResolvedTicketIdForCSATRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type FetchLatestResolvedTicketIdForCSATResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TicketId int64       `protobuf:"varint,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
}

func (x *FetchLatestResolvedTicketIdForCSATResponse) Reset() {
	*x = FetchLatestResolvedTicketIdForCSATResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchLatestResolvedTicketIdForCSATResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchLatestResolvedTicketIdForCSATResponse) ProtoMessage() {}

func (x *FetchLatestResolvedTicketIdForCSATResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchLatestResolvedTicketIdForCSATResponse.ProtoReflect.Descriptor instead.
func (*FetchLatestResolvedTicketIdForCSATResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{1}
}

func (x *FetchLatestResolvedTicketIdForCSATResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchLatestResolvedTicketIdForCSATResponse) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

type GetCategoryTransformationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Ticket *Ticket    `protobuf:"bytes,2,opt,name=ticket,proto3" json:"ticket,omitempty"`
}

func (x *GetCategoryTransformationsRequest) Reset() {
	*x = GetCategoryTransformationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCategoryTransformationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCategoryTransformationsRequest) ProtoMessage() {}

func (x *GetCategoryTransformationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCategoryTransformationsRequest.ProtoReflect.Descriptor instead.
func (*GetCategoryTransformationsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetCategoryTransformationsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetCategoryTransformationsRequest) GetTicket() *Ticket {
	if x != nil {
		return x.Ticket
	}
	return nil
}

type GetCategoryTransformationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Title fetched via ticket details transformations
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Description fetched via ticket details transformations
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *GetCategoryTransformationsResponse) Reset() {
	*x = GetCategoryTransformationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCategoryTransformationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCategoryTransformationsResponse) ProtoMessage() {}

func (x *GetCategoryTransformationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCategoryTransformationsResponse.ProtoReflect.Descriptor instead.
func (*GetCategoryTransformationsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetCategoryTransformationsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCategoryTransformationsResponse) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetCategoryTransformationsResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type SubmitCsatFeedbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// request identifier token will be provided by CX ticket service while triggering CSAT survey
	RequestIdentifierToken string `protobuf:"bytes,1,opt,name=request_identifier_token,json=requestIdentifierToken,proto3" json:"request_identifier_token,omitempty"`
	// csat score given by user
	Score int32 `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	// optional feedback that user can provide
	Feedback string `protobuf:"bytes,3,opt,name=feedback,proto3" json:"feedback,omitempty"`
}

func (x *SubmitCsatFeedbackRequest) Reset() {
	*x = SubmitCsatFeedbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitCsatFeedbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitCsatFeedbackRequest) ProtoMessage() {}

func (x *SubmitCsatFeedbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitCsatFeedbackRequest.ProtoReflect.Descriptor instead.
func (*SubmitCsatFeedbackRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{4}
}

func (x *SubmitCsatFeedbackRequest) GetRequestIdentifierToken() string {
	if x != nil {
		return x.RequestIdentifierToken
	}
	return ""
}

func (x *SubmitCsatFeedbackRequest) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *SubmitCsatFeedbackRequest) GetFeedback() string {
	if x != nil {
		return x.Feedback
	}
	return ""
}

type SubmitCsatFeedbackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SubmitCsatFeedbackResponse) Reset() {
	*x = SubmitCsatFeedbackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitCsatFeedbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitCsatFeedbackResponse) ProtoMessage() {}

func (x *SubmitCsatFeedbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitCsatFeedbackResponse.ProtoReflect.Descriptor instead.
func (*SubmitCsatFeedbackResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{5}
}

func (x *SubmitCsatFeedbackResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetTicketDetailsForSherlockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *GetTicketDetailsForSherlockRequest) Reset() {
	*x = GetTicketDetailsForSherlockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTicketDetailsForSherlockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicketDetailsForSherlockRequest) ProtoMessage() {}

func (x *GetTicketDetailsForSherlockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicketDetailsForSherlockRequest.ProtoReflect.Descriptor instead.
func (*GetTicketDetailsForSherlockRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetTicketDetailsForSherlockRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

type GetTicketDetailsForSherlockResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Figma: https://www.figma.com/design/mPO3o3O1axCViWDGWLOn6o/%E2%98%8E%EF%B8%8F-Sherlock-%E2%80%A2%C2%A0Workfile?node-id=5102-43493&t=x9pUkUamjNYAlkUw-0
	DetailViews []*webui.DetailView `protobuf:"bytes,2,rep,name=detail_views,json=detailViews,proto3" json:"detail_views,omitempty"`
	// Deprecated: Marked as deprecated in api/cx/ticket/service.proto.
	InfoComponentViews []*webui.InfoComponentView `protobuf:"bytes,3,rep,name=info_component_views,json=infoComponentViews,proto3" json:"info_component_views,omitempty"`
}

func (x *GetTicketDetailsForSherlockResponse) Reset() {
	*x = GetTicketDetailsForSherlockResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTicketDetailsForSherlockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicketDetailsForSherlockResponse) ProtoMessage() {}

func (x *GetTicketDetailsForSherlockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicketDetailsForSherlockResponse.ProtoReflect.Descriptor instead.
func (*GetTicketDetailsForSherlockResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetTicketDetailsForSherlockResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTicketDetailsForSherlockResponse) GetDetailViews() []*webui.DetailView {
	if x != nil {
		return x.DetailViews
	}
	return nil
}

// Deprecated: Marked as deprecated in api/cx/ticket/service.proto.
func (x *GetTicketDetailsForSherlockResponse) GetInfoComponentViews() []*webui.InfoComponentView {
	if x != nil {
		return x.InfoComponentViews
	}
	return nil
}

type GetAgentInstructionForTicketRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header              *cx.Header           `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	PrevInstructionType AgentInstructionType `protobuf:"varint,2,opt,name=prev_instruction_type,json=prevInstructionType,proto3,enum=cx.ticket.AgentInstructionType" json:"prev_instruction_type,omitempty"`
}

func (x *GetAgentInstructionForTicketRequest) Reset() {
	*x = GetAgentInstructionForTicketRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgentInstructionForTicketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentInstructionForTicketRequest) ProtoMessage() {}

func (x *GetAgentInstructionForTicketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentInstructionForTicketRequest.ProtoReflect.Descriptor instead.
func (*GetAgentInstructionForTicketRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetAgentInstructionForTicketRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetAgentInstructionForTicketRequest) GetPrevInstructionType() AgentInstructionType {
	if x != nil {
		return x.PrevInstructionType
	}
	return AgentInstructionType_AGENT_INSTRUCTION_TYPE_UNSPECIFIED
}

type GetAgentInstructionForTicketResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Figma: https://www.figma.com/design/mPO3o3O1axCViWDGWLOn6o/%E2%98%8E%EF%B8%8F-Sherlock-%E2%80%A2%C2%A0Workfile?node-id=5102-43493&t=x9pUkUamjNYAlkUw-0
	InstructionType AgentInstructionType `protobuf:"varint,2,opt,name=instruction_type,json=instructionType,proto3,enum=cx.ticket.AgentInstructionType" json:"instruction_type,omitempty"`
	// Types that are assignable to Instruction:
	//
	//	*GetAgentInstructionForTicketResponse_FinalAgentInstruction_
	//	*GetAgentInstructionForTicketResponse_AgentInstruction
	Instruction isGetAgentInstructionForTicketResponse_Instruction `protobuf_oneof:"instruction"`
}

func (x *GetAgentInstructionForTicketResponse) Reset() {
	*x = GetAgentInstructionForTicketResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgentInstructionForTicketResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentInstructionForTicketResponse) ProtoMessage() {}

func (x *GetAgentInstructionForTicketResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentInstructionForTicketResponse.ProtoReflect.Descriptor instead.
func (*GetAgentInstructionForTicketResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetAgentInstructionForTicketResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAgentInstructionForTicketResponse) GetInstructionType() AgentInstructionType {
	if x != nil {
		return x.InstructionType
	}
	return AgentInstructionType_AGENT_INSTRUCTION_TYPE_UNSPECIFIED
}

func (m *GetAgentInstructionForTicketResponse) GetInstruction() isGetAgentInstructionForTicketResponse_Instruction {
	if m != nil {
		return m.Instruction
	}
	return nil
}

func (x *GetAgentInstructionForTicketResponse) GetFinalAgentInstruction() *GetAgentInstructionForTicketResponse_FinalAgentInstruction {
	if x, ok := x.GetInstruction().(*GetAgentInstructionForTicketResponse_FinalAgentInstruction_); ok {
		return x.FinalAgentInstruction
	}
	return nil
}

func (x *GetAgentInstructionForTicketResponse) GetAgentInstruction() *webui.InfoComponentView {
	if x, ok := x.GetInstruction().(*GetAgentInstructionForTicketResponse_AgentInstruction); ok {
		return x.AgentInstruction
	}
	return nil
}

type isGetAgentInstructionForTicketResponse_Instruction interface {
	isGetAgentInstructionForTicketResponse_Instruction()
}

type GetAgentInstructionForTicketResponse_FinalAgentInstruction_ struct {
	// final agent instruction is to be populated when instruction type is FINAL INSTRUCTION
	// otherwise agent instruction is to be populated
	FinalAgentInstruction *GetAgentInstructionForTicketResponse_FinalAgentInstruction `protobuf:"bytes,3,opt,name=final_agent_instruction,json=finalAgentInstruction,proto3,oneof"`
}

type GetAgentInstructionForTicketResponse_AgentInstruction struct {
	AgentInstruction *webui.InfoComponentView `protobuf:"bytes,4,opt,name=agent_instruction,json=agentInstruction,proto3,oneof"`
}

func (*GetAgentInstructionForTicketResponse_FinalAgentInstruction_) isGetAgentInstructionForTicketResponse_Instruction() {
}

func (*GetAgentInstructionForTicketResponse_AgentInstruction) isGetAgentInstructionForTicketResponse_Instruction() {
}

type GetFreshdeskTicketCategoriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *GetFreshdeskTicketCategoriesRequest) Reset() {
	*x = GetFreshdeskTicketCategoriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFreshdeskTicketCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFreshdeskTicketCategoriesRequest) ProtoMessage() {}

func (x *GetFreshdeskTicketCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFreshdeskTicketCategoriesRequest.ProtoReflect.Descriptor instead.
func (*GetFreshdeskTicketCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetFreshdeskTicketCategoriesRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

type GetFreshdeskTicketCategoriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Nested message containing product category, product category details and subcategory list
	TicketCategories *TicketCategories `protobuf:"bytes,2,opt,name=ticket_categories,json=ticketCategories,proto3" json:"ticket_categories,omitempty"`
}

func (x *GetFreshdeskTicketCategoriesResponse) Reset() {
	*x = GetFreshdeskTicketCategoriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFreshdeskTicketCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFreshdeskTicketCategoriesResponse) ProtoMessage() {}

func (x *GetFreshdeskTicketCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFreshdeskTicketCategoriesResponse.ProtoReflect.Descriptor instead.
func (*GetFreshdeskTicketCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetFreshdeskTicketCategoriesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFreshdeskTicketCategoriesResponse) GetTicketCategories() *TicketCategories {
	if x != nil {
		return x.TicketCategories
	}
	return nil
}

type GetTicketInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *GetTicketInfoRequest) Reset() {
	*x = GetTicketInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTicketInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicketInfoRequest) ProtoMessage() {}

func (x *GetTicketInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicketInfoRequest.ProtoReflect.Descriptor instead.
func (*GetTicketInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetTicketInfoRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

type GetTicketInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// product category
	ProductCategory string `protobuf:"bytes,2,opt,name=product_category,json=productCategory,proto3" json:"product_category,omitempty"`
	// product category details
	ProductCategoryDetail string `protobuf:"bytes,3,opt,name=product_category_detail,json=productCategoryDetail,proto3" json:"product_category_detail,omitempty"`
	// subcategory
	Subcategory string `protobuf:"bytes,4,opt,name=subcategory,proto3" json:"subcategory,omitempty"`
	// sla mapped to (product category, product category details, subcategory) in ticket_details_transformations table
	Sla string `protobuf:"bytes,5,opt,name=sla,proto3" json:"sla,omitempty"`
	// expected resolution time as determined by sla config from support_tickets table
	ExpectedResolutionTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=expected_resolution_time,json=expectedResolutionTime,proto3" json:"expected_resolution_time,omitempty"`
	// escalation teams mapped to (product category, product category details, subcategory) in ticket_details_transformations table
	EscalationTeams []EscalationTeam `protobuf:"varint,7,rep,packed,name=escalation_teams,json=escalationTeams,proto3,enum=cx.ticket.EscalationTeam" json:"escalation_teams,omitempty"`
	// common note mapped to (product category, product category details, subcategory) in ticket_details_transformations table
	Note string `protobuf:"bytes,8,opt,name=note,proto3" json:"note,omitempty"`
	// guru link mapped to (product category, product category details, subcategory) in ticket_details_transformations table
	GuruLink string `protobuf:"bytes,9,opt,name=guru_link,json=guruLink,proto3" json:"guru_link,omitempty"`
	// is fcr mapped to (product category, product category details, subcategory) in ticket_details_transformations table
	IsFcr common.BooleanEnum `protobuf:"varint,10,opt,name=is_fcr,json=isFcr,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_fcr,omitempty"`
}

func (x *GetTicketInfoResponse) Reset() {
	*x = GetTicketInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTicketInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicketInfoResponse) ProtoMessage() {}

func (x *GetTicketInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicketInfoResponse.ProtoReflect.Descriptor instead.
func (*GetTicketInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetTicketInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTicketInfoResponse) GetProductCategory() string {
	if x != nil {
		return x.ProductCategory
	}
	return ""
}

func (x *GetTicketInfoResponse) GetProductCategoryDetail() string {
	if x != nil {
		return x.ProductCategoryDetail
	}
	return ""
}

func (x *GetTicketInfoResponse) GetSubcategory() string {
	if x != nil {
		return x.Subcategory
	}
	return ""
}

func (x *GetTicketInfoResponse) GetSla() string {
	if x != nil {
		return x.Sla
	}
	return ""
}

func (x *GetTicketInfoResponse) GetExpectedResolutionTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpectedResolutionTime
	}
	return nil
}

func (x *GetTicketInfoResponse) GetEscalationTeams() []EscalationTeam {
	if x != nil {
		return x.EscalationTeams
	}
	return nil
}

func (x *GetTicketInfoResponse) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *GetTicketInfoResponse) GetGuruLink() string {
	if x != nil {
		return x.GuruLink
	}
	return ""
}

func (x *GetTicketInfoResponse) GetIsFcr() common.BooleanEnum {
	if x != nil {
		return x.IsFcr
	}
	return common.BooleanEnum(0)
}

type UpdateTicketInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// product category in string format as it exists on freshdesk
	ProductCategory string `protobuf:"bytes,2,opt,name=product_category,json=productCategory,proto3" json:"product_category,omitempty"`
	// product category details in string format as it exists on freshdesk
	ProductCategoryDetails string `protobuf:"bytes,3,opt,name=product_category_details,json=productCategoryDetails,proto3" json:"product_category_details,omitempty"`
	// subcategory in string format as it exists on freshdesk
	Subcategory string `protobuf:"bytes,4,opt,name=subcategory,proto3" json:"subcategory,omitempty"`
}

func (x *UpdateTicketInfoRequest) Reset() {
	*x = UpdateTicketInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTicketInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTicketInfoRequest) ProtoMessage() {}

func (x *UpdateTicketInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTicketInfoRequest.ProtoReflect.Descriptor instead.
func (*UpdateTicketInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateTicketInfoRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpdateTicketInfoRequest) GetProductCategory() string {
	if x != nil {
		return x.ProductCategory
	}
	return ""
}

func (x *UpdateTicketInfoRequest) GetProductCategoryDetails() string {
	if x != nil {
		return x.ProductCategoryDetails
	}
	return ""
}

func (x *UpdateTicketInfoRequest) GetSubcategory() string {
	if x != nil {
		return x.Subcategory
	}
	return ""
}

type UpdateTicketInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateTicketInfoResponse) Reset() {
	*x = UpdateTicketInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTicketInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTicketInfoResponse) ProtoMessage() {}

func (x *UpdateTicketInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTicketInfoResponse.ProtoReflect.Descriptor instead.
func (*UpdateTicketInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateTicketInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type TicketCategories struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductCategoryChoices []*ProductCategoryChoice `protobuf:"bytes,1,rep,name=product_category_choices,json=productCategoryChoices,proto3" json:"product_category_choices,omitempty"`
}

func (x *TicketCategories) Reset() {
	*x = TicketCategories{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TicketCategories) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TicketCategories) ProtoMessage() {}

func (x *TicketCategories) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TicketCategories.ProtoReflect.Descriptor instead.
func (*TicketCategories) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{16}
}

func (x *TicketCategories) GetProductCategoryChoices() []*ProductCategoryChoice {
	if x != nil {
		return x.ProductCategoryChoices
	}
	return nil
}

type ProductCategoryChoice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductCategory               string                          `protobuf:"bytes,1,opt,name=product_category,json=productCategory,proto3" json:"product_category,omitempty"`
	ProductCategoryDetailsChoices []*ProductCategoryDetailsChoice `protobuf:"bytes,2,rep,name=product_category_details_choices,json=productCategoryDetailsChoices,proto3" json:"product_category_details_choices,omitempty"`
}

func (x *ProductCategoryChoice) Reset() {
	*x = ProductCategoryChoice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductCategoryChoice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductCategoryChoice) ProtoMessage() {}

func (x *ProductCategoryChoice) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductCategoryChoice.ProtoReflect.Descriptor instead.
func (*ProductCategoryChoice) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{17}
}

func (x *ProductCategoryChoice) GetProductCategory() string {
	if x != nil {
		return x.ProductCategory
	}
	return ""
}

func (x *ProductCategoryChoice) GetProductCategoryDetailsChoices() []*ProductCategoryDetailsChoice {
	if x != nil {
		return x.ProductCategoryDetailsChoices
	}
	return nil
}

type ProductCategoryDetailsChoice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductCategoryDetails string               `protobuf:"bytes,1,opt,name=product_category_details,json=productCategoryDetails,proto3" json:"product_category_details,omitempty"`
	SubcategoryChoices     []*SubcategoryChoice `protobuf:"bytes,2,rep,name=subcategory_choices,json=subcategoryChoices,proto3" json:"subcategory_choices,omitempty"`
}

func (x *ProductCategoryDetailsChoice) Reset() {
	*x = ProductCategoryDetailsChoice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductCategoryDetailsChoice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductCategoryDetailsChoice) ProtoMessage() {}

func (x *ProductCategoryDetailsChoice) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductCategoryDetailsChoice.ProtoReflect.Descriptor instead.
func (*ProductCategoryDetailsChoice) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{18}
}

func (x *ProductCategoryDetailsChoice) GetProductCategoryDetails() string {
	if x != nil {
		return x.ProductCategoryDetails
	}
	return ""
}

func (x *ProductCategoryDetailsChoice) GetSubcategoryChoices() []*SubcategoryChoice {
	if x != nil {
		return x.SubcategoryChoices
	}
	return nil
}

type SubcategoryChoice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subcategory string `protobuf:"bytes,1,opt,name=subcategory,proto3" json:"subcategory,omitempty"`
}

func (x *SubcategoryChoice) Reset() {
	*x = SubcategoryChoice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubcategoryChoice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubcategoryChoice) ProtoMessage() {}

func (x *SubcategoryChoice) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubcategoryChoice.ProtoReflect.Descriptor instead.
func (*SubcategoryChoice) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{19}
}

func (x *SubcategoryChoice) GetSubcategory() string {
	if x != nil {
		return x.Subcategory
	}
	return ""
}

type AttachEntityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email, access token and ticket id is mandatory in header
	Header     *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	EntityType EntityType `protobuf:"varint,2,opt,name=entity_type,json=entityType,proto3,enum=cx.ticket.EntityType" json:"entity_type,omitempty"`
	// Deprecated: Marked as deprecated in api/cx/ticket/service.proto.
	EntityId string `protobuf:"bytes,3,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// list of AttachEntityMeta which contains entity info which has to be attached to the ticket
	//
	// Deprecated: Marked as deprecated in api/cx/ticket/service.proto.
	AttachEntityMetaList []*AttachEntityMeta `protobuf:"bytes,4,rep,name=attach_entity_meta_list,json=attachEntityMetaList,proto3" json:"attach_entity_meta_list,omitempty"`
	// list of AttachEntityMeta which contains entity info which has to be attached to the ticket
	// as per current string validations there is a no way to add a validation if its a valid protojson
	// pattern based regex validation can be added. will add once we have a clarity on how this looks
	AttachEntityMetaListV2 []string `protobuf:"bytes,5,rep,name=attach_entity_meta_list_v2,json=attachEntityMetaListV2,proto3" json:"attach_entity_meta_list_v2,omitempty"`
}

func (x *AttachEntityRequest) Reset() {
	*x = AttachEntityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttachEntityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttachEntityRequest) ProtoMessage() {}

func (x *AttachEntityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttachEntityRequest.ProtoReflect.Descriptor instead.
func (*AttachEntityRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{20}
}

func (x *AttachEntityRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AttachEntityRequest) GetEntityType() EntityType {
	if x != nil {
		return x.EntityType
	}
	return EntityType_ENTITY_TYPE_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/cx/ticket/service.proto.
func (x *AttachEntityRequest) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/cx/ticket/service.proto.
func (x *AttachEntityRequest) GetAttachEntityMetaList() []*AttachEntityMeta {
	if x != nil {
		return x.AttachEntityMetaList
	}
	return nil
}

func (x *AttachEntityRequest) GetAttachEntityMetaListV2() []string {
	if x != nil {
		return x.AttachEntityMetaListV2
	}
	return nil
}

type AttachEntityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *AttachEntityResponse) Reset() {
	*x = AttachEntityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttachEntityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttachEntityResponse) ProtoMessage() {}

func (x *AttachEntityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttachEntityResponse.ProtoReflect.Descriptor instead.
func (*AttachEntityResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{21}
}

func (x *AttachEntityResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetRelatedTicketsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email, access token and ticket id is mandatory in header
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *GetRelatedTicketsRequest) Reset() {
	*x = GetRelatedTicketsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRelatedTicketsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRelatedTicketsRequest) ProtoMessage() {}

func (x *GetRelatedTicketsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRelatedTicketsRequest.ProtoReflect.Descriptor instead.
func (*GetRelatedTicketsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetRelatedTicketsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

type GetRelatedTicketsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Tickets []*freshdesk.Ticket `protobuf:"bytes,2,rep,name=tickets,proto3" json:"tickets,omitempty"`
}

func (x *GetRelatedTicketsResponse) Reset() {
	*x = GetRelatedTicketsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRelatedTicketsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRelatedTicketsResponse) ProtoMessage() {}

func (x *GetRelatedTicketsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRelatedTicketsResponse.ProtoReflect.Descriptor instead.
func (*GetRelatedTicketsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetRelatedTicketsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRelatedTicketsResponse) GetTickets() []*freshdesk.Ticket {
	if x != nil {
		return x.Tickets
	}
	return nil
}

type MergeTicketsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email, access token and ticket id is mandatory in header
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// id of ticket in which other tickets needs be merged
	PrimaryTicketId int64 `protobuf:"varint,2,opt,name=primary_ticket_id,json=primaryTicketId,proto3" json:"primary_ticket_id,omitempty"`
	// list of ticket id's which needs to be merged into the primary ticket
	SecondaryTicketIds  []int64 `protobuf:"varint,3,rep,packed,name=secondary_ticket_ids,json=secondaryTicketIds,proto3" json:"secondary_ticket_ids,omitempty"`
	PrimaryTicketNote   string  `protobuf:"bytes,4,opt,name=primary_ticket_note,json=primaryTicketNote,proto3" json:"primary_ticket_note,omitempty"`
	SecondaryTicketNote string  `protobuf:"bytes,5,opt,name=secondary_ticket_note,json=secondaryTicketNote,proto3" json:"secondary_ticket_note,omitempty"`
}

func (x *MergeTicketsRequest) Reset() {
	*x = MergeTicketsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeTicketsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeTicketsRequest) ProtoMessage() {}

func (x *MergeTicketsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeTicketsRequest.ProtoReflect.Descriptor instead.
func (*MergeTicketsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{24}
}

func (x *MergeTicketsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MergeTicketsRequest) GetPrimaryTicketId() int64 {
	if x != nil {
		return x.PrimaryTicketId
	}
	return 0
}

func (x *MergeTicketsRequest) GetSecondaryTicketIds() []int64 {
	if x != nil {
		return x.SecondaryTicketIds
	}
	return nil
}

func (x *MergeTicketsRequest) GetPrimaryTicketNote() string {
	if x != nil {
		return x.PrimaryTicketNote
	}
	return ""
}

func (x *MergeTicketsRequest) GetSecondaryTicketNote() string {
	if x != nil {
		return x.SecondaryTicketNote
	}
	return ""
}

type MergeTicketsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *MergeTicketsResponse) Reset() {
	*x = MergeTicketsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeTicketsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeTicketsResponse) ProtoMessage() {}

func (x *MergeTicketsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeTicketsResponse.ProtoReflect.Descriptor instead.
func (*MergeTicketsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{25}
}

func (x *MergeTicketsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetCallRecordingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email, access token and ticket id is mandatory in header
	Header      *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RecordingId string     `protobuf:"bytes,2,opt,name=recording_id,json=recordingId,proto3" json:"recording_id,omitempty"`
}

func (x *GetCallRecordingRequest) Reset() {
	*x = GetCallRecordingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCallRecordingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallRecordingRequest) ProtoMessage() {}

func (x *GetCallRecordingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallRecordingRequest.ProtoReflect.Descriptor instead.
func (*GetCallRecordingRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetCallRecordingRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetCallRecordingRequest) GetRecordingId() string {
	if x != nil {
		return x.RecordingId
	}
	return ""
}

type GetCallRecordingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// chunk of call recording
	Chunk []byte `protobuf:"bytes,2,opt,name=chunk,proto3" json:"chunk,omitempty"`
}

func (x *GetCallRecordingResponse) Reset() {
	*x = GetCallRecordingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCallRecordingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallRecordingResponse) ProtoMessage() {}

func (x *GetCallRecordingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallRecordingResponse.ProtoReflect.Descriptor instead.
func (*GetCallRecordingResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetCallRecordingResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCallRecordingResponse) GetChunk() []byte {
	if x != nil {
		return x.Chunk
	}
	return nil
}

type GetCallTranscriptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email, access token and ticket id is mandatory in header
	Header      *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RecordingId string     `protobuf:"bytes,2,opt,name=recording_id,json=recordingId,proto3" json:"recording_id,omitempty"`
}

func (x *GetCallTranscriptRequest) Reset() {
	*x = GetCallTranscriptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCallTranscriptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallTranscriptRequest) ProtoMessage() {}

func (x *GetCallTranscriptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallTranscriptRequest.ProtoReflect.Descriptor instead.
func (*GetCallTranscriptRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetCallTranscriptRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetCallTranscriptRequest) GetRecordingId() string {
	if x != nil {
		return x.RecordingId
	}
	return ""
}

type GetCallTranscriptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// chunk of call recording
	Chunk []byte `protobuf:"bytes,2,opt,name=chunk,proto3" json:"chunk,omitempty"`
}

func (x *GetCallTranscriptResponse) Reset() {
	*x = GetCallTranscriptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCallTranscriptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallTranscriptResponse) ProtoMessage() {}

func (x *GetCallTranscriptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallTranscriptResponse.ProtoReflect.Descriptor instead.
func (*GetCallTranscriptResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{29}
}

func (x *GetCallTranscriptResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCallTranscriptResponse) GetChunk() []byte {
	if x != nil {
		return x.Chunk
	}
	return nil
}

type GetSupportTicketsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// optional
	// will single ticket with given id if passed
	TicketId int64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	// list of ticket filters that needs to be applied
	TicketFilters *TicketFilters `protobuf:"bytes,2,opt,name=ticket_filters,json=ticketFilters,proto3" json:"ticket_filters,omitempty"`
	// max page size allowed is 50
	// if page size is not passed default page size of 30 will be used
	PageContextRequest *rpc.PageContextRequest `protobuf:"bytes,3,opt,name=page_context_request,json=pageContextRequest,proto3" json:"page_context_request,omitempty"`
}

func (x *GetSupportTicketsRequest) Reset() {
	*x = GetSupportTicketsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupportTicketsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportTicketsRequest) ProtoMessage() {}

func (x *GetSupportTicketsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportTicketsRequest.ProtoReflect.Descriptor instead.
func (*GetSupportTicketsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetSupportTicketsRequest) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

func (x *GetSupportTicketsRequest) GetTicketFilters() *TicketFilters {
	if x != nil {
		return x.TicketFilters
	}
	return nil
}

func (x *GetSupportTicketsRequest) GetPageContextRequest() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContextRequest
	}
	return nil
}

type GetSupportTicketsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// will return
	// OK for success
	// NOT_FOUND if no ticket is found with given conditions
	Status              *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Tickets             []*Ticket                `protobuf:"bytes,2,rep,name=tickets,proto3" json:"tickets,omitempty"`
	PageContextResponse *rpc.PageContextResponse `protobuf:"bytes,3,opt,name=page_context_response,json=pageContextResponse,proto3" json:"page_context_response,omitempty"`
}

func (x *GetSupportTicketsResponse) Reset() {
	*x = GetSupportTicketsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupportTicketsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportTicketsResponse) ProtoMessage() {}

func (x *GetSupportTicketsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportTicketsResponse.ProtoReflect.Descriptor instead.
func (*GetSupportTicketsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{31}
}

func (x *GetSupportTicketsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSupportTicketsResponse) GetTickets() []*Ticket {
	if x != nil {
		return x.Tickets
	}
	return nil
}

func (x *GetSupportTicketsResponse) GetPageContextResponse() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContextResponse
	}
	return nil
}

type BulkUpdateTicketsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email, access token are mandatory in header
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// csv file containing details of tickets to be updated
	UpdateTicketCsv []byte `protobuf:"bytes,2,opt,name=update_ticket_csv,json=updateTicketCsv,proto3" json:"update_ticket_csv,omitempty"`
	// email id of the agent who verified the file for update
	CheckerEmail string `protobuf:"bytes,3,opt,name=checker_email,json=checkerEmail,proto3" json:"checker_email,omitempty"`
	// description of the job
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *BulkUpdateTicketsRequest) Reset() {
	*x = BulkUpdateTicketsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkUpdateTicketsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkUpdateTicketsRequest) ProtoMessage() {}

func (x *BulkUpdateTicketsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkUpdateTicketsRequest.ProtoReflect.Descriptor instead.
func (*BulkUpdateTicketsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{32}
}

func (x *BulkUpdateTicketsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *BulkUpdateTicketsRequest) GetUpdateTicketCsv() []byte {
	if x != nil {
		return x.UpdateTicketCsv
	}
	return nil
}

func (x *BulkUpdateTicketsRequest) GetCheckerEmail() string {
	if x != nil {
		return x.CheckerEmail
	}
	return ""
}

func (x *BulkUpdateTicketsRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type BulkUpdateTicketsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// job id for bulk update ticket case
	JobId int64 `protobuf:"varint,2,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
}

func (x *BulkUpdateTicketsResponse) Reset() {
	*x = BulkUpdateTicketsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkUpdateTicketsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkUpdateTicketsResponse) ProtoMessage() {}

func (x *BulkUpdateTicketsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkUpdateTicketsResponse.ProtoReflect.Descriptor instead.
func (*BulkUpdateTicketsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{33}
}

func (x *BulkUpdateTicketsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *BulkUpdateTicketsResponse) GetJobId() int64 {
	if x != nil {
		return x.JobId
	}
	return 0
}

type GetAllBulkTicketJobsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email, access token are mandatory in header
	Header  *cx.Header            `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Filters *BulkTicketJobFilters `protobuf:"bytes,2,opt,name=filters,proto3" json:"filters,omitempty"`
	// max page size allowed is 50
	// if page size is not passed default page size of 30 will be used
	PageContext *rpc.PageContextRequest `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetAllBulkTicketJobsRequest) Reset() {
	*x = GetAllBulkTicketJobsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllBulkTicketJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllBulkTicketJobsRequest) ProtoMessage() {}

func (x *GetAllBulkTicketJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllBulkTicketJobsRequest.ProtoReflect.Descriptor instead.
func (*GetAllBulkTicketJobsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetAllBulkTicketJobsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetAllBulkTicketJobsRequest) GetFilters() *BulkTicketJobFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *GetAllBulkTicketJobsRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetAllBulkTicketJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status              *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	JobList             []*BulkTicketJobDetails  `protobuf:"bytes,2,rep,name=job_list,json=jobList,proto3" json:"job_list,omitempty"`
	PageContextResponse *rpc.PageContextResponse `protobuf:"bytes,3,opt,name=page_context_response,json=pageContextResponse,proto3" json:"page_context_response,omitempty"`
}

func (x *GetAllBulkTicketJobsResponse) Reset() {
	*x = GetAllBulkTicketJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllBulkTicketJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllBulkTicketJobsResponse) ProtoMessage() {}

func (x *GetAllBulkTicketJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllBulkTicketJobsResponse.ProtoReflect.Descriptor instead.
func (*GetAllBulkTicketJobsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetAllBulkTicketJobsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAllBulkTicketJobsResponse) GetJobList() []*BulkTicketJobDetails {
	if x != nil {
		return x.JobList
	}
	return nil
}

func (x *GetAllBulkTicketJobsResponse) GetPageContextResponse() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContextResponse
	}
	return nil
}

type GetJobFailureLogsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email, access token are mandatory in header
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	JobId  int64      `protobuf:"varint,2,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
}

func (x *GetJobFailureLogsRequest) Reset() {
	*x = GetJobFailureLogsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobFailureLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobFailureLogsRequest) ProtoMessage() {}

func (x *GetJobFailureLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobFailureLogsRequest.ProtoReflect.Descriptor instead.
func (*GetJobFailureLogsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetJobFailureLogsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetJobFailureLogsRequest) GetJobId() int64 {
	if x != nil {
		return x.JobId
	}
	return 0
}

type GetJobFailureLogsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of ticket failures
	FailureList []*TicketFailureLog `protobuf:"bytes,2,rep,name=failure_list,json=failureList,proto3" json:"failure_list,omitempty"`
}

func (x *GetJobFailureLogsResponse) Reset() {
	*x = GetJobFailureLogsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobFailureLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobFailureLogsResponse) ProtoMessage() {}

func (x *GetJobFailureLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobFailureLogsResponse.ProtoReflect.Descriptor instead.
func (*GetJobFailureLogsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetJobFailureLogsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetJobFailureLogsResponse) GetFailureList() []*TicketFailureLog {
	if x != nil {
		return x.FailureList
	}
	return nil
}

type KillJobProcessingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email, access token are mandatory in header
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// id of the job to be killed
	JobId int64 `protobuf:"varint,2,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
}

func (x *KillJobProcessingRequest) Reset() {
	*x = KillJobProcessingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KillJobProcessingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KillJobProcessingRequest) ProtoMessage() {}

func (x *KillJobProcessingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KillJobProcessingRequest.ProtoReflect.Descriptor instead.
func (*KillJobProcessingRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{38}
}

func (x *KillJobProcessingRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *KillJobProcessingRequest) GetJobId() int64 {
	if x != nil {
		return x.JobId
	}
	return 0
}

type KillJobProcessingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *KillJobProcessingResponse) Reset() {
	*x = KillJobProcessingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KillJobProcessingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KillJobProcessingResponse) ProtoMessage() {}

func (x *KillJobProcessingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KillJobProcessingResponse.ProtoReflect.Descriptor instead.
func (*KillJobProcessingResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{39}
}

func (x *KillJobProcessingResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ContactDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to ContactInfo:
	//
	//	*ContactDetails_EmailId
	//	*ContactDetails_PhoneNumber
	ContactInfo isContactDetails_ContactInfo `protobuf_oneof:"contact_info"`
}

func (x *ContactDetails) Reset() {
	*x = ContactDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContactDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactDetails) ProtoMessage() {}

func (x *ContactDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactDetails.ProtoReflect.Descriptor instead.
func (*ContactDetails) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{40}
}

func (m *ContactDetails) GetContactInfo() isContactDetails_ContactInfo {
	if m != nil {
		return m.ContactInfo
	}
	return nil
}

func (x *ContactDetails) GetEmailId() string {
	if x, ok := x.GetContactInfo().(*ContactDetails_EmailId); ok {
		return x.EmailId
	}
	return ""
}

func (x *ContactDetails) GetPhoneNumber() *common.PhoneNumber {
	if x, ok := x.GetContactInfo().(*ContactDetails_PhoneNumber); ok {
		return x.PhoneNumber
	}
	return nil
}

type isContactDetails_ContactInfo interface {
	isContactDetails_ContactInfo()
}

type ContactDetails_EmailId struct {
	EmailId string `protobuf:"bytes,1,opt,name=email_id,json=emailId,proto3,oneof"`
}

type ContactDetails_PhoneNumber struct {
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3,oneof"`
}

func (*ContactDetails_EmailId) isContactDetails_ContactInfo() {}

func (*ContactDetails_PhoneNumber) isContactDetails_ContactInfo() {}

type GetSupportTicketsForSherlockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// header is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// specifies from which source to fetch tickets details from
	// if ticket filters is passed than fetches from db
	// if contact details is passed than fetches directly from freshdesk
	//
	// Types that are assignable to FetchFilter:
	//
	//	*GetSupportTicketsForSherlockRequest_TicketFilters
	//	*GetSupportTicketsForSherlockRequest_ContactDetails
	FetchFilter isGetSupportTicketsForSherlockRequest_FetchFilter `protobuf_oneof:"fetch_filter"`
	// max page size allowed is 50
	// if page size is not passed default page size of 30 will be used
	PageContextRequest *rpc.PageContextRequest `protobuf:"bytes,4,opt,name=page_context_request,json=pageContextRequest,proto3" json:"page_context_request,omitempty"`
}

func (x *GetSupportTicketsForSherlockRequest) Reset() {
	*x = GetSupportTicketsForSherlockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupportTicketsForSherlockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportTicketsForSherlockRequest) ProtoMessage() {}

func (x *GetSupportTicketsForSherlockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportTicketsForSherlockRequest.ProtoReflect.Descriptor instead.
func (*GetSupportTicketsForSherlockRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{41}
}

func (x *GetSupportTicketsForSherlockRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (m *GetSupportTicketsForSherlockRequest) GetFetchFilter() isGetSupportTicketsForSherlockRequest_FetchFilter {
	if m != nil {
		return m.FetchFilter
	}
	return nil
}

func (x *GetSupportTicketsForSherlockRequest) GetTicketFilters() *TicketFilters {
	if x, ok := x.GetFetchFilter().(*GetSupportTicketsForSherlockRequest_TicketFilters); ok {
		return x.TicketFilters
	}
	return nil
}

func (x *GetSupportTicketsForSherlockRequest) GetContactDetails() *ContactDetails {
	if x, ok := x.GetFetchFilter().(*GetSupportTicketsForSherlockRequest_ContactDetails); ok {
		return x.ContactDetails
	}
	return nil
}

func (x *GetSupportTicketsForSherlockRequest) GetPageContextRequest() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContextRequest
	}
	return nil
}

type isGetSupportTicketsForSherlockRequest_FetchFilter interface {
	isGetSupportTicketsForSherlockRequest_FetchFilter()
}

type GetSupportTicketsForSherlockRequest_TicketFilters struct {
	// list of ticket filters that needs to be applied
	TicketFilters *TicketFilters `protobuf:"bytes,2,opt,name=ticket_filters,json=ticketFilters,proto3,oneof"`
}

type GetSupportTicketsForSherlockRequest_ContactDetails struct {
	// need to specify what contact needs to be passed and will be queried on freshdesk accordingly
	ContactDetails *ContactDetails `protobuf:"bytes,3,opt,name=contact_details,json=contactDetails,proto3,oneof"`
}

func (*GetSupportTicketsForSherlockRequest_TicketFilters) isGetSupportTicketsForSherlockRequest_FetchFilter() {
}

func (*GetSupportTicketsForSherlockRequest_ContactDetails) isGetSupportTicketsForSherlockRequest_FetchFilter() {
}

type GetSupportTicketsForSherlockResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of tickets
	Tickets []*Ticket `protobuf:"bytes,2,rep,name=tickets,proto3" json:"tickets,omitempty"`
	// pagination request field
	PageContextResponse *rpc.PageContextResponse `protobuf:"bytes,3,opt,name=page_context_response,json=pageContextResponse,proto3" json:"page_context_response,omitempty"`
}

func (x *GetSupportTicketsForSherlockResponse) Reset() {
	*x = GetSupportTicketsForSherlockResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupportTicketsForSherlockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportTicketsForSherlockResponse) ProtoMessage() {}

func (x *GetSupportTicketsForSherlockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportTicketsForSherlockResponse.ProtoReflect.Descriptor instead.
func (*GetSupportTicketsForSherlockResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{42}
}

func (x *GetSupportTicketsForSherlockResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSupportTicketsForSherlockResponse) GetTickets() []*Ticket {
	if x != nil {
		return x.Tickets
	}
	return nil
}

func (x *GetSupportTicketsForSherlockResponse) GetPageContextResponse() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContextResponse
	}
	return nil
}

type GetSupportTicketsForAppRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor_id is mandatory
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// list of ticket filters that needs to be applied
	TicketFilters *TicketFiltersForUser `protobuf:"bytes,2,opt,name=ticket_filters,json=ticketFilters,proto3" json:"ticket_filters,omitempty"`
	// max page size allowed is 20
	// if page size is not passed default page size of 10 will be used
	PageContextRequest *rpc.PageContextRequest `protobuf:"bytes,3,opt,name=page_context_request,json=pageContextRequest,proto3" json:"page_context_request,omitempty"`
	// flag to specify whether cache data is required
	// as latest ticket of user is being shown in Home, and can have a lot of hits it's better to cache the results
	ShouldUseCache bool `protobuf:"varint,4,opt,name=should_use_cache,json=shouldUseCache,proto3" json:"should_use_cache,omitempty"`
}

func (x *GetSupportTicketsForAppRequest) Reset() {
	*x = GetSupportTicketsForAppRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupportTicketsForAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportTicketsForAppRequest) ProtoMessage() {}

func (x *GetSupportTicketsForAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportTicketsForAppRequest.ProtoReflect.Descriptor instead.
func (*GetSupportTicketsForAppRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{43}
}

func (x *GetSupportTicketsForAppRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetSupportTicketsForAppRequest) GetTicketFilters() *TicketFiltersForUser {
	if x != nil {
		return x.TicketFilters
	}
	return nil
}

func (x *GetSupportTicketsForAppRequest) GetPageContextRequest() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContextRequest
	}
	return nil
}

func (x *GetSupportTicketsForAppRequest) GetShouldUseCache() bool {
	if x != nil {
		return x.ShouldUseCache
	}
	return false
}

type GetSupportTicketsForAppResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of tickets
	// The status of first ticket is used to decide whether to inform user that the latest ticket details still being updated
	// This is decided based on list of mandatory params to be populated before a ticket can be shown
	// If the status is set to UPDATING_DETAILS, latest-ticket-still-updating card must be shown to user
	Tickets []*TicketDetailsForUser `protobuf:"bytes,2,rep,name=tickets,proto3" json:"tickets,omitempty"`
	// isLatestTicketDetailsStillUpdating flag is used to decide whether to inform user that the latest ticket details still being updated
	// This is decided based on list of mandatory params to be populated before a ticket can be shown
	// If this flag is set to TRUE, latest-ticket-still-updating card must be shown to user
	IsLatestTicketDetailsStillUpdating common.BooleanEnum `protobuf:"varint,3,opt,name=isLatestTicketDetailsStillUpdating,proto3,enum=api.typesv2.common.BooleanEnum" json:"isLatestTicketDetailsStillUpdating,omitempty"`
	// pagination request field
	PageContextResponse *rpc.PageContextResponse `protobuf:"bytes,4,opt,name=page_context_response,json=pageContextResponse,proto3" json:"page_context_response,omitempty"`
}

func (x *GetSupportTicketsForAppResponse) Reset() {
	*x = GetSupportTicketsForAppResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupportTicketsForAppResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportTicketsForAppResponse) ProtoMessage() {}

func (x *GetSupportTicketsForAppResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportTicketsForAppResponse.ProtoReflect.Descriptor instead.
func (*GetSupportTicketsForAppResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{44}
}

func (x *GetSupportTicketsForAppResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSupportTicketsForAppResponse) GetTickets() []*TicketDetailsForUser {
	if x != nil {
		return x.Tickets
	}
	return nil
}

func (x *GetSupportTicketsForAppResponse) GetIsLatestTicketDetailsStillUpdating() common.BooleanEnum {
	if x != nil {
		return x.IsLatestTicketDetailsStillUpdating
	}
	return common.BooleanEnum(0)
}

func (x *GetSupportTicketsForAppResponse) GetPageContextResponse() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContextResponse
	}
	return nil
}

type CreateTicketDetailsTransformationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// details of the transformation to be created
	TransformationsList []*TicketDetailsTransformation `protobuf:"bytes,1,rep,name=transformations_list,json=transformationsList,proto3" json:"transformations_list,omitempty"`
}

func (x *CreateTicketDetailsTransformationsRequest) Reset() {
	*x = CreateTicketDetailsTransformationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTicketDetailsTransformationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTicketDetailsTransformationsRequest) ProtoMessage() {}

func (x *CreateTicketDetailsTransformationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTicketDetailsTransformationsRequest.ProtoReflect.Descriptor instead.
func (*CreateTicketDetailsTransformationsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{45}
}

func (x *CreateTicketDetailsTransformationsRequest) GetTransformationsList() []*TicketDetailsTransformation {
	if x != nil {
		return x.TransformationsList
	}
	return nil
}

type CreateTicketDetailsTransformationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// created transformation with Id
	TransformationsList []*TicketDetailsTransformation `protobuf:"bytes,2,rep,name=transformations_list,json=transformationsList,proto3" json:"transformations_list,omitempty"`
}

func (x *CreateTicketDetailsTransformationsResponse) Reset() {
	*x = CreateTicketDetailsTransformationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTicketDetailsTransformationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTicketDetailsTransformationsResponse) ProtoMessage() {}

func (x *CreateTicketDetailsTransformationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTicketDetailsTransformationsResponse.ProtoReflect.Descriptor instead.
func (*CreateTicketDetailsTransformationsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{46}
}

func (x *CreateTicketDetailsTransformationsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateTicketDetailsTransformationsResponse) GetTransformationsList() []*TicketDetailsTransformation {
	if x != nil {
		return x.TransformationsList
	}
	return nil
}

type UpdateTicketDetailsTransformationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Id is mandatory in the transformation
	Transformation *TicketDetailsTransformation `protobuf:"bytes,1,opt,name=transformation,proto3" json:"transformation,omitempty"`
	// update mask
	UpdateMask []TicketDetailsTransformationFieldMask `protobuf:"varint,2,rep,packed,name=update_mask,json=updateMask,proto3,enum=cx.ticket.TicketDetailsTransformationFieldMask" json:"update_mask,omitempty"`
}

func (x *UpdateTicketDetailsTransformationRequest) Reset() {
	*x = UpdateTicketDetailsTransformationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTicketDetailsTransformationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTicketDetailsTransformationRequest) ProtoMessage() {}

func (x *UpdateTicketDetailsTransformationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTicketDetailsTransformationRequest.ProtoReflect.Descriptor instead.
func (*UpdateTicketDetailsTransformationRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{47}
}

func (x *UpdateTicketDetailsTransformationRequest) GetTransformation() *TicketDetailsTransformation {
	if x != nil {
		return x.Transformation
	}
	return nil
}

func (x *UpdateTicketDetailsTransformationRequest) GetUpdateMask() []TicketDetailsTransformationFieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

type UpdateTicketDetailsTransformationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateTicketDetailsTransformationResponse) Reset() {
	*x = UpdateTicketDetailsTransformationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTicketDetailsTransformationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTicketDetailsTransformationResponse) ProtoMessage() {}

func (x *UpdateTicketDetailsTransformationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTicketDetailsTransformationResponse.ProtoReflect.Descriptor instead.
func (*UpdateTicketDetailsTransformationResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{48}
}

func (x *UpdateTicketDetailsTransformationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteTicketDetailsTransformationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Delete will be performed by applying AND on the following filters if exists
	// At least one of the filters must be passed to avoid accidental deletes
	RowIdList              []string                   `protobuf:"bytes,1,rep,name=row_id_list,json=rowIdList,proto3" json:"row_id_list,omitempty"`
	TransformationTypeList []TicketTransformationType `protobuf:"varint,2,rep,packed,name=transformation_type_list,json=transformationTypeList,proto3,enum=cx.ticket.TicketTransformationType" json:"transformation_type_list,omitempty"`
	ProductCategoryList    []ProductCategory          `protobuf:"varint,3,rep,packed,name=product_category_list,json=productCategoryList,proto3,enum=cx.ticket.ProductCategory" json:"product_category_list,omitempty"`
}

func (x *DeleteTicketDetailsTransformationsRequest) Reset() {
	*x = DeleteTicketDetailsTransformationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTicketDetailsTransformationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTicketDetailsTransformationsRequest) ProtoMessage() {}

func (x *DeleteTicketDetailsTransformationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTicketDetailsTransformationsRequest.ProtoReflect.Descriptor instead.
func (*DeleteTicketDetailsTransformationsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{49}
}

func (x *DeleteTicketDetailsTransformationsRequest) GetRowIdList() []string {
	if x != nil {
		return x.RowIdList
	}
	return nil
}

func (x *DeleteTicketDetailsTransformationsRequest) GetTransformationTypeList() []TicketTransformationType {
	if x != nil {
		return x.TransformationTypeList
	}
	return nil
}

func (x *DeleteTicketDetailsTransformationsRequest) GetProductCategoryList() []ProductCategory {
	if x != nil {
		return x.ProductCategoryList
	}
	return nil
}

type DeleteTicketDetailsTransformationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// number of records deleted
	DeletedCount int64 `protobuf:"varint,2,opt,name=deleted_count,json=deletedCount,proto3" json:"deleted_count,omitempty"`
}

func (x *DeleteTicketDetailsTransformationsResponse) Reset() {
	*x = DeleteTicketDetailsTransformationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTicketDetailsTransformationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTicketDetailsTransformationsResponse) ProtoMessage() {}

func (x *DeleteTicketDetailsTransformationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTicketDetailsTransformationsResponse.ProtoReflect.Descriptor instead.
func (*DeleteTicketDetailsTransformationsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{50}
}

func (x *DeleteTicketDetailsTransformationsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *DeleteTicketDetailsTransformationsResponse) GetDeletedCount() int64 {
	if x != nil {
		return x.DeletedCount
	}
	return 0
}

type UpdateTicketAsyncRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// cx ticket with updated fields
	Ticket *Ticket `protobuf:"bytes,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
}

func (x *UpdateTicketAsyncRequest) Reset() {
	*x = UpdateTicketAsyncRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTicketAsyncRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTicketAsyncRequest) ProtoMessage() {}

func (x *UpdateTicketAsyncRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTicketAsyncRequest.ProtoReflect.Descriptor instead.
func (*UpdateTicketAsyncRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{51}
}

func (x *UpdateTicketAsyncRequest) GetTicket() *Ticket {
	if x != nil {
		return x.Ticket
	}
	return nil
}

type UpdateTicketAsyncResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateTicketAsyncResponse) Reset() {
	*x = UpdateTicketAsyncResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTicketAsyncResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTicketAsyncResponse) ProtoMessage() {}

func (x *UpdateTicketAsyncResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTicketAsyncResponse.ProtoReflect.Descriptor instead.
func (*UpdateTicketAsyncResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{52}
}

func (x *UpdateTicketAsyncResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type AddPrivateNoteAsyncRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory: Id of the ticket to which the private note is to be added
	TicketId int64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	// mandatory: body of note
	Body string `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	// list of emails to notify
	NotifyEmails []string `protobuf:"bytes,3,rep,name=notify_emails,json=notifyEmails,proto3" json:"notify_emails,omitempty"`
	// id of agent who is adding the note
	AgentId int64 `protobuf:"varint,4,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
}

func (x *AddPrivateNoteAsyncRequest) Reset() {
	*x = AddPrivateNoteAsyncRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPrivateNoteAsyncRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPrivateNoteAsyncRequest) ProtoMessage() {}

func (x *AddPrivateNoteAsyncRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPrivateNoteAsyncRequest.ProtoReflect.Descriptor instead.
func (*AddPrivateNoteAsyncRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{53}
}

func (x *AddPrivateNoteAsyncRequest) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

func (x *AddPrivateNoteAsyncRequest) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *AddPrivateNoteAsyncRequest) GetNotifyEmails() []string {
	if x != nil {
		return x.NotifyEmails
	}
	return nil
}

func (x *AddPrivateNoteAsyncRequest) GetAgentId() int64 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

type AddPrivateNoteAsyncResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *AddPrivateNoteAsyncResponse) Reset() {
	*x = AddPrivateNoteAsyncResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPrivateNoteAsyncResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPrivateNoteAsyncResponse) ProtoMessage() {}

func (x *AddPrivateNoteAsyncResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPrivateNoteAsyncResponse.ProtoReflect.Descriptor instead.
func (*AddPrivateNoteAsyncResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{54}
}

func (x *AddPrivateNoteAsyncResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateTicketRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ticket *Ticket `protobuf:"bytes,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
	// in case the caller wants the ticket creation to be async
	// for high traffic flow, we might want to do this due to the vendor rate limits we have on creation API
	// also async ensures the intermittent failures are handled by retrying
	// if opted for async ticket creation ticket id won't be shared in response
	// deprecated in favour of CreateTicketAsync RPC
	//
	// Deprecated: Marked as deprecated in api/cx/ticket/service.proto.
	IsAsyncCreationRequired bool `protobuf:"varint,2,opt,name=is_async_creation_required,json=isAsyncCreationRequired,proto3" json:"is_async_creation_required,omitempty"`
}

func (x *CreateTicketRequest) Reset() {
	*x = CreateTicketRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTicketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTicketRequest) ProtoMessage() {}

func (x *CreateTicketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTicketRequest.ProtoReflect.Descriptor instead.
func (*CreateTicketRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{55}
}

func (x *CreateTicketRequest) GetTicket() *Ticket {
	if x != nil {
		return x.Ticket
	}
	return nil
}

// Deprecated: Marked as deprecated in api/cx/ticket/service.proto.
func (x *CreateTicketRequest) GetIsAsyncCreationRequired() bool {
	if x != nil {
		return x.IsAsyncCreationRequired
	}
	return false
}

type CreateTicketResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Ticket *Ticket     `protobuf:"bytes,2,opt,name=ticket,proto3" json:"ticket,omitempty"`
}

func (x *CreateTicketResponse) Reset() {
	*x = CreateTicketResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTicketResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTicketResponse) ProtoMessage() {}

func (x *CreateTicketResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTicketResponse.ProtoReflect.Descriptor instead.
func (*CreateTicketResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{56}
}

func (x *CreateTicketResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateTicketResponse) GetTicket() *Ticket {
	if x != nil {
		return x.Ticket
	}
	return nil
}

type GetSupportTicketByIdForAppRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TicketId int64  `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	ActorId  string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetSupportTicketByIdForAppRequest) Reset() {
	*x = GetSupportTicketByIdForAppRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupportTicketByIdForAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportTicketByIdForAppRequest) ProtoMessage() {}

func (x *GetSupportTicketByIdForAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportTicketByIdForAppRequest.ProtoReflect.Descriptor instead.
func (*GetSupportTicketByIdForAppRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{57}
}

func (x *GetSupportTicketByIdForAppRequest) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

func (x *GetSupportTicketByIdForAppRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetSupportTicketByIdForAppResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// ticket details for the given ticket_id
	Ticket *TicketDetailsForUser `protobuf:"bytes,2,opt,name=ticket,proto3" json:"ticket,omitempty"`
}

func (x *GetSupportTicketByIdForAppResponse) Reset() {
	*x = GetSupportTicketByIdForAppResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupportTicketByIdForAppResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportTicketByIdForAppResponse) ProtoMessage() {}

func (x *GetSupportTicketByIdForAppResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportTicketByIdForAppResponse.ProtoReflect.Descriptor instead.
func (*GetSupportTicketByIdForAppResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{58}
}

func (x *GetSupportTicketByIdForAppResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSupportTicketByIdForAppResponse) GetTicket() *TicketDetailsForUser {
	if x != nil {
		return x.Ticket
	}
	return nil
}

type GetMergedTicketsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of ticket for which we want to fetch merged tickets
	TicketId int64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
}

func (x *GetMergedTicketsRequest) Reset() {
	*x = GetMergedTicketsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMergedTicketsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMergedTicketsRequest) ProtoMessage() {}

func (x *GetMergedTicketsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMergedTicketsRequest.ProtoReflect.Descriptor instead.
func (*GetMergedTicketsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{59}
}

func (x *GetMergedTicketsRequest) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

type GetMergedTicketsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// ids of ticket which are merged into ticket provided in required
	// note: this list only contains id of merged tickets so the primary ticket (the one in request) won't be present in it
	MergedTicketIds []int64 `protobuf:"varint,2,rep,packed,name=merged_ticket_ids,json=mergedTicketIds,proto3" json:"merged_ticket_ids,omitempty"`
}

func (x *GetMergedTicketsResponse) Reset() {
	*x = GetMergedTicketsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMergedTicketsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMergedTicketsResponse) ProtoMessage() {}

func (x *GetMergedTicketsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMergedTicketsResponse.ProtoReflect.Descriptor instead.
func (*GetMergedTicketsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{60}
}

func (x *GetMergedTicketsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetMergedTicketsResponse) GetMergedTicketIds() []int64 {
	if x != nil {
		return x.MergedTicketIds
	}
	return nil
}

type CreateTicketAsyncRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// cx ticket with updated fields
	Ticket *Ticket `protobuf:"bytes,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
	// identifier to be used by clients for listening to the updates on ticket creation
	ClientRequestInfo *ClientRequestInfo `protobuf:"bytes,2,opt,name=client_request_info,json=clientRequestInfo,proto3" json:"client_request_info,omitempty"`
}

func (x *CreateTicketAsyncRequest) Reset() {
	*x = CreateTicketAsyncRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTicketAsyncRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTicketAsyncRequest) ProtoMessage() {}

func (x *CreateTicketAsyncRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTicketAsyncRequest.ProtoReflect.Descriptor instead.
func (*CreateTicketAsyncRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{61}
}

func (x *CreateTicketAsyncRequest) GetTicket() *Ticket {
	if x != nil {
		return x.Ticket
	}
	return nil
}

func (x *CreateTicketAsyncRequest) GetClientRequestInfo() *ClientRequestInfo {
	if x != nil {
		return x.ClientRequestInfo
	}
	return nil
}

type CreateTicketAsyncResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateTicketAsyncResponse) Reset() {
	*x = CreateTicketAsyncResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTicketAsyncResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTicketAsyncResponse) ProtoMessage() {}

func (x *CreateTicketAsyncResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTicketAsyncResponse.ProtoReflect.Descriptor instead.
func (*CreateTicketAsyncResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{62}
}

func (x *CreateTicketAsyncResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetAgentInstructionForTicketResponse_FinalAgentInstruction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title string                                                              `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Notes []*GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes `protobuf:"bytes,2,rep,name=notes,proto3" json:"notes,omitempty"`
}

func (x *GetAgentInstructionForTicketResponse_FinalAgentInstruction) Reset() {
	*x = GetAgentInstructionForTicketResponse_FinalAgentInstruction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgentInstructionForTicketResponse_FinalAgentInstruction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentInstructionForTicketResponse_FinalAgentInstruction) ProtoMessage() {}

func (x *GetAgentInstructionForTicketResponse_FinalAgentInstruction) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentInstructionForTicketResponse_FinalAgentInstruction.ProtoReflect.Descriptor instead.
func (*GetAgentInstructionForTicketResponse_FinalAgentInstruction) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *GetAgentInstructionForTicketResponse_FinalAgentInstruction) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetAgentInstructionForTicketResponse_FinalAgentInstruction) GetNotes() []*GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes {
	if x != nil {
		return x.Notes
	}
	return nil
}

type GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Question string `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	Answer   string `protobuf:"bytes,2,opt,name=answer,proto3" json:"answer,omitempty"`
}

func (x *GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes) Reset() {
	*x = GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_ticket_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes) ProtoMessage() {}

func (x *GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_ticket_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes.ProtoReflect.Descriptor instead.
func (*GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes) Descriptor() ([]byte, []int) {
	return file_api_cx_ticket_service_proto_rawDescGZIP(), []int{9, 0, 0}
}

func (x *GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

var File_api_cx_ticket_service_proto protoreflect.FileDescriptor

var file_api_cx_ticket_service_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x63,
	0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78,
	0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x78, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2f, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78,
	0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61,
	0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x5f, 0x67, 0x72, 0x69, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2f,
	0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x63, 0x78, 0x2f, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x64, 0x65, 0x73, 0x6b, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x46, 0x0a, 0x29, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76,
	0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x43, 0x53, 0x41,
	0x54, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x22, 0x6e, 0x0a, 0x2a, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x49, 0x64, 0x46, 0x6f, 0x72, 0x43, 0x53, 0x41, 0x54, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x49, 0x64, 0x22, 0x72, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x06,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63,
	0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52,
	0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x81, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x87, 0x01, 0x0a, 0x19,
	0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x43, 0x73, 0x61, 0x74, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x18, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x41, 0x0a, 0x1a, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x43,
	0x73, 0x61, 0x74, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x48, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x53,
	0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a,
	0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x22, 0xe8, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f,
	0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x40, 0x0a, 0x0c, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77,
	0x73, 0x12, 0x5a, 0x0a, 0x14, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x77, 0x65,
	0x62, 0x75, 0x69, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x56, 0x69, 0x65, 0x77, 0x42, 0x02, 0x18, 0x01, 0x52, 0x12, 0x69, 0x6e, 0x66, 0x6f, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x73, 0x22, 0x9e, 0x01,
	0x0a, 0x23, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x53, 0x0a, 0x15, 0x70, 0x72, 0x65,
	0x76, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x70, 0x72, 0x65, 0x76, 0x49,
	0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0xcc,
	0x04, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a, 0x10,
	0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x7f, 0x0a, 0x17, 0x66, 0x69, 0x6e, 0x61,
	0x6c, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x63, 0x78, 0x2e, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46, 0x69, 0x6e, 0x61, 0x6c,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x48, 0x00, 0x52, 0x15, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a, 0x11, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x10, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xcd,
	0x01, 0x0a, 0x15, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x61,
	0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e,
	0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46,
	0x69, 0x6e, 0x61, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65,
	0x73, 0x1a, 0x3b, 0x0a, 0x05, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x42, 0x0d,
	0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x49, 0x0a,
	0x23, 0x47, 0x65, 0x74, 0x46, 0x72, 0x65, 0x73, 0x68, 0x64, 0x65, 0x73, 0x6b, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x95, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74,
	0x46, 0x72, 0x65, 0x73, 0x68, 0x64, 0x65, 0x73, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x11, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x10,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x22, 0x3a, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xd8, 0x03, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x20,
	0x0a, 0x0b, 0x73, 0x75, 0x62, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x6c, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73,
	0x6c, 0x61, 0x12, 0x54, 0x0a, 0x18, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x72,
	0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x16, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x10, 0x65, 0x73, 0x63, 0x61,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x45,
	0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x0f, 0x65,
	0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f,
	0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x75, 0x72, 0x75, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x75, 0x72, 0x75, 0x4c, 0x69, 0x6e, 0x6b, 0x12,
	0x36, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x66, 0x63, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x05, 0x69, 0x73, 0x46, 0x63, 0x72, 0x22, 0xc4, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x12, 0x38, 0x0a, 0x18, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x20, 0x0a, 0x0b,
	0x73, 0x75, 0x62, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x3f,
	0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x6e, 0x0a, 0x10, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x12, 0x5a, 0x0a, 0x18, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x16, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x22,
	0xb4, 0x01, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x70, 0x0a, 0x20, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x5f, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x1d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43,
	0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x22, 0xa7, 0x01, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x4d, 0x0a, 0x13, 0x73, 0x75, 0x62, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x12, 0x73, 0x75,
	0x62, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73,
	0x22, 0x35, 0x0a, 0x11, 0x53, 0x75, 0x62, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43,
	0x68, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0xa6, 0x02, 0x0a, 0x13, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x09, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x56, 0x0a, 0x17,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x42, 0x02, 0x18, 0x01, 0x52, 0x14,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x65, 0x74, 0x61,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x1a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x5f, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f,
	0x76, 0x32, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x16, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x32,
	0x22, 0x3b, 0x0a, 0x14, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x3e, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x70, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x2e, 0x0a, 0x07, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x78, 0x2e, 0x66, 0x72, 0x65, 0x73, 0x68, 0x64, 0x65, 0x73, 0x6b, 0x2e,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x07, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x22,
	0xfb, 0x01, 0x0a, 0x13, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x70,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x61, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x6f,
	0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x61, 0x72, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x22, 0x3b, 0x0a,
	0x14, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x60, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x22, 0x55, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x22, 0x61, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x22, 0x56, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c,
	0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x22, 0xc3,
	0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0e, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x0d, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x49, 0x0a, 0x14, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x12, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0xbb, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x07, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x07, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x12, 0x4c, 0x0a, 0x15, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x13, 0x70,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x18, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x73, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x73, 0x76, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x57, 0x0a, 0x19, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22,
	0xb8, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x42, 0x75, 0x6c, 0x6b, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3a,
	0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0xcd, 0x01, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x41, 0x6c, 0x6c, 0x42, 0x75, 0x6c, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4a,
	0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x3a, 0x0a, 0x08, 0x6a, 0x6f, 0x62, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x42,
	0x75, 0x6c, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4c, 0x0a, 0x15,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x13, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x55, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x4a, 0x6f, 0x62, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x4c, 0x6f, 0x67, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f,
	0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49,
	0x64, 0x22, 0x80, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x78, 0x2e,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x55, 0x0a, 0x18, 0x4b, 0x69, 0x6c, 0x6c, 0x4a, 0x6f, 0x62, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x40, 0x0a, 0x19, 0x4b,
	0x69, 0x6c, 0x6c, 0x4a, 0x6f, 0x62, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x83, 0x01,
	0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x1b, 0x0a, 0x08, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x44, 0x0a,
	0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x42, 0x0e, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x22, 0xb7, 0x02, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x53, 0x68, 0x65, 0x72,
	0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78,
	0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0e, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x0d, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x44, 0x0a, 0x0f,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x48, 0x00, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x49, 0x0a, 0x14, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x12, 0x70, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x0e, 0x0a,
	0x0c, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xc6, 0x01,
	0x0a, 0x24, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x07, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63,
	0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52,
	0x07, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x4c, 0x0a, 0x15, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x13, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x81, 0x02, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x53, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41,
	0x70, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x46, 0x0a,
	0x0e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x46,
	0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x0d, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x49, 0x0a, 0x14, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x12, 0x70, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x28, 0x0a, 0x10, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x5f, 0x63,
	0x61, 0x63, 0x68, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x73, 0x68, 0x6f, 0x75,
	0x6c, 0x64, 0x55, 0x73, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x22, 0xc0, 0x02, 0x0a, 0x1f, 0x47,
	0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73,
	0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x07, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f,
	0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x07, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x6f,
	0x0a, 0x22, 0x69, 0x73, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x53, 0x74, 0x69, 0x6c, 0x6c, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x22, 0x69, 0x73, 0x4c,
	0x61, 0x74, 0x65, 0x73, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x53, 0x74, 0x69, 0x6c, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12,
	0x4c, 0x0a, 0x15, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x13, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x86, 0x01,
	0x0a, 0x29, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x59, 0x0a, 0x14, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x78, 0x2e, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xac, 0x01, 0x0a, 0x2a, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x14, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xcc, 0x01, 0x0a, 0x28, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x4e, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x78, 0x2e,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x73,
	0x6b, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4d, 0x61, 0x73, 0x6b, 0x22, 0x50, 0x0a, 0x29, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xfa, 0x01, 0x0a, 0x29, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x72, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x77, 0x49, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x5d, 0x0a, 0x18, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x16, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x4e, 0x0a, 0x15, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x13,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0x76, 0x0a, 0x2a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x45, 0x0a, 0x18, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x41, 0x73, 0x79, 0x6e, 0x63,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x22, 0x40, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x8d, 0x01, 0x0a, 0x1a, 0x41, 0x64, 0x64, 0x50, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x22, 0x42, 0x0a, 0x1b, 0x41, 0x64, 0x64, 0x50, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x29, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x3f, 0x0a, 0x1a, 0x69,
	0x73, 0x5f, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x17, 0x69, 0x73, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x22, 0x66, 0x0a, 0x14,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x06, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x78, 0x2e, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x06, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x22, 0x5b, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x79, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x41,
	0x70, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x22, 0x82, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x79, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x70, 0x70,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a,
	0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x06,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x3f, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x72,
	0x67, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x09, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x01, 0x52, 0x08, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x22, 0xa1, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4d,
	0x65, 0x72, 0x67, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x49, 0x64, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52,
	0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x93, 0x01, 0x0a, 0x18,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x41, 0x73, 0x79, 0x6e,
	0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x06, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x12, 0x4c, 0x0a, 0x13, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x40, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x32, 0xb4, 0x21, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x75,
	0x0a, 0x0c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1e,
	0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f,
	0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e,
	0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01,
	0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0x84, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x63, 0x78,
	0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x24, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7,
	0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8,
	0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0x75, 0x0a, 0x0c,
	0x4d, 0x65, 0x72, 0x67, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x1e, 0x2e, 0x63,
	0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x63,
	0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8,
	0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49,
	0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92,
	0xe8, 0x6a, 0x01, 0x12, 0x83, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63,
	0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53,
	0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8,
	0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x30, 0x01, 0x12, 0x86, 0x01, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x6c, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12,
	0x23, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x61, 0x6c, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a,
	0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56,
	0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00,
	0x30, 0x01, 0x12, 0x84, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e,
	0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49,
	0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00, 0xa8,
	0x8d, 0xe8, 0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x84, 0x01, 0x0a, 0x11, 0x42, 0x75,
	0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x12,
	0x23, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x42, 0x75, 0x6c, 0x6b,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a,
	0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56,
	0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00,
	0x12, 0x8d, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x42, 0x75, 0x6c, 0x6b, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x26, 0x2e, 0x63, 0x78, 0x2e, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x42, 0x75, 0x6c, 0x6b,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x27, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x42, 0x75, 0x6c, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4a, 0x6f,
	0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a,
	0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56,
	0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00,
	0x12, 0x84, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x46, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x23, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x78,
	0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53,
	0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8,
	0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x84, 0x01, 0x0a, 0x11, 0x4b, 0x69, 0x6c, 0x6c,
	0x4a, 0x6f, 0x62, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x12, 0x23, 0x2e,
	0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x4b, 0x69, 0x6c, 0x6c, 0x4a, 0x6f,
	0x62, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x4b,
	0x69, 0x6c, 0x6c, 0x4a, 0x6f, 0x62, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda,
	0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0,
	0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0xa5,
	0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x12,
	0x2e, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x46, 0x6f, 0x72,
	0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2f, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x46, 0x6f, 0x72,
	0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45,
	0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a,
	0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x96, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41,
	0x70, 0x70, 0x12, 0x29, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73,
	0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x70,
	0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00,
	0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45,
	0xa0, 0x8d, 0xe8, 0x6a, 0x00, 0xa8, 0x8d, 0xe8, 0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12,
	0xb7, 0x01, 0x0a, 0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x34, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x63,
	0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49,
	0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00, 0xa8,
	0x8d, 0xe8, 0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0xb4, 0x01, 0x0a, 0x21, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x33, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a,
	0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56,
	0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00, 0xa8, 0x8d, 0xe8, 0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00,
	0x12, 0xb7, 0x01, 0x0a, 0x22, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x34, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e,
	0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b,
	0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00,
	0xa8, 0x8d, 0xe8, 0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0xa5, 0x01, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x46, 0x72, 0x65, 0x73, 0x68, 0x64, 0x65, 0x73, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x2e, 0x2e, 0x63, 0x78,
	0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x72, 0x65, 0x73, 0x68,
	0x64, 0x65, 0x73, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x63, 0x78,
	0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x72, 0x65, 0x73, 0x68,
	0x64, 0x65, 0x73, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba,
	0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54,
	0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8,
	0x6a, 0x00, 0x12, 0x78, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7,
	0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8,
	0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0x81, 0x01, 0x0a,
	0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a,
	0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56,
	0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01,
	0x12, 0x84, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x12, 0x23, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x41,
	0x73, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x78,
	0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53,
	0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00, 0xa8, 0x8d, 0xe8,
	0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x8a, 0x01, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x50,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x12,
	0x25, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x41, 0x64, 0x64, 0x50,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74,
	0x65, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24,
	0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53,
	0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00, 0xa8, 0x8d, 0xe8, 0x6a, 0x00, 0xf0,
	0x92, 0xe8, 0x6a, 0x00, 0x12, 0x75, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x12, 0x1e, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a,
	0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a,
	0x00, 0xa8, 0x8d, 0xe8, 0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x9f, 0x01, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x42, 0x79, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x12, 0x2c, 0x2e, 0x63, 0x78, 0x2e,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x79, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x70,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x79, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2,
	0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d,
	0xe8, 0x6a, 0x00, 0xa8, 0x8d, 0xe8, 0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x81, 0x01,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x73, 0x12, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7,
	0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49,
	0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00, 0xa8, 0x8d, 0xe8, 0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a,
	0x00, 0x12, 0xa2, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63,
	0x6b, 0x12, 0x2d, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f,
	0x72, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2e, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72,
	0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45,
	0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a,
	0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0xa5, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f,
	0x72, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x2e, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda,
	0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0,
	0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x12, 0x84,
	0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x41,
	0x73, 0x79, 0x6e, 0x63, 0x12, 0x23, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x41, 0x73, 0x79,
	0x6e, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x78, 0x2e, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e,
	0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00, 0xa8, 0x8d, 0xe8, 0x6a, 0x00,
	0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x12, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x43, 0x73, 0x61, 0x74, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x24, 0x2e, 0x63,
	0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x43,
	0x73, 0x61, 0x74, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x25, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x53,
	0x75, 0x62, 0x6d, 0x69, 0x74, 0x43, 0x73, 0x61, 0x74, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00,
	0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45,
	0xa0, 0x8d, 0xe8, 0x6a, 0x00, 0xa8, 0x8d, 0xe8, 0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12,
	0x9e, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x2e,
	0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x63, 0x78,
	0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a,
	0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56,
	0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00, 0xa8, 0x8d, 0xe8, 0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00,
	0x12, 0xb7, 0x01, 0x0a, 0x22, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64,
	0x46, 0x6f, 0x72, 0x43, 0x53, 0x41, 0x54, 0x12, 0x34, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x46,
	0x6f, 0x72, 0x43, 0x53, 0x41, 0x54, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e,
	0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c,
	0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x43, 0x53, 0x41, 0x54, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b,
	0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00,
	0xa8, 0x8d, 0xe8, 0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x42, 0x4c, 0x0a, 0x24, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x5a, 0x24, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x78, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_ticket_service_proto_rawDescOnce sync.Once
	file_api_cx_ticket_service_proto_rawDescData = file_api_cx_ticket_service_proto_rawDesc
)

func file_api_cx_ticket_service_proto_rawDescGZIP() []byte {
	file_api_cx_ticket_service_proto_rawDescOnce.Do(func() {
		file_api_cx_ticket_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_ticket_service_proto_rawDescData)
	})
	return file_api_cx_ticket_service_proto_rawDescData
}

var file_api_cx_ticket_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_cx_ticket_service_proto_msgTypes = make([]protoimpl.MessageInfo, 65)
var file_api_cx_ticket_service_proto_goTypes = []interface{}{
	(GetMergedTicketsResponse_Status)(0),                                     // 0: cx.ticket.GetMergedTicketsResponse.Status
	(*FetchLatestResolvedTicketIdForCSATRequest)(nil),                        // 1: cx.ticket.FetchLatestResolvedTicketIdForCSATRequest
	(*FetchLatestResolvedTicketIdForCSATResponse)(nil),                       // 2: cx.ticket.FetchLatestResolvedTicketIdForCSATResponse
	(*GetCategoryTransformationsRequest)(nil),                                // 3: cx.ticket.GetCategoryTransformationsRequest
	(*GetCategoryTransformationsResponse)(nil),                               // 4: cx.ticket.GetCategoryTransformationsResponse
	(*SubmitCsatFeedbackRequest)(nil),                                        // 5: cx.ticket.SubmitCsatFeedbackRequest
	(*SubmitCsatFeedbackResponse)(nil),                                       // 6: cx.ticket.SubmitCsatFeedbackResponse
	(*GetTicketDetailsForSherlockRequest)(nil),                               // 7: cx.ticket.GetTicketDetailsForSherlockRequest
	(*GetTicketDetailsForSherlockResponse)(nil),                              // 8: cx.ticket.GetTicketDetailsForSherlockResponse
	(*GetAgentInstructionForTicketRequest)(nil),                              // 9: cx.ticket.GetAgentInstructionForTicketRequest
	(*GetAgentInstructionForTicketResponse)(nil),                             // 10: cx.ticket.GetAgentInstructionForTicketResponse
	(*GetFreshdeskTicketCategoriesRequest)(nil),                              // 11: cx.ticket.GetFreshdeskTicketCategoriesRequest
	(*GetFreshdeskTicketCategoriesResponse)(nil),                             // 12: cx.ticket.GetFreshdeskTicketCategoriesResponse
	(*GetTicketInfoRequest)(nil),                                             // 13: cx.ticket.GetTicketInfoRequest
	(*GetTicketInfoResponse)(nil),                                            // 14: cx.ticket.GetTicketInfoResponse
	(*UpdateTicketInfoRequest)(nil),                                          // 15: cx.ticket.UpdateTicketInfoRequest
	(*UpdateTicketInfoResponse)(nil),                                         // 16: cx.ticket.UpdateTicketInfoResponse
	(*TicketCategories)(nil),                                                 // 17: cx.ticket.TicketCategories
	(*ProductCategoryChoice)(nil),                                            // 18: cx.ticket.ProductCategoryChoice
	(*ProductCategoryDetailsChoice)(nil),                                     // 19: cx.ticket.ProductCategoryDetailsChoice
	(*SubcategoryChoice)(nil),                                                // 20: cx.ticket.SubcategoryChoice
	(*AttachEntityRequest)(nil),                                              // 21: cx.ticket.AttachEntityRequest
	(*AttachEntityResponse)(nil),                                             // 22: cx.ticket.AttachEntityResponse
	(*GetRelatedTicketsRequest)(nil),                                         // 23: cx.ticket.GetRelatedTicketsRequest
	(*GetRelatedTicketsResponse)(nil),                                        // 24: cx.ticket.GetRelatedTicketsResponse
	(*MergeTicketsRequest)(nil),                                              // 25: cx.ticket.MergeTicketsRequest
	(*MergeTicketsResponse)(nil),                                             // 26: cx.ticket.MergeTicketsResponse
	(*GetCallRecordingRequest)(nil),                                          // 27: cx.ticket.GetCallRecordingRequest
	(*GetCallRecordingResponse)(nil),                                         // 28: cx.ticket.GetCallRecordingResponse
	(*GetCallTranscriptRequest)(nil),                                         // 29: cx.ticket.GetCallTranscriptRequest
	(*GetCallTranscriptResponse)(nil),                                        // 30: cx.ticket.GetCallTranscriptResponse
	(*GetSupportTicketsRequest)(nil),                                         // 31: cx.ticket.GetSupportTicketsRequest
	(*GetSupportTicketsResponse)(nil),                                        // 32: cx.ticket.GetSupportTicketsResponse
	(*BulkUpdateTicketsRequest)(nil),                                         // 33: cx.ticket.BulkUpdateTicketsRequest
	(*BulkUpdateTicketsResponse)(nil),                                        // 34: cx.ticket.BulkUpdateTicketsResponse
	(*GetAllBulkTicketJobsRequest)(nil),                                      // 35: cx.ticket.GetAllBulkTicketJobsRequest
	(*GetAllBulkTicketJobsResponse)(nil),                                     // 36: cx.ticket.GetAllBulkTicketJobsResponse
	(*GetJobFailureLogsRequest)(nil),                                         // 37: cx.ticket.GetJobFailureLogsRequest
	(*GetJobFailureLogsResponse)(nil),                                        // 38: cx.ticket.GetJobFailureLogsResponse
	(*KillJobProcessingRequest)(nil),                                         // 39: cx.ticket.KillJobProcessingRequest
	(*KillJobProcessingResponse)(nil),                                        // 40: cx.ticket.KillJobProcessingResponse
	(*ContactDetails)(nil),                                                   // 41: cx.ticket.ContactDetails
	(*GetSupportTicketsForSherlockRequest)(nil),                              // 42: cx.ticket.GetSupportTicketsForSherlockRequest
	(*GetSupportTicketsForSherlockResponse)(nil),                             // 43: cx.ticket.GetSupportTicketsForSherlockResponse
	(*GetSupportTicketsForAppRequest)(nil),                                   // 44: cx.ticket.GetSupportTicketsForAppRequest
	(*GetSupportTicketsForAppResponse)(nil),                                  // 45: cx.ticket.GetSupportTicketsForAppResponse
	(*CreateTicketDetailsTransformationsRequest)(nil),                        // 46: cx.ticket.CreateTicketDetailsTransformationsRequest
	(*CreateTicketDetailsTransformationsResponse)(nil),                       // 47: cx.ticket.CreateTicketDetailsTransformationsResponse
	(*UpdateTicketDetailsTransformationRequest)(nil),                         // 48: cx.ticket.UpdateTicketDetailsTransformationRequest
	(*UpdateTicketDetailsTransformationResponse)(nil),                        // 49: cx.ticket.UpdateTicketDetailsTransformationResponse
	(*DeleteTicketDetailsTransformationsRequest)(nil),                        // 50: cx.ticket.DeleteTicketDetailsTransformationsRequest
	(*DeleteTicketDetailsTransformationsResponse)(nil),                       // 51: cx.ticket.DeleteTicketDetailsTransformationsResponse
	(*UpdateTicketAsyncRequest)(nil),                                         // 52: cx.ticket.UpdateTicketAsyncRequest
	(*UpdateTicketAsyncResponse)(nil),                                        // 53: cx.ticket.UpdateTicketAsyncResponse
	(*AddPrivateNoteAsyncRequest)(nil),                                       // 54: cx.ticket.AddPrivateNoteAsyncRequest
	(*AddPrivateNoteAsyncResponse)(nil),                                      // 55: cx.ticket.AddPrivateNoteAsyncResponse
	(*CreateTicketRequest)(nil),                                              // 56: cx.ticket.CreateTicketRequest
	(*CreateTicketResponse)(nil),                                             // 57: cx.ticket.CreateTicketResponse
	(*GetSupportTicketByIdForAppRequest)(nil),                                // 58: cx.ticket.GetSupportTicketByIdForAppRequest
	(*GetSupportTicketByIdForAppResponse)(nil),                               // 59: cx.ticket.GetSupportTicketByIdForAppResponse
	(*GetMergedTicketsRequest)(nil),                                          // 60: cx.ticket.GetMergedTicketsRequest
	(*GetMergedTicketsResponse)(nil),                                         // 61: cx.ticket.GetMergedTicketsResponse
	(*CreateTicketAsyncRequest)(nil),                                         // 62: cx.ticket.CreateTicketAsyncRequest
	(*CreateTicketAsyncResponse)(nil),                                        // 63: cx.ticket.CreateTicketAsyncResponse
	(*GetAgentInstructionForTicketResponse_FinalAgentInstruction)(nil),       // 64: cx.ticket.GetAgentInstructionForTicketResponse.FinalAgentInstruction
	(*GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes)(nil), // 65: cx.ticket.GetAgentInstructionForTicketResponse.FinalAgentInstruction.Notes
	(*rpc.Status)(nil),                                                       // 66: rpc.Status
	(*cx.Header)(nil),                                                        // 67: cx.Header
	(*Ticket)(nil),                                                           // 68: cx.ticket.Ticket
	(*webui.DetailView)(nil),                                                 // 69: api.typesv2.webui.DetailView
	(*webui.InfoComponentView)(nil),                                          // 70: api.typesv2.webui.InfoComponentView
	(AgentInstructionType)(0),                                                // 71: cx.ticket.AgentInstructionType
	(*timestamppb.Timestamp)(nil),                                            // 72: google.protobuf.Timestamp
	(EscalationTeam)(0),                                                      // 73: cx.ticket.EscalationTeam
	(common.BooleanEnum)(0),                                                  // 74: api.typesv2.common.BooleanEnum
	(EntityType)(0),                                                          // 75: cx.ticket.EntityType
	(*AttachEntityMeta)(nil),                                                 // 76: cx.ticket.AttachEntityMeta
	(*freshdesk.Ticket)(nil),                                                 // 77: cx.freshdesk.Ticket
	(*TicketFilters)(nil),                                                    // 78: cx.ticket.TicketFilters
	(*rpc.PageContextRequest)(nil),                                           // 79: rpc.PageContextRequest
	(*rpc.PageContextResponse)(nil),                                          // 80: rpc.PageContextResponse
	(*BulkTicketJobFilters)(nil),                                             // 81: cx.ticket.BulkTicketJobFilters
	(*BulkTicketJobDetails)(nil),                                             // 82: cx.ticket.BulkTicketJobDetails
	(*TicketFailureLog)(nil),                                                 // 83: cx.ticket.TicketFailureLog
	(*common.PhoneNumber)(nil),                                               // 84: api.typesv2.common.PhoneNumber
	(*TicketFiltersForUser)(nil),                                             // 85: cx.ticket.TicketFiltersForUser
	(*TicketDetailsForUser)(nil),                                             // 86: cx.ticket.TicketDetailsForUser
	(*TicketDetailsTransformation)(nil),                                      // 87: cx.ticket.TicketDetailsTransformation
	(TicketDetailsTransformationFieldMask)(0),                                // 88: cx.ticket.TicketDetailsTransformationFieldMask
	(TicketTransformationType)(0),                                            // 89: cx.ticket.TicketTransformationType
	(ProductCategory)(0),                                                     // 90: cx.ticket.ProductCategory
	(*ClientRequestInfo)(nil),                                                // 91: cx.ticket.ClientRequestInfo
}
var file_api_cx_ticket_service_proto_depIdxs = []int32{
	66,  // 0: cx.ticket.FetchLatestResolvedTicketIdForCSATResponse.status:type_name -> rpc.Status
	67,  // 1: cx.ticket.GetCategoryTransformationsRequest.header:type_name -> cx.Header
	68,  // 2: cx.ticket.GetCategoryTransformationsRequest.ticket:type_name -> cx.ticket.Ticket
	66,  // 3: cx.ticket.GetCategoryTransformationsResponse.status:type_name -> rpc.Status
	66,  // 4: cx.ticket.SubmitCsatFeedbackResponse.status:type_name -> rpc.Status
	67,  // 5: cx.ticket.GetTicketDetailsForSherlockRequest.header:type_name -> cx.Header
	66,  // 6: cx.ticket.GetTicketDetailsForSherlockResponse.status:type_name -> rpc.Status
	69,  // 7: cx.ticket.GetTicketDetailsForSherlockResponse.detail_views:type_name -> api.typesv2.webui.DetailView
	70,  // 8: cx.ticket.GetTicketDetailsForSherlockResponse.info_component_views:type_name -> api.typesv2.webui.InfoComponentView
	67,  // 9: cx.ticket.GetAgentInstructionForTicketRequest.header:type_name -> cx.Header
	71,  // 10: cx.ticket.GetAgentInstructionForTicketRequest.prev_instruction_type:type_name -> cx.ticket.AgentInstructionType
	66,  // 11: cx.ticket.GetAgentInstructionForTicketResponse.status:type_name -> rpc.Status
	71,  // 12: cx.ticket.GetAgentInstructionForTicketResponse.instruction_type:type_name -> cx.ticket.AgentInstructionType
	64,  // 13: cx.ticket.GetAgentInstructionForTicketResponse.final_agent_instruction:type_name -> cx.ticket.GetAgentInstructionForTicketResponse.FinalAgentInstruction
	70,  // 14: cx.ticket.GetAgentInstructionForTicketResponse.agent_instruction:type_name -> api.typesv2.webui.InfoComponentView
	67,  // 15: cx.ticket.GetFreshdeskTicketCategoriesRequest.header:type_name -> cx.Header
	66,  // 16: cx.ticket.GetFreshdeskTicketCategoriesResponse.status:type_name -> rpc.Status
	17,  // 17: cx.ticket.GetFreshdeskTicketCategoriesResponse.ticket_categories:type_name -> cx.ticket.TicketCategories
	67,  // 18: cx.ticket.GetTicketInfoRequest.header:type_name -> cx.Header
	66,  // 19: cx.ticket.GetTicketInfoResponse.status:type_name -> rpc.Status
	72,  // 20: cx.ticket.GetTicketInfoResponse.expected_resolution_time:type_name -> google.protobuf.Timestamp
	73,  // 21: cx.ticket.GetTicketInfoResponse.escalation_teams:type_name -> cx.ticket.EscalationTeam
	74,  // 22: cx.ticket.GetTicketInfoResponse.is_fcr:type_name -> api.typesv2.common.BooleanEnum
	67,  // 23: cx.ticket.UpdateTicketInfoRequest.header:type_name -> cx.Header
	66,  // 24: cx.ticket.UpdateTicketInfoResponse.status:type_name -> rpc.Status
	18,  // 25: cx.ticket.TicketCategories.product_category_choices:type_name -> cx.ticket.ProductCategoryChoice
	19,  // 26: cx.ticket.ProductCategoryChoice.product_category_details_choices:type_name -> cx.ticket.ProductCategoryDetailsChoice
	20,  // 27: cx.ticket.ProductCategoryDetailsChoice.subcategory_choices:type_name -> cx.ticket.SubcategoryChoice
	67,  // 28: cx.ticket.AttachEntityRequest.header:type_name -> cx.Header
	75,  // 29: cx.ticket.AttachEntityRequest.entity_type:type_name -> cx.ticket.EntityType
	76,  // 30: cx.ticket.AttachEntityRequest.attach_entity_meta_list:type_name -> cx.ticket.AttachEntityMeta
	66,  // 31: cx.ticket.AttachEntityResponse.status:type_name -> rpc.Status
	67,  // 32: cx.ticket.GetRelatedTicketsRequest.header:type_name -> cx.Header
	66,  // 33: cx.ticket.GetRelatedTicketsResponse.status:type_name -> rpc.Status
	77,  // 34: cx.ticket.GetRelatedTicketsResponse.tickets:type_name -> cx.freshdesk.Ticket
	67,  // 35: cx.ticket.MergeTicketsRequest.header:type_name -> cx.Header
	66,  // 36: cx.ticket.MergeTicketsResponse.status:type_name -> rpc.Status
	67,  // 37: cx.ticket.GetCallRecordingRequest.header:type_name -> cx.Header
	66,  // 38: cx.ticket.GetCallRecordingResponse.status:type_name -> rpc.Status
	67,  // 39: cx.ticket.GetCallTranscriptRequest.header:type_name -> cx.Header
	66,  // 40: cx.ticket.GetCallTranscriptResponse.status:type_name -> rpc.Status
	78,  // 41: cx.ticket.GetSupportTicketsRequest.ticket_filters:type_name -> cx.ticket.TicketFilters
	79,  // 42: cx.ticket.GetSupportTicketsRequest.page_context_request:type_name -> rpc.PageContextRequest
	66,  // 43: cx.ticket.GetSupportTicketsResponse.status:type_name -> rpc.Status
	68,  // 44: cx.ticket.GetSupportTicketsResponse.tickets:type_name -> cx.ticket.Ticket
	80,  // 45: cx.ticket.GetSupportTicketsResponse.page_context_response:type_name -> rpc.PageContextResponse
	67,  // 46: cx.ticket.BulkUpdateTicketsRequest.header:type_name -> cx.Header
	66,  // 47: cx.ticket.BulkUpdateTicketsResponse.status:type_name -> rpc.Status
	67,  // 48: cx.ticket.GetAllBulkTicketJobsRequest.header:type_name -> cx.Header
	81,  // 49: cx.ticket.GetAllBulkTicketJobsRequest.filters:type_name -> cx.ticket.BulkTicketJobFilters
	79,  // 50: cx.ticket.GetAllBulkTicketJobsRequest.page_context:type_name -> rpc.PageContextRequest
	66,  // 51: cx.ticket.GetAllBulkTicketJobsResponse.status:type_name -> rpc.Status
	82,  // 52: cx.ticket.GetAllBulkTicketJobsResponse.job_list:type_name -> cx.ticket.BulkTicketJobDetails
	80,  // 53: cx.ticket.GetAllBulkTicketJobsResponse.page_context_response:type_name -> rpc.PageContextResponse
	67,  // 54: cx.ticket.GetJobFailureLogsRequest.header:type_name -> cx.Header
	66,  // 55: cx.ticket.GetJobFailureLogsResponse.status:type_name -> rpc.Status
	83,  // 56: cx.ticket.GetJobFailureLogsResponse.failure_list:type_name -> cx.ticket.TicketFailureLog
	67,  // 57: cx.ticket.KillJobProcessingRequest.header:type_name -> cx.Header
	66,  // 58: cx.ticket.KillJobProcessingResponse.status:type_name -> rpc.Status
	84,  // 59: cx.ticket.ContactDetails.phone_number:type_name -> api.typesv2.common.PhoneNumber
	67,  // 60: cx.ticket.GetSupportTicketsForSherlockRequest.header:type_name -> cx.Header
	78,  // 61: cx.ticket.GetSupportTicketsForSherlockRequest.ticket_filters:type_name -> cx.ticket.TicketFilters
	41,  // 62: cx.ticket.GetSupportTicketsForSherlockRequest.contact_details:type_name -> cx.ticket.ContactDetails
	79,  // 63: cx.ticket.GetSupportTicketsForSherlockRequest.page_context_request:type_name -> rpc.PageContextRequest
	66,  // 64: cx.ticket.GetSupportTicketsForSherlockResponse.status:type_name -> rpc.Status
	68,  // 65: cx.ticket.GetSupportTicketsForSherlockResponse.tickets:type_name -> cx.ticket.Ticket
	80,  // 66: cx.ticket.GetSupportTicketsForSherlockResponse.page_context_response:type_name -> rpc.PageContextResponse
	85,  // 67: cx.ticket.GetSupportTicketsForAppRequest.ticket_filters:type_name -> cx.ticket.TicketFiltersForUser
	79,  // 68: cx.ticket.GetSupportTicketsForAppRequest.page_context_request:type_name -> rpc.PageContextRequest
	66,  // 69: cx.ticket.GetSupportTicketsForAppResponse.status:type_name -> rpc.Status
	86,  // 70: cx.ticket.GetSupportTicketsForAppResponse.tickets:type_name -> cx.ticket.TicketDetailsForUser
	74,  // 71: cx.ticket.GetSupportTicketsForAppResponse.isLatestTicketDetailsStillUpdating:type_name -> api.typesv2.common.BooleanEnum
	80,  // 72: cx.ticket.GetSupportTicketsForAppResponse.page_context_response:type_name -> rpc.PageContextResponse
	87,  // 73: cx.ticket.CreateTicketDetailsTransformationsRequest.transformations_list:type_name -> cx.ticket.TicketDetailsTransformation
	66,  // 74: cx.ticket.CreateTicketDetailsTransformationsResponse.status:type_name -> rpc.Status
	87,  // 75: cx.ticket.CreateTicketDetailsTransformationsResponse.transformations_list:type_name -> cx.ticket.TicketDetailsTransformation
	87,  // 76: cx.ticket.UpdateTicketDetailsTransformationRequest.transformation:type_name -> cx.ticket.TicketDetailsTransformation
	88,  // 77: cx.ticket.UpdateTicketDetailsTransformationRequest.update_mask:type_name -> cx.ticket.TicketDetailsTransformationFieldMask
	66,  // 78: cx.ticket.UpdateTicketDetailsTransformationResponse.status:type_name -> rpc.Status
	89,  // 79: cx.ticket.DeleteTicketDetailsTransformationsRequest.transformation_type_list:type_name -> cx.ticket.TicketTransformationType
	90,  // 80: cx.ticket.DeleteTicketDetailsTransformationsRequest.product_category_list:type_name -> cx.ticket.ProductCategory
	66,  // 81: cx.ticket.DeleteTicketDetailsTransformationsResponse.status:type_name -> rpc.Status
	68,  // 82: cx.ticket.UpdateTicketAsyncRequest.ticket:type_name -> cx.ticket.Ticket
	66,  // 83: cx.ticket.UpdateTicketAsyncResponse.status:type_name -> rpc.Status
	66,  // 84: cx.ticket.AddPrivateNoteAsyncResponse.status:type_name -> rpc.Status
	68,  // 85: cx.ticket.CreateTicketRequest.ticket:type_name -> cx.ticket.Ticket
	66,  // 86: cx.ticket.CreateTicketResponse.status:type_name -> rpc.Status
	68,  // 87: cx.ticket.CreateTicketResponse.ticket:type_name -> cx.ticket.Ticket
	66,  // 88: cx.ticket.GetSupportTicketByIdForAppResponse.status:type_name -> rpc.Status
	86,  // 89: cx.ticket.GetSupportTicketByIdForAppResponse.ticket:type_name -> cx.ticket.TicketDetailsForUser
	66,  // 90: cx.ticket.GetMergedTicketsResponse.status:type_name -> rpc.Status
	68,  // 91: cx.ticket.CreateTicketAsyncRequest.ticket:type_name -> cx.ticket.Ticket
	91,  // 92: cx.ticket.CreateTicketAsyncRequest.client_request_info:type_name -> cx.ticket.ClientRequestInfo
	66,  // 93: cx.ticket.CreateTicketAsyncResponse.status:type_name -> rpc.Status
	65,  // 94: cx.ticket.GetAgentInstructionForTicketResponse.FinalAgentInstruction.notes:type_name -> cx.ticket.GetAgentInstructionForTicketResponse.FinalAgentInstruction.Notes
	21,  // 95: cx.ticket.ticket.AttachEntity:input_type -> cx.ticket.AttachEntityRequest
	23,  // 96: cx.ticket.ticket.GetRelatedTickets:input_type -> cx.ticket.GetRelatedTicketsRequest
	25,  // 97: cx.ticket.ticket.MergeTickets:input_type -> cx.ticket.MergeTicketsRequest
	27,  // 98: cx.ticket.ticket.GetCallRecording:input_type -> cx.ticket.GetCallRecordingRequest
	29,  // 99: cx.ticket.ticket.GetCallTranscript:input_type -> cx.ticket.GetCallTranscriptRequest
	31,  // 100: cx.ticket.ticket.GetSupportTickets:input_type -> cx.ticket.GetSupportTicketsRequest
	33,  // 101: cx.ticket.ticket.BulkUpdateTickets:input_type -> cx.ticket.BulkUpdateTicketsRequest
	35,  // 102: cx.ticket.ticket.GetAllBulkTicketJobs:input_type -> cx.ticket.GetAllBulkTicketJobsRequest
	37,  // 103: cx.ticket.ticket.GetJobFailureLogs:input_type -> cx.ticket.GetJobFailureLogsRequest
	39,  // 104: cx.ticket.ticket.KillJobProcessing:input_type -> cx.ticket.KillJobProcessingRequest
	42,  // 105: cx.ticket.ticket.GetSupportTicketsForSherlock:input_type -> cx.ticket.GetSupportTicketsForSherlockRequest
	44,  // 106: cx.ticket.ticket.GetSupportTicketsForApp:input_type -> cx.ticket.GetSupportTicketsForAppRequest
	46,  // 107: cx.ticket.ticket.CreateTicketDetailsTransformations:input_type -> cx.ticket.CreateTicketDetailsTransformationsRequest
	48,  // 108: cx.ticket.ticket.UpdateTicketDetailsTransformation:input_type -> cx.ticket.UpdateTicketDetailsTransformationRequest
	50,  // 109: cx.ticket.ticket.DeleteTicketDetailsTransformations:input_type -> cx.ticket.DeleteTicketDetailsTransformationsRequest
	11,  // 110: cx.ticket.ticket.GetFreshdeskTicketCategories:input_type -> cx.ticket.GetFreshdeskTicketCategoriesRequest
	13,  // 111: cx.ticket.ticket.GetTicketInfo:input_type -> cx.ticket.GetTicketInfoRequest
	15,  // 112: cx.ticket.ticket.UpdateTicketInfo:input_type -> cx.ticket.UpdateTicketInfoRequest
	52,  // 113: cx.ticket.ticket.UpdateTicketAsync:input_type -> cx.ticket.UpdateTicketAsyncRequest
	54,  // 114: cx.ticket.ticket.AddPrivateNoteAsync:input_type -> cx.ticket.AddPrivateNoteAsyncRequest
	56,  // 115: cx.ticket.ticket.CreateTicket:input_type -> cx.ticket.CreateTicketRequest
	58,  // 116: cx.ticket.ticket.GetSupportTicketByIdForApp:input_type -> cx.ticket.GetSupportTicketByIdForAppRequest
	60,  // 117: cx.ticket.ticket.GetMergedTickets:input_type -> cx.ticket.GetMergedTicketsRequest
	7,   // 118: cx.ticket.ticket.GetTicketDetailsForSherlock:input_type -> cx.ticket.GetTicketDetailsForSherlockRequest
	9,   // 119: cx.ticket.ticket.GetAgentInstructionForTicket:input_type -> cx.ticket.GetAgentInstructionForTicketRequest
	62,  // 120: cx.ticket.ticket.CreateTicketAsync:input_type -> cx.ticket.CreateTicketAsyncRequest
	5,   // 121: cx.ticket.ticket.SubmitCsatFeedback:input_type -> cx.ticket.SubmitCsatFeedbackRequest
	3,   // 122: cx.ticket.ticket.GetCategoryTransformation:input_type -> cx.ticket.GetCategoryTransformationsRequest
	1,   // 123: cx.ticket.ticket.FetchLatestResolvedTicketIdForCSAT:input_type -> cx.ticket.FetchLatestResolvedTicketIdForCSATRequest
	22,  // 124: cx.ticket.ticket.AttachEntity:output_type -> cx.ticket.AttachEntityResponse
	24,  // 125: cx.ticket.ticket.GetRelatedTickets:output_type -> cx.ticket.GetRelatedTicketsResponse
	26,  // 126: cx.ticket.ticket.MergeTickets:output_type -> cx.ticket.MergeTicketsResponse
	28,  // 127: cx.ticket.ticket.GetCallRecording:output_type -> cx.ticket.GetCallRecordingResponse
	30,  // 128: cx.ticket.ticket.GetCallTranscript:output_type -> cx.ticket.GetCallTranscriptResponse
	32,  // 129: cx.ticket.ticket.GetSupportTickets:output_type -> cx.ticket.GetSupportTicketsResponse
	34,  // 130: cx.ticket.ticket.BulkUpdateTickets:output_type -> cx.ticket.BulkUpdateTicketsResponse
	36,  // 131: cx.ticket.ticket.GetAllBulkTicketJobs:output_type -> cx.ticket.GetAllBulkTicketJobsResponse
	38,  // 132: cx.ticket.ticket.GetJobFailureLogs:output_type -> cx.ticket.GetJobFailureLogsResponse
	40,  // 133: cx.ticket.ticket.KillJobProcessing:output_type -> cx.ticket.KillJobProcessingResponse
	43,  // 134: cx.ticket.ticket.GetSupportTicketsForSherlock:output_type -> cx.ticket.GetSupportTicketsForSherlockResponse
	45,  // 135: cx.ticket.ticket.GetSupportTicketsForApp:output_type -> cx.ticket.GetSupportTicketsForAppResponse
	47,  // 136: cx.ticket.ticket.CreateTicketDetailsTransformations:output_type -> cx.ticket.CreateTicketDetailsTransformationsResponse
	49,  // 137: cx.ticket.ticket.UpdateTicketDetailsTransformation:output_type -> cx.ticket.UpdateTicketDetailsTransformationResponse
	51,  // 138: cx.ticket.ticket.DeleteTicketDetailsTransformations:output_type -> cx.ticket.DeleteTicketDetailsTransformationsResponse
	12,  // 139: cx.ticket.ticket.GetFreshdeskTicketCategories:output_type -> cx.ticket.GetFreshdeskTicketCategoriesResponse
	14,  // 140: cx.ticket.ticket.GetTicketInfo:output_type -> cx.ticket.GetTicketInfoResponse
	16,  // 141: cx.ticket.ticket.UpdateTicketInfo:output_type -> cx.ticket.UpdateTicketInfoResponse
	53,  // 142: cx.ticket.ticket.UpdateTicketAsync:output_type -> cx.ticket.UpdateTicketAsyncResponse
	55,  // 143: cx.ticket.ticket.AddPrivateNoteAsync:output_type -> cx.ticket.AddPrivateNoteAsyncResponse
	57,  // 144: cx.ticket.ticket.CreateTicket:output_type -> cx.ticket.CreateTicketResponse
	59,  // 145: cx.ticket.ticket.GetSupportTicketByIdForApp:output_type -> cx.ticket.GetSupportTicketByIdForAppResponse
	61,  // 146: cx.ticket.ticket.GetMergedTickets:output_type -> cx.ticket.GetMergedTicketsResponse
	8,   // 147: cx.ticket.ticket.GetTicketDetailsForSherlock:output_type -> cx.ticket.GetTicketDetailsForSherlockResponse
	10,  // 148: cx.ticket.ticket.GetAgentInstructionForTicket:output_type -> cx.ticket.GetAgentInstructionForTicketResponse
	63,  // 149: cx.ticket.ticket.CreateTicketAsync:output_type -> cx.ticket.CreateTicketAsyncResponse
	6,   // 150: cx.ticket.ticket.SubmitCsatFeedback:output_type -> cx.ticket.SubmitCsatFeedbackResponse
	4,   // 151: cx.ticket.ticket.GetCategoryTransformation:output_type -> cx.ticket.GetCategoryTransformationsResponse
	2,   // 152: cx.ticket.ticket.FetchLatestResolvedTicketIdForCSAT:output_type -> cx.ticket.FetchLatestResolvedTicketIdForCSATResponse
	124, // [124:153] is the sub-list for method output_type
	95,  // [95:124] is the sub-list for method input_type
	95,  // [95:95] is the sub-list for extension type_name
	95,  // [95:95] is the sub-list for extension extendee
	0,   // [0:95] is the sub-list for field type_name
}

func init() { file_api_cx_ticket_service_proto_init() }
func file_api_cx_ticket_service_proto_init() {
	if File_api_cx_ticket_service_proto != nil {
		return
	}
	file_api_cx_ticket_attach_entity_proto_init()
	file_api_cx_ticket_enums_proto_init()
	file_api_cx_ticket_ticket_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_cx_ticket_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchLatestResolvedTicketIdForCSATRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchLatestResolvedTicketIdForCSATResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCategoryTransformationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCategoryTransformationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitCsatFeedbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitCsatFeedbackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTicketDetailsForSherlockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTicketDetailsForSherlockResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgentInstructionForTicketRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgentInstructionForTicketResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFreshdeskTicketCategoriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFreshdeskTicketCategoriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTicketInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTicketInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTicketInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTicketInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TicketCategories); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductCategoryChoice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductCategoryDetailsChoice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubcategoryChoice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttachEntityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttachEntityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRelatedTicketsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRelatedTicketsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeTicketsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeTicketsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCallRecordingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCallRecordingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCallTranscriptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCallTranscriptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupportTicketsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupportTicketsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkUpdateTicketsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkUpdateTicketsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllBulkTicketJobsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllBulkTicketJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobFailureLogsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobFailureLogsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KillJobProcessingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KillJobProcessingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContactDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupportTicketsForSherlockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupportTicketsForSherlockResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupportTicketsForAppRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupportTicketsForAppResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTicketDetailsTransformationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTicketDetailsTransformationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTicketDetailsTransformationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTicketDetailsTransformationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTicketDetailsTransformationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTicketDetailsTransformationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTicketAsyncRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTicketAsyncResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPrivateNoteAsyncRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPrivateNoteAsyncResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTicketRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTicketResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupportTicketByIdForAppRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupportTicketByIdForAppResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMergedTicketsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMergedTicketsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTicketAsyncRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTicketAsyncResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgentInstructionForTicketResponse_FinalAgentInstruction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_ticket_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_cx_ticket_service_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*GetAgentInstructionForTicketResponse_FinalAgentInstruction_)(nil),
		(*GetAgentInstructionForTicketResponse_AgentInstruction)(nil),
	}
	file_api_cx_ticket_service_proto_msgTypes[40].OneofWrappers = []interface{}{
		(*ContactDetails_EmailId)(nil),
		(*ContactDetails_PhoneNumber)(nil),
	}
	file_api_cx_ticket_service_proto_msgTypes[41].OneofWrappers = []interface{}{
		(*GetSupportTicketsForSherlockRequest_TicketFilters)(nil),
		(*GetSupportTicketsForSherlockRequest_ContactDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_ticket_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   65,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_cx_ticket_service_proto_goTypes,
		DependencyIndexes: file_api_cx_ticket_service_proto_depIdxs,
		EnumInfos:         file_api_cx_ticket_service_proto_enumTypes,
		MessageInfos:      file_api_cx_ticket_service_proto_msgTypes,
	}.Build()
	File_api_cx_ticket_service_proto = out.File
	file_api_cx_ticket_service_proto_rawDesc = nil
	file_api_cx_ticket_service_proto_goTypes = nil
	file_api_cx_ticket_service_proto_depIdxs = nil
}
