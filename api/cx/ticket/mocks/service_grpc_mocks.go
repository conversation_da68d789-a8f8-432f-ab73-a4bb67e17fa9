// Code generated by MockGen. DO NOT EDIT.
// Source: api/cx/ticket/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	ticket "github.com/epifi/gamma/api/cx/ticket"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
)

// MockTicketClient is a mock of TicketClient interface.
type MockTicketClient struct {
	ctrl     *gomock.Controller
	recorder *MockTicketClientMockRecorder
}

// MockTicketClientMockRecorder is the mock recorder for MockTicketClient.
type MockTicketClientMockRecorder struct {
	mock *MockTicketClient
}

// NewMockTicketClient creates a new mock instance.
func NewMockTicketClient(ctrl *gomock.Controller) *MockTicketClient {
	mock := &MockTicketClient{ctrl: ctrl}
	mock.recorder = &MockTicketClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTicketClient) EXPECT() *MockTicketClientMockRecorder {
	return m.recorder
}

// AddPrivateNoteAsync mocks base method.
func (m *MockTicketClient) AddPrivateNoteAsync(ctx context.Context, in *ticket.AddPrivateNoteAsyncRequest, opts ...grpc.CallOption) (*ticket.AddPrivateNoteAsyncResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddPrivateNoteAsync", varargs...)
	ret0, _ := ret[0].(*ticket.AddPrivateNoteAsyncResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPrivateNoteAsync indicates an expected call of AddPrivateNoteAsync.
func (mr *MockTicketClientMockRecorder) AddPrivateNoteAsync(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPrivateNoteAsync", reflect.TypeOf((*MockTicketClient)(nil).AddPrivateNoteAsync), varargs...)
}

// AttachEntity mocks base method.
func (m *MockTicketClient) AttachEntity(ctx context.Context, in *ticket.AttachEntityRequest, opts ...grpc.CallOption) (*ticket.AttachEntityResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AttachEntity", varargs...)
	ret0, _ := ret[0].(*ticket.AttachEntityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AttachEntity indicates an expected call of AttachEntity.
func (mr *MockTicketClientMockRecorder) AttachEntity(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AttachEntity", reflect.TypeOf((*MockTicketClient)(nil).AttachEntity), varargs...)
}

// BulkUpdateTickets mocks base method.
func (m *MockTicketClient) BulkUpdateTickets(ctx context.Context, in *ticket.BulkUpdateTicketsRequest, opts ...grpc.CallOption) (*ticket.BulkUpdateTicketsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BulkUpdateTickets", varargs...)
	ret0, _ := ret[0].(*ticket.BulkUpdateTicketsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkUpdateTickets indicates an expected call of BulkUpdateTickets.
func (mr *MockTicketClientMockRecorder) BulkUpdateTickets(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkUpdateTickets", reflect.TypeOf((*MockTicketClient)(nil).BulkUpdateTickets), varargs...)
}

// CreateTicket mocks base method.
func (m *MockTicketClient) CreateTicket(ctx context.Context, in *ticket.CreateTicketRequest, opts ...grpc.CallOption) (*ticket.CreateTicketResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTicket", varargs...)
	ret0, _ := ret[0].(*ticket.CreateTicketResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTicket indicates an expected call of CreateTicket.
func (mr *MockTicketClientMockRecorder) CreateTicket(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTicket", reflect.TypeOf((*MockTicketClient)(nil).CreateTicket), varargs...)
}

// CreateTicketAsync mocks base method.
func (m *MockTicketClient) CreateTicketAsync(ctx context.Context, in *ticket.CreateTicketAsyncRequest, opts ...grpc.CallOption) (*ticket.CreateTicketAsyncResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTicketAsync", varargs...)
	ret0, _ := ret[0].(*ticket.CreateTicketAsyncResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTicketAsync indicates an expected call of CreateTicketAsync.
func (mr *MockTicketClientMockRecorder) CreateTicketAsync(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTicketAsync", reflect.TypeOf((*MockTicketClient)(nil).CreateTicketAsync), varargs...)
}

// CreateTicketDetailsTransformations mocks base method.
func (m *MockTicketClient) CreateTicketDetailsTransformations(ctx context.Context, in *ticket.CreateTicketDetailsTransformationsRequest, opts ...grpc.CallOption) (*ticket.CreateTicketDetailsTransformationsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTicketDetailsTransformations", varargs...)
	ret0, _ := ret[0].(*ticket.CreateTicketDetailsTransformationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTicketDetailsTransformations indicates an expected call of CreateTicketDetailsTransformations.
func (mr *MockTicketClientMockRecorder) CreateTicketDetailsTransformations(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTicketDetailsTransformations", reflect.TypeOf((*MockTicketClient)(nil).CreateTicketDetailsTransformations), varargs...)
}

// DeleteTicketDetailsTransformations mocks base method.
func (m *MockTicketClient) DeleteTicketDetailsTransformations(ctx context.Context, in *ticket.DeleteTicketDetailsTransformationsRequest, opts ...grpc.CallOption) (*ticket.DeleteTicketDetailsTransformationsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTicketDetailsTransformations", varargs...)
	ret0, _ := ret[0].(*ticket.DeleteTicketDetailsTransformationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTicketDetailsTransformations indicates an expected call of DeleteTicketDetailsTransformations.
func (mr *MockTicketClientMockRecorder) DeleteTicketDetailsTransformations(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTicketDetailsTransformations", reflect.TypeOf((*MockTicketClient)(nil).DeleteTicketDetailsTransformations), varargs...)
}

// FetchLatestResolvedTicketIdForCSAT mocks base method.
func (m *MockTicketClient) FetchLatestResolvedTicketIdForCSAT(ctx context.Context, in *ticket.FetchLatestResolvedTicketIdForCSATRequest, opts ...grpc.CallOption) (*ticket.FetchLatestResolvedTicketIdForCSATResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchLatestResolvedTicketIdForCSAT", varargs...)
	ret0, _ := ret[0].(*ticket.FetchLatestResolvedTicketIdForCSATResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchLatestResolvedTicketIdForCSAT indicates an expected call of FetchLatestResolvedTicketIdForCSAT.
func (mr *MockTicketClientMockRecorder) FetchLatestResolvedTicketIdForCSAT(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchLatestResolvedTicketIdForCSAT", reflect.TypeOf((*MockTicketClient)(nil).FetchLatestResolvedTicketIdForCSAT), varargs...)
}

// GetAgentInstructionForTicket mocks base method.
func (m *MockTicketClient) GetAgentInstructionForTicket(ctx context.Context, in *ticket.GetAgentInstructionForTicketRequest, opts ...grpc.CallOption) (*ticket.GetAgentInstructionForTicketResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAgentInstructionForTicket", varargs...)
	ret0, _ := ret[0].(*ticket.GetAgentInstructionForTicketResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAgentInstructionForTicket indicates an expected call of GetAgentInstructionForTicket.
func (mr *MockTicketClientMockRecorder) GetAgentInstructionForTicket(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAgentInstructionForTicket", reflect.TypeOf((*MockTicketClient)(nil).GetAgentInstructionForTicket), varargs...)
}

// GetAllBulkTicketJobs mocks base method.
func (m *MockTicketClient) GetAllBulkTicketJobs(ctx context.Context, in *ticket.GetAllBulkTicketJobsRequest, opts ...grpc.CallOption) (*ticket.GetAllBulkTicketJobsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllBulkTicketJobs", varargs...)
	ret0, _ := ret[0].(*ticket.GetAllBulkTicketJobsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllBulkTicketJobs indicates an expected call of GetAllBulkTicketJobs.
func (mr *MockTicketClientMockRecorder) GetAllBulkTicketJobs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllBulkTicketJobs", reflect.TypeOf((*MockTicketClient)(nil).GetAllBulkTicketJobs), varargs...)
}

// GetCallRecording mocks base method.
func (m *MockTicketClient) GetCallRecording(ctx context.Context, in *ticket.GetCallRecordingRequest, opts ...grpc.CallOption) (ticket.Ticket_GetCallRecordingClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCallRecording", varargs...)
	ret0, _ := ret[0].(ticket.Ticket_GetCallRecordingClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCallRecording indicates an expected call of GetCallRecording.
func (mr *MockTicketClientMockRecorder) GetCallRecording(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCallRecording", reflect.TypeOf((*MockTicketClient)(nil).GetCallRecording), varargs...)
}

// GetCallTranscript mocks base method.
func (m *MockTicketClient) GetCallTranscript(ctx context.Context, in *ticket.GetCallTranscriptRequest, opts ...grpc.CallOption) (ticket.Ticket_GetCallTranscriptClient, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCallTranscript", varargs...)
	ret0, _ := ret[0].(ticket.Ticket_GetCallTranscriptClient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCallTranscript indicates an expected call of GetCallTranscript.
func (mr *MockTicketClientMockRecorder) GetCallTranscript(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCallTranscript", reflect.TypeOf((*MockTicketClient)(nil).GetCallTranscript), varargs...)
}

// GetCategoryTransformation mocks base method.
func (m *MockTicketClient) GetCategoryTransformation(ctx context.Context, in *ticket.GetCategoryTransformationsRequest, opts ...grpc.CallOption) (*ticket.GetCategoryTransformationsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCategoryTransformation", varargs...)
	ret0, _ := ret[0].(*ticket.GetCategoryTransformationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryTransformation indicates an expected call of GetCategoryTransformation.
func (mr *MockTicketClientMockRecorder) GetCategoryTransformation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryTransformation", reflect.TypeOf((*MockTicketClient)(nil).GetCategoryTransformation), varargs...)
}

// GetFreshdeskTicketCategories mocks base method.
func (m *MockTicketClient) GetFreshdeskTicketCategories(ctx context.Context, in *ticket.GetFreshdeskTicketCategoriesRequest, opts ...grpc.CallOption) (*ticket.GetFreshdeskTicketCategoriesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFreshdeskTicketCategories", varargs...)
	ret0, _ := ret[0].(*ticket.GetFreshdeskTicketCategoriesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFreshdeskTicketCategories indicates an expected call of GetFreshdeskTicketCategories.
func (mr *MockTicketClientMockRecorder) GetFreshdeskTicketCategories(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFreshdeskTicketCategories", reflect.TypeOf((*MockTicketClient)(nil).GetFreshdeskTicketCategories), varargs...)
}

// GetJobFailureLogs mocks base method.
func (m *MockTicketClient) GetJobFailureLogs(ctx context.Context, in *ticket.GetJobFailureLogsRequest, opts ...grpc.CallOption) (*ticket.GetJobFailureLogsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetJobFailureLogs", varargs...)
	ret0, _ := ret[0].(*ticket.GetJobFailureLogsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetJobFailureLogs indicates an expected call of GetJobFailureLogs.
func (mr *MockTicketClientMockRecorder) GetJobFailureLogs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetJobFailureLogs", reflect.TypeOf((*MockTicketClient)(nil).GetJobFailureLogs), varargs...)
}

// GetMergedTickets mocks base method.
func (m *MockTicketClient) GetMergedTickets(ctx context.Context, in *ticket.GetMergedTicketsRequest, opts ...grpc.CallOption) (*ticket.GetMergedTicketsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMergedTickets", varargs...)
	ret0, _ := ret[0].(*ticket.GetMergedTicketsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMergedTickets indicates an expected call of GetMergedTickets.
func (mr *MockTicketClientMockRecorder) GetMergedTickets(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMergedTickets", reflect.TypeOf((*MockTicketClient)(nil).GetMergedTickets), varargs...)
}

// GetRelatedTickets mocks base method.
func (m *MockTicketClient) GetRelatedTickets(ctx context.Context, in *ticket.GetRelatedTicketsRequest, opts ...grpc.CallOption) (*ticket.GetRelatedTicketsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRelatedTickets", varargs...)
	ret0, _ := ret[0].(*ticket.GetRelatedTicketsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelatedTickets indicates an expected call of GetRelatedTickets.
func (mr *MockTicketClientMockRecorder) GetRelatedTickets(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelatedTickets", reflect.TypeOf((*MockTicketClient)(nil).GetRelatedTickets), varargs...)
}

// GetSupportTicketByIdForApp mocks base method.
func (m *MockTicketClient) GetSupportTicketByIdForApp(ctx context.Context, in *ticket.GetSupportTicketByIdForAppRequest, opts ...grpc.CallOption) (*ticket.GetSupportTicketByIdForAppResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSupportTicketByIdForApp", varargs...)
	ret0, _ := ret[0].(*ticket.GetSupportTicketByIdForAppResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSupportTicketByIdForApp indicates an expected call of GetSupportTicketByIdForApp.
func (mr *MockTicketClientMockRecorder) GetSupportTicketByIdForApp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportTicketByIdForApp", reflect.TypeOf((*MockTicketClient)(nil).GetSupportTicketByIdForApp), varargs...)
}

// GetSupportTickets mocks base method.
func (m *MockTicketClient) GetSupportTickets(ctx context.Context, in *ticket.GetSupportTicketsRequest, opts ...grpc.CallOption) (*ticket.GetSupportTicketsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSupportTickets", varargs...)
	ret0, _ := ret[0].(*ticket.GetSupportTicketsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSupportTickets indicates an expected call of GetSupportTickets.
func (mr *MockTicketClientMockRecorder) GetSupportTickets(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportTickets", reflect.TypeOf((*MockTicketClient)(nil).GetSupportTickets), varargs...)
}

// GetSupportTicketsForApp mocks base method.
func (m *MockTicketClient) GetSupportTicketsForApp(ctx context.Context, in *ticket.GetSupportTicketsForAppRequest, opts ...grpc.CallOption) (*ticket.GetSupportTicketsForAppResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSupportTicketsForApp", varargs...)
	ret0, _ := ret[0].(*ticket.GetSupportTicketsForAppResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSupportTicketsForApp indicates an expected call of GetSupportTicketsForApp.
func (mr *MockTicketClientMockRecorder) GetSupportTicketsForApp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportTicketsForApp", reflect.TypeOf((*MockTicketClient)(nil).GetSupportTicketsForApp), varargs...)
}

// GetSupportTicketsForSherlock mocks base method.
func (m *MockTicketClient) GetSupportTicketsForSherlock(ctx context.Context, in *ticket.GetSupportTicketsForSherlockRequest, opts ...grpc.CallOption) (*ticket.GetSupportTicketsForSherlockResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSupportTicketsForSherlock", varargs...)
	ret0, _ := ret[0].(*ticket.GetSupportTicketsForSherlockResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSupportTicketsForSherlock indicates an expected call of GetSupportTicketsForSherlock.
func (mr *MockTicketClientMockRecorder) GetSupportTicketsForSherlock(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportTicketsForSherlock", reflect.TypeOf((*MockTicketClient)(nil).GetSupportTicketsForSherlock), varargs...)
}

// GetTicketDetailsForSherlock mocks base method.
func (m *MockTicketClient) GetTicketDetailsForSherlock(ctx context.Context, in *ticket.GetTicketDetailsForSherlockRequest, opts ...grpc.CallOption) (*ticket.GetTicketDetailsForSherlockResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTicketDetailsForSherlock", varargs...)
	ret0, _ := ret[0].(*ticket.GetTicketDetailsForSherlockResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTicketDetailsForSherlock indicates an expected call of GetTicketDetailsForSherlock.
func (mr *MockTicketClientMockRecorder) GetTicketDetailsForSherlock(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicketDetailsForSherlock", reflect.TypeOf((*MockTicketClient)(nil).GetTicketDetailsForSherlock), varargs...)
}

// GetTicketInfo mocks base method.
func (m *MockTicketClient) GetTicketInfo(ctx context.Context, in *ticket.GetTicketInfoRequest, opts ...grpc.CallOption) (*ticket.GetTicketInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTicketInfo", varargs...)
	ret0, _ := ret[0].(*ticket.GetTicketInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTicketInfo indicates an expected call of GetTicketInfo.
func (mr *MockTicketClientMockRecorder) GetTicketInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicketInfo", reflect.TypeOf((*MockTicketClient)(nil).GetTicketInfo), varargs...)
}

// KillJobProcessing mocks base method.
func (m *MockTicketClient) KillJobProcessing(ctx context.Context, in *ticket.KillJobProcessingRequest, opts ...grpc.CallOption) (*ticket.KillJobProcessingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "KillJobProcessing", varargs...)
	ret0, _ := ret[0].(*ticket.KillJobProcessingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// KillJobProcessing indicates an expected call of KillJobProcessing.
func (mr *MockTicketClientMockRecorder) KillJobProcessing(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KillJobProcessing", reflect.TypeOf((*MockTicketClient)(nil).KillJobProcessing), varargs...)
}

// MergeTickets mocks base method.
func (m *MockTicketClient) MergeTickets(ctx context.Context, in *ticket.MergeTicketsRequest, opts ...grpc.CallOption) (*ticket.MergeTicketsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MergeTickets", varargs...)
	ret0, _ := ret[0].(*ticket.MergeTicketsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MergeTickets indicates an expected call of MergeTickets.
func (mr *MockTicketClientMockRecorder) MergeTickets(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MergeTickets", reflect.TypeOf((*MockTicketClient)(nil).MergeTickets), varargs...)
}

// SubmitCsatFeedback mocks base method.
func (m *MockTicketClient) SubmitCsatFeedback(ctx context.Context, in *ticket.SubmitCsatFeedbackRequest, opts ...grpc.CallOption) (*ticket.SubmitCsatFeedbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitCsatFeedback", varargs...)
	ret0, _ := ret[0].(*ticket.SubmitCsatFeedbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitCsatFeedback indicates an expected call of SubmitCsatFeedback.
func (mr *MockTicketClientMockRecorder) SubmitCsatFeedback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitCsatFeedback", reflect.TypeOf((*MockTicketClient)(nil).SubmitCsatFeedback), varargs...)
}

// UpdateTicketAsync mocks base method.
func (m *MockTicketClient) UpdateTicketAsync(ctx context.Context, in *ticket.UpdateTicketAsyncRequest, opts ...grpc.CallOption) (*ticket.UpdateTicketAsyncResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTicketAsync", varargs...)
	ret0, _ := ret[0].(*ticket.UpdateTicketAsyncResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTicketAsync indicates an expected call of UpdateTicketAsync.
func (mr *MockTicketClientMockRecorder) UpdateTicketAsync(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTicketAsync", reflect.TypeOf((*MockTicketClient)(nil).UpdateTicketAsync), varargs...)
}

// UpdateTicketDetailsTransformation mocks base method.
func (m *MockTicketClient) UpdateTicketDetailsTransformation(ctx context.Context, in *ticket.UpdateTicketDetailsTransformationRequest, opts ...grpc.CallOption) (*ticket.UpdateTicketDetailsTransformationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTicketDetailsTransformation", varargs...)
	ret0, _ := ret[0].(*ticket.UpdateTicketDetailsTransformationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTicketDetailsTransformation indicates an expected call of UpdateTicketDetailsTransformation.
func (mr *MockTicketClientMockRecorder) UpdateTicketDetailsTransformation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTicketDetailsTransformation", reflect.TypeOf((*MockTicketClient)(nil).UpdateTicketDetailsTransformation), varargs...)
}

// UpdateTicketInfo mocks base method.
func (m *MockTicketClient) UpdateTicketInfo(ctx context.Context, in *ticket.UpdateTicketInfoRequest, opts ...grpc.CallOption) (*ticket.UpdateTicketInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTicketInfo", varargs...)
	ret0, _ := ret[0].(*ticket.UpdateTicketInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTicketInfo indicates an expected call of UpdateTicketInfo.
func (mr *MockTicketClientMockRecorder) UpdateTicketInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTicketInfo", reflect.TypeOf((*MockTicketClient)(nil).UpdateTicketInfo), varargs...)
}

// MockTicket_GetCallRecordingClient is a mock of Ticket_GetCallRecordingClient interface.
type MockTicket_GetCallRecordingClient struct {
	ctrl     *gomock.Controller
	recorder *MockTicket_GetCallRecordingClientMockRecorder
}

// MockTicket_GetCallRecordingClientMockRecorder is the mock recorder for MockTicket_GetCallRecordingClient.
type MockTicket_GetCallRecordingClientMockRecorder struct {
	mock *MockTicket_GetCallRecordingClient
}

// NewMockTicket_GetCallRecordingClient creates a new mock instance.
func NewMockTicket_GetCallRecordingClient(ctrl *gomock.Controller) *MockTicket_GetCallRecordingClient {
	mock := &MockTicket_GetCallRecordingClient{ctrl: ctrl}
	mock.recorder = &MockTicket_GetCallRecordingClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTicket_GetCallRecordingClient) EXPECT() *MockTicket_GetCallRecordingClientMockRecorder {
	return m.recorder
}

// CloseSend mocks base method.
func (m *MockTicket_GetCallRecordingClient) CloseSend() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseSend")
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseSend indicates an expected call of CloseSend.
func (mr *MockTicket_GetCallRecordingClientMockRecorder) CloseSend() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseSend", reflect.TypeOf((*MockTicket_GetCallRecordingClient)(nil).CloseSend))
}

// Context mocks base method.
func (m *MockTicket_GetCallRecordingClient) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockTicket_GetCallRecordingClientMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockTicket_GetCallRecordingClient)(nil).Context))
}

// Header mocks base method.
func (m *MockTicket_GetCallRecordingClient) Header() (metadata.MD, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Header")
	ret0, _ := ret[0].(metadata.MD)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Header indicates an expected call of Header.
func (mr *MockTicket_GetCallRecordingClientMockRecorder) Header() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Header", reflect.TypeOf((*MockTicket_GetCallRecordingClient)(nil).Header))
}

// Recv mocks base method.
func (m *MockTicket_GetCallRecordingClient) Recv() (*ticket.GetCallRecordingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Recv")
	ret0, _ := ret[0].(*ticket.GetCallRecordingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Recv indicates an expected call of Recv.
func (mr *MockTicket_GetCallRecordingClientMockRecorder) Recv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Recv", reflect.TypeOf((*MockTicket_GetCallRecordingClient)(nil).Recv))
}

// RecvMsg mocks base method.
func (m_2 *MockTicket_GetCallRecordingClient) RecvMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "RecvMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockTicket_GetCallRecordingClientMockRecorder) RecvMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockTicket_GetCallRecordingClient)(nil).RecvMsg), m)
}

// SendMsg mocks base method.
func (m_2 *MockTicket_GetCallRecordingClient) SendMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "SendMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockTicket_GetCallRecordingClientMockRecorder) SendMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockTicket_GetCallRecordingClient)(nil).SendMsg), m)
}

// Trailer mocks base method.
func (m *MockTicket_GetCallRecordingClient) Trailer() metadata.MD {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Trailer")
	ret0, _ := ret[0].(metadata.MD)
	return ret0
}

// Trailer indicates an expected call of Trailer.
func (mr *MockTicket_GetCallRecordingClientMockRecorder) Trailer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Trailer", reflect.TypeOf((*MockTicket_GetCallRecordingClient)(nil).Trailer))
}

// MockTicket_GetCallTranscriptClient is a mock of Ticket_GetCallTranscriptClient interface.
type MockTicket_GetCallTranscriptClient struct {
	ctrl     *gomock.Controller
	recorder *MockTicket_GetCallTranscriptClientMockRecorder
}

// MockTicket_GetCallTranscriptClientMockRecorder is the mock recorder for MockTicket_GetCallTranscriptClient.
type MockTicket_GetCallTranscriptClientMockRecorder struct {
	mock *MockTicket_GetCallTranscriptClient
}

// NewMockTicket_GetCallTranscriptClient creates a new mock instance.
func NewMockTicket_GetCallTranscriptClient(ctrl *gomock.Controller) *MockTicket_GetCallTranscriptClient {
	mock := &MockTicket_GetCallTranscriptClient{ctrl: ctrl}
	mock.recorder = &MockTicket_GetCallTranscriptClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTicket_GetCallTranscriptClient) EXPECT() *MockTicket_GetCallTranscriptClientMockRecorder {
	return m.recorder
}

// CloseSend mocks base method.
func (m *MockTicket_GetCallTranscriptClient) CloseSend() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseSend")
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseSend indicates an expected call of CloseSend.
func (mr *MockTicket_GetCallTranscriptClientMockRecorder) CloseSend() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseSend", reflect.TypeOf((*MockTicket_GetCallTranscriptClient)(nil).CloseSend))
}

// Context mocks base method.
func (m *MockTicket_GetCallTranscriptClient) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockTicket_GetCallTranscriptClientMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockTicket_GetCallTranscriptClient)(nil).Context))
}

// Header mocks base method.
func (m *MockTicket_GetCallTranscriptClient) Header() (metadata.MD, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Header")
	ret0, _ := ret[0].(metadata.MD)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Header indicates an expected call of Header.
func (mr *MockTicket_GetCallTranscriptClientMockRecorder) Header() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Header", reflect.TypeOf((*MockTicket_GetCallTranscriptClient)(nil).Header))
}

// Recv mocks base method.
func (m *MockTicket_GetCallTranscriptClient) Recv() (*ticket.GetCallTranscriptResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Recv")
	ret0, _ := ret[0].(*ticket.GetCallTranscriptResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Recv indicates an expected call of Recv.
func (mr *MockTicket_GetCallTranscriptClientMockRecorder) Recv() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Recv", reflect.TypeOf((*MockTicket_GetCallTranscriptClient)(nil).Recv))
}

// RecvMsg mocks base method.
func (m_2 *MockTicket_GetCallTranscriptClient) RecvMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "RecvMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockTicket_GetCallTranscriptClientMockRecorder) RecvMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockTicket_GetCallTranscriptClient)(nil).RecvMsg), m)
}

// SendMsg mocks base method.
func (m_2 *MockTicket_GetCallTranscriptClient) SendMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "SendMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockTicket_GetCallTranscriptClientMockRecorder) SendMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockTicket_GetCallTranscriptClient)(nil).SendMsg), m)
}

// Trailer mocks base method.
func (m *MockTicket_GetCallTranscriptClient) Trailer() metadata.MD {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Trailer")
	ret0, _ := ret[0].(metadata.MD)
	return ret0
}

// Trailer indicates an expected call of Trailer.
func (mr *MockTicket_GetCallTranscriptClientMockRecorder) Trailer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Trailer", reflect.TypeOf((*MockTicket_GetCallTranscriptClient)(nil).Trailer))
}

// MockTicketServer is a mock of TicketServer interface.
type MockTicketServer struct {
	ctrl     *gomock.Controller
	recorder *MockTicketServerMockRecorder
}

// MockTicketServerMockRecorder is the mock recorder for MockTicketServer.
type MockTicketServerMockRecorder struct {
	mock *MockTicketServer
}

// NewMockTicketServer creates a new mock instance.
func NewMockTicketServer(ctrl *gomock.Controller) *MockTicketServer {
	mock := &MockTicketServer{ctrl: ctrl}
	mock.recorder = &MockTicketServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTicketServer) EXPECT() *MockTicketServerMockRecorder {
	return m.recorder
}

// AddPrivateNoteAsync mocks base method.
func (m *MockTicketServer) AddPrivateNoteAsync(arg0 context.Context, arg1 *ticket.AddPrivateNoteAsyncRequest) (*ticket.AddPrivateNoteAsyncResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPrivateNoteAsync", arg0, arg1)
	ret0, _ := ret[0].(*ticket.AddPrivateNoteAsyncResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPrivateNoteAsync indicates an expected call of AddPrivateNoteAsync.
func (mr *MockTicketServerMockRecorder) AddPrivateNoteAsync(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPrivateNoteAsync", reflect.TypeOf((*MockTicketServer)(nil).AddPrivateNoteAsync), arg0, arg1)
}

// AttachEntity mocks base method.
func (m *MockTicketServer) AttachEntity(arg0 context.Context, arg1 *ticket.AttachEntityRequest) (*ticket.AttachEntityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AttachEntity", arg0, arg1)
	ret0, _ := ret[0].(*ticket.AttachEntityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AttachEntity indicates an expected call of AttachEntity.
func (mr *MockTicketServerMockRecorder) AttachEntity(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AttachEntity", reflect.TypeOf((*MockTicketServer)(nil).AttachEntity), arg0, arg1)
}

// BulkUpdateTickets mocks base method.
func (m *MockTicketServer) BulkUpdateTickets(arg0 context.Context, arg1 *ticket.BulkUpdateTicketsRequest) (*ticket.BulkUpdateTicketsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkUpdateTickets", arg0, arg1)
	ret0, _ := ret[0].(*ticket.BulkUpdateTicketsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkUpdateTickets indicates an expected call of BulkUpdateTickets.
func (mr *MockTicketServerMockRecorder) BulkUpdateTickets(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkUpdateTickets", reflect.TypeOf((*MockTicketServer)(nil).BulkUpdateTickets), arg0, arg1)
}

// CreateTicket mocks base method.
func (m *MockTicketServer) CreateTicket(arg0 context.Context, arg1 *ticket.CreateTicketRequest) (*ticket.CreateTicketResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTicket", arg0, arg1)
	ret0, _ := ret[0].(*ticket.CreateTicketResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTicket indicates an expected call of CreateTicket.
func (mr *MockTicketServerMockRecorder) CreateTicket(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTicket", reflect.TypeOf((*MockTicketServer)(nil).CreateTicket), arg0, arg1)
}

// CreateTicketAsync mocks base method.
func (m *MockTicketServer) CreateTicketAsync(arg0 context.Context, arg1 *ticket.CreateTicketAsyncRequest) (*ticket.CreateTicketAsyncResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTicketAsync", arg0, arg1)
	ret0, _ := ret[0].(*ticket.CreateTicketAsyncResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTicketAsync indicates an expected call of CreateTicketAsync.
func (mr *MockTicketServerMockRecorder) CreateTicketAsync(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTicketAsync", reflect.TypeOf((*MockTicketServer)(nil).CreateTicketAsync), arg0, arg1)
}

// CreateTicketDetailsTransformations mocks base method.
func (m *MockTicketServer) CreateTicketDetailsTransformations(arg0 context.Context, arg1 *ticket.CreateTicketDetailsTransformationsRequest) (*ticket.CreateTicketDetailsTransformationsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTicketDetailsTransformations", arg0, arg1)
	ret0, _ := ret[0].(*ticket.CreateTicketDetailsTransformationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTicketDetailsTransformations indicates an expected call of CreateTicketDetailsTransformations.
func (mr *MockTicketServerMockRecorder) CreateTicketDetailsTransformations(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTicketDetailsTransformations", reflect.TypeOf((*MockTicketServer)(nil).CreateTicketDetailsTransformations), arg0, arg1)
}

// DeleteTicketDetailsTransformations mocks base method.
func (m *MockTicketServer) DeleteTicketDetailsTransformations(arg0 context.Context, arg1 *ticket.DeleteTicketDetailsTransformationsRequest) (*ticket.DeleteTicketDetailsTransformationsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTicketDetailsTransformations", arg0, arg1)
	ret0, _ := ret[0].(*ticket.DeleteTicketDetailsTransformationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTicketDetailsTransformations indicates an expected call of DeleteTicketDetailsTransformations.
func (mr *MockTicketServerMockRecorder) DeleteTicketDetailsTransformations(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTicketDetailsTransformations", reflect.TypeOf((*MockTicketServer)(nil).DeleteTicketDetailsTransformations), arg0, arg1)
}

// FetchLatestResolvedTicketIdForCSAT mocks base method.
func (m *MockTicketServer) FetchLatestResolvedTicketIdForCSAT(arg0 context.Context, arg1 *ticket.FetchLatestResolvedTicketIdForCSATRequest) (*ticket.FetchLatestResolvedTicketIdForCSATResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchLatestResolvedTicketIdForCSAT", arg0, arg1)
	ret0, _ := ret[0].(*ticket.FetchLatestResolvedTicketIdForCSATResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchLatestResolvedTicketIdForCSAT indicates an expected call of FetchLatestResolvedTicketIdForCSAT.
func (mr *MockTicketServerMockRecorder) FetchLatestResolvedTicketIdForCSAT(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchLatestResolvedTicketIdForCSAT", reflect.TypeOf((*MockTicketServer)(nil).FetchLatestResolvedTicketIdForCSAT), arg0, arg1)
}

// GetAgentInstructionForTicket mocks base method.
func (m *MockTicketServer) GetAgentInstructionForTicket(arg0 context.Context, arg1 *ticket.GetAgentInstructionForTicketRequest) (*ticket.GetAgentInstructionForTicketResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAgentInstructionForTicket", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetAgentInstructionForTicketResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAgentInstructionForTicket indicates an expected call of GetAgentInstructionForTicket.
func (mr *MockTicketServerMockRecorder) GetAgentInstructionForTicket(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAgentInstructionForTicket", reflect.TypeOf((*MockTicketServer)(nil).GetAgentInstructionForTicket), arg0, arg1)
}

// GetAllBulkTicketJobs mocks base method.
func (m *MockTicketServer) GetAllBulkTicketJobs(arg0 context.Context, arg1 *ticket.GetAllBulkTicketJobsRequest) (*ticket.GetAllBulkTicketJobsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllBulkTicketJobs", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetAllBulkTicketJobsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllBulkTicketJobs indicates an expected call of GetAllBulkTicketJobs.
func (mr *MockTicketServerMockRecorder) GetAllBulkTicketJobs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllBulkTicketJobs", reflect.TypeOf((*MockTicketServer)(nil).GetAllBulkTicketJobs), arg0, arg1)
}

// GetCallRecording mocks base method.
func (m *MockTicketServer) GetCallRecording(arg0 *ticket.GetCallRecordingRequest, arg1 ticket.Ticket_GetCallRecordingServer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCallRecording", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetCallRecording indicates an expected call of GetCallRecording.
func (mr *MockTicketServerMockRecorder) GetCallRecording(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCallRecording", reflect.TypeOf((*MockTicketServer)(nil).GetCallRecording), arg0, arg1)
}

// GetCallTranscript mocks base method.
func (m *MockTicketServer) GetCallTranscript(arg0 *ticket.GetCallTranscriptRequest, arg1 ticket.Ticket_GetCallTranscriptServer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCallTranscript", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetCallTranscript indicates an expected call of GetCallTranscript.
func (mr *MockTicketServerMockRecorder) GetCallTranscript(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCallTranscript", reflect.TypeOf((*MockTicketServer)(nil).GetCallTranscript), arg0, arg1)
}

// GetCategoryTransformation mocks base method.
func (m *MockTicketServer) GetCategoryTransformation(arg0 context.Context, arg1 *ticket.GetCategoryTransformationsRequest) (*ticket.GetCategoryTransformationsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategoryTransformation", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetCategoryTransformationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryTransformation indicates an expected call of GetCategoryTransformation.
func (mr *MockTicketServerMockRecorder) GetCategoryTransformation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryTransformation", reflect.TypeOf((*MockTicketServer)(nil).GetCategoryTransformation), arg0, arg1)
}

// GetFreshdeskTicketCategories mocks base method.
func (m *MockTicketServer) GetFreshdeskTicketCategories(arg0 context.Context, arg1 *ticket.GetFreshdeskTicketCategoriesRequest) (*ticket.GetFreshdeskTicketCategoriesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFreshdeskTicketCategories", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetFreshdeskTicketCategoriesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFreshdeskTicketCategories indicates an expected call of GetFreshdeskTicketCategories.
func (mr *MockTicketServerMockRecorder) GetFreshdeskTicketCategories(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFreshdeskTicketCategories", reflect.TypeOf((*MockTicketServer)(nil).GetFreshdeskTicketCategories), arg0, arg1)
}

// GetJobFailureLogs mocks base method.
func (m *MockTicketServer) GetJobFailureLogs(arg0 context.Context, arg1 *ticket.GetJobFailureLogsRequest) (*ticket.GetJobFailureLogsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetJobFailureLogs", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetJobFailureLogsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetJobFailureLogs indicates an expected call of GetJobFailureLogs.
func (mr *MockTicketServerMockRecorder) GetJobFailureLogs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetJobFailureLogs", reflect.TypeOf((*MockTicketServer)(nil).GetJobFailureLogs), arg0, arg1)
}

// GetMergedTickets mocks base method.
func (m *MockTicketServer) GetMergedTickets(arg0 context.Context, arg1 *ticket.GetMergedTicketsRequest) (*ticket.GetMergedTicketsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMergedTickets", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetMergedTicketsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMergedTickets indicates an expected call of GetMergedTickets.
func (mr *MockTicketServerMockRecorder) GetMergedTickets(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMergedTickets", reflect.TypeOf((*MockTicketServer)(nil).GetMergedTickets), arg0, arg1)
}

// GetRelatedTickets mocks base method.
func (m *MockTicketServer) GetRelatedTickets(arg0 context.Context, arg1 *ticket.GetRelatedTicketsRequest) (*ticket.GetRelatedTicketsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelatedTickets", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetRelatedTicketsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelatedTickets indicates an expected call of GetRelatedTickets.
func (mr *MockTicketServerMockRecorder) GetRelatedTickets(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelatedTickets", reflect.TypeOf((*MockTicketServer)(nil).GetRelatedTickets), arg0, arg1)
}

// GetSupportTicketByIdForApp mocks base method.
func (m *MockTicketServer) GetSupportTicketByIdForApp(arg0 context.Context, arg1 *ticket.GetSupportTicketByIdForAppRequest) (*ticket.GetSupportTicketByIdForAppResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSupportTicketByIdForApp", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetSupportTicketByIdForAppResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSupportTicketByIdForApp indicates an expected call of GetSupportTicketByIdForApp.
func (mr *MockTicketServerMockRecorder) GetSupportTicketByIdForApp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportTicketByIdForApp", reflect.TypeOf((*MockTicketServer)(nil).GetSupportTicketByIdForApp), arg0, arg1)
}

// GetSupportTickets mocks base method.
func (m *MockTicketServer) GetSupportTickets(arg0 context.Context, arg1 *ticket.GetSupportTicketsRequest) (*ticket.GetSupportTicketsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSupportTickets", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetSupportTicketsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSupportTickets indicates an expected call of GetSupportTickets.
func (mr *MockTicketServerMockRecorder) GetSupportTickets(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportTickets", reflect.TypeOf((*MockTicketServer)(nil).GetSupportTickets), arg0, arg1)
}

// GetSupportTicketsForApp mocks base method.
func (m *MockTicketServer) GetSupportTicketsForApp(arg0 context.Context, arg1 *ticket.GetSupportTicketsForAppRequest) (*ticket.GetSupportTicketsForAppResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSupportTicketsForApp", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetSupportTicketsForAppResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSupportTicketsForApp indicates an expected call of GetSupportTicketsForApp.
func (mr *MockTicketServerMockRecorder) GetSupportTicketsForApp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportTicketsForApp", reflect.TypeOf((*MockTicketServer)(nil).GetSupportTicketsForApp), arg0, arg1)
}

// GetSupportTicketsForSherlock mocks base method.
func (m *MockTicketServer) GetSupportTicketsForSherlock(arg0 context.Context, arg1 *ticket.GetSupportTicketsForSherlockRequest) (*ticket.GetSupportTicketsForSherlockResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSupportTicketsForSherlock", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetSupportTicketsForSherlockResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSupportTicketsForSherlock indicates an expected call of GetSupportTicketsForSherlock.
func (mr *MockTicketServerMockRecorder) GetSupportTicketsForSherlock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportTicketsForSherlock", reflect.TypeOf((*MockTicketServer)(nil).GetSupportTicketsForSherlock), arg0, arg1)
}

// GetTicketDetailsForSherlock mocks base method.
func (m *MockTicketServer) GetTicketDetailsForSherlock(arg0 context.Context, arg1 *ticket.GetTicketDetailsForSherlockRequest) (*ticket.GetTicketDetailsForSherlockResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTicketDetailsForSherlock", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetTicketDetailsForSherlockResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTicketDetailsForSherlock indicates an expected call of GetTicketDetailsForSherlock.
func (mr *MockTicketServerMockRecorder) GetTicketDetailsForSherlock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicketDetailsForSherlock", reflect.TypeOf((*MockTicketServer)(nil).GetTicketDetailsForSherlock), arg0, arg1)
}

// GetTicketInfo mocks base method.
func (m *MockTicketServer) GetTicketInfo(arg0 context.Context, arg1 *ticket.GetTicketInfoRequest) (*ticket.GetTicketInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTicketInfo", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetTicketInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTicketInfo indicates an expected call of GetTicketInfo.
func (mr *MockTicketServerMockRecorder) GetTicketInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicketInfo", reflect.TypeOf((*MockTicketServer)(nil).GetTicketInfo), arg0, arg1)
}

// KillJobProcessing mocks base method.
func (m *MockTicketServer) KillJobProcessing(arg0 context.Context, arg1 *ticket.KillJobProcessingRequest) (*ticket.KillJobProcessingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KillJobProcessing", arg0, arg1)
	ret0, _ := ret[0].(*ticket.KillJobProcessingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// KillJobProcessing indicates an expected call of KillJobProcessing.
func (mr *MockTicketServerMockRecorder) KillJobProcessing(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KillJobProcessing", reflect.TypeOf((*MockTicketServer)(nil).KillJobProcessing), arg0, arg1)
}

// MergeTickets mocks base method.
func (m *MockTicketServer) MergeTickets(arg0 context.Context, arg1 *ticket.MergeTicketsRequest) (*ticket.MergeTicketsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MergeTickets", arg0, arg1)
	ret0, _ := ret[0].(*ticket.MergeTicketsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MergeTickets indicates an expected call of MergeTickets.
func (mr *MockTicketServerMockRecorder) MergeTickets(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MergeTickets", reflect.TypeOf((*MockTicketServer)(nil).MergeTickets), arg0, arg1)
}

// SubmitCsatFeedback mocks base method.
func (m *MockTicketServer) SubmitCsatFeedback(arg0 context.Context, arg1 *ticket.SubmitCsatFeedbackRequest) (*ticket.SubmitCsatFeedbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitCsatFeedback", arg0, arg1)
	ret0, _ := ret[0].(*ticket.SubmitCsatFeedbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitCsatFeedback indicates an expected call of SubmitCsatFeedback.
func (mr *MockTicketServerMockRecorder) SubmitCsatFeedback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitCsatFeedback", reflect.TypeOf((*MockTicketServer)(nil).SubmitCsatFeedback), arg0, arg1)
}

// UpdateTicketAsync mocks base method.
func (m *MockTicketServer) UpdateTicketAsync(arg0 context.Context, arg1 *ticket.UpdateTicketAsyncRequest) (*ticket.UpdateTicketAsyncResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTicketAsync", arg0, arg1)
	ret0, _ := ret[0].(*ticket.UpdateTicketAsyncResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTicketAsync indicates an expected call of UpdateTicketAsync.
func (mr *MockTicketServerMockRecorder) UpdateTicketAsync(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTicketAsync", reflect.TypeOf((*MockTicketServer)(nil).UpdateTicketAsync), arg0, arg1)
}

// UpdateTicketDetailsTransformation mocks base method.
func (m *MockTicketServer) UpdateTicketDetailsTransformation(arg0 context.Context, arg1 *ticket.UpdateTicketDetailsTransformationRequest) (*ticket.UpdateTicketDetailsTransformationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTicketDetailsTransformation", arg0, arg1)
	ret0, _ := ret[0].(*ticket.UpdateTicketDetailsTransformationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTicketDetailsTransformation indicates an expected call of UpdateTicketDetailsTransformation.
func (mr *MockTicketServerMockRecorder) UpdateTicketDetailsTransformation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTicketDetailsTransformation", reflect.TypeOf((*MockTicketServer)(nil).UpdateTicketDetailsTransformation), arg0, arg1)
}

// UpdateTicketInfo mocks base method.
func (m *MockTicketServer) UpdateTicketInfo(arg0 context.Context, arg1 *ticket.UpdateTicketInfoRequest) (*ticket.UpdateTicketInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTicketInfo", arg0, arg1)
	ret0, _ := ret[0].(*ticket.UpdateTicketInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTicketInfo indicates an expected call of UpdateTicketInfo.
func (mr *MockTicketServerMockRecorder) UpdateTicketInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTicketInfo", reflect.TypeOf((*MockTicketServer)(nil).UpdateTicketInfo), arg0, arg1)
}

// MockUnsafeTicketServer is a mock of UnsafeTicketServer interface.
type MockUnsafeTicketServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTicketServerMockRecorder
}

// MockUnsafeTicketServerMockRecorder is the mock recorder for MockUnsafeTicketServer.
type MockUnsafeTicketServerMockRecorder struct {
	mock *MockUnsafeTicketServer
}

// NewMockUnsafeTicketServer creates a new mock instance.
func NewMockUnsafeTicketServer(ctrl *gomock.Controller) *MockUnsafeTicketServer {
	mock := &MockUnsafeTicketServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTicketServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTicketServer) EXPECT() *MockUnsafeTicketServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTicketServer mocks base method.
func (m *MockUnsafeTicketServer) mustEmbedUnimplementedTicketServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTicketServer")
}

// mustEmbedUnimplementedTicketServer indicates an expected call of mustEmbedUnimplementedTicketServer.
func (mr *MockUnsafeTicketServerMockRecorder) mustEmbedUnimplementedTicketServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTicketServer", reflect.TypeOf((*MockUnsafeTicketServer)(nil).mustEmbedUnimplementedTicketServer))
}

// MockTicket_GetCallRecordingServer is a mock of Ticket_GetCallRecordingServer interface.
type MockTicket_GetCallRecordingServer struct {
	ctrl     *gomock.Controller
	recorder *MockTicket_GetCallRecordingServerMockRecorder
}

// MockTicket_GetCallRecordingServerMockRecorder is the mock recorder for MockTicket_GetCallRecordingServer.
type MockTicket_GetCallRecordingServerMockRecorder struct {
	mock *MockTicket_GetCallRecordingServer
}

// NewMockTicket_GetCallRecordingServer creates a new mock instance.
func NewMockTicket_GetCallRecordingServer(ctrl *gomock.Controller) *MockTicket_GetCallRecordingServer {
	mock := &MockTicket_GetCallRecordingServer{ctrl: ctrl}
	mock.recorder = &MockTicket_GetCallRecordingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTicket_GetCallRecordingServer) EXPECT() *MockTicket_GetCallRecordingServerMockRecorder {
	return m.recorder
}

// Context mocks base method.
func (m *MockTicket_GetCallRecordingServer) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockTicket_GetCallRecordingServerMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockTicket_GetCallRecordingServer)(nil).Context))
}

// RecvMsg mocks base method.
func (m_2 *MockTicket_GetCallRecordingServer) RecvMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "RecvMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockTicket_GetCallRecordingServerMockRecorder) RecvMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockTicket_GetCallRecordingServer)(nil).RecvMsg), m)
}

// Send mocks base method.
func (m *MockTicket_GetCallRecordingServer) Send(arg0 *ticket.GetCallRecordingResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send indicates an expected call of Send.
func (mr *MockTicket_GetCallRecordingServerMockRecorder) Send(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockTicket_GetCallRecordingServer)(nil).Send), arg0)
}

// SendHeader mocks base method.
func (m *MockTicket_GetCallRecordingServer) SendHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendHeader indicates an expected call of SendHeader.
func (mr *MockTicket_GetCallRecordingServerMockRecorder) SendHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendHeader", reflect.TypeOf((*MockTicket_GetCallRecordingServer)(nil).SendHeader), arg0)
}

// SendMsg mocks base method.
func (m_2 *MockTicket_GetCallRecordingServer) SendMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "SendMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockTicket_GetCallRecordingServerMockRecorder) SendMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockTicket_GetCallRecordingServer)(nil).SendMsg), m)
}

// SetHeader mocks base method.
func (m *MockTicket_GetCallRecordingServer) SetHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetHeader indicates an expected call of SetHeader.
func (mr *MockTicket_GetCallRecordingServerMockRecorder) SetHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeader", reflect.TypeOf((*MockTicket_GetCallRecordingServer)(nil).SetHeader), arg0)
}

// SetTrailer mocks base method.
func (m *MockTicket_GetCallRecordingServer) SetTrailer(arg0 metadata.MD) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetTrailer", arg0)
}

// SetTrailer indicates an expected call of SetTrailer.
func (mr *MockTicket_GetCallRecordingServerMockRecorder) SetTrailer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTrailer", reflect.TypeOf((*MockTicket_GetCallRecordingServer)(nil).SetTrailer), arg0)
}

// MockTicket_GetCallTranscriptServer is a mock of Ticket_GetCallTranscriptServer interface.
type MockTicket_GetCallTranscriptServer struct {
	ctrl     *gomock.Controller
	recorder *MockTicket_GetCallTranscriptServerMockRecorder
}

// MockTicket_GetCallTranscriptServerMockRecorder is the mock recorder for MockTicket_GetCallTranscriptServer.
type MockTicket_GetCallTranscriptServerMockRecorder struct {
	mock *MockTicket_GetCallTranscriptServer
}

// NewMockTicket_GetCallTranscriptServer creates a new mock instance.
func NewMockTicket_GetCallTranscriptServer(ctrl *gomock.Controller) *MockTicket_GetCallTranscriptServer {
	mock := &MockTicket_GetCallTranscriptServer{ctrl: ctrl}
	mock.recorder = &MockTicket_GetCallTranscriptServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTicket_GetCallTranscriptServer) EXPECT() *MockTicket_GetCallTranscriptServerMockRecorder {
	return m.recorder
}

// Context mocks base method.
func (m *MockTicket_GetCallTranscriptServer) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockTicket_GetCallTranscriptServerMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockTicket_GetCallTranscriptServer)(nil).Context))
}

// RecvMsg mocks base method.
func (m_2 *MockTicket_GetCallTranscriptServer) RecvMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "RecvMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecvMsg indicates an expected call of RecvMsg.
func (mr *MockTicket_GetCallTranscriptServerMockRecorder) RecvMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecvMsg", reflect.TypeOf((*MockTicket_GetCallTranscriptServer)(nil).RecvMsg), m)
}

// Send mocks base method.
func (m *MockTicket_GetCallTranscriptServer) Send(arg0 *ticket.GetCallTranscriptResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send indicates an expected call of Send.
func (mr *MockTicket_GetCallTranscriptServerMockRecorder) Send(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockTicket_GetCallTranscriptServer)(nil).Send), arg0)
}

// SendHeader mocks base method.
func (m *MockTicket_GetCallTranscriptServer) SendHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendHeader indicates an expected call of SendHeader.
func (mr *MockTicket_GetCallTranscriptServerMockRecorder) SendHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendHeader", reflect.TypeOf((*MockTicket_GetCallTranscriptServer)(nil).SendHeader), arg0)
}

// SendMsg mocks base method.
func (m_2 *MockTicket_GetCallTranscriptServer) SendMsg(m any) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "SendMsg", m)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMsg indicates an expected call of SendMsg.
func (mr *MockTicket_GetCallTranscriptServerMockRecorder) SendMsg(m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsg", reflect.TypeOf((*MockTicket_GetCallTranscriptServer)(nil).SendMsg), m)
}

// SetHeader mocks base method.
func (m *MockTicket_GetCallTranscriptServer) SetHeader(arg0 metadata.MD) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHeader", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetHeader indicates an expected call of SetHeader.
func (mr *MockTicket_GetCallTranscriptServerMockRecorder) SetHeader(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeader", reflect.TypeOf((*MockTicket_GetCallTranscriptServer)(nil).SetHeader), arg0)
}

// SetTrailer mocks base method.
func (m *MockTicket_GetCallTranscriptServer) SetTrailer(arg0 metadata.MD) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetTrailer", arg0)
}

// SetTrailer indicates an expected call of SetTrailer.
func (mr *MockTicket_GetCallTranscriptServerMockRecorder) SetTrailer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTrailer", reflect.TypeOf((*MockTicket_GetCallTranscriptServer)(nil).SetTrailer), arg0)
}
