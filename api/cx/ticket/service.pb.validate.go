// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/ticket/service.proto

package ticket

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)
)

// Validate checks the field values on
// FetchLatestResolvedTicketIdForCSATRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FetchLatestResolvedTicketIdForCSATRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchLatestResolvedTicketIdForCSATRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FetchLatestResolvedTicketIdForCSATRequestMultiError, or nil if none found.
func (m *FetchLatestResolvedTicketIdForCSATRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchLatestResolvedTicketIdForCSATRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return FetchLatestResolvedTicketIdForCSATRequestMultiError(errors)
	}

	return nil
}

// FetchLatestResolvedTicketIdForCSATRequestMultiError is an error wrapping
// multiple validation errors returned by
// FetchLatestResolvedTicketIdForCSATRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchLatestResolvedTicketIdForCSATRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchLatestResolvedTicketIdForCSATRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchLatestResolvedTicketIdForCSATRequestMultiError) AllErrors() []error { return m }

// FetchLatestResolvedTicketIdForCSATRequestValidationError is the validation
// error returned by FetchLatestResolvedTicketIdForCSATRequest.Validate if the
// designated constraints aren't met.
type FetchLatestResolvedTicketIdForCSATRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchLatestResolvedTicketIdForCSATRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchLatestResolvedTicketIdForCSATRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchLatestResolvedTicketIdForCSATRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchLatestResolvedTicketIdForCSATRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchLatestResolvedTicketIdForCSATRequestValidationError) ErrorName() string {
	return "FetchLatestResolvedTicketIdForCSATRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchLatestResolvedTicketIdForCSATRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchLatestResolvedTicketIdForCSATRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchLatestResolvedTicketIdForCSATRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchLatestResolvedTicketIdForCSATRequestValidationError{}

// Validate checks the field values on
// FetchLatestResolvedTicketIdForCSATResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FetchLatestResolvedTicketIdForCSATResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchLatestResolvedTicketIdForCSATResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FetchLatestResolvedTicketIdForCSATResponseMultiError, or nil if none found.
func (m *FetchLatestResolvedTicketIdForCSATResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchLatestResolvedTicketIdForCSATResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchLatestResolvedTicketIdForCSATResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchLatestResolvedTicketIdForCSATResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchLatestResolvedTicketIdForCSATResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TicketId

	if len(errors) > 0 {
		return FetchLatestResolvedTicketIdForCSATResponseMultiError(errors)
	}

	return nil
}

// FetchLatestResolvedTicketIdForCSATResponseMultiError is an error wrapping
// multiple validation errors returned by
// FetchLatestResolvedTicketIdForCSATResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchLatestResolvedTicketIdForCSATResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchLatestResolvedTicketIdForCSATResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchLatestResolvedTicketIdForCSATResponseMultiError) AllErrors() []error { return m }

// FetchLatestResolvedTicketIdForCSATResponseValidationError is the validation
// error returned by FetchLatestResolvedTicketIdForCSATResponse.Validate if
// the designated constraints aren't met.
type FetchLatestResolvedTicketIdForCSATResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchLatestResolvedTicketIdForCSATResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchLatestResolvedTicketIdForCSATResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchLatestResolvedTicketIdForCSATResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchLatestResolvedTicketIdForCSATResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchLatestResolvedTicketIdForCSATResponseValidationError) ErrorName() string {
	return "FetchLatestResolvedTicketIdForCSATResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchLatestResolvedTicketIdForCSATResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchLatestResolvedTicketIdForCSATResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchLatestResolvedTicketIdForCSATResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchLatestResolvedTicketIdForCSATResponseValidationError{}

// Validate checks the field values on GetCategoryTransformationsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCategoryTransformationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCategoryTransformationsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCategoryTransformationsRequestMultiError, or nil if none found.
func (m *GetCategoryTransformationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCategoryTransformationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCategoryTransformationsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCategoryTransformationsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCategoryTransformationsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCategoryTransformationsRequestValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCategoryTransformationsRequestValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCategoryTransformationsRequestValidationError{
				field:  "Ticket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCategoryTransformationsRequestMultiError(errors)
	}

	return nil
}

// GetCategoryTransformationsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetCategoryTransformationsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCategoryTransformationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCategoryTransformationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCategoryTransformationsRequestMultiError) AllErrors() []error { return m }

// GetCategoryTransformationsRequestValidationError is the validation error
// returned by GetCategoryTransformationsRequest.Validate if the designated
// constraints aren't met.
type GetCategoryTransformationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCategoryTransformationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCategoryTransformationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCategoryTransformationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCategoryTransformationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCategoryTransformationsRequestValidationError) ErrorName() string {
	return "GetCategoryTransformationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCategoryTransformationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCategoryTransformationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCategoryTransformationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCategoryTransformationsRequestValidationError{}

// Validate checks the field values on GetCategoryTransformationsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCategoryTransformationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCategoryTransformationsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCategoryTransformationsResponseMultiError, or nil if none found.
func (m *GetCategoryTransformationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCategoryTransformationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCategoryTransformationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCategoryTransformationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCategoryTransformationsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Title

	// no validation rules for Description

	if len(errors) > 0 {
		return GetCategoryTransformationsResponseMultiError(errors)
	}

	return nil
}

// GetCategoryTransformationsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetCategoryTransformationsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCategoryTransformationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCategoryTransformationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCategoryTransformationsResponseMultiError) AllErrors() []error { return m }

// GetCategoryTransformationsResponseValidationError is the validation error
// returned by GetCategoryTransformationsResponse.Validate if the designated
// constraints aren't met.
type GetCategoryTransformationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCategoryTransformationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCategoryTransformationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCategoryTransformationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCategoryTransformationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCategoryTransformationsResponseValidationError) ErrorName() string {
	return "GetCategoryTransformationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCategoryTransformationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCategoryTransformationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCategoryTransformationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCategoryTransformationsResponseValidationError{}

// Validate checks the field values on SubmitCsatFeedbackRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitCsatFeedbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitCsatFeedbackRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitCsatFeedbackRequestMultiError, or nil if none found.
func (m *SubmitCsatFeedbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitCsatFeedbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestIdentifierToken

	// no validation rules for Score

	// no validation rules for Feedback

	if len(errors) > 0 {
		return SubmitCsatFeedbackRequestMultiError(errors)
	}

	return nil
}

// SubmitCsatFeedbackRequestMultiError is an error wrapping multiple validation
// errors returned by SubmitCsatFeedbackRequest.ValidateAll() if the
// designated constraints aren't met.
type SubmitCsatFeedbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitCsatFeedbackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitCsatFeedbackRequestMultiError) AllErrors() []error { return m }

// SubmitCsatFeedbackRequestValidationError is the validation error returned by
// SubmitCsatFeedbackRequest.Validate if the designated constraints aren't met.
type SubmitCsatFeedbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitCsatFeedbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitCsatFeedbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitCsatFeedbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitCsatFeedbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitCsatFeedbackRequestValidationError) ErrorName() string {
	return "SubmitCsatFeedbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitCsatFeedbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitCsatFeedbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitCsatFeedbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitCsatFeedbackRequestValidationError{}

// Validate checks the field values on SubmitCsatFeedbackResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitCsatFeedbackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitCsatFeedbackResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitCsatFeedbackResponseMultiError, or nil if none found.
func (m *SubmitCsatFeedbackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitCsatFeedbackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitCsatFeedbackResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitCsatFeedbackResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitCsatFeedbackResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitCsatFeedbackResponseMultiError(errors)
	}

	return nil
}

// SubmitCsatFeedbackResponseMultiError is an error wrapping multiple
// validation errors returned by SubmitCsatFeedbackResponse.ValidateAll() if
// the designated constraints aren't met.
type SubmitCsatFeedbackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitCsatFeedbackResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitCsatFeedbackResponseMultiError) AllErrors() []error { return m }

// SubmitCsatFeedbackResponseValidationError is the validation error returned
// by SubmitCsatFeedbackResponse.Validate if the designated constraints aren't met.
type SubmitCsatFeedbackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitCsatFeedbackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitCsatFeedbackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitCsatFeedbackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitCsatFeedbackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitCsatFeedbackResponseValidationError) ErrorName() string {
	return "SubmitCsatFeedbackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitCsatFeedbackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitCsatFeedbackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitCsatFeedbackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitCsatFeedbackResponseValidationError{}

// Validate checks the field values on GetTicketDetailsForSherlockRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTicketDetailsForSherlockRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTicketDetailsForSherlockRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetTicketDetailsForSherlockRequestMultiError, or nil if none found.
func (m *GetTicketDetailsForSherlockRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTicketDetailsForSherlockRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketDetailsForSherlockRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketDetailsForSherlockRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketDetailsForSherlockRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTicketDetailsForSherlockRequestMultiError(errors)
	}

	return nil
}

// GetTicketDetailsForSherlockRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetTicketDetailsForSherlockRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTicketDetailsForSherlockRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTicketDetailsForSherlockRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTicketDetailsForSherlockRequestMultiError) AllErrors() []error { return m }

// GetTicketDetailsForSherlockRequestValidationError is the validation error
// returned by GetTicketDetailsForSherlockRequest.Validate if the designated
// constraints aren't met.
type GetTicketDetailsForSherlockRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTicketDetailsForSherlockRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTicketDetailsForSherlockRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTicketDetailsForSherlockRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTicketDetailsForSherlockRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTicketDetailsForSherlockRequestValidationError) ErrorName() string {
	return "GetTicketDetailsForSherlockRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTicketDetailsForSherlockRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTicketDetailsForSherlockRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTicketDetailsForSherlockRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTicketDetailsForSherlockRequestValidationError{}

// Validate checks the field values on GetTicketDetailsForSherlockResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTicketDetailsForSherlockResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTicketDetailsForSherlockResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetTicketDetailsForSherlockResponseMultiError, or nil if none found.
func (m *GetTicketDetailsForSherlockResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTicketDetailsForSherlockResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketDetailsForSherlockResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketDetailsForSherlockResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketDetailsForSherlockResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDetailViews() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTicketDetailsForSherlockResponseValidationError{
						field:  fmt.Sprintf("DetailViews[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTicketDetailsForSherlockResponseValidationError{
						field:  fmt.Sprintf("DetailViews[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTicketDetailsForSherlockResponseValidationError{
					field:  fmt.Sprintf("DetailViews[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetInfoComponentViews() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTicketDetailsForSherlockResponseValidationError{
						field:  fmt.Sprintf("InfoComponentViews[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTicketDetailsForSherlockResponseValidationError{
						field:  fmt.Sprintf("InfoComponentViews[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTicketDetailsForSherlockResponseValidationError{
					field:  fmt.Sprintf("InfoComponentViews[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTicketDetailsForSherlockResponseMultiError(errors)
	}

	return nil
}

// GetTicketDetailsForSherlockResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetTicketDetailsForSherlockResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTicketDetailsForSherlockResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTicketDetailsForSherlockResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTicketDetailsForSherlockResponseMultiError) AllErrors() []error { return m }

// GetTicketDetailsForSherlockResponseValidationError is the validation error
// returned by GetTicketDetailsForSherlockResponse.Validate if the designated
// constraints aren't met.
type GetTicketDetailsForSherlockResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTicketDetailsForSherlockResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTicketDetailsForSherlockResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTicketDetailsForSherlockResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTicketDetailsForSherlockResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTicketDetailsForSherlockResponseValidationError) ErrorName() string {
	return "GetTicketDetailsForSherlockResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTicketDetailsForSherlockResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTicketDetailsForSherlockResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTicketDetailsForSherlockResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTicketDetailsForSherlockResponseValidationError{}

// Validate checks the field values on GetAgentInstructionForTicketRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAgentInstructionForTicketRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAgentInstructionForTicketRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAgentInstructionForTicketRequestMultiError, or nil if none found.
func (m *GetAgentInstructionForTicketRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentInstructionForTicketRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAgentInstructionForTicketRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAgentInstructionForTicketRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAgentInstructionForTicketRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PrevInstructionType

	if len(errors) > 0 {
		return GetAgentInstructionForTicketRequestMultiError(errors)
	}

	return nil
}

// GetAgentInstructionForTicketRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAgentInstructionForTicketRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAgentInstructionForTicketRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentInstructionForTicketRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentInstructionForTicketRequestMultiError) AllErrors() []error { return m }

// GetAgentInstructionForTicketRequestValidationError is the validation error
// returned by GetAgentInstructionForTicketRequest.Validate if the designated
// constraints aren't met.
type GetAgentInstructionForTicketRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentInstructionForTicketRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAgentInstructionForTicketRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAgentInstructionForTicketRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAgentInstructionForTicketRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAgentInstructionForTicketRequestValidationError) ErrorName() string {
	return "GetAgentInstructionForTicketRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAgentInstructionForTicketRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentInstructionForTicketRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentInstructionForTicketRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentInstructionForTicketRequestValidationError{}

// Validate checks the field values on GetAgentInstructionForTicketResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetAgentInstructionForTicketResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAgentInstructionForTicketResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAgentInstructionForTicketResponseMultiError, or nil if none found.
func (m *GetAgentInstructionForTicketResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentInstructionForTicketResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAgentInstructionForTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAgentInstructionForTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAgentInstructionForTicketResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InstructionType

	switch v := m.Instruction.(type) {
	case *GetAgentInstructionForTicketResponse_FinalAgentInstruction_:
		if v == nil {
			err := GetAgentInstructionForTicketResponseValidationError{
				field:  "Instruction",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFinalAgentInstruction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAgentInstructionForTicketResponseValidationError{
						field:  "FinalAgentInstruction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAgentInstructionForTicketResponseValidationError{
						field:  "FinalAgentInstruction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFinalAgentInstruction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAgentInstructionForTicketResponseValidationError{
					field:  "FinalAgentInstruction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetAgentInstructionForTicketResponse_AgentInstruction:
		if v == nil {
			err := GetAgentInstructionForTicketResponseValidationError{
				field:  "Instruction",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAgentInstruction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAgentInstructionForTicketResponseValidationError{
						field:  "AgentInstruction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAgentInstructionForTicketResponseValidationError{
						field:  "AgentInstruction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAgentInstruction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAgentInstructionForTicketResponseValidationError{
					field:  "AgentInstruction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetAgentInstructionForTicketResponseMultiError(errors)
	}

	return nil
}

// GetAgentInstructionForTicketResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAgentInstructionForTicketResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAgentInstructionForTicketResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentInstructionForTicketResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentInstructionForTicketResponseMultiError) AllErrors() []error { return m }

// GetAgentInstructionForTicketResponseValidationError is the validation error
// returned by GetAgentInstructionForTicketResponse.Validate if the designated
// constraints aren't met.
type GetAgentInstructionForTicketResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentInstructionForTicketResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAgentInstructionForTicketResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAgentInstructionForTicketResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAgentInstructionForTicketResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAgentInstructionForTicketResponseValidationError) ErrorName() string {
	return "GetAgentInstructionForTicketResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAgentInstructionForTicketResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentInstructionForTicketResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentInstructionForTicketResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentInstructionForTicketResponseValidationError{}

// Validate checks the field values on GetFreshdeskTicketCategoriesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFreshdeskTicketCategoriesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFreshdeskTicketCategoriesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFreshdeskTicketCategoriesRequestMultiError, or nil if none found.
func (m *GetFreshdeskTicketCategoriesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFreshdeskTicketCategoriesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFreshdeskTicketCategoriesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFreshdeskTicketCategoriesRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFreshdeskTicketCategoriesRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFreshdeskTicketCategoriesRequestMultiError(errors)
	}

	return nil
}

// GetFreshdeskTicketCategoriesRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetFreshdeskTicketCategoriesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFreshdeskTicketCategoriesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFreshdeskTicketCategoriesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFreshdeskTicketCategoriesRequestMultiError) AllErrors() []error { return m }

// GetFreshdeskTicketCategoriesRequestValidationError is the validation error
// returned by GetFreshdeskTicketCategoriesRequest.Validate if the designated
// constraints aren't met.
type GetFreshdeskTicketCategoriesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFreshdeskTicketCategoriesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFreshdeskTicketCategoriesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFreshdeskTicketCategoriesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFreshdeskTicketCategoriesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFreshdeskTicketCategoriesRequestValidationError) ErrorName() string {
	return "GetFreshdeskTicketCategoriesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFreshdeskTicketCategoriesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFreshdeskTicketCategoriesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFreshdeskTicketCategoriesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFreshdeskTicketCategoriesRequestValidationError{}

// Validate checks the field values on GetFreshdeskTicketCategoriesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetFreshdeskTicketCategoriesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFreshdeskTicketCategoriesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFreshdeskTicketCategoriesResponseMultiError, or nil if none found.
func (m *GetFreshdeskTicketCategoriesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFreshdeskTicketCategoriesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFreshdeskTicketCategoriesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFreshdeskTicketCategoriesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFreshdeskTicketCategoriesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicketCategories()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFreshdeskTicketCategoriesResponseValidationError{
					field:  "TicketCategories",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFreshdeskTicketCategoriesResponseValidationError{
					field:  "TicketCategories",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicketCategories()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFreshdeskTicketCategoriesResponseValidationError{
				field:  "TicketCategories",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFreshdeskTicketCategoriesResponseMultiError(errors)
	}

	return nil
}

// GetFreshdeskTicketCategoriesResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetFreshdeskTicketCategoriesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFreshdeskTicketCategoriesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFreshdeskTicketCategoriesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFreshdeskTicketCategoriesResponseMultiError) AllErrors() []error { return m }

// GetFreshdeskTicketCategoriesResponseValidationError is the validation error
// returned by GetFreshdeskTicketCategoriesResponse.Validate if the designated
// constraints aren't met.
type GetFreshdeskTicketCategoriesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFreshdeskTicketCategoriesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFreshdeskTicketCategoriesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFreshdeskTicketCategoriesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFreshdeskTicketCategoriesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFreshdeskTicketCategoriesResponseValidationError) ErrorName() string {
	return "GetFreshdeskTicketCategoriesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFreshdeskTicketCategoriesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFreshdeskTicketCategoriesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFreshdeskTicketCategoriesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFreshdeskTicketCategoriesResponseValidationError{}

// Validate checks the field values on GetTicketInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTicketInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTicketInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTicketInfoRequestMultiError, or nil if none found.
func (m *GetTicketInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTicketInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketInfoRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketInfoRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketInfoRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTicketInfoRequestMultiError(errors)
	}

	return nil
}

// GetTicketInfoRequestMultiError is an error wrapping multiple validation
// errors returned by GetTicketInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTicketInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTicketInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTicketInfoRequestMultiError) AllErrors() []error { return m }

// GetTicketInfoRequestValidationError is the validation error returned by
// GetTicketInfoRequest.Validate if the designated constraints aren't met.
type GetTicketInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTicketInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTicketInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTicketInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTicketInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTicketInfoRequestValidationError) ErrorName() string {
	return "GetTicketInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTicketInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTicketInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTicketInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTicketInfoRequestValidationError{}

// Validate checks the field values on GetTicketInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTicketInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTicketInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTicketInfoResponseMultiError, or nil if none found.
func (m *GetTicketInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTicketInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProductCategory

	// no validation rules for ProductCategoryDetail

	// no validation rules for Subcategory

	// no validation rules for Sla

	if all {
		switch v := interface{}(m.GetExpectedResolutionTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketInfoResponseValidationError{
					field:  "ExpectedResolutionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketInfoResponseValidationError{
					field:  "ExpectedResolutionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectedResolutionTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketInfoResponseValidationError{
				field:  "ExpectedResolutionTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Note

	// no validation rules for GuruLink

	// no validation rules for IsFcr

	if len(errors) > 0 {
		return GetTicketInfoResponseMultiError(errors)
	}

	return nil
}

// GetTicketInfoResponseMultiError is an error wrapping multiple validation
// errors returned by GetTicketInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTicketInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTicketInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTicketInfoResponseMultiError) AllErrors() []error { return m }

// GetTicketInfoResponseValidationError is the validation error returned by
// GetTicketInfoResponse.Validate if the designated constraints aren't met.
type GetTicketInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTicketInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTicketInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTicketInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTicketInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTicketInfoResponseValidationError) ErrorName() string {
	return "GetTicketInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTicketInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTicketInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTicketInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTicketInfoResponseValidationError{}

// Validate checks the field values on UpdateTicketInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTicketInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTicketInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTicketInfoRequestMultiError, or nil if none found.
func (m *UpdateTicketInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTicketInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTicketInfoRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTicketInfoRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTicketInfoRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProductCategory

	// no validation rules for ProductCategoryDetails

	// no validation rules for Subcategory

	if len(errors) > 0 {
		return UpdateTicketInfoRequestMultiError(errors)
	}

	return nil
}

// UpdateTicketInfoRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateTicketInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateTicketInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTicketInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTicketInfoRequestMultiError) AllErrors() []error { return m }

// UpdateTicketInfoRequestValidationError is the validation error returned by
// UpdateTicketInfoRequest.Validate if the designated constraints aren't met.
type UpdateTicketInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTicketInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTicketInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTicketInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTicketInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTicketInfoRequestValidationError) ErrorName() string {
	return "UpdateTicketInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTicketInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTicketInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTicketInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTicketInfoRequestValidationError{}

// Validate checks the field values on UpdateTicketInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTicketInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTicketInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTicketInfoResponseMultiError, or nil if none found.
func (m *UpdateTicketInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTicketInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTicketInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTicketInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTicketInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateTicketInfoResponseMultiError(errors)
	}

	return nil
}

// UpdateTicketInfoResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateTicketInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateTicketInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTicketInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTicketInfoResponseMultiError) AllErrors() []error { return m }

// UpdateTicketInfoResponseValidationError is the validation error returned by
// UpdateTicketInfoResponse.Validate if the designated constraints aren't met.
type UpdateTicketInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTicketInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTicketInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTicketInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTicketInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTicketInfoResponseValidationError) ErrorName() string {
	return "UpdateTicketInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTicketInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTicketInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTicketInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTicketInfoResponseValidationError{}

// Validate checks the field values on TicketCategories with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TicketCategories) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TicketCategories with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TicketCategoriesMultiError, or nil if none found.
func (m *TicketCategories) ValidateAll() error {
	return m.validate(true)
}

func (m *TicketCategories) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetProductCategoryChoices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TicketCategoriesValidationError{
						field:  fmt.Sprintf("ProductCategoryChoices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TicketCategoriesValidationError{
						field:  fmt.Sprintf("ProductCategoryChoices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TicketCategoriesValidationError{
					field:  fmt.Sprintf("ProductCategoryChoices[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TicketCategoriesMultiError(errors)
	}

	return nil
}

// TicketCategoriesMultiError is an error wrapping multiple validation errors
// returned by TicketCategories.ValidateAll() if the designated constraints
// aren't met.
type TicketCategoriesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TicketCategoriesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TicketCategoriesMultiError) AllErrors() []error { return m }

// TicketCategoriesValidationError is the validation error returned by
// TicketCategories.Validate if the designated constraints aren't met.
type TicketCategoriesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TicketCategoriesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TicketCategoriesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TicketCategoriesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TicketCategoriesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TicketCategoriesValidationError) ErrorName() string { return "TicketCategoriesValidationError" }

// Error satisfies the builtin error interface
func (e TicketCategoriesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTicketCategories.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TicketCategoriesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TicketCategoriesValidationError{}

// Validate checks the field values on ProductCategoryChoice with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProductCategoryChoice) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProductCategoryChoice with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProductCategoryChoiceMultiError, or nil if none found.
func (m *ProductCategoryChoice) ValidateAll() error {
	return m.validate(true)
}

func (m *ProductCategoryChoice) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProductCategory

	for idx, item := range m.GetProductCategoryDetailsChoices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProductCategoryChoiceValidationError{
						field:  fmt.Sprintf("ProductCategoryDetailsChoices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProductCategoryChoiceValidationError{
						field:  fmt.Sprintf("ProductCategoryDetailsChoices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProductCategoryChoiceValidationError{
					field:  fmt.Sprintf("ProductCategoryDetailsChoices[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProductCategoryChoiceMultiError(errors)
	}

	return nil
}

// ProductCategoryChoiceMultiError is an error wrapping multiple validation
// errors returned by ProductCategoryChoice.ValidateAll() if the designated
// constraints aren't met.
type ProductCategoryChoiceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProductCategoryChoiceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProductCategoryChoiceMultiError) AllErrors() []error { return m }

// ProductCategoryChoiceValidationError is the validation error returned by
// ProductCategoryChoice.Validate if the designated constraints aren't met.
type ProductCategoryChoiceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProductCategoryChoiceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProductCategoryChoiceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProductCategoryChoiceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProductCategoryChoiceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProductCategoryChoiceValidationError) ErrorName() string {
	return "ProductCategoryChoiceValidationError"
}

// Error satisfies the builtin error interface
func (e ProductCategoryChoiceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProductCategoryChoice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProductCategoryChoiceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProductCategoryChoiceValidationError{}

// Validate checks the field values on ProductCategoryDetailsChoice with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProductCategoryDetailsChoice) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProductCategoryDetailsChoice with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProductCategoryDetailsChoiceMultiError, or nil if none found.
func (m *ProductCategoryDetailsChoice) ValidateAll() error {
	return m.validate(true)
}

func (m *ProductCategoryDetailsChoice) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProductCategoryDetails

	for idx, item := range m.GetSubcategoryChoices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProductCategoryDetailsChoiceValidationError{
						field:  fmt.Sprintf("SubcategoryChoices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProductCategoryDetailsChoiceValidationError{
						field:  fmt.Sprintf("SubcategoryChoices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProductCategoryDetailsChoiceValidationError{
					field:  fmt.Sprintf("SubcategoryChoices[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProductCategoryDetailsChoiceMultiError(errors)
	}

	return nil
}

// ProductCategoryDetailsChoiceMultiError is an error wrapping multiple
// validation errors returned by ProductCategoryDetailsChoice.ValidateAll() if
// the designated constraints aren't met.
type ProductCategoryDetailsChoiceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProductCategoryDetailsChoiceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProductCategoryDetailsChoiceMultiError) AllErrors() []error { return m }

// ProductCategoryDetailsChoiceValidationError is the validation error returned
// by ProductCategoryDetailsChoice.Validate if the designated constraints
// aren't met.
type ProductCategoryDetailsChoiceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProductCategoryDetailsChoiceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProductCategoryDetailsChoiceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProductCategoryDetailsChoiceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProductCategoryDetailsChoiceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProductCategoryDetailsChoiceValidationError) ErrorName() string {
	return "ProductCategoryDetailsChoiceValidationError"
}

// Error satisfies the builtin error interface
func (e ProductCategoryDetailsChoiceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProductCategoryDetailsChoice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProductCategoryDetailsChoiceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProductCategoryDetailsChoiceValidationError{}

// Validate checks the field values on SubcategoryChoice with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SubcategoryChoice) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubcategoryChoice with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubcategoryChoiceMultiError, or nil if none found.
func (m *SubcategoryChoice) ValidateAll() error {
	return m.validate(true)
}

func (m *SubcategoryChoice) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Subcategory

	if len(errors) > 0 {
		return SubcategoryChoiceMultiError(errors)
	}

	return nil
}

// SubcategoryChoiceMultiError is an error wrapping multiple validation errors
// returned by SubcategoryChoice.ValidateAll() if the designated constraints
// aren't met.
type SubcategoryChoiceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubcategoryChoiceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubcategoryChoiceMultiError) AllErrors() []error { return m }

// SubcategoryChoiceValidationError is the validation error returned by
// SubcategoryChoice.Validate if the designated constraints aren't met.
type SubcategoryChoiceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubcategoryChoiceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubcategoryChoiceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubcategoryChoiceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubcategoryChoiceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubcategoryChoiceValidationError) ErrorName() string {
	return "SubcategoryChoiceValidationError"
}

// Error satisfies the builtin error interface
func (e SubcategoryChoiceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubcategoryChoice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubcategoryChoiceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubcategoryChoiceValidationError{}

// Validate checks the field values on AttachEntityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AttachEntityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttachEntityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AttachEntityRequestMultiError, or nil if none found.
func (m *AttachEntityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AttachEntityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AttachEntityRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AttachEntityRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AttachEntityRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntityType

	// no validation rules for EntityId

	for idx, item := range m.GetAttachEntityMetaList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AttachEntityRequestValidationError{
						field:  fmt.Sprintf("AttachEntityMetaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AttachEntityRequestValidationError{
						field:  fmt.Sprintf("AttachEntityMetaList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AttachEntityRequestValidationError{
					field:  fmt.Sprintf("AttachEntityMetaList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AttachEntityRequestMultiError(errors)
	}

	return nil
}

// AttachEntityRequestMultiError is an error wrapping multiple validation
// errors returned by AttachEntityRequest.ValidateAll() if the designated
// constraints aren't met.
type AttachEntityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttachEntityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttachEntityRequestMultiError) AllErrors() []error { return m }

// AttachEntityRequestValidationError is the validation error returned by
// AttachEntityRequest.Validate if the designated constraints aren't met.
type AttachEntityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttachEntityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttachEntityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttachEntityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttachEntityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttachEntityRequestValidationError) ErrorName() string {
	return "AttachEntityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AttachEntityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttachEntityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttachEntityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttachEntityRequestValidationError{}

// Validate checks the field values on AttachEntityResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AttachEntityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttachEntityResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AttachEntityResponseMultiError, or nil if none found.
func (m *AttachEntityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AttachEntityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AttachEntityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AttachEntityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AttachEntityResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AttachEntityResponseMultiError(errors)
	}

	return nil
}

// AttachEntityResponseMultiError is an error wrapping multiple validation
// errors returned by AttachEntityResponse.ValidateAll() if the designated
// constraints aren't met.
type AttachEntityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttachEntityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttachEntityResponseMultiError) AllErrors() []error { return m }

// AttachEntityResponseValidationError is the validation error returned by
// AttachEntityResponse.Validate if the designated constraints aren't met.
type AttachEntityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttachEntityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttachEntityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttachEntityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttachEntityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttachEntityResponseValidationError) ErrorName() string {
	return "AttachEntityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AttachEntityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttachEntityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttachEntityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttachEntityResponseValidationError{}

// Validate checks the field values on GetRelatedTicketsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRelatedTicketsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRelatedTicketsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRelatedTicketsRequestMultiError, or nil if none found.
func (m *GetRelatedTicketsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRelatedTicketsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelatedTicketsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelatedTicketsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelatedTicketsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRelatedTicketsRequestMultiError(errors)
	}

	return nil
}

// GetRelatedTicketsRequestMultiError is an error wrapping multiple validation
// errors returned by GetRelatedTicketsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRelatedTicketsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRelatedTicketsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRelatedTicketsRequestMultiError) AllErrors() []error { return m }

// GetRelatedTicketsRequestValidationError is the validation error returned by
// GetRelatedTicketsRequest.Validate if the designated constraints aren't met.
type GetRelatedTicketsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRelatedTicketsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRelatedTicketsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRelatedTicketsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRelatedTicketsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRelatedTicketsRequestValidationError) ErrorName() string {
	return "GetRelatedTicketsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRelatedTicketsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRelatedTicketsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRelatedTicketsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRelatedTicketsRequestValidationError{}

// Validate checks the field values on GetRelatedTicketsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRelatedTicketsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRelatedTicketsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRelatedTicketsResponseMultiError, or nil if none found.
func (m *GetRelatedTicketsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRelatedTicketsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelatedTicketsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelatedTicketsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelatedTicketsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTickets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRelatedTicketsResponseValidationError{
						field:  fmt.Sprintf("Tickets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRelatedTicketsResponseValidationError{
						field:  fmt.Sprintf("Tickets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRelatedTicketsResponseValidationError{
					field:  fmt.Sprintf("Tickets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRelatedTicketsResponseMultiError(errors)
	}

	return nil
}

// GetRelatedTicketsResponseMultiError is an error wrapping multiple validation
// errors returned by GetRelatedTicketsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetRelatedTicketsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRelatedTicketsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRelatedTicketsResponseMultiError) AllErrors() []error { return m }

// GetRelatedTicketsResponseValidationError is the validation error returned by
// GetRelatedTicketsResponse.Validate if the designated constraints aren't met.
type GetRelatedTicketsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRelatedTicketsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRelatedTicketsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRelatedTicketsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRelatedTicketsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRelatedTicketsResponseValidationError) ErrorName() string {
	return "GetRelatedTicketsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRelatedTicketsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRelatedTicketsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRelatedTicketsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRelatedTicketsResponseValidationError{}

// Validate checks the field values on MergeTicketsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MergeTicketsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MergeTicketsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MergeTicketsRequestMultiError, or nil if none found.
func (m *MergeTicketsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MergeTicketsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MergeTicketsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MergeTicketsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MergeTicketsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PrimaryTicketId

	// no validation rules for PrimaryTicketNote

	// no validation rules for SecondaryTicketNote

	if len(errors) > 0 {
		return MergeTicketsRequestMultiError(errors)
	}

	return nil
}

// MergeTicketsRequestMultiError is an error wrapping multiple validation
// errors returned by MergeTicketsRequest.ValidateAll() if the designated
// constraints aren't met.
type MergeTicketsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MergeTicketsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MergeTicketsRequestMultiError) AllErrors() []error { return m }

// MergeTicketsRequestValidationError is the validation error returned by
// MergeTicketsRequest.Validate if the designated constraints aren't met.
type MergeTicketsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MergeTicketsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MergeTicketsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MergeTicketsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MergeTicketsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MergeTicketsRequestValidationError) ErrorName() string {
	return "MergeTicketsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MergeTicketsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMergeTicketsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MergeTicketsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MergeTicketsRequestValidationError{}

// Validate checks the field values on MergeTicketsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MergeTicketsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MergeTicketsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MergeTicketsResponseMultiError, or nil if none found.
func (m *MergeTicketsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MergeTicketsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MergeTicketsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MergeTicketsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MergeTicketsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MergeTicketsResponseMultiError(errors)
	}

	return nil
}

// MergeTicketsResponseMultiError is an error wrapping multiple validation
// errors returned by MergeTicketsResponse.ValidateAll() if the designated
// constraints aren't met.
type MergeTicketsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MergeTicketsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MergeTicketsResponseMultiError) AllErrors() []error { return m }

// MergeTicketsResponseValidationError is the validation error returned by
// MergeTicketsResponse.Validate if the designated constraints aren't met.
type MergeTicketsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MergeTicketsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MergeTicketsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MergeTicketsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MergeTicketsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MergeTicketsResponseValidationError) ErrorName() string {
	return "MergeTicketsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MergeTicketsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMergeTicketsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MergeTicketsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MergeTicketsResponseValidationError{}

// Validate checks the field values on GetCallRecordingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCallRecordingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCallRecordingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCallRecordingRequestMultiError, or nil if none found.
func (m *GetCallRecordingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCallRecordingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCallRecordingRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCallRecordingRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCallRecordingRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecordingId

	if len(errors) > 0 {
		return GetCallRecordingRequestMultiError(errors)
	}

	return nil
}

// GetCallRecordingRequestMultiError is an error wrapping multiple validation
// errors returned by GetCallRecordingRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCallRecordingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCallRecordingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCallRecordingRequestMultiError) AllErrors() []error { return m }

// GetCallRecordingRequestValidationError is the validation error returned by
// GetCallRecordingRequest.Validate if the designated constraints aren't met.
type GetCallRecordingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCallRecordingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCallRecordingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCallRecordingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCallRecordingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCallRecordingRequestValidationError) ErrorName() string {
	return "GetCallRecordingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCallRecordingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCallRecordingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCallRecordingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCallRecordingRequestValidationError{}

// Validate checks the field values on GetCallRecordingResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCallRecordingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCallRecordingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCallRecordingResponseMultiError, or nil if none found.
func (m *GetCallRecordingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCallRecordingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCallRecordingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCallRecordingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCallRecordingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Chunk

	if len(errors) > 0 {
		return GetCallRecordingResponseMultiError(errors)
	}

	return nil
}

// GetCallRecordingResponseMultiError is an error wrapping multiple validation
// errors returned by GetCallRecordingResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCallRecordingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCallRecordingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCallRecordingResponseMultiError) AllErrors() []error { return m }

// GetCallRecordingResponseValidationError is the validation error returned by
// GetCallRecordingResponse.Validate if the designated constraints aren't met.
type GetCallRecordingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCallRecordingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCallRecordingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCallRecordingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCallRecordingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCallRecordingResponseValidationError) ErrorName() string {
	return "GetCallRecordingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCallRecordingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCallRecordingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCallRecordingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCallRecordingResponseValidationError{}

// Validate checks the field values on GetCallTranscriptRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCallTranscriptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCallTranscriptRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCallTranscriptRequestMultiError, or nil if none found.
func (m *GetCallTranscriptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCallTranscriptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCallTranscriptRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCallTranscriptRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCallTranscriptRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecordingId

	if len(errors) > 0 {
		return GetCallTranscriptRequestMultiError(errors)
	}

	return nil
}

// GetCallTranscriptRequestMultiError is an error wrapping multiple validation
// errors returned by GetCallTranscriptRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCallTranscriptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCallTranscriptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCallTranscriptRequestMultiError) AllErrors() []error { return m }

// GetCallTranscriptRequestValidationError is the validation error returned by
// GetCallTranscriptRequest.Validate if the designated constraints aren't met.
type GetCallTranscriptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCallTranscriptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCallTranscriptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCallTranscriptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCallTranscriptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCallTranscriptRequestValidationError) ErrorName() string {
	return "GetCallTranscriptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCallTranscriptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCallTranscriptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCallTranscriptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCallTranscriptRequestValidationError{}

// Validate checks the field values on GetCallTranscriptResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCallTranscriptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCallTranscriptResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCallTranscriptResponseMultiError, or nil if none found.
func (m *GetCallTranscriptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCallTranscriptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCallTranscriptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCallTranscriptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCallTranscriptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Chunk

	if len(errors) > 0 {
		return GetCallTranscriptResponseMultiError(errors)
	}

	return nil
}

// GetCallTranscriptResponseMultiError is an error wrapping multiple validation
// errors returned by GetCallTranscriptResponse.ValidateAll() if the
// designated constraints aren't met.
type GetCallTranscriptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCallTranscriptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCallTranscriptResponseMultiError) AllErrors() []error { return m }

// GetCallTranscriptResponseValidationError is the validation error returned by
// GetCallTranscriptResponse.Validate if the designated constraints aren't met.
type GetCallTranscriptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCallTranscriptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCallTranscriptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCallTranscriptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCallTranscriptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCallTranscriptResponseValidationError) ErrorName() string {
	return "GetCallTranscriptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCallTranscriptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCallTranscriptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCallTranscriptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCallTranscriptResponseValidationError{}

// Validate checks the field values on GetSupportTicketsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSupportTicketsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupportTicketsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSupportTicketsRequestMultiError, or nil if none found.
func (m *GetSupportTicketsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupportTicketsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TicketId

	if all {
		switch v := interface{}(m.GetTicketFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsRequestValidationError{
					field:  "TicketFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsRequestValidationError{
					field:  "TicketFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicketFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsRequestValidationError{
				field:  "TicketFilters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContextRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsRequestValidationError{
				field:  "PageContextRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSupportTicketsRequestMultiError(errors)
	}

	return nil
}

// GetSupportTicketsRequestMultiError is an error wrapping multiple validation
// errors returned by GetSupportTicketsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSupportTicketsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupportTicketsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupportTicketsRequestMultiError) AllErrors() []error { return m }

// GetSupportTicketsRequestValidationError is the validation error returned by
// GetSupportTicketsRequest.Validate if the designated constraints aren't met.
type GetSupportTicketsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupportTicketsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupportTicketsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupportTicketsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupportTicketsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupportTicketsRequestValidationError) ErrorName() string {
	return "GetSupportTicketsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSupportTicketsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupportTicketsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupportTicketsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupportTicketsRequestValidationError{}

// Validate checks the field values on GetSupportTicketsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSupportTicketsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupportTicketsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSupportTicketsResponseMultiError, or nil if none found.
func (m *GetSupportTicketsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupportTicketsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTickets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSupportTicketsResponseValidationError{
						field:  fmt.Sprintf("Tickets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSupportTicketsResponseValidationError{
						field:  fmt.Sprintf("Tickets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSupportTicketsResponseValidationError{
					field:  fmt.Sprintf("Tickets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContextResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsResponseValidationError{
				field:  "PageContextResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSupportTicketsResponseMultiError(errors)
	}

	return nil
}

// GetSupportTicketsResponseMultiError is an error wrapping multiple validation
// errors returned by GetSupportTicketsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetSupportTicketsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupportTicketsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupportTicketsResponseMultiError) AllErrors() []error { return m }

// GetSupportTicketsResponseValidationError is the validation error returned by
// GetSupportTicketsResponse.Validate if the designated constraints aren't met.
type GetSupportTicketsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupportTicketsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupportTicketsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupportTicketsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupportTicketsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupportTicketsResponseValidationError) ErrorName() string {
	return "GetSupportTicketsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSupportTicketsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupportTicketsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupportTicketsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupportTicketsResponseValidationError{}

// Validate checks the field values on BulkUpdateTicketsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkUpdateTicketsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkUpdateTicketsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkUpdateTicketsRequestMultiError, or nil if none found.
func (m *BulkUpdateTicketsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkUpdateTicketsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BulkUpdateTicketsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BulkUpdateTicketsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BulkUpdateTicketsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpdateTicketCsv

	// no validation rules for CheckerEmail

	// no validation rules for Description

	if len(errors) > 0 {
		return BulkUpdateTicketsRequestMultiError(errors)
	}

	return nil
}

// BulkUpdateTicketsRequestMultiError is an error wrapping multiple validation
// errors returned by BulkUpdateTicketsRequest.ValidateAll() if the designated
// constraints aren't met.
type BulkUpdateTicketsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkUpdateTicketsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkUpdateTicketsRequestMultiError) AllErrors() []error { return m }

// BulkUpdateTicketsRequestValidationError is the validation error returned by
// BulkUpdateTicketsRequest.Validate if the designated constraints aren't met.
type BulkUpdateTicketsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkUpdateTicketsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkUpdateTicketsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkUpdateTicketsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkUpdateTicketsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkUpdateTicketsRequestValidationError) ErrorName() string {
	return "BulkUpdateTicketsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BulkUpdateTicketsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkUpdateTicketsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkUpdateTicketsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkUpdateTicketsRequestValidationError{}

// Validate checks the field values on BulkUpdateTicketsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkUpdateTicketsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkUpdateTicketsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkUpdateTicketsResponseMultiError, or nil if none found.
func (m *BulkUpdateTicketsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkUpdateTicketsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BulkUpdateTicketsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BulkUpdateTicketsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BulkUpdateTicketsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for JobId

	if len(errors) > 0 {
		return BulkUpdateTicketsResponseMultiError(errors)
	}

	return nil
}

// BulkUpdateTicketsResponseMultiError is an error wrapping multiple validation
// errors returned by BulkUpdateTicketsResponse.ValidateAll() if the
// designated constraints aren't met.
type BulkUpdateTicketsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkUpdateTicketsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkUpdateTicketsResponseMultiError) AllErrors() []error { return m }

// BulkUpdateTicketsResponseValidationError is the validation error returned by
// BulkUpdateTicketsResponse.Validate if the designated constraints aren't met.
type BulkUpdateTicketsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkUpdateTicketsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkUpdateTicketsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkUpdateTicketsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkUpdateTicketsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkUpdateTicketsResponseValidationError) ErrorName() string {
	return "BulkUpdateTicketsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BulkUpdateTicketsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkUpdateTicketsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkUpdateTicketsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkUpdateTicketsResponseValidationError{}

// Validate checks the field values on GetAllBulkTicketJobsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllBulkTicketJobsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllBulkTicketJobsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllBulkTicketJobsRequestMultiError, or nil if none found.
func (m *GetAllBulkTicketJobsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllBulkTicketJobsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllBulkTicketJobsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllBulkTicketJobsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllBulkTicketJobsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllBulkTicketJobsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllBulkTicketJobsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllBulkTicketJobsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllBulkTicketJobsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllBulkTicketJobsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllBulkTicketJobsRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAllBulkTicketJobsRequestMultiError(errors)
	}

	return nil
}

// GetAllBulkTicketJobsRequestMultiError is an error wrapping multiple
// validation errors returned by GetAllBulkTicketJobsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAllBulkTicketJobsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllBulkTicketJobsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllBulkTicketJobsRequestMultiError) AllErrors() []error { return m }

// GetAllBulkTicketJobsRequestValidationError is the validation error returned
// by GetAllBulkTicketJobsRequest.Validate if the designated constraints
// aren't met.
type GetAllBulkTicketJobsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllBulkTicketJobsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllBulkTicketJobsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllBulkTicketJobsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllBulkTicketJobsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllBulkTicketJobsRequestValidationError) ErrorName() string {
	return "GetAllBulkTicketJobsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllBulkTicketJobsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllBulkTicketJobsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllBulkTicketJobsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllBulkTicketJobsRequestValidationError{}

// Validate checks the field values on GetAllBulkTicketJobsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllBulkTicketJobsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllBulkTicketJobsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllBulkTicketJobsResponseMultiError, or nil if none found.
func (m *GetAllBulkTicketJobsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllBulkTicketJobsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllBulkTicketJobsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllBulkTicketJobsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllBulkTicketJobsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetJobList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllBulkTicketJobsResponseValidationError{
						field:  fmt.Sprintf("JobList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllBulkTicketJobsResponseValidationError{
						field:  fmt.Sprintf("JobList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllBulkTicketJobsResponseValidationError{
					field:  fmt.Sprintf("JobList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContextResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllBulkTicketJobsResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllBulkTicketJobsResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllBulkTicketJobsResponseValidationError{
				field:  "PageContextResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAllBulkTicketJobsResponseMultiError(errors)
	}

	return nil
}

// GetAllBulkTicketJobsResponseMultiError is an error wrapping multiple
// validation errors returned by GetAllBulkTicketJobsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetAllBulkTicketJobsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllBulkTicketJobsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllBulkTicketJobsResponseMultiError) AllErrors() []error { return m }

// GetAllBulkTicketJobsResponseValidationError is the validation error returned
// by GetAllBulkTicketJobsResponse.Validate if the designated constraints
// aren't met.
type GetAllBulkTicketJobsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllBulkTicketJobsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllBulkTicketJobsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllBulkTicketJobsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllBulkTicketJobsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllBulkTicketJobsResponseValidationError) ErrorName() string {
	return "GetAllBulkTicketJobsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllBulkTicketJobsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllBulkTicketJobsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllBulkTicketJobsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllBulkTicketJobsResponseValidationError{}

// Validate checks the field values on GetJobFailureLogsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJobFailureLogsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobFailureLogsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobFailureLogsRequestMultiError, or nil if none found.
func (m *GetJobFailureLogsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobFailureLogsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetJobFailureLogsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetJobFailureLogsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetJobFailureLogsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for JobId

	if len(errors) > 0 {
		return GetJobFailureLogsRequestMultiError(errors)
	}

	return nil
}

// GetJobFailureLogsRequestMultiError is an error wrapping multiple validation
// errors returned by GetJobFailureLogsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetJobFailureLogsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobFailureLogsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobFailureLogsRequestMultiError) AllErrors() []error { return m }

// GetJobFailureLogsRequestValidationError is the validation error returned by
// GetJobFailureLogsRequest.Validate if the designated constraints aren't met.
type GetJobFailureLogsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobFailureLogsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobFailureLogsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobFailureLogsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobFailureLogsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobFailureLogsRequestValidationError) ErrorName() string {
	return "GetJobFailureLogsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetJobFailureLogsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobFailureLogsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobFailureLogsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobFailureLogsRequestValidationError{}

// Validate checks the field values on GetJobFailureLogsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetJobFailureLogsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetJobFailureLogsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetJobFailureLogsResponseMultiError, or nil if none found.
func (m *GetJobFailureLogsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetJobFailureLogsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetJobFailureLogsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetJobFailureLogsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetJobFailureLogsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFailureList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetJobFailureLogsResponseValidationError{
						field:  fmt.Sprintf("FailureList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetJobFailureLogsResponseValidationError{
						field:  fmt.Sprintf("FailureList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetJobFailureLogsResponseValidationError{
					field:  fmt.Sprintf("FailureList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetJobFailureLogsResponseMultiError(errors)
	}

	return nil
}

// GetJobFailureLogsResponseMultiError is an error wrapping multiple validation
// errors returned by GetJobFailureLogsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetJobFailureLogsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetJobFailureLogsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetJobFailureLogsResponseMultiError) AllErrors() []error { return m }

// GetJobFailureLogsResponseValidationError is the validation error returned by
// GetJobFailureLogsResponse.Validate if the designated constraints aren't met.
type GetJobFailureLogsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetJobFailureLogsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetJobFailureLogsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetJobFailureLogsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetJobFailureLogsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetJobFailureLogsResponseValidationError) ErrorName() string {
	return "GetJobFailureLogsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetJobFailureLogsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetJobFailureLogsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetJobFailureLogsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetJobFailureLogsResponseValidationError{}

// Validate checks the field values on KillJobProcessingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KillJobProcessingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KillJobProcessingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KillJobProcessingRequestMultiError, or nil if none found.
func (m *KillJobProcessingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *KillJobProcessingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KillJobProcessingRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KillJobProcessingRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KillJobProcessingRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for JobId

	if len(errors) > 0 {
		return KillJobProcessingRequestMultiError(errors)
	}

	return nil
}

// KillJobProcessingRequestMultiError is an error wrapping multiple validation
// errors returned by KillJobProcessingRequest.ValidateAll() if the designated
// constraints aren't met.
type KillJobProcessingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KillJobProcessingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KillJobProcessingRequestMultiError) AllErrors() []error { return m }

// KillJobProcessingRequestValidationError is the validation error returned by
// KillJobProcessingRequest.Validate if the designated constraints aren't met.
type KillJobProcessingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KillJobProcessingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KillJobProcessingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KillJobProcessingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KillJobProcessingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KillJobProcessingRequestValidationError) ErrorName() string {
	return "KillJobProcessingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e KillJobProcessingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKillJobProcessingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KillJobProcessingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KillJobProcessingRequestValidationError{}

// Validate checks the field values on KillJobProcessingResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KillJobProcessingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KillJobProcessingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KillJobProcessingResponseMultiError, or nil if none found.
func (m *KillJobProcessingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *KillJobProcessingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KillJobProcessingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KillJobProcessingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KillJobProcessingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KillJobProcessingResponseMultiError(errors)
	}

	return nil
}

// KillJobProcessingResponseMultiError is an error wrapping multiple validation
// errors returned by KillJobProcessingResponse.ValidateAll() if the
// designated constraints aren't met.
type KillJobProcessingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KillJobProcessingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KillJobProcessingResponseMultiError) AllErrors() []error { return m }

// KillJobProcessingResponseValidationError is the validation error returned by
// KillJobProcessingResponse.Validate if the designated constraints aren't met.
type KillJobProcessingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KillJobProcessingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KillJobProcessingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KillJobProcessingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KillJobProcessingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KillJobProcessingResponseValidationError) ErrorName() string {
	return "KillJobProcessingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e KillJobProcessingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKillJobProcessingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KillJobProcessingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KillJobProcessingResponseValidationError{}

// Validate checks the field values on ContactDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ContactDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContactDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ContactDetailsMultiError,
// or nil if none found.
func (m *ContactDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ContactDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.ContactInfo.(type) {
	case *ContactDetails_EmailId:
		if v == nil {
			err := ContactDetailsValidationError{
				field:  "ContactInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for EmailId
	case *ContactDetails_PhoneNumber:
		if v == nil {
			err := ContactDetailsValidationError{
				field:  "ContactInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPhoneNumber()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ContactDetailsValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ContactDetailsValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ContactDetailsValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ContactDetailsMultiError(errors)
	}

	return nil
}

// ContactDetailsMultiError is an error wrapping multiple validation errors
// returned by ContactDetails.ValidateAll() if the designated constraints
// aren't met.
type ContactDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContactDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContactDetailsMultiError) AllErrors() []error { return m }

// ContactDetailsValidationError is the validation error returned by
// ContactDetails.Validate if the designated constraints aren't met.
type ContactDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContactDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContactDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContactDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContactDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContactDetailsValidationError) ErrorName() string { return "ContactDetailsValidationError" }

// Error satisfies the builtin error interface
func (e ContactDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContactDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContactDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContactDetailsValidationError{}

// Validate checks the field values on GetSupportTicketsForSherlockRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSupportTicketsForSherlockRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupportTicketsForSherlockRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSupportTicketsForSherlockRequestMultiError, or nil if none found.
func (m *GetSupportTicketsForSherlockRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupportTicketsForSherlockRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := GetSupportTicketsForSherlockRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsForSherlockRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsForSherlockRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsForSherlockRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContextRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsForSherlockRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsForSherlockRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsForSherlockRequestValidationError{
				field:  "PageContextRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.FetchFilter.(type) {
	case *GetSupportTicketsForSherlockRequest_TicketFilters:
		if v == nil {
			err := GetSupportTicketsForSherlockRequestValidationError{
				field:  "FetchFilter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTicketFilters()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSupportTicketsForSherlockRequestValidationError{
						field:  "TicketFilters",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSupportTicketsForSherlockRequestValidationError{
						field:  "TicketFilters",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTicketFilters()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSupportTicketsForSherlockRequestValidationError{
					field:  "TicketFilters",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetSupportTicketsForSherlockRequest_ContactDetails:
		if v == nil {
			err := GetSupportTicketsForSherlockRequestValidationError{
				field:  "FetchFilter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetContactDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSupportTicketsForSherlockRequestValidationError{
						field:  "ContactDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSupportTicketsForSherlockRequestValidationError{
						field:  "ContactDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetContactDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSupportTicketsForSherlockRequestValidationError{
					field:  "ContactDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetSupportTicketsForSherlockRequestMultiError(errors)
	}

	return nil
}

// GetSupportTicketsForSherlockRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetSupportTicketsForSherlockRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSupportTicketsForSherlockRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupportTicketsForSherlockRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupportTicketsForSherlockRequestMultiError) AllErrors() []error { return m }

// GetSupportTicketsForSherlockRequestValidationError is the validation error
// returned by GetSupportTicketsForSherlockRequest.Validate if the designated
// constraints aren't met.
type GetSupportTicketsForSherlockRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupportTicketsForSherlockRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupportTicketsForSherlockRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupportTicketsForSherlockRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupportTicketsForSherlockRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupportTicketsForSherlockRequestValidationError) ErrorName() string {
	return "GetSupportTicketsForSherlockRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSupportTicketsForSherlockRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupportTicketsForSherlockRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupportTicketsForSherlockRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupportTicketsForSherlockRequestValidationError{}

// Validate checks the field values on GetSupportTicketsForSherlockResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetSupportTicketsForSherlockResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupportTicketsForSherlockResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSupportTicketsForSherlockResponseMultiError, or nil if none found.
func (m *GetSupportTicketsForSherlockResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupportTicketsForSherlockResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsForSherlockResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsForSherlockResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsForSherlockResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTickets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSupportTicketsForSherlockResponseValidationError{
						field:  fmt.Sprintf("Tickets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSupportTicketsForSherlockResponseValidationError{
						field:  fmt.Sprintf("Tickets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSupportTicketsForSherlockResponseValidationError{
					field:  fmt.Sprintf("Tickets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContextResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsForSherlockResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsForSherlockResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsForSherlockResponseValidationError{
				field:  "PageContextResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSupportTicketsForSherlockResponseMultiError(errors)
	}

	return nil
}

// GetSupportTicketsForSherlockResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetSupportTicketsForSherlockResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSupportTicketsForSherlockResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupportTicketsForSherlockResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupportTicketsForSherlockResponseMultiError) AllErrors() []error { return m }

// GetSupportTicketsForSherlockResponseValidationError is the validation error
// returned by GetSupportTicketsForSherlockResponse.Validate if the designated
// constraints aren't met.
type GetSupportTicketsForSherlockResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupportTicketsForSherlockResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupportTicketsForSherlockResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupportTicketsForSherlockResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupportTicketsForSherlockResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupportTicketsForSherlockResponseValidationError) ErrorName() string {
	return "GetSupportTicketsForSherlockResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSupportTicketsForSherlockResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupportTicketsForSherlockResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupportTicketsForSherlockResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupportTicketsForSherlockResponseValidationError{}

// Validate checks the field values on GetSupportTicketsForAppRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSupportTicketsForAppRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupportTicketsForAppRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetSupportTicketsForAppRequestMultiError, or nil if none found.
func (m *GetSupportTicketsForAppRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupportTicketsForAppRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetSupportTicketsForAppRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTicketFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsForAppRequestValidationError{
					field:  "TicketFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsForAppRequestValidationError{
					field:  "TicketFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicketFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsForAppRequestValidationError{
				field:  "TicketFilters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContextRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsForAppRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsForAppRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsForAppRequestValidationError{
				field:  "PageContextRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShouldUseCache

	if len(errors) > 0 {
		return GetSupportTicketsForAppRequestMultiError(errors)
	}

	return nil
}

// GetSupportTicketsForAppRequestMultiError is an error wrapping multiple
// validation errors returned by GetSupportTicketsForAppRequest.ValidateAll()
// if the designated constraints aren't met.
type GetSupportTicketsForAppRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupportTicketsForAppRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupportTicketsForAppRequestMultiError) AllErrors() []error { return m }

// GetSupportTicketsForAppRequestValidationError is the validation error
// returned by GetSupportTicketsForAppRequest.Validate if the designated
// constraints aren't met.
type GetSupportTicketsForAppRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupportTicketsForAppRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupportTicketsForAppRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupportTicketsForAppRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupportTicketsForAppRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupportTicketsForAppRequestValidationError) ErrorName() string {
	return "GetSupportTicketsForAppRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSupportTicketsForAppRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupportTicketsForAppRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupportTicketsForAppRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupportTicketsForAppRequestValidationError{}

// Validate checks the field values on GetSupportTicketsForAppResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSupportTicketsForAppResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupportTicketsForAppResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetSupportTicketsForAppResponseMultiError, or nil if none found.
func (m *GetSupportTicketsForAppResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupportTicketsForAppResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsForAppResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsForAppResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsForAppResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTickets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSupportTicketsForAppResponseValidationError{
						field:  fmt.Sprintf("Tickets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSupportTicketsForAppResponseValidationError{
						field:  fmt.Sprintf("Tickets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSupportTicketsForAppResponseValidationError{
					field:  fmt.Sprintf("Tickets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsLatestTicketDetailsStillUpdating

	if all {
		switch v := interface{}(m.GetPageContextResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsForAppResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsForAppResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsForAppResponseValidationError{
				field:  "PageContextResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSupportTicketsForAppResponseMultiError(errors)
	}

	return nil
}

// GetSupportTicketsForAppResponseMultiError is an error wrapping multiple
// validation errors returned by GetSupportTicketsForAppResponse.ValidateAll()
// if the designated constraints aren't met.
type GetSupportTicketsForAppResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupportTicketsForAppResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupportTicketsForAppResponseMultiError) AllErrors() []error { return m }

// GetSupportTicketsForAppResponseValidationError is the validation error
// returned by GetSupportTicketsForAppResponse.Validate if the designated
// constraints aren't met.
type GetSupportTicketsForAppResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupportTicketsForAppResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupportTicketsForAppResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupportTicketsForAppResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupportTicketsForAppResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupportTicketsForAppResponseValidationError) ErrorName() string {
	return "GetSupportTicketsForAppResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSupportTicketsForAppResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupportTicketsForAppResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupportTicketsForAppResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupportTicketsForAppResponseValidationError{}

// Validate checks the field values on
// CreateTicketDetailsTransformationsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateTicketDetailsTransformationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateTicketDetailsTransformationsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreateTicketDetailsTransformationsRequestMultiError, or nil if none found.
func (m *CreateTicketDetailsTransformationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTicketDetailsTransformationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTransformationsList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateTicketDetailsTransformationsRequestValidationError{
						field:  fmt.Sprintf("TransformationsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateTicketDetailsTransformationsRequestValidationError{
						field:  fmt.Sprintf("TransformationsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateTicketDetailsTransformationsRequestValidationError{
					field:  fmt.Sprintf("TransformationsList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateTicketDetailsTransformationsRequestMultiError(errors)
	}

	return nil
}

// CreateTicketDetailsTransformationsRequestMultiError is an error wrapping
// multiple validation errors returned by
// CreateTicketDetailsTransformationsRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateTicketDetailsTransformationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTicketDetailsTransformationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTicketDetailsTransformationsRequestMultiError) AllErrors() []error { return m }

// CreateTicketDetailsTransformationsRequestValidationError is the validation
// error returned by CreateTicketDetailsTransformationsRequest.Validate if the
// designated constraints aren't met.
type CreateTicketDetailsTransformationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTicketDetailsTransformationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTicketDetailsTransformationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTicketDetailsTransformationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTicketDetailsTransformationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTicketDetailsTransformationsRequestValidationError) ErrorName() string {
	return "CreateTicketDetailsTransformationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTicketDetailsTransformationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTicketDetailsTransformationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTicketDetailsTransformationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTicketDetailsTransformationsRequestValidationError{}

// Validate checks the field values on
// CreateTicketDetailsTransformationsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateTicketDetailsTransformationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateTicketDetailsTransformationsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreateTicketDetailsTransformationsResponseMultiError, or nil if none found.
func (m *CreateTicketDetailsTransformationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTicketDetailsTransformationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTicketDetailsTransformationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTicketDetailsTransformationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTicketDetailsTransformationsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTransformationsList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateTicketDetailsTransformationsResponseValidationError{
						field:  fmt.Sprintf("TransformationsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateTicketDetailsTransformationsResponseValidationError{
						field:  fmt.Sprintf("TransformationsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateTicketDetailsTransformationsResponseValidationError{
					field:  fmt.Sprintf("TransformationsList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateTicketDetailsTransformationsResponseMultiError(errors)
	}

	return nil
}

// CreateTicketDetailsTransformationsResponseMultiError is an error wrapping
// multiple validation errors returned by
// CreateTicketDetailsTransformationsResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateTicketDetailsTransformationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTicketDetailsTransformationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTicketDetailsTransformationsResponseMultiError) AllErrors() []error { return m }

// CreateTicketDetailsTransformationsResponseValidationError is the validation
// error returned by CreateTicketDetailsTransformationsResponse.Validate if
// the designated constraints aren't met.
type CreateTicketDetailsTransformationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTicketDetailsTransformationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTicketDetailsTransformationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTicketDetailsTransformationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTicketDetailsTransformationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTicketDetailsTransformationsResponseValidationError) ErrorName() string {
	return "CreateTicketDetailsTransformationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTicketDetailsTransformationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTicketDetailsTransformationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTicketDetailsTransformationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTicketDetailsTransformationsResponseValidationError{}

// Validate checks the field values on UpdateTicketDetailsTransformationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateTicketDetailsTransformationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateTicketDetailsTransformationRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateTicketDetailsTransformationRequestMultiError, or nil if none found.
func (m *UpdateTicketDetailsTransformationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTicketDetailsTransformationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTransformation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTicketDetailsTransformationRequestValidationError{
					field:  "Transformation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTicketDetailsTransformationRequestValidationError{
					field:  "Transformation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransformation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTicketDetailsTransformationRequestValidationError{
				field:  "Transformation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateTicketDetailsTransformationRequestMultiError(errors)
	}

	return nil
}

// UpdateTicketDetailsTransformationRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateTicketDetailsTransformationRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateTicketDetailsTransformationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTicketDetailsTransformationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTicketDetailsTransformationRequestMultiError) AllErrors() []error { return m }

// UpdateTicketDetailsTransformationRequestValidationError is the validation
// error returned by UpdateTicketDetailsTransformationRequest.Validate if the
// designated constraints aren't met.
type UpdateTicketDetailsTransformationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTicketDetailsTransformationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTicketDetailsTransformationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTicketDetailsTransformationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTicketDetailsTransformationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTicketDetailsTransformationRequestValidationError) ErrorName() string {
	return "UpdateTicketDetailsTransformationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTicketDetailsTransformationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTicketDetailsTransformationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTicketDetailsTransformationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTicketDetailsTransformationRequestValidationError{}

// Validate checks the field values on
// UpdateTicketDetailsTransformationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateTicketDetailsTransformationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateTicketDetailsTransformationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateTicketDetailsTransformationResponseMultiError, or nil if none found.
func (m *UpdateTicketDetailsTransformationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTicketDetailsTransformationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTicketDetailsTransformationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTicketDetailsTransformationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTicketDetailsTransformationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateTicketDetailsTransformationResponseMultiError(errors)
	}

	return nil
}

// UpdateTicketDetailsTransformationResponseMultiError is an error wrapping
// multiple validation errors returned by
// UpdateTicketDetailsTransformationResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateTicketDetailsTransformationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTicketDetailsTransformationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTicketDetailsTransformationResponseMultiError) AllErrors() []error { return m }

// UpdateTicketDetailsTransformationResponseValidationError is the validation
// error returned by UpdateTicketDetailsTransformationResponse.Validate if the
// designated constraints aren't met.
type UpdateTicketDetailsTransformationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTicketDetailsTransformationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTicketDetailsTransformationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTicketDetailsTransformationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTicketDetailsTransformationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTicketDetailsTransformationResponseValidationError) ErrorName() string {
	return "UpdateTicketDetailsTransformationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTicketDetailsTransformationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTicketDetailsTransformationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTicketDetailsTransformationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTicketDetailsTransformationResponseValidationError{}

// Validate checks the field values on
// DeleteTicketDetailsTransformationsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeleteTicketDetailsTransformationsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeleteTicketDetailsTransformationsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// DeleteTicketDetailsTransformationsRequestMultiError, or nil if none found.
func (m *DeleteTicketDetailsTransformationsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteTicketDetailsTransformationsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteTicketDetailsTransformationsRequestMultiError(errors)
	}

	return nil
}

// DeleteTicketDetailsTransformationsRequestMultiError is an error wrapping
// multiple validation errors returned by
// DeleteTicketDetailsTransformationsRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteTicketDetailsTransformationsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteTicketDetailsTransformationsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteTicketDetailsTransformationsRequestMultiError) AllErrors() []error { return m }

// DeleteTicketDetailsTransformationsRequestValidationError is the validation
// error returned by DeleteTicketDetailsTransformationsRequest.Validate if the
// designated constraints aren't met.
type DeleteTicketDetailsTransformationsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteTicketDetailsTransformationsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteTicketDetailsTransformationsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteTicketDetailsTransformationsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteTicketDetailsTransformationsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteTicketDetailsTransformationsRequestValidationError) ErrorName() string {
	return "DeleteTicketDetailsTransformationsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteTicketDetailsTransformationsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteTicketDetailsTransformationsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteTicketDetailsTransformationsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteTicketDetailsTransformationsRequestValidationError{}

// Validate checks the field values on
// DeleteTicketDetailsTransformationsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeleteTicketDetailsTransformationsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeleteTicketDetailsTransformationsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// DeleteTicketDetailsTransformationsResponseMultiError, or nil if none found.
func (m *DeleteTicketDetailsTransformationsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteTicketDetailsTransformationsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteTicketDetailsTransformationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteTicketDetailsTransformationsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteTicketDetailsTransformationsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeletedCount

	if len(errors) > 0 {
		return DeleteTicketDetailsTransformationsResponseMultiError(errors)
	}

	return nil
}

// DeleteTicketDetailsTransformationsResponseMultiError is an error wrapping
// multiple validation errors returned by
// DeleteTicketDetailsTransformationsResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteTicketDetailsTransformationsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteTicketDetailsTransformationsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteTicketDetailsTransformationsResponseMultiError) AllErrors() []error { return m }

// DeleteTicketDetailsTransformationsResponseValidationError is the validation
// error returned by DeleteTicketDetailsTransformationsResponse.Validate if
// the designated constraints aren't met.
type DeleteTicketDetailsTransformationsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteTicketDetailsTransformationsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteTicketDetailsTransformationsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteTicketDetailsTransformationsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteTicketDetailsTransformationsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteTicketDetailsTransformationsResponseValidationError) ErrorName() string {
	return "DeleteTicketDetailsTransformationsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteTicketDetailsTransformationsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteTicketDetailsTransformationsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteTicketDetailsTransformationsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteTicketDetailsTransformationsResponseValidationError{}

// Validate checks the field values on UpdateTicketAsyncRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTicketAsyncRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTicketAsyncRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTicketAsyncRequestMultiError, or nil if none found.
func (m *UpdateTicketAsyncRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTicketAsyncRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTicketAsyncRequestValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTicketAsyncRequestValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTicketAsyncRequestValidationError{
				field:  "Ticket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateTicketAsyncRequestMultiError(errors)
	}

	return nil
}

// UpdateTicketAsyncRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateTicketAsyncRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateTicketAsyncRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTicketAsyncRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTicketAsyncRequestMultiError) AllErrors() []error { return m }

// UpdateTicketAsyncRequestValidationError is the validation error returned by
// UpdateTicketAsyncRequest.Validate if the designated constraints aren't met.
type UpdateTicketAsyncRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTicketAsyncRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTicketAsyncRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTicketAsyncRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTicketAsyncRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTicketAsyncRequestValidationError) ErrorName() string {
	return "UpdateTicketAsyncRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTicketAsyncRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTicketAsyncRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTicketAsyncRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTicketAsyncRequestValidationError{}

// Validate checks the field values on UpdateTicketAsyncResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTicketAsyncResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTicketAsyncResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTicketAsyncResponseMultiError, or nil if none found.
func (m *UpdateTicketAsyncResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTicketAsyncResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTicketAsyncResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTicketAsyncResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTicketAsyncResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateTicketAsyncResponseMultiError(errors)
	}

	return nil
}

// UpdateTicketAsyncResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateTicketAsyncResponse.ValidateAll() if the
// designated constraints aren't met.
type UpdateTicketAsyncResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTicketAsyncResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTicketAsyncResponseMultiError) AllErrors() []error { return m }

// UpdateTicketAsyncResponseValidationError is the validation error returned by
// UpdateTicketAsyncResponse.Validate if the designated constraints aren't met.
type UpdateTicketAsyncResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTicketAsyncResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTicketAsyncResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTicketAsyncResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTicketAsyncResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTicketAsyncResponseValidationError) ErrorName() string {
	return "UpdateTicketAsyncResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTicketAsyncResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTicketAsyncResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTicketAsyncResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTicketAsyncResponseValidationError{}

// Validate checks the field values on AddPrivateNoteAsyncRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddPrivateNoteAsyncRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddPrivateNoteAsyncRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddPrivateNoteAsyncRequestMultiError, or nil if none found.
func (m *AddPrivateNoteAsyncRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddPrivateNoteAsyncRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TicketId

	// no validation rules for Body

	// no validation rules for AgentId

	if len(errors) > 0 {
		return AddPrivateNoteAsyncRequestMultiError(errors)
	}

	return nil
}

// AddPrivateNoteAsyncRequestMultiError is an error wrapping multiple
// validation errors returned by AddPrivateNoteAsyncRequest.ValidateAll() if
// the designated constraints aren't met.
type AddPrivateNoteAsyncRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddPrivateNoteAsyncRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddPrivateNoteAsyncRequestMultiError) AllErrors() []error { return m }

// AddPrivateNoteAsyncRequestValidationError is the validation error returned
// by AddPrivateNoteAsyncRequest.Validate if the designated constraints aren't met.
type AddPrivateNoteAsyncRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddPrivateNoteAsyncRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddPrivateNoteAsyncRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddPrivateNoteAsyncRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddPrivateNoteAsyncRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddPrivateNoteAsyncRequestValidationError) ErrorName() string {
	return "AddPrivateNoteAsyncRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddPrivateNoteAsyncRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddPrivateNoteAsyncRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddPrivateNoteAsyncRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddPrivateNoteAsyncRequestValidationError{}

// Validate checks the field values on AddPrivateNoteAsyncResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddPrivateNoteAsyncResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddPrivateNoteAsyncResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddPrivateNoteAsyncResponseMultiError, or nil if none found.
func (m *AddPrivateNoteAsyncResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddPrivateNoteAsyncResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddPrivateNoteAsyncResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddPrivateNoteAsyncResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddPrivateNoteAsyncResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddPrivateNoteAsyncResponseMultiError(errors)
	}

	return nil
}

// AddPrivateNoteAsyncResponseMultiError is an error wrapping multiple
// validation errors returned by AddPrivateNoteAsyncResponse.ValidateAll() if
// the designated constraints aren't met.
type AddPrivateNoteAsyncResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddPrivateNoteAsyncResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddPrivateNoteAsyncResponseMultiError) AllErrors() []error { return m }

// AddPrivateNoteAsyncResponseValidationError is the validation error returned
// by AddPrivateNoteAsyncResponse.Validate if the designated constraints
// aren't met.
type AddPrivateNoteAsyncResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddPrivateNoteAsyncResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddPrivateNoteAsyncResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddPrivateNoteAsyncResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddPrivateNoteAsyncResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddPrivateNoteAsyncResponseValidationError) ErrorName() string {
	return "AddPrivateNoteAsyncResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddPrivateNoteAsyncResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddPrivateNoteAsyncResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddPrivateNoteAsyncResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddPrivateNoteAsyncResponseValidationError{}

// Validate checks the field values on CreateTicketRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTicketRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTicketRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTicketRequestMultiError, or nil if none found.
func (m *CreateTicketRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTicketRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTicketRequestValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTicketRequestValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTicketRequestValidationError{
				field:  "Ticket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsAsyncCreationRequired

	if len(errors) > 0 {
		return CreateTicketRequestMultiError(errors)
	}

	return nil
}

// CreateTicketRequestMultiError is an error wrapping multiple validation
// errors returned by CreateTicketRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateTicketRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTicketRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTicketRequestMultiError) AllErrors() []error { return m }

// CreateTicketRequestValidationError is the validation error returned by
// CreateTicketRequest.Validate if the designated constraints aren't met.
type CreateTicketRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTicketRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTicketRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTicketRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTicketRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTicketRequestValidationError) ErrorName() string {
	return "CreateTicketRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTicketRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTicketRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTicketRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTicketRequestValidationError{}

// Validate checks the field values on CreateTicketResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTicketResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTicketResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTicketResponseMultiError, or nil if none found.
func (m *CreateTicketResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTicketResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTicketResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTicketResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTicketResponseValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTicketResponseValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTicketResponseValidationError{
				field:  "Ticket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTicketResponseMultiError(errors)
	}

	return nil
}

// CreateTicketResponseMultiError is an error wrapping multiple validation
// errors returned by CreateTicketResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateTicketResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTicketResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTicketResponseMultiError) AllErrors() []error { return m }

// CreateTicketResponseValidationError is the validation error returned by
// CreateTicketResponse.Validate if the designated constraints aren't met.
type CreateTicketResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTicketResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTicketResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTicketResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTicketResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTicketResponseValidationError) ErrorName() string {
	return "CreateTicketResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTicketResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTicketResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTicketResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTicketResponseValidationError{}

// Validate checks the field values on GetSupportTicketByIdForAppRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSupportTicketByIdForAppRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupportTicketByIdForAppRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSupportTicketByIdForAppRequestMultiError, or nil if none found.
func (m *GetSupportTicketByIdForAppRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupportTicketByIdForAppRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TicketId

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetSupportTicketByIdForAppRequestMultiError(errors)
	}

	return nil
}

// GetSupportTicketByIdForAppRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetSupportTicketByIdForAppRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSupportTicketByIdForAppRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupportTicketByIdForAppRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupportTicketByIdForAppRequestMultiError) AllErrors() []error { return m }

// GetSupportTicketByIdForAppRequestValidationError is the validation error
// returned by GetSupportTicketByIdForAppRequest.Validate if the designated
// constraints aren't met.
type GetSupportTicketByIdForAppRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupportTicketByIdForAppRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupportTicketByIdForAppRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupportTicketByIdForAppRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupportTicketByIdForAppRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupportTicketByIdForAppRequestValidationError) ErrorName() string {
	return "GetSupportTicketByIdForAppRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSupportTicketByIdForAppRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupportTicketByIdForAppRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupportTicketByIdForAppRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupportTicketByIdForAppRequestValidationError{}

// Validate checks the field values on GetSupportTicketByIdForAppResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSupportTicketByIdForAppResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupportTicketByIdForAppResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSupportTicketByIdForAppResponseMultiError, or nil if none found.
func (m *GetSupportTicketByIdForAppResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupportTicketByIdForAppResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketByIdForAppResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketByIdForAppResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketByIdForAppResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketByIdForAppResponseValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketByIdForAppResponseValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketByIdForAppResponseValidationError{
				field:  "Ticket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSupportTicketByIdForAppResponseMultiError(errors)
	}

	return nil
}

// GetSupportTicketByIdForAppResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetSupportTicketByIdForAppResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSupportTicketByIdForAppResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupportTicketByIdForAppResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupportTicketByIdForAppResponseMultiError) AllErrors() []error { return m }

// GetSupportTicketByIdForAppResponseValidationError is the validation error
// returned by GetSupportTicketByIdForAppResponse.Validate if the designated
// constraints aren't met.
type GetSupportTicketByIdForAppResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupportTicketByIdForAppResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupportTicketByIdForAppResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupportTicketByIdForAppResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupportTicketByIdForAppResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupportTicketByIdForAppResponseValidationError) ErrorName() string {
	return "GetSupportTicketByIdForAppResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSupportTicketByIdForAppResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupportTicketByIdForAppResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupportTicketByIdForAppResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupportTicketByIdForAppResponseValidationError{}

// Validate checks the field values on GetMergedTicketsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMergedTicketsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMergedTicketsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMergedTicketsRequestMultiError, or nil if none found.
func (m *GetMergedTicketsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMergedTicketsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTicketId() < 1 {
		err := GetMergedTicketsRequestValidationError{
			field:  "TicketId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetMergedTicketsRequestMultiError(errors)
	}

	return nil
}

// GetMergedTicketsRequestMultiError is an error wrapping multiple validation
// errors returned by GetMergedTicketsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMergedTicketsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMergedTicketsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMergedTicketsRequestMultiError) AllErrors() []error { return m }

// GetMergedTicketsRequestValidationError is the validation error returned by
// GetMergedTicketsRequest.Validate if the designated constraints aren't met.
type GetMergedTicketsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMergedTicketsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMergedTicketsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMergedTicketsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMergedTicketsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMergedTicketsRequestValidationError) ErrorName() string {
	return "GetMergedTicketsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMergedTicketsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMergedTicketsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMergedTicketsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMergedTicketsRequestValidationError{}

// Validate checks the field values on GetMergedTicketsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMergedTicketsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMergedTicketsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMergedTicketsResponseMultiError, or nil if none found.
func (m *GetMergedTicketsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMergedTicketsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMergedTicketsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMergedTicketsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMergedTicketsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMergedTicketsResponseMultiError(errors)
	}

	return nil
}

// GetMergedTicketsResponseMultiError is an error wrapping multiple validation
// errors returned by GetMergedTicketsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMergedTicketsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMergedTicketsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMergedTicketsResponseMultiError) AllErrors() []error { return m }

// GetMergedTicketsResponseValidationError is the validation error returned by
// GetMergedTicketsResponse.Validate if the designated constraints aren't met.
type GetMergedTicketsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMergedTicketsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMergedTicketsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMergedTicketsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMergedTicketsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMergedTicketsResponseValidationError) ErrorName() string {
	return "GetMergedTicketsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMergedTicketsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMergedTicketsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMergedTicketsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMergedTicketsResponseValidationError{}

// Validate checks the field values on CreateTicketAsyncRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTicketAsyncRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTicketAsyncRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTicketAsyncRequestMultiError, or nil if none found.
func (m *CreateTicketAsyncRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTicketAsyncRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTicketAsyncRequestValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTicketAsyncRequestValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTicketAsyncRequestValidationError{
				field:  "Ticket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClientRequestInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTicketAsyncRequestValidationError{
					field:  "ClientRequestInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTicketAsyncRequestValidationError{
					field:  "ClientRequestInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientRequestInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTicketAsyncRequestValidationError{
				field:  "ClientRequestInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTicketAsyncRequestMultiError(errors)
	}

	return nil
}

// CreateTicketAsyncRequestMultiError is an error wrapping multiple validation
// errors returned by CreateTicketAsyncRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateTicketAsyncRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTicketAsyncRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTicketAsyncRequestMultiError) AllErrors() []error { return m }

// CreateTicketAsyncRequestValidationError is the validation error returned by
// CreateTicketAsyncRequest.Validate if the designated constraints aren't met.
type CreateTicketAsyncRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTicketAsyncRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTicketAsyncRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTicketAsyncRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTicketAsyncRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTicketAsyncRequestValidationError) ErrorName() string {
	return "CreateTicketAsyncRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTicketAsyncRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTicketAsyncRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTicketAsyncRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTicketAsyncRequestValidationError{}

// Validate checks the field values on CreateTicketAsyncResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTicketAsyncResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTicketAsyncResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTicketAsyncResponseMultiError, or nil if none found.
func (m *CreateTicketAsyncResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTicketAsyncResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTicketAsyncResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTicketAsyncResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTicketAsyncResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTicketAsyncResponseMultiError(errors)
	}

	return nil
}

// CreateTicketAsyncResponseMultiError is an error wrapping multiple validation
// errors returned by CreateTicketAsyncResponse.ValidateAll() if the
// designated constraints aren't met.
type CreateTicketAsyncResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTicketAsyncResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTicketAsyncResponseMultiError) AllErrors() []error { return m }

// CreateTicketAsyncResponseValidationError is the validation error returned by
// CreateTicketAsyncResponse.Validate if the designated constraints aren't met.
type CreateTicketAsyncResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTicketAsyncResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTicketAsyncResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTicketAsyncResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTicketAsyncResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTicketAsyncResponseValidationError) ErrorName() string {
	return "CreateTicketAsyncResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTicketAsyncResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTicketAsyncResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTicketAsyncResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTicketAsyncResponseValidationError{}

// Validate checks the field values on
// GetAgentInstructionForTicketResponse_FinalAgentInstruction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAgentInstructionForTicketResponse_FinalAgentInstruction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetAgentInstructionForTicketResponse_FinalAgentInstruction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAgentInstructionForTicketResponse_FinalAgentInstructionMultiError, or
// nil if none found.
func (m *GetAgentInstructionForTicketResponse_FinalAgentInstruction) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentInstructionForTicketResponse_FinalAgentInstruction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetNotes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError{
						field:  fmt.Sprintf("Notes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError{
						field:  fmt.Sprintf("Notes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError{
					field:  fmt.Sprintf("Notes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAgentInstructionForTicketResponse_FinalAgentInstructionMultiError(errors)
	}

	return nil
}

// GetAgentInstructionForTicketResponse_FinalAgentInstructionMultiError is an
// error wrapping multiple validation errors returned by
// GetAgentInstructionForTicketResponse_FinalAgentInstruction.ValidateAll() if
// the designated constraints aren't met.
type GetAgentInstructionForTicketResponse_FinalAgentInstructionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentInstructionForTicketResponse_FinalAgentInstructionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentInstructionForTicketResponse_FinalAgentInstructionMultiError) AllErrors() []error {
	return m
}

// GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError is
// the validation error returned by
// GetAgentInstructionForTicketResponse_FinalAgentInstruction.Validate if the
// designated constraints aren't met.
type GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError) ErrorName() string {
	return "GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError"
}

// Error satisfies the builtin error interface
func (e GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentInstructionForTicketResponse_FinalAgentInstruction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentInstructionForTicketResponse_FinalAgentInstructionValidationError{}

// Validate checks the field values on
// GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesMultiError,
// or nil if none found.
func (m *GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Question

	// no validation rules for Answer

	if len(errors) > 0 {
		return GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesMultiError(errors)
	}

	return nil
}

// GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesMultiError
// is an error wrapping multiple validation errors returned by
// GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes.ValidateAll()
// if the designated constraints aren't met.
type GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesMultiError) AllErrors() []error {
	return m
}

// GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesValidationError
// is the validation error returned by
// GetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes.Validate
// if the designated constraints aren't met.
type GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesValidationError) ErrorName() string {
	return "GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesValidationError"
}

// Error satisfies the builtin error interface
func (e GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentInstructionForTicketResponse_FinalAgentInstruction_Notes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentInstructionForTicketResponse_FinalAgentInstruction_NotesValidationError{}
