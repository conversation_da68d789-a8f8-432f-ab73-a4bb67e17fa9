// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/cx/ticket/service.proto

package ticket

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Ticket_AttachEntity_FullMethodName                       = "/cx.ticket.ticket/AttachEntity"
	Ticket_GetRelatedTickets_FullMethodName                  = "/cx.ticket.ticket/GetRelatedTickets"
	Ticket_MergeTickets_FullMethodName                       = "/cx.ticket.ticket/MergeTickets"
	Ticket_GetCallRecording_FullMethodName                   = "/cx.ticket.ticket/GetCallRecording"
	Ticket_GetCallTranscript_FullMethodName                  = "/cx.ticket.ticket/GetCallTranscript"
	Ticket_GetSupportTickets_FullMethodName                  = "/cx.ticket.ticket/GetSupportTickets"
	Ticket_BulkUpdateTickets_FullMethodName                  = "/cx.ticket.ticket/BulkUpdateTickets"
	Ticket_GetAllBulkTicketJobs_FullMethodName               = "/cx.ticket.ticket/GetAllBulkTicketJobs"
	Ticket_GetJobFailureLogs_FullMethodName                  = "/cx.ticket.ticket/GetJobFailureLogs"
	Ticket_KillJobProcessing_FullMethodName                  = "/cx.ticket.ticket/KillJobProcessing"
	Ticket_GetSupportTicketsForSherlock_FullMethodName       = "/cx.ticket.ticket/GetSupportTicketsForSherlock"
	Ticket_GetSupportTicketsForApp_FullMethodName            = "/cx.ticket.ticket/GetSupportTicketsForApp"
	Ticket_CreateTicketDetailsTransformations_FullMethodName = "/cx.ticket.ticket/CreateTicketDetailsTransformations"
	Ticket_UpdateTicketDetailsTransformation_FullMethodName  = "/cx.ticket.ticket/UpdateTicketDetailsTransformation"
	Ticket_DeleteTicketDetailsTransformations_FullMethodName = "/cx.ticket.ticket/DeleteTicketDetailsTransformations"
	Ticket_GetFreshdeskTicketCategories_FullMethodName       = "/cx.ticket.ticket/GetFreshdeskTicketCategories"
	Ticket_GetTicketInfo_FullMethodName                      = "/cx.ticket.ticket/GetTicketInfo"
	Ticket_UpdateTicketInfo_FullMethodName                   = "/cx.ticket.ticket/UpdateTicketInfo"
	Ticket_UpdateTicketAsync_FullMethodName                  = "/cx.ticket.ticket/UpdateTicketAsync"
	Ticket_AddPrivateNoteAsync_FullMethodName                = "/cx.ticket.ticket/AddPrivateNoteAsync"
	Ticket_CreateTicket_FullMethodName                       = "/cx.ticket.ticket/CreateTicket"
	Ticket_GetSupportTicketByIdForApp_FullMethodName         = "/cx.ticket.ticket/GetSupportTicketByIdForApp"
	Ticket_GetMergedTickets_FullMethodName                   = "/cx.ticket.ticket/GetMergedTickets"
	Ticket_GetTicketDetailsForSherlock_FullMethodName        = "/cx.ticket.ticket/GetTicketDetailsForSherlock"
	Ticket_GetAgentInstructionForTicket_FullMethodName       = "/cx.ticket.ticket/GetAgentInstructionForTicket"
	Ticket_CreateTicketAsync_FullMethodName                  = "/cx.ticket.ticket/CreateTicketAsync"
	Ticket_SubmitCsatFeedback_FullMethodName                 = "/cx.ticket.ticket/SubmitCsatFeedback"
	Ticket_GetCategoryTransformation_FullMethodName          = "/cx.ticket.ticket/GetCategoryTransformation"
	Ticket_FetchLatestResolvedTicketIdForCSAT_FullMethodName = "/cx.ticket.ticket/FetchLatestResolvedTicketIdForCSAT"
)

// TicketClient is the client API for Ticket service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TicketClient interface {
	AttachEntity(ctx context.Context, in *AttachEntityRequest, opts ...grpc.CallOption) (*AttachEntityResponse, error)
	// returns list of tickets linked to the current ticket
	GetRelatedTickets(ctx context.Context, in *GetRelatedTicketsRequest, opts ...grpc.CallOption) (*GetRelatedTicketsResponse, error)
	MergeTickets(ctx context.Context, in *MergeTicketsRequest, opts ...grpc.CallOption) (*MergeTicketsResponse, error)
	// GetCallRecording gets the call recording of a particular recording id(for a call ticket)
	// returns the recording in chunks
	// returns internal error if recording not found in s3 bucket
	GetCallRecording(ctx context.Context, in *GetCallRecordingRequest, opts ...grpc.CallOption) (Ticket_GetCallRecordingClient, error)
	// GetCallTranscript gets the transcript file for a particular recording id(for a call ticket)
	// returns the file in chunks
	// returns internal error if transcript not found in s3 bucket
	GetCallTranscript(ctx context.Context, in *GetCallTranscriptRequest, opts ...grpc.CallOption) (Ticket_GetCallTranscriptClient, error)
	// rpc to fetch support tickets stored in cx db
	// will return not found if no ticket is found with given conditions
	// will return invalid argument if mandatory parameteres are not passed or values are invalid
	// all method options are set to false since this rpc won't be called in agent flows
	GetSupportTickets(ctx context.Context, in *GetSupportTicketsRequest, opts ...grpc.CallOption) (*GetSupportTicketsResponse, error)
	// rpc to update ticket details in bulk
	// will return invalid argument if given csv file or checker email is invalid
	// will return ok if job is added successfully in db and events are published to queue
	// will return internal for any server errors
	// enrichment, ticket validation method options are set to false since this rpc won't be called in agent flows
	BulkUpdateTickets(ctx context.Context, in *BulkUpdateTicketsRequest, opts ...grpc.CallOption) (*BulkUpdateTicketsResponse, error)
	// rpc to fetch list of all bulk ticket jobs
	// will return invalid argument if mandatory paramertes are missing in request
	// will return ok for success
	// will return internal for any server errors
	// will return not found if no job is found with given conditions
	// enrichment, ticket validation method options are set to false since this rpc won't be called in agent flows
	GetAllBulkTicketJobs(ctx context.Context, in *GetAllBulkTicketJobsRequest, opts ...grpc.CallOption) (*GetAllBulkTicketJobsResponse, error)
	// rpc to fetch list of all failures logs for a given job
	// will return invalid argument if mandatory paramertes are missing in request
	// will return ok for success
	// will return internal for any server errors
	// will return not found if no job or logs are found for given job id
	// enrichment, ticket validation method options are set to false since this rpc won't be called in agent flows
	GetJobFailureLogs(ctx context.Context, in *GetJobFailureLogsRequest, opts ...grpc.CallOption) (*GetJobFailureLogsResponse, error)
	// rpc to kill the given job, this will ensure no more tickets in queue are processed
	// will return invalid argument if mandatory paramertes are missing in request
	// will return ok for success
	// will return internal for any server errors
	// will return not found if no job or logs are found for given job id
	// enrichment, ticket validation method options are set to false since this rpc won't be called in agent flows
	KillJobProcessing(ctx context.Context, in *KillJobProcessingRequest, opts ...grpc.CallOption) (*KillJobProcessingResponse, error)
	// rpc to retrieve freshdesk tickets from db
	// request accepts cx header, ticket filters and page context request
	// response contains rpc status code, list of tickets and page context response
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	GetSupportTicketsForSherlock(ctx context.Context, in *GetSupportTicketsForSherlockRequest, opts ...grpc.CallOption) (*GetSupportTicketsForSherlockResponse, error)
	// rpc to retrieve support tickets from db to be shown to the users in app
	// request accepts actor id, ticket filters and page context request
	// response contains rpc status code, list of tickets and page context response
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	GetSupportTicketsForApp(ctx context.Context, in *GetSupportTicketsForAppRequest, opts ...grpc.CallOption) (*GetSupportTicketsForAppResponse, error)
	// rpc to create ticket details transformation entry in the db (used from dev action)
	// request accepts ticket details transformations with required fields populated
	// response contains rpc status code, transformations with generated ids
	// OK if successful
	// InvalidArg if mandatory params are missing
	// AlreadyExists if duplicate entry for (product_category, product_category_details, subcategory, transformation_type) as key
	// ISE for any other errors
	CreateTicketDetailsTransformations(ctx context.Context, in *CreateTicketDetailsTransformationsRequest, opts ...grpc.CallOption) (*CreateTicketDetailsTransformationsResponse, error)
	// rpc to update ticket details transformation entry in the db (used from dev action)
	// request accepts transformation (with id mandatory) and field mask
	// response contains rpc status code
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	UpdateTicketDetailsTransformation(ctx context.Context, in *UpdateTicketDetailsTransformationRequest, opts ...grpc.CallOption) (*UpdateTicketDetailsTransformationResponse, error)
	// rpc to delete ticket details transformation entry in the db (used from dev action)
	// request accepts row id list
	// response contains rpc status code number of rows deleted
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	DeleteTicketDetailsTransformations(ctx context.Context, in *DeleteTicketDetailsTransformationsRequest, opts ...grpc.CallOption) (*DeleteTicketDetailsTransformationsResponse, error)
	// rpc to get freshdesk ticket categories i.e product category, product category details and subcategory
	// values in string format populated in a nested manner
	// request only requires cx header
	// response contains status along with freshdesk ticket categories
	// OK if successful
	// ISE for any other errors
	GetFreshdeskTicketCategories(ctx context.Context, in *GetFreshdeskTicketCategoriesRequest, opts ...grpc.CallOption) (*GetFreshdeskTicketCategoriesResponse, error)
	// rpc to retrieve ticket info like ticket category details as well as additional info like expected resolution time, sla, note, guru link etc
	// additional details are populated in the best effort manner
	// request accepts ticket id, which is taken as populated in cx header
	// ticket id is mandatory
	// OK if successful
	// Invalid argument if ticket id is not present or invalid details are passed for update
	// ISE for any other errors
	GetTicketInfo(ctx context.Context, in *GetTicketInfoRequest, opts ...grpc.CallOption) (*GetTicketInfoResponse, error)
	// rpc to update ticket info, currently only supports product category, product category details and subcategory
	// request accepts ticket id, which is taken as populated in cx header
	// ticket id is mandatory
	// OK if successful
	// Invalid argument if ticket or ticket id is not present
	// ISE for any other errors
	UpdateTicketInfo(ctx context.Context, in *UpdateTicketInfoRequest, opts ...grpc.CallOption) (*UpdateTicketInfoResponse, error)
	// rpc to publish update ticket event to cx update ticket queue
	// request accepts cx ticket, where ticket id is mandatory field
	// only non-empty fills are updated, empty fields are ignored
	// OK if successful
	// Invalid argument if ticket or ticket id is not present
	// ISE for any other errors
	UpdateTicketAsync(ctx context.Context, in *UpdateTicketAsyncRequest, opts ...grpc.CallOption) (*UpdateTicketAsyncResponse, error)
	// rpc to asynchronously add a private note to ticket
	// This will publish private note event to a queue which allows to throttle the requests to avoid vendor rate limits
	// OK if successfully published to queue
	// Invalid argument if ticket id or body is not present
	// ISE for any other errors
	AddPrivateNoteAsync(ctx context.Context, in *AddPrivateNoteAsyncRequest, opts ...grpc.CallOption) (*AddPrivateNoteAsyncResponse, error)
	// A wrapper RPC to create a freshdesk ticket from given CX ticket using VG CreateTicketRaw.
	// Request accepts cx ticket. Uses issue category Id if present in the ticket.
	// The fields subject, description, status, priority are mandatory
	// Other than that one of the following 5 fields are mandatory for creating ticket
	// requester_id, email, facebook_id, phone, twitter_id, unique_external_id. Refer ticket proto for more details about each field
	// In case of only phone(i.e. other 4 fields are null), name is also mandatory
	// default value for source is 2 if not sent with request
	CreateTicket(ctx context.Context, in *CreateTicketRequest, opts ...grpc.CallOption) (*CreateTicketResponse, error)
	// rpc to retrieve support ticket from db for given ticket id to be shown to the users in app
	// request accepts id of ticket whose details needs to be fetched
	// response contains rpc status code, ticket details
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	GetSupportTicketByIdForApp(ctx context.Context, in *GetSupportTicketByIdForAppRequest, opts ...grpc.CallOption) (*GetSupportTicketByIdForAppResponse, error)
	// RPC to fetch all the tickets which are merged into given ticket
	// Merged tickets are also called secondary tickets which are merged into a primary ticket
	// Request requires a single field which is id of ticket for which we want to fetch merged tickets
	// Response contains list of ids representing tickets which are merged into ticket provided in request
	GetMergedTickets(ctx context.Context, in *GetMergedTicketsRequest, opts ...grpc.CallOption) (*GetMergedTicketsResponse, error)
	// GetTicketDetailsForSherlock fetches ticket details for sherlock
	GetTicketDetailsForSherlock(ctx context.Context, in *GetTicketDetailsForSherlockRequest, opts ...grpc.CallOption) (*GetTicketDetailsForSherlockResponse, error)
	// GetTicketDetailsForSherlock fetches agent instruction for a ticket which the agent has to do for the ticket
	GetAgentInstructionForTicket(ctx context.Context, in *GetAgentInstructionForTicketRequest, opts ...grpc.CallOption) (*GetAgentInstructionForTicketResponse, error)
	// CreateTicketAsync RPC creates support ticket in async manner
	// Request accepts cx ticket, if issue category id is provided L1, L2, L3 will be populated based on it
	// The fields subject, description, status, priority are mandatory
	// Other than that one of the following 5 fields are mandatory for creating ticket
	// requester_id, email, facebook_id, phone, twitter_id, unique_external_id. Refer ticket proto for more details about each field
	// In case of only phone(i.e. other 4 fields are null), name is also mandatory
	// clients can subscribe to sns topic: cx-ticket-create-event to get notified once ticket creation is completed
	CreateTicketAsync(ctx context.Context, in *CreateTicketAsyncRequest, opts ...grpc.CallOption) (*CreateTicketAsyncResponse, error)
	// SubmitCsatFeedback RPC is used to record a csat response
	// it accepts request identifier token, csat score both are mandatory parameter
	SubmitCsatFeedback(ctx context.Context, in *SubmitCsatFeedbackRequest, opts ...grpc.CallOption) (*SubmitCsatFeedbackResponse, error)
	// GetCategoryTransformation RPC fetches the category transformation details for a given ticket
	// It accepts ticket details
	GetCategoryTransformation(ctx context.Context, in *GetCategoryTransformationsRequest, opts ...grpc.CallOption) (*GetCategoryTransformationsResponse, error)
	// FetchLatestResolvedTicketPendingCSATByUserID fetches the most recent resolved ticket
	// for the given user that is still awaiting CSAT feedback.
	FetchLatestResolvedTicketIdForCSAT(ctx context.Context, in *FetchLatestResolvedTicketIdForCSATRequest, opts ...grpc.CallOption) (*FetchLatestResolvedTicketIdForCSATResponse, error)
}

type ticketClient struct {
	cc grpc.ClientConnInterface
}

func NewTicketClient(cc grpc.ClientConnInterface) TicketClient {
	return &ticketClient{cc}
}

func (c *ticketClient) AttachEntity(ctx context.Context, in *AttachEntityRequest, opts ...grpc.CallOption) (*AttachEntityResponse, error) {
	out := new(AttachEntityResponse)
	err := c.cc.Invoke(ctx, Ticket_AttachEntity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetRelatedTickets(ctx context.Context, in *GetRelatedTicketsRequest, opts ...grpc.CallOption) (*GetRelatedTicketsResponse, error) {
	out := new(GetRelatedTicketsResponse)
	err := c.cc.Invoke(ctx, Ticket_GetRelatedTickets_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) MergeTickets(ctx context.Context, in *MergeTicketsRequest, opts ...grpc.CallOption) (*MergeTicketsResponse, error) {
	out := new(MergeTicketsResponse)
	err := c.cc.Invoke(ctx, Ticket_MergeTickets_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetCallRecording(ctx context.Context, in *GetCallRecordingRequest, opts ...grpc.CallOption) (Ticket_GetCallRecordingClient, error) {
	stream, err := c.cc.NewStream(ctx, &Ticket_ServiceDesc.Streams[0], Ticket_GetCallRecording_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &ticketGetCallRecordingClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Ticket_GetCallRecordingClient interface {
	Recv() (*GetCallRecordingResponse, error)
	grpc.ClientStream
}

type ticketGetCallRecordingClient struct {
	grpc.ClientStream
}

func (x *ticketGetCallRecordingClient) Recv() (*GetCallRecordingResponse, error) {
	m := new(GetCallRecordingResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *ticketClient) GetCallTranscript(ctx context.Context, in *GetCallTranscriptRequest, opts ...grpc.CallOption) (Ticket_GetCallTranscriptClient, error) {
	stream, err := c.cc.NewStream(ctx, &Ticket_ServiceDesc.Streams[1], Ticket_GetCallTranscript_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &ticketGetCallTranscriptClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Ticket_GetCallTranscriptClient interface {
	Recv() (*GetCallTranscriptResponse, error)
	grpc.ClientStream
}

type ticketGetCallTranscriptClient struct {
	grpc.ClientStream
}

func (x *ticketGetCallTranscriptClient) Recv() (*GetCallTranscriptResponse, error) {
	m := new(GetCallTranscriptResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *ticketClient) GetSupportTickets(ctx context.Context, in *GetSupportTicketsRequest, opts ...grpc.CallOption) (*GetSupportTicketsResponse, error) {
	out := new(GetSupportTicketsResponse)
	err := c.cc.Invoke(ctx, Ticket_GetSupportTickets_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) BulkUpdateTickets(ctx context.Context, in *BulkUpdateTicketsRequest, opts ...grpc.CallOption) (*BulkUpdateTicketsResponse, error) {
	out := new(BulkUpdateTicketsResponse)
	err := c.cc.Invoke(ctx, Ticket_BulkUpdateTickets_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetAllBulkTicketJobs(ctx context.Context, in *GetAllBulkTicketJobsRequest, opts ...grpc.CallOption) (*GetAllBulkTicketJobsResponse, error) {
	out := new(GetAllBulkTicketJobsResponse)
	err := c.cc.Invoke(ctx, Ticket_GetAllBulkTicketJobs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetJobFailureLogs(ctx context.Context, in *GetJobFailureLogsRequest, opts ...grpc.CallOption) (*GetJobFailureLogsResponse, error) {
	out := new(GetJobFailureLogsResponse)
	err := c.cc.Invoke(ctx, Ticket_GetJobFailureLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) KillJobProcessing(ctx context.Context, in *KillJobProcessingRequest, opts ...grpc.CallOption) (*KillJobProcessingResponse, error) {
	out := new(KillJobProcessingResponse)
	err := c.cc.Invoke(ctx, Ticket_KillJobProcessing_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetSupportTicketsForSherlock(ctx context.Context, in *GetSupportTicketsForSherlockRequest, opts ...grpc.CallOption) (*GetSupportTicketsForSherlockResponse, error) {
	out := new(GetSupportTicketsForSherlockResponse)
	err := c.cc.Invoke(ctx, Ticket_GetSupportTicketsForSherlock_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetSupportTicketsForApp(ctx context.Context, in *GetSupportTicketsForAppRequest, opts ...grpc.CallOption) (*GetSupportTicketsForAppResponse, error) {
	out := new(GetSupportTicketsForAppResponse)
	err := c.cc.Invoke(ctx, Ticket_GetSupportTicketsForApp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) CreateTicketDetailsTransformations(ctx context.Context, in *CreateTicketDetailsTransformationsRequest, opts ...grpc.CallOption) (*CreateTicketDetailsTransformationsResponse, error) {
	out := new(CreateTicketDetailsTransformationsResponse)
	err := c.cc.Invoke(ctx, Ticket_CreateTicketDetailsTransformations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) UpdateTicketDetailsTransformation(ctx context.Context, in *UpdateTicketDetailsTransformationRequest, opts ...grpc.CallOption) (*UpdateTicketDetailsTransformationResponse, error) {
	out := new(UpdateTicketDetailsTransformationResponse)
	err := c.cc.Invoke(ctx, Ticket_UpdateTicketDetailsTransformation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) DeleteTicketDetailsTransformations(ctx context.Context, in *DeleteTicketDetailsTransformationsRequest, opts ...grpc.CallOption) (*DeleteTicketDetailsTransformationsResponse, error) {
	out := new(DeleteTicketDetailsTransformationsResponse)
	err := c.cc.Invoke(ctx, Ticket_DeleteTicketDetailsTransformations_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetFreshdeskTicketCategories(ctx context.Context, in *GetFreshdeskTicketCategoriesRequest, opts ...grpc.CallOption) (*GetFreshdeskTicketCategoriesResponse, error) {
	out := new(GetFreshdeskTicketCategoriesResponse)
	err := c.cc.Invoke(ctx, Ticket_GetFreshdeskTicketCategories_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetTicketInfo(ctx context.Context, in *GetTicketInfoRequest, opts ...grpc.CallOption) (*GetTicketInfoResponse, error) {
	out := new(GetTicketInfoResponse)
	err := c.cc.Invoke(ctx, Ticket_GetTicketInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) UpdateTicketInfo(ctx context.Context, in *UpdateTicketInfoRequest, opts ...grpc.CallOption) (*UpdateTicketInfoResponse, error) {
	out := new(UpdateTicketInfoResponse)
	err := c.cc.Invoke(ctx, Ticket_UpdateTicketInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) UpdateTicketAsync(ctx context.Context, in *UpdateTicketAsyncRequest, opts ...grpc.CallOption) (*UpdateTicketAsyncResponse, error) {
	out := new(UpdateTicketAsyncResponse)
	err := c.cc.Invoke(ctx, Ticket_UpdateTicketAsync_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) AddPrivateNoteAsync(ctx context.Context, in *AddPrivateNoteAsyncRequest, opts ...grpc.CallOption) (*AddPrivateNoteAsyncResponse, error) {
	out := new(AddPrivateNoteAsyncResponse)
	err := c.cc.Invoke(ctx, Ticket_AddPrivateNoteAsync_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) CreateTicket(ctx context.Context, in *CreateTicketRequest, opts ...grpc.CallOption) (*CreateTicketResponse, error) {
	out := new(CreateTicketResponse)
	err := c.cc.Invoke(ctx, Ticket_CreateTicket_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetSupportTicketByIdForApp(ctx context.Context, in *GetSupportTicketByIdForAppRequest, opts ...grpc.CallOption) (*GetSupportTicketByIdForAppResponse, error) {
	out := new(GetSupportTicketByIdForAppResponse)
	err := c.cc.Invoke(ctx, Ticket_GetSupportTicketByIdForApp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetMergedTickets(ctx context.Context, in *GetMergedTicketsRequest, opts ...grpc.CallOption) (*GetMergedTicketsResponse, error) {
	out := new(GetMergedTicketsResponse)
	err := c.cc.Invoke(ctx, Ticket_GetMergedTickets_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetTicketDetailsForSherlock(ctx context.Context, in *GetTicketDetailsForSherlockRequest, opts ...grpc.CallOption) (*GetTicketDetailsForSherlockResponse, error) {
	out := new(GetTicketDetailsForSherlockResponse)
	err := c.cc.Invoke(ctx, Ticket_GetTicketDetailsForSherlock_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetAgentInstructionForTicket(ctx context.Context, in *GetAgentInstructionForTicketRequest, opts ...grpc.CallOption) (*GetAgentInstructionForTicketResponse, error) {
	out := new(GetAgentInstructionForTicketResponse)
	err := c.cc.Invoke(ctx, Ticket_GetAgentInstructionForTicket_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) CreateTicketAsync(ctx context.Context, in *CreateTicketAsyncRequest, opts ...grpc.CallOption) (*CreateTicketAsyncResponse, error) {
	out := new(CreateTicketAsyncResponse)
	err := c.cc.Invoke(ctx, Ticket_CreateTicketAsync_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) SubmitCsatFeedback(ctx context.Context, in *SubmitCsatFeedbackRequest, opts ...grpc.CallOption) (*SubmitCsatFeedbackResponse, error) {
	out := new(SubmitCsatFeedbackResponse)
	err := c.cc.Invoke(ctx, Ticket_SubmitCsatFeedback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetCategoryTransformation(ctx context.Context, in *GetCategoryTransformationsRequest, opts ...grpc.CallOption) (*GetCategoryTransformationsResponse, error) {
	out := new(GetCategoryTransformationsResponse)
	err := c.cc.Invoke(ctx, Ticket_GetCategoryTransformation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) FetchLatestResolvedTicketIdForCSAT(ctx context.Context, in *FetchLatestResolvedTicketIdForCSATRequest, opts ...grpc.CallOption) (*FetchLatestResolvedTicketIdForCSATResponse, error) {
	out := new(FetchLatestResolvedTicketIdForCSATResponse)
	err := c.cc.Invoke(ctx, Ticket_FetchLatestResolvedTicketIdForCSAT_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TicketServer is the server API for Ticket service.
// All implementations should embed UnimplementedTicketServer
// for forward compatibility
type TicketServer interface {
	AttachEntity(context.Context, *AttachEntityRequest) (*AttachEntityResponse, error)
	// returns list of tickets linked to the current ticket
	GetRelatedTickets(context.Context, *GetRelatedTicketsRequest) (*GetRelatedTicketsResponse, error)
	MergeTickets(context.Context, *MergeTicketsRequest) (*MergeTicketsResponse, error)
	// GetCallRecording gets the call recording of a particular recording id(for a call ticket)
	// returns the recording in chunks
	// returns internal error if recording not found in s3 bucket
	GetCallRecording(*GetCallRecordingRequest, Ticket_GetCallRecordingServer) error
	// GetCallTranscript gets the transcript file for a particular recording id(for a call ticket)
	// returns the file in chunks
	// returns internal error if transcript not found in s3 bucket
	GetCallTranscript(*GetCallTranscriptRequest, Ticket_GetCallTranscriptServer) error
	// rpc to fetch support tickets stored in cx db
	// will return not found if no ticket is found with given conditions
	// will return invalid argument if mandatory parameteres are not passed or values are invalid
	// all method options are set to false since this rpc won't be called in agent flows
	GetSupportTickets(context.Context, *GetSupportTicketsRequest) (*GetSupportTicketsResponse, error)
	// rpc to update ticket details in bulk
	// will return invalid argument if given csv file or checker email is invalid
	// will return ok if job is added successfully in db and events are published to queue
	// will return internal for any server errors
	// enrichment, ticket validation method options are set to false since this rpc won't be called in agent flows
	BulkUpdateTickets(context.Context, *BulkUpdateTicketsRequest) (*BulkUpdateTicketsResponse, error)
	// rpc to fetch list of all bulk ticket jobs
	// will return invalid argument if mandatory paramertes are missing in request
	// will return ok for success
	// will return internal for any server errors
	// will return not found if no job is found with given conditions
	// enrichment, ticket validation method options are set to false since this rpc won't be called in agent flows
	GetAllBulkTicketJobs(context.Context, *GetAllBulkTicketJobsRequest) (*GetAllBulkTicketJobsResponse, error)
	// rpc to fetch list of all failures logs for a given job
	// will return invalid argument if mandatory paramertes are missing in request
	// will return ok for success
	// will return internal for any server errors
	// will return not found if no job or logs are found for given job id
	// enrichment, ticket validation method options are set to false since this rpc won't be called in agent flows
	GetJobFailureLogs(context.Context, *GetJobFailureLogsRequest) (*GetJobFailureLogsResponse, error)
	// rpc to kill the given job, this will ensure no more tickets in queue are processed
	// will return invalid argument if mandatory paramertes are missing in request
	// will return ok for success
	// will return internal for any server errors
	// will return not found if no job or logs are found for given job id
	// enrichment, ticket validation method options are set to false since this rpc won't be called in agent flows
	KillJobProcessing(context.Context, *KillJobProcessingRequest) (*KillJobProcessingResponse, error)
	// rpc to retrieve freshdesk tickets from db
	// request accepts cx header, ticket filters and page context request
	// response contains rpc status code, list of tickets and page context response
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	GetSupportTicketsForSherlock(context.Context, *GetSupportTicketsForSherlockRequest) (*GetSupportTicketsForSherlockResponse, error)
	// rpc to retrieve support tickets from db to be shown to the users in app
	// request accepts actor id, ticket filters and page context request
	// response contains rpc status code, list of tickets and page context response
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	GetSupportTicketsForApp(context.Context, *GetSupportTicketsForAppRequest) (*GetSupportTicketsForAppResponse, error)
	// rpc to create ticket details transformation entry in the db (used from dev action)
	// request accepts ticket details transformations with required fields populated
	// response contains rpc status code, transformations with generated ids
	// OK if successful
	// InvalidArg if mandatory params are missing
	// AlreadyExists if duplicate entry for (product_category, product_category_details, subcategory, transformation_type) as key
	// ISE for any other errors
	CreateTicketDetailsTransformations(context.Context, *CreateTicketDetailsTransformationsRequest) (*CreateTicketDetailsTransformationsResponse, error)
	// rpc to update ticket details transformation entry in the db (used from dev action)
	// request accepts transformation (with id mandatory) and field mask
	// response contains rpc status code
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	UpdateTicketDetailsTransformation(context.Context, *UpdateTicketDetailsTransformationRequest) (*UpdateTicketDetailsTransformationResponse, error)
	// rpc to delete ticket details transformation entry in the db (used from dev action)
	// request accepts row id list
	// response contains rpc status code number of rows deleted
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	DeleteTicketDetailsTransformations(context.Context, *DeleteTicketDetailsTransformationsRequest) (*DeleteTicketDetailsTransformationsResponse, error)
	// rpc to get freshdesk ticket categories i.e product category, product category details and subcategory
	// values in string format populated in a nested manner
	// request only requires cx header
	// response contains status along with freshdesk ticket categories
	// OK if successful
	// ISE for any other errors
	GetFreshdeskTicketCategories(context.Context, *GetFreshdeskTicketCategoriesRequest) (*GetFreshdeskTicketCategoriesResponse, error)
	// rpc to retrieve ticket info like ticket category details as well as additional info like expected resolution time, sla, note, guru link etc
	// additional details are populated in the best effort manner
	// request accepts ticket id, which is taken as populated in cx header
	// ticket id is mandatory
	// OK if successful
	// Invalid argument if ticket id is not present or invalid details are passed for update
	// ISE for any other errors
	GetTicketInfo(context.Context, *GetTicketInfoRequest) (*GetTicketInfoResponse, error)
	// rpc to update ticket info, currently only supports product category, product category details and subcategory
	// request accepts ticket id, which is taken as populated in cx header
	// ticket id is mandatory
	// OK if successful
	// Invalid argument if ticket or ticket id is not present
	// ISE for any other errors
	UpdateTicketInfo(context.Context, *UpdateTicketInfoRequest) (*UpdateTicketInfoResponse, error)
	// rpc to publish update ticket event to cx update ticket queue
	// request accepts cx ticket, where ticket id is mandatory field
	// only non-empty fills are updated, empty fields are ignored
	// OK if successful
	// Invalid argument if ticket or ticket id is not present
	// ISE for any other errors
	UpdateTicketAsync(context.Context, *UpdateTicketAsyncRequest) (*UpdateTicketAsyncResponse, error)
	// rpc to asynchronously add a private note to ticket
	// This will publish private note event to a queue which allows to throttle the requests to avoid vendor rate limits
	// OK if successfully published to queue
	// Invalid argument if ticket id or body is not present
	// ISE for any other errors
	AddPrivateNoteAsync(context.Context, *AddPrivateNoteAsyncRequest) (*AddPrivateNoteAsyncResponse, error)
	// A wrapper RPC to create a freshdesk ticket from given CX ticket using VG CreateTicketRaw.
	// Request accepts cx ticket. Uses issue category Id if present in the ticket.
	// The fields subject, description, status, priority are mandatory
	// Other than that one of the following 5 fields are mandatory for creating ticket
	// requester_id, email, facebook_id, phone, twitter_id, unique_external_id. Refer ticket proto for more details about each field
	// In case of only phone(i.e. other 4 fields are null), name is also mandatory
	// default value for source is 2 if not sent with request
	CreateTicket(context.Context, *CreateTicketRequest) (*CreateTicketResponse, error)
	// rpc to retrieve support ticket from db for given ticket id to be shown to the users in app
	// request accepts id of ticket whose details needs to be fetched
	// response contains rpc status code, ticket details
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	GetSupportTicketByIdForApp(context.Context, *GetSupportTicketByIdForAppRequest) (*GetSupportTicketByIdForAppResponse, error)
	// RPC to fetch all the tickets which are merged into given ticket
	// Merged tickets are also called secondary tickets which are merged into a primary ticket
	// Request requires a single field which is id of ticket for which we want to fetch merged tickets
	// Response contains list of ids representing tickets which are merged into ticket provided in request
	GetMergedTickets(context.Context, *GetMergedTicketsRequest) (*GetMergedTicketsResponse, error)
	// GetTicketDetailsForSherlock fetches ticket details for sherlock
	GetTicketDetailsForSherlock(context.Context, *GetTicketDetailsForSherlockRequest) (*GetTicketDetailsForSherlockResponse, error)
	// GetTicketDetailsForSherlock fetches agent instruction for a ticket which the agent has to do for the ticket
	GetAgentInstructionForTicket(context.Context, *GetAgentInstructionForTicketRequest) (*GetAgentInstructionForTicketResponse, error)
	// CreateTicketAsync RPC creates support ticket in async manner
	// Request accepts cx ticket, if issue category id is provided L1, L2, L3 will be populated based on it
	// The fields subject, description, status, priority are mandatory
	// Other than that one of the following 5 fields are mandatory for creating ticket
	// requester_id, email, facebook_id, phone, twitter_id, unique_external_id. Refer ticket proto for more details about each field
	// In case of only phone(i.e. other 4 fields are null), name is also mandatory
	// clients can subscribe to sns topic: cx-ticket-create-event to get notified once ticket creation is completed
	CreateTicketAsync(context.Context, *CreateTicketAsyncRequest) (*CreateTicketAsyncResponse, error)
	// SubmitCsatFeedback RPC is used to record a csat response
	// it accepts request identifier token, csat score both are mandatory parameter
	SubmitCsatFeedback(context.Context, *SubmitCsatFeedbackRequest) (*SubmitCsatFeedbackResponse, error)
	// GetCategoryTransformation RPC fetches the category transformation details for a given ticket
	// It accepts ticket details
	GetCategoryTransformation(context.Context, *GetCategoryTransformationsRequest) (*GetCategoryTransformationsResponse, error)
	// FetchLatestResolvedTicketPendingCSATByUserID fetches the most recent resolved ticket
	// for the given user that is still awaiting CSAT feedback.
	FetchLatestResolvedTicketIdForCSAT(context.Context, *FetchLatestResolvedTicketIdForCSATRequest) (*FetchLatestResolvedTicketIdForCSATResponse, error)
}

// UnimplementedTicketServer should be embedded to have forward compatible implementations.
type UnimplementedTicketServer struct {
}

func (UnimplementedTicketServer) AttachEntity(context.Context, *AttachEntityRequest) (*AttachEntityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AttachEntity not implemented")
}
func (UnimplementedTicketServer) GetRelatedTickets(context.Context, *GetRelatedTicketsRequest) (*GetRelatedTicketsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRelatedTickets not implemented")
}
func (UnimplementedTicketServer) MergeTickets(context.Context, *MergeTicketsRequest) (*MergeTicketsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MergeTickets not implemented")
}
func (UnimplementedTicketServer) GetCallRecording(*GetCallRecordingRequest, Ticket_GetCallRecordingServer) error {
	return status.Errorf(codes.Unimplemented, "method GetCallRecording not implemented")
}
func (UnimplementedTicketServer) GetCallTranscript(*GetCallTranscriptRequest, Ticket_GetCallTranscriptServer) error {
	return status.Errorf(codes.Unimplemented, "method GetCallTranscript not implemented")
}
func (UnimplementedTicketServer) GetSupportTickets(context.Context, *GetSupportTicketsRequest) (*GetSupportTicketsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSupportTickets not implemented")
}
func (UnimplementedTicketServer) BulkUpdateTickets(context.Context, *BulkUpdateTicketsRequest) (*BulkUpdateTicketsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BulkUpdateTickets not implemented")
}
func (UnimplementedTicketServer) GetAllBulkTicketJobs(context.Context, *GetAllBulkTicketJobsRequest) (*GetAllBulkTicketJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllBulkTicketJobs not implemented")
}
func (UnimplementedTicketServer) GetJobFailureLogs(context.Context, *GetJobFailureLogsRequest) (*GetJobFailureLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobFailureLogs not implemented")
}
func (UnimplementedTicketServer) KillJobProcessing(context.Context, *KillJobProcessingRequest) (*KillJobProcessingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KillJobProcessing not implemented")
}
func (UnimplementedTicketServer) GetSupportTicketsForSherlock(context.Context, *GetSupportTicketsForSherlockRequest) (*GetSupportTicketsForSherlockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSupportTicketsForSherlock not implemented")
}
func (UnimplementedTicketServer) GetSupportTicketsForApp(context.Context, *GetSupportTicketsForAppRequest) (*GetSupportTicketsForAppResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSupportTicketsForApp not implemented")
}
func (UnimplementedTicketServer) CreateTicketDetailsTransformations(context.Context, *CreateTicketDetailsTransformationsRequest) (*CreateTicketDetailsTransformationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTicketDetailsTransformations not implemented")
}
func (UnimplementedTicketServer) UpdateTicketDetailsTransformation(context.Context, *UpdateTicketDetailsTransformationRequest) (*UpdateTicketDetailsTransformationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTicketDetailsTransformation not implemented")
}
func (UnimplementedTicketServer) DeleteTicketDetailsTransformations(context.Context, *DeleteTicketDetailsTransformationsRequest) (*DeleteTicketDetailsTransformationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTicketDetailsTransformations not implemented")
}
func (UnimplementedTicketServer) GetFreshdeskTicketCategories(context.Context, *GetFreshdeskTicketCategoriesRequest) (*GetFreshdeskTicketCategoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFreshdeskTicketCategories not implemented")
}
func (UnimplementedTicketServer) GetTicketInfo(context.Context, *GetTicketInfoRequest) (*GetTicketInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTicketInfo not implemented")
}
func (UnimplementedTicketServer) UpdateTicketInfo(context.Context, *UpdateTicketInfoRequest) (*UpdateTicketInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTicketInfo not implemented")
}
func (UnimplementedTicketServer) UpdateTicketAsync(context.Context, *UpdateTicketAsyncRequest) (*UpdateTicketAsyncResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTicketAsync not implemented")
}
func (UnimplementedTicketServer) AddPrivateNoteAsync(context.Context, *AddPrivateNoteAsyncRequest) (*AddPrivateNoteAsyncResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPrivateNoteAsync not implemented")
}
func (UnimplementedTicketServer) CreateTicket(context.Context, *CreateTicketRequest) (*CreateTicketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTicket not implemented")
}
func (UnimplementedTicketServer) GetSupportTicketByIdForApp(context.Context, *GetSupportTicketByIdForAppRequest) (*GetSupportTicketByIdForAppResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSupportTicketByIdForApp not implemented")
}
func (UnimplementedTicketServer) GetMergedTickets(context.Context, *GetMergedTicketsRequest) (*GetMergedTicketsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMergedTickets not implemented")
}
func (UnimplementedTicketServer) GetTicketDetailsForSherlock(context.Context, *GetTicketDetailsForSherlockRequest) (*GetTicketDetailsForSherlockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTicketDetailsForSherlock not implemented")
}
func (UnimplementedTicketServer) GetAgentInstructionForTicket(context.Context, *GetAgentInstructionForTicketRequest) (*GetAgentInstructionForTicketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAgentInstructionForTicket not implemented")
}
func (UnimplementedTicketServer) CreateTicketAsync(context.Context, *CreateTicketAsyncRequest) (*CreateTicketAsyncResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTicketAsync not implemented")
}
func (UnimplementedTicketServer) SubmitCsatFeedback(context.Context, *SubmitCsatFeedbackRequest) (*SubmitCsatFeedbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitCsatFeedback not implemented")
}
func (UnimplementedTicketServer) GetCategoryTransformation(context.Context, *GetCategoryTransformationsRequest) (*GetCategoryTransformationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCategoryTransformation not implemented")
}
func (UnimplementedTicketServer) FetchLatestResolvedTicketIdForCSAT(context.Context, *FetchLatestResolvedTicketIdForCSATRequest) (*FetchLatestResolvedTicketIdForCSATResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchLatestResolvedTicketIdForCSAT not implemented")
}

// UnsafeTicketServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TicketServer will
// result in compilation errors.
type UnsafeTicketServer interface {
	mustEmbedUnimplementedTicketServer()
}

func RegisterTicketServer(s grpc.ServiceRegistrar, srv TicketServer) {
	s.RegisterService(&Ticket_ServiceDesc, srv)
}

func _Ticket_AttachEntity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AttachEntityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).AttachEntity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_AttachEntity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).AttachEntity(ctx, req.(*AttachEntityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetRelatedTickets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRelatedTicketsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetRelatedTickets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetRelatedTickets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetRelatedTickets(ctx, req.(*GetRelatedTicketsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_MergeTickets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MergeTicketsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).MergeTickets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_MergeTickets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).MergeTickets(ctx, req.(*MergeTicketsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetCallRecording_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetCallRecordingRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(TicketServer).GetCallRecording(m, &ticketGetCallRecordingServer{stream})
}

type Ticket_GetCallRecordingServer interface {
	Send(*GetCallRecordingResponse) error
	grpc.ServerStream
}

type ticketGetCallRecordingServer struct {
	grpc.ServerStream
}

func (x *ticketGetCallRecordingServer) Send(m *GetCallRecordingResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Ticket_GetCallTranscript_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetCallTranscriptRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(TicketServer).GetCallTranscript(m, &ticketGetCallTranscriptServer{stream})
}

type Ticket_GetCallTranscriptServer interface {
	Send(*GetCallTranscriptResponse) error
	grpc.ServerStream
}

type ticketGetCallTranscriptServer struct {
	grpc.ServerStream
}

func (x *ticketGetCallTranscriptServer) Send(m *GetCallTranscriptResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Ticket_GetSupportTickets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSupportTicketsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetSupportTickets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetSupportTickets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetSupportTickets(ctx, req.(*GetSupportTicketsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_BulkUpdateTickets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkUpdateTicketsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).BulkUpdateTickets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_BulkUpdateTickets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).BulkUpdateTickets(ctx, req.(*BulkUpdateTicketsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetAllBulkTicketJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllBulkTicketJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetAllBulkTicketJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetAllBulkTicketJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetAllBulkTicketJobs(ctx, req.(*GetAllBulkTicketJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetJobFailureLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobFailureLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetJobFailureLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetJobFailureLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetJobFailureLogs(ctx, req.(*GetJobFailureLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_KillJobProcessing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KillJobProcessingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).KillJobProcessing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_KillJobProcessing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).KillJobProcessing(ctx, req.(*KillJobProcessingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetSupportTicketsForSherlock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSupportTicketsForSherlockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetSupportTicketsForSherlock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetSupportTicketsForSherlock_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetSupportTicketsForSherlock(ctx, req.(*GetSupportTicketsForSherlockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetSupportTicketsForApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSupportTicketsForAppRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetSupportTicketsForApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetSupportTicketsForApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetSupportTicketsForApp(ctx, req.(*GetSupportTicketsForAppRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_CreateTicketDetailsTransformations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTicketDetailsTransformationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).CreateTicketDetailsTransformations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_CreateTicketDetailsTransformations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).CreateTicketDetailsTransformations(ctx, req.(*CreateTicketDetailsTransformationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_UpdateTicketDetailsTransformation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTicketDetailsTransformationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).UpdateTicketDetailsTransformation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_UpdateTicketDetailsTransformation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).UpdateTicketDetailsTransformation(ctx, req.(*UpdateTicketDetailsTransformationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_DeleteTicketDetailsTransformations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTicketDetailsTransformationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).DeleteTicketDetailsTransformations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_DeleteTicketDetailsTransformations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).DeleteTicketDetailsTransformations(ctx, req.(*DeleteTicketDetailsTransformationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetFreshdeskTicketCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFreshdeskTicketCategoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetFreshdeskTicketCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetFreshdeskTicketCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetFreshdeskTicketCategories(ctx, req.(*GetFreshdeskTicketCategoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetTicketInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTicketInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetTicketInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetTicketInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetTicketInfo(ctx, req.(*GetTicketInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_UpdateTicketInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTicketInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).UpdateTicketInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_UpdateTicketInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).UpdateTicketInfo(ctx, req.(*UpdateTicketInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_UpdateTicketAsync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTicketAsyncRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).UpdateTicketAsync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_UpdateTicketAsync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).UpdateTicketAsync(ctx, req.(*UpdateTicketAsyncRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_AddPrivateNoteAsync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPrivateNoteAsyncRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).AddPrivateNoteAsync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_AddPrivateNoteAsync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).AddPrivateNoteAsync(ctx, req.(*AddPrivateNoteAsyncRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_CreateTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTicketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).CreateTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_CreateTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).CreateTicket(ctx, req.(*CreateTicketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetSupportTicketByIdForApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSupportTicketByIdForAppRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetSupportTicketByIdForApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetSupportTicketByIdForApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetSupportTicketByIdForApp(ctx, req.(*GetSupportTicketByIdForAppRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetMergedTickets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMergedTicketsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetMergedTickets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetMergedTickets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetMergedTickets(ctx, req.(*GetMergedTicketsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetTicketDetailsForSherlock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTicketDetailsForSherlockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetTicketDetailsForSherlock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetTicketDetailsForSherlock_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetTicketDetailsForSherlock(ctx, req.(*GetTicketDetailsForSherlockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetAgentInstructionForTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAgentInstructionForTicketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetAgentInstructionForTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetAgentInstructionForTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetAgentInstructionForTicket(ctx, req.(*GetAgentInstructionForTicketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_CreateTicketAsync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTicketAsyncRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).CreateTicketAsync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_CreateTicketAsync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).CreateTicketAsync(ctx, req.(*CreateTicketAsyncRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_SubmitCsatFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitCsatFeedbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).SubmitCsatFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_SubmitCsatFeedback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).SubmitCsatFeedback(ctx, req.(*SubmitCsatFeedbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetCategoryTransformation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCategoryTransformationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetCategoryTransformation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetCategoryTransformation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetCategoryTransformation(ctx, req.(*GetCategoryTransformationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_FetchLatestResolvedTicketIdForCSAT_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchLatestResolvedTicketIdForCSATRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).FetchLatestResolvedTicketIdForCSAT(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_FetchLatestResolvedTicketIdForCSAT_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).FetchLatestResolvedTicketIdForCSAT(ctx, req.(*FetchLatestResolvedTicketIdForCSATRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Ticket_ServiceDesc is the grpc.ServiceDesc for Ticket service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Ticket_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cx.ticket.ticket",
	HandlerType: (*TicketServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AttachEntity",
			Handler:    _Ticket_AttachEntity_Handler,
		},
		{
			MethodName: "GetRelatedTickets",
			Handler:    _Ticket_GetRelatedTickets_Handler,
		},
		{
			MethodName: "MergeTickets",
			Handler:    _Ticket_MergeTickets_Handler,
		},
		{
			MethodName: "GetSupportTickets",
			Handler:    _Ticket_GetSupportTickets_Handler,
		},
		{
			MethodName: "BulkUpdateTickets",
			Handler:    _Ticket_BulkUpdateTickets_Handler,
		},
		{
			MethodName: "GetAllBulkTicketJobs",
			Handler:    _Ticket_GetAllBulkTicketJobs_Handler,
		},
		{
			MethodName: "GetJobFailureLogs",
			Handler:    _Ticket_GetJobFailureLogs_Handler,
		},
		{
			MethodName: "KillJobProcessing",
			Handler:    _Ticket_KillJobProcessing_Handler,
		},
		{
			MethodName: "GetSupportTicketsForSherlock",
			Handler:    _Ticket_GetSupportTicketsForSherlock_Handler,
		},
		{
			MethodName: "GetSupportTicketsForApp",
			Handler:    _Ticket_GetSupportTicketsForApp_Handler,
		},
		{
			MethodName: "CreateTicketDetailsTransformations",
			Handler:    _Ticket_CreateTicketDetailsTransformations_Handler,
		},
		{
			MethodName: "UpdateTicketDetailsTransformation",
			Handler:    _Ticket_UpdateTicketDetailsTransformation_Handler,
		},
		{
			MethodName: "DeleteTicketDetailsTransformations",
			Handler:    _Ticket_DeleteTicketDetailsTransformations_Handler,
		},
		{
			MethodName: "GetFreshdeskTicketCategories",
			Handler:    _Ticket_GetFreshdeskTicketCategories_Handler,
		},
		{
			MethodName: "GetTicketInfo",
			Handler:    _Ticket_GetTicketInfo_Handler,
		},
		{
			MethodName: "UpdateTicketInfo",
			Handler:    _Ticket_UpdateTicketInfo_Handler,
		},
		{
			MethodName: "UpdateTicketAsync",
			Handler:    _Ticket_UpdateTicketAsync_Handler,
		},
		{
			MethodName: "AddPrivateNoteAsync",
			Handler:    _Ticket_AddPrivateNoteAsync_Handler,
		},
		{
			MethodName: "CreateTicket",
			Handler:    _Ticket_CreateTicket_Handler,
		},
		{
			MethodName: "GetSupportTicketByIdForApp",
			Handler:    _Ticket_GetSupportTicketByIdForApp_Handler,
		},
		{
			MethodName: "GetMergedTickets",
			Handler:    _Ticket_GetMergedTickets_Handler,
		},
		{
			MethodName: "GetTicketDetailsForSherlock",
			Handler:    _Ticket_GetTicketDetailsForSherlock_Handler,
		},
		{
			MethodName: "GetAgentInstructionForTicket",
			Handler:    _Ticket_GetAgentInstructionForTicket_Handler,
		},
		{
			MethodName: "CreateTicketAsync",
			Handler:    _Ticket_CreateTicketAsync_Handler,
		},
		{
			MethodName: "SubmitCsatFeedback",
			Handler:    _Ticket_SubmitCsatFeedback_Handler,
		},
		{
			MethodName: "GetCategoryTransformation",
			Handler:    _Ticket_GetCategoryTransformation_Handler,
		},
		{
			MethodName: "FetchLatestResolvedTicketIdForCSAT",
			Handler:    _Ticket_FetchLatestResolvedTicketIdForCSAT_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetCallRecording",
			Handler:       _Ticket_GetCallRecording_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "GetCallTranscript",
			Handler:       _Ticket_GetCallTranscript_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "api/cx/ticket/service.proto",
}
