// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/cx/sprinklr/service.proto

package sprinklr

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Sprinklr_ProcessTicketFromSprinklrEvent_FullMethodName = "/cx.sprinklr.Sprinklr/ProcessTicketFromSprinklrEvent"
)

// SprinklrClient is the client API for Sprinklr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SprinklrClient interface {
	ProcessTicketFromSprinklrEvent(ctx context.Context, in *ProcessTicketFromSprinklrEventRequest, opts ...grpc.CallOption) (*ProcessTicketFromSprinklrEventResponse, error)
}

type sprinklrClient struct {
	cc grpc.ClientConnInterface
}

func NewSprinklrClient(cc grpc.ClientConnInterface) SprinklrClient {
	return &sprinklrClient{cc}
}

func (c *sprinklrClient) ProcessTicketFromSprinklrEvent(ctx context.Context, in *ProcessTicketFromSprinklrEventRequest, opts ...grpc.CallOption) (*ProcessTicketFromSprinklrEventResponse, error) {
	out := new(ProcessTicketFromSprinklrEventResponse)
	err := c.cc.Invoke(ctx, Sprinklr_ProcessTicketFromSprinklrEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SprinklrServer is the server API for Sprinklr service.
// All implementations should embed UnimplementedSprinklrServer
// for forward compatibility
type SprinklrServer interface {
	ProcessTicketFromSprinklrEvent(context.Context, *ProcessTicketFromSprinklrEventRequest) (*ProcessTicketFromSprinklrEventResponse, error)
}

// UnimplementedSprinklrServer should be embedded to have forward compatible implementations.
type UnimplementedSprinklrServer struct {
}

func (UnimplementedSprinklrServer) ProcessTicketFromSprinklrEvent(context.Context, *ProcessTicketFromSprinklrEventRequest) (*ProcessTicketFromSprinklrEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessTicketFromSprinklrEvent not implemented")
}

// UnsafeSprinklrServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SprinklrServer will
// result in compilation errors.
type UnsafeSprinklrServer interface {
	mustEmbedUnimplementedSprinklrServer()
}

func RegisterSprinklrServer(s grpc.ServiceRegistrar, srv SprinklrServer) {
	s.RegisterService(&Sprinklr_ServiceDesc, srv)
}

func _Sprinklr_ProcessTicketFromSprinklrEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessTicketFromSprinklrEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SprinklrServer).ProcessTicketFromSprinklrEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Sprinklr_ProcessTicketFromSprinklrEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SprinklrServer).ProcessTicketFromSprinklrEvent(ctx, req.(*ProcessTicketFromSprinklrEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Sprinklr_ServiceDesc is the grpc.ServiceDesc for Sprinklr service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Sprinklr_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cx.sprinklr.Sprinklr",
	HandlerType: (*SprinklrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessTicketFromSprinklrEvent",
			Handler:    _Sprinklr_ProcessTicketFromSprinklrEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/cx/sprinklr/service.proto",
}
