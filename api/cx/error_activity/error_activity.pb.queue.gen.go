// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/cx/error_activity
package error_activity

import (
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessEventMethod = "ProcessEvent"
)

// RegisterProcessEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessEventMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessEventMethod)
}
