package ticket_summary

import alPb "github.com/epifi/gamma/api/cx/audit_log"

func (m *GetTicketDetailsRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetTicketDetailsRequest) GetObject() alPb.Object {
	return alPb.Object_TICKET
}

func (m *GetTicketConversationsRequest) GetAction() alPb.Action {
	return alPb.Action_FETCH
}

func (m *GetTicketConversationsRequest) GetObject() alPb.Object {
	return alPb.Object_TICKET
}
