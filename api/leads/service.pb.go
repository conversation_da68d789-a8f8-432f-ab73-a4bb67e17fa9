// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/leads/service.proto

package leads

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateLeadResponse_Status int32

const (
	CreateLeadResponse_OK                                        CreateLeadResponse_Status = 0
	CreateLeadResponse_STATUS_LEAD_EXISTS_WITH_CLIENT_REQUEST_ID CreateLeadResponse_Status = 101
	// if lead already exists with same user identifiers associated with different client
	CreateLeadResponse_STATUS_LEAD_EXISTS_WITH_USER_IDENTIFIERS CreateLeadResponse_Status = 102
	CreateLeadResponse_STATUS_USER_CANNOT_START_PRODUCT_JOURNEY CreateLeadResponse_Status = 103
	// can happen if a evaluation for a lead results in a lead not being of use
	CreateLeadResponse_STATUS_LEAD_REJECTED CreateLeadResponse_Status = 104
	// if lead already exists with same user identifiers associated with same client
	CreateLeadResponse_STATUS_LEAD_EXISTS_WITH_USER_IDENTIFIERS_FOR_SAME_CLIENT CreateLeadResponse_Status = 105
)

// Enum value maps for CreateLeadResponse_Status.
var (
	CreateLeadResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "STATUS_LEAD_EXISTS_WITH_CLIENT_REQUEST_ID",
		102: "STATUS_LEAD_EXISTS_WITH_USER_IDENTIFIERS",
		103: "STATUS_USER_CANNOT_START_PRODUCT_JOURNEY",
		104: "STATUS_LEAD_REJECTED",
		105: "STATUS_LEAD_EXISTS_WITH_USER_IDENTIFIERS_FOR_SAME_CLIENT",
	}
	CreateLeadResponse_Status_value = map[string]int32{
		"OK": 0,
		"STATUS_LEAD_EXISTS_WITH_CLIENT_REQUEST_ID":                101,
		"STATUS_LEAD_EXISTS_WITH_USER_IDENTIFIERS":                 102,
		"STATUS_USER_CANNOT_START_PRODUCT_JOURNEY":                 103,
		"STATUS_LEAD_REJECTED":                                     104,
		"STATUS_LEAD_EXISTS_WITH_USER_IDENTIFIERS_FOR_SAME_CLIENT": 105,
	}
)

func (x CreateLeadResponse_Status) Enum() *CreateLeadResponse_Status {
	p := new(CreateLeadResponse_Status)
	*p = x
	return p
}

func (x CreateLeadResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateLeadResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_leads_service_proto_enumTypes[0].Descriptor()
}

func (CreateLeadResponse_Status) Type() protoreflect.EnumType {
	return &file_api_leads_service_proto_enumTypes[0]
}

func (x CreateLeadResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateLeadResponse_Status.Descriptor instead.
func (CreateLeadResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_leads_service_proto_rawDescGZIP(), []int{5, 0}
}

type SetActorIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lead ids for which actor id needs to be set
	LeadIds []string `protobuf:"bytes,1,rep,name=lead_ids,json=leadIds,proto3" json:"lead_ids,omitempty"`
	ActorId string   `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Email   string   `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *SetActorIdRequest) Reset() {
	*x = SetActorIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetActorIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetActorIdRequest) ProtoMessage() {}

func (x *SetActorIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetActorIdRequest.ProtoReflect.Descriptor instead.
func (*SetActorIdRequest) Descriptor() ([]byte, []int) {
	return file_api_leads_service_proto_rawDescGZIP(), []int{0}
}

func (x *SetActorIdRequest) GetLeadIds() []string {
	if x != nil {
		return x.LeadIds
	}
	return nil
}

func (x *SetActorIdRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *SetActorIdRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type SetActorIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SetActorIdResponse) Reset() {
	*x = SetActorIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetActorIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetActorIdResponse) ProtoMessage() {}

func (x *SetActorIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetActorIdResponse.ProtoReflect.Descriptor instead.
func (*SetActorIdResponse) Descriptor() ([]byte, []int) {
	return file_api_leads_service_proto_rawDescGZIP(), []int{1}
}

func (x *SetActorIdResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetActiveLeadsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user identifiers
	Pan         string              `protobuf:"bytes,1,opt,name=pan,proto3" json:"pan,omitempty"`
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Email       string              `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	// optional, if empty active leads for all product types will be returned
	ProductTypes []ProductType `protobuf:"varint,4,rep,packed,name=product_types,json=productTypes,proto3,enum=leads.ProductType" json:"product_types,omitempty"`
}

func (x *GetActiveLeadsRequest) Reset() {
	*x = GetActiveLeadsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActiveLeadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveLeadsRequest) ProtoMessage() {}

func (x *GetActiveLeadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveLeadsRequest.ProtoReflect.Descriptor instead.
func (*GetActiveLeadsRequest) Descriptor() ([]byte, []int) {
	return file_api_leads_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetActiveLeadsRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *GetActiveLeadsRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *GetActiveLeadsRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetActiveLeadsRequest) GetProductTypes() []ProductType {
	if x != nil {
		return x.ProductTypes
	}
	return nil
}

type GetActiveLeadsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// key is the integer value of ProductType enum
	ProductTypeToActiveLeadMap map[int32]*UserLead `protobuf:"bytes,2,rep,name=product_type_to_active_lead_map,json=productTypeToActiveLeadMap,proto3" json:"product_type_to_active_lead_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetActiveLeadsResponse) Reset() {
	*x = GetActiveLeadsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActiveLeadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveLeadsResponse) ProtoMessage() {}

func (x *GetActiveLeadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveLeadsResponse.ProtoReflect.Descriptor instead.
func (*GetActiveLeadsResponse) Descriptor() ([]byte, []int) {
	return file_api_leads_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetActiveLeadsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetActiveLeadsResponse) GetProductTypeToActiveLeadMap() map[int32]*UserLead {
	if x != nil {
		return x.ProductTypeToActiveLeadMap
	}
	return nil
}

type CreateLeadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientRequestId   string              `protobuf:"bytes,1,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	ClientId          string              `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	PhoneNumber       *common.PhoneNumber `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Email             string              `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Pan               string              `protobuf:"bytes,5,opt,name=pan,proto3" json:"pan,omitempty"`
	PersonalDetails   *PersonalDetails    `protobuf:"bytes,6,opt,name=personal_details,json=personalDetails,proto3" json:"personal_details,omitempty"`
	AdditionalDetails *AdditionalDetails  `protobuf:"bytes,7,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
	ProductType       ProductType         `protobuf:"varint,8,opt,name=product_type,json=productType,proto3,enum=leads.ProductType" json:"product_type,omitempty"`
}

func (x *CreateLeadRequest) Reset() {
	*x = CreateLeadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLeadRequest) ProtoMessage() {}

func (x *CreateLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLeadRequest.ProtoReflect.Descriptor instead.
func (*CreateLeadRequest) Descriptor() ([]byte, []int) {
	return file_api_leads_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateLeadRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (x *CreateLeadRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *CreateLeadRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *CreateLeadRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateLeadRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *CreateLeadRequest) GetPersonalDetails() *PersonalDetails {
	if x != nil {
		return x.PersonalDetails
	}
	return nil
}

func (x *CreateLeadRequest) GetAdditionalDetails() *AdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

func (x *CreateLeadRequest) GetProductType() ProductType {
	if x != nil {
		return x.ProductType
	}
	return ProductType_PRODUCT_TYPE_UNSPECIFIED
}

type CreateLeadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// product specific response data
	//
	// Types that are assignable to Data:
	//
	//	*CreateLeadResponse_FiLoansLeadResponse
	Data isCreateLeadResponse_Data `protobuf_oneof:"data"`
}

func (x *CreateLeadResponse) Reset() {
	*x = CreateLeadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLeadResponse) ProtoMessage() {}

func (x *CreateLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLeadResponse.ProtoReflect.Descriptor instead.
func (*CreateLeadResponse) Descriptor() ([]byte, []int) {
	return file_api_leads_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateLeadResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (m *CreateLeadResponse) GetData() isCreateLeadResponse_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *CreateLeadResponse) GetFiLoansLeadResponse() *FiLoansLeadResponse {
	if x, ok := x.GetData().(*CreateLeadResponse_FiLoansLeadResponse); ok {
		return x.FiLoansLeadResponse
	}
	return nil
}

type isCreateLeadResponse_Data interface {
	isCreateLeadResponse_Data()
}

type CreateLeadResponse_FiLoansLeadResponse struct {
	FiLoansLeadResponse *FiLoansLeadResponse `protobuf:"bytes,2,opt,name=fi_loans_lead_response,json=fiLoansLeadResponse,proto3,oneof"`
}

func (*CreateLeadResponse_FiLoansLeadResponse) isCreateLeadResponse_Data() {}

var File_api_leads_service_proto protoreflect.FileDescriptor

var file_api_leads_service_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x1a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x6c, 0x65, 0x61,
	0x64, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6c, 0x65, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x5f, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x65, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x65, 0x61, 0x64, 0x49, 0x64, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x22, 0x39, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xbc, 0x01, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x37, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xa2, 0x02, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x82, 0x01, 0x0a, 0x1f, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6c, 0x65, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54,
	0x6f, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x1a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x54, 0x6f, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x4d, 0x61, 0x70, 0x1a,
	0x5e, 0x0a, 0x1f, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x25, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x4c, 0x65, 0x61, 0x64, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x8b, 0x03, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x42,
	0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x41, 0x0a, 0x10, 0x70, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x50, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x70, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x47, 0x0a,
	0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x11, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x35, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x6c,
	0x65, 0x61, 0x64, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x8a, 0x03,
	0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x51, 0x0a, 0x16, 0x66, 0x69, 0x5f,
	0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x5f, 0x6c, 0x65, 0x61, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x2e, 0x46, 0x69, 0x4c, 0x6f, 0x61, 0x6e, 0x73, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x13, 0x66, 0x69, 0x4c, 0x6f, 0x61, 0x6e, 0x73,
	0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xf3, 0x01, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12,
	0x2d, 0x0a, 0x29, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x45,
	0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x65, 0x12, 0x2c,
	0x0a, 0x28, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x45, 0x58,
	0x49, 0x53, 0x54, 0x53, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x53, 0x10, 0x66, 0x12, 0x2c, 0x0a, 0x28,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x43, 0x41, 0x4e, 0x4e,
	0x4f, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54,
	0x5f, 0x4a, 0x4f, 0x55, 0x52, 0x4e, 0x45, 0x59, 0x10, 0x67, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54,
	0x45, 0x44, 0x10, 0x68, 0x12, 0x3c, 0x0a, 0x38, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c,
	0x45, 0x41, 0x44, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x53,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x53, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54,
	0x10, 0x69, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0xe8, 0x01, 0x0a, 0x0b, 0x55,
	0x73, 0x65, 0x72, 0x4c, 0x65, 0x61, 0x64, 0x53, 0x76, 0x63, 0x12, 0x43, 0x0a, 0x0a, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x12, 0x18, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x19, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4c, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x4f, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x65, 0x61, 0x64,
	0x73, 0x12, 0x1c, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1d, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x4c, 0x65, 0x61, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x43, 0x0a, 0x0a, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x18,
	0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73,
	0x2e, 0x53, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x44, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x5a, 0x20, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_leads_service_proto_rawDescOnce sync.Once
	file_api_leads_service_proto_rawDescData = file_api_leads_service_proto_rawDesc
)

func file_api_leads_service_proto_rawDescGZIP() []byte {
	file_api_leads_service_proto_rawDescOnce.Do(func() {
		file_api_leads_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_leads_service_proto_rawDescData)
	})
	return file_api_leads_service_proto_rawDescData
}

var file_api_leads_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_leads_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_leads_service_proto_goTypes = []interface{}{
	(CreateLeadResponse_Status)(0), // 0: leads.CreateLeadResponse.Status
	(*SetActorIdRequest)(nil),      // 1: leads.SetActorIdRequest
	(*SetActorIdResponse)(nil),     // 2: leads.SetActorIdResponse
	(*GetActiveLeadsRequest)(nil),  // 3: leads.GetActiveLeadsRequest
	(*GetActiveLeadsResponse)(nil), // 4: leads.GetActiveLeadsResponse
	(*CreateLeadRequest)(nil),      // 5: leads.CreateLeadRequest
	(*CreateLeadResponse)(nil),     // 6: leads.CreateLeadResponse
	nil,                            // 7: leads.GetActiveLeadsResponse.ProductTypeToActiveLeadMapEntry
	(*rpc.Status)(nil),             // 8: rpc.Status
	(*common.PhoneNumber)(nil),     // 9: api.typesv2.common.PhoneNumber
	(ProductType)(0),               // 10: leads.ProductType
	(*PersonalDetails)(nil),        // 11: leads.PersonalDetails
	(*AdditionalDetails)(nil),      // 12: leads.AdditionalDetails
	(*FiLoansLeadResponse)(nil),    // 13: leads.FiLoansLeadResponse
	(*UserLead)(nil),               // 14: leads.UserLead
}
var file_api_leads_service_proto_depIdxs = []int32{
	8,  // 0: leads.SetActorIdResponse.status:type_name -> rpc.Status
	9,  // 1: leads.GetActiveLeadsRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	10, // 2: leads.GetActiveLeadsRequest.product_types:type_name -> leads.ProductType
	8,  // 3: leads.GetActiveLeadsResponse.status:type_name -> rpc.Status
	7,  // 4: leads.GetActiveLeadsResponse.product_type_to_active_lead_map:type_name -> leads.GetActiveLeadsResponse.ProductTypeToActiveLeadMapEntry
	9,  // 5: leads.CreateLeadRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	11, // 6: leads.CreateLeadRequest.personal_details:type_name -> leads.PersonalDetails
	12, // 7: leads.CreateLeadRequest.additional_details:type_name -> leads.AdditionalDetails
	10, // 8: leads.CreateLeadRequest.product_type:type_name -> leads.ProductType
	8,  // 9: leads.CreateLeadResponse.status:type_name -> rpc.Status
	13, // 10: leads.CreateLeadResponse.fi_loans_lead_response:type_name -> leads.FiLoansLeadResponse
	14, // 11: leads.GetActiveLeadsResponse.ProductTypeToActiveLeadMapEntry.value:type_name -> leads.UserLead
	5,  // 12: leads.UserLeadSvc.CreateLead:input_type -> leads.CreateLeadRequest
	3,  // 13: leads.UserLeadSvc.GetActiveLeads:input_type -> leads.GetActiveLeadsRequest
	1,  // 14: leads.UserLeadSvc.SetActorId:input_type -> leads.SetActorIdRequest
	6,  // 15: leads.UserLeadSvc.CreateLead:output_type -> leads.CreateLeadResponse
	4,  // 16: leads.UserLeadSvc.GetActiveLeads:output_type -> leads.GetActiveLeadsResponse
	2,  // 17: leads.UserLeadSvc.SetActorId:output_type -> leads.SetActorIdResponse
	15, // [15:18] is the sub-list for method output_type
	12, // [12:15] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_api_leads_service_proto_init() }
func file_api_leads_service_proto_init() {
	if File_api_leads_service_proto != nil {
		return
	}
	file_api_leads_enums_proto_init()
	file_api_leads_user_lead_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_leads_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetActorIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_leads_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetActorIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_leads_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActiveLeadsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_leads_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActiveLeadsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_leads_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLeadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_leads_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLeadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_leads_service_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*CreateLeadResponse_FiLoansLeadResponse)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_leads_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_leads_service_proto_goTypes,
		DependencyIndexes: file_api_leads_service_proto_depIdxs,
		EnumInfos:         file_api_leads_service_proto_enumTypes,
		MessageInfos:      file_api_leads_service_proto_msgTypes,
	}.Build()
	File_api_leads_service_proto = out.File
	file_api_leads_service_proto_rawDesc = nil
	file_api_leads_service_proto_goTypes = nil
	file_api_leads_service_proto_depIdxs = nil
}
