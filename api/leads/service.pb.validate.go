// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/leads/service.proto

package leads

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SetActorIdRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SetActorIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetActorIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetActorIdRequestMultiError, or nil if none found.
func (m *SetActorIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetActorIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Email

	if len(errors) > 0 {
		return SetActorIdRequestMultiError(errors)
	}

	return nil
}

// SetActorIdRequestMultiError is an error wrapping multiple validation errors
// returned by SetActorIdRequest.ValidateAll() if the designated constraints
// aren't met.
type SetActorIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetActorIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetActorIdRequestMultiError) AllErrors() []error { return m }

// SetActorIdRequestValidationError is the validation error returned by
// SetActorIdRequest.Validate if the designated constraints aren't met.
type SetActorIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetActorIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetActorIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetActorIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetActorIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetActorIdRequestValidationError) ErrorName() string {
	return "SetActorIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetActorIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetActorIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetActorIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetActorIdRequestValidationError{}

// Validate checks the field values on SetActorIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetActorIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetActorIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetActorIdResponseMultiError, or nil if none found.
func (m *SetActorIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SetActorIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetActorIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetActorIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetActorIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SetActorIdResponseMultiError(errors)
	}

	return nil
}

// SetActorIdResponseMultiError is an error wrapping multiple validation errors
// returned by SetActorIdResponse.ValidateAll() if the designated constraints
// aren't met.
type SetActorIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetActorIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetActorIdResponseMultiError) AllErrors() []error { return m }

// SetActorIdResponseValidationError is the validation error returned by
// SetActorIdResponse.Validate if the designated constraints aren't met.
type SetActorIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetActorIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetActorIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetActorIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetActorIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetActorIdResponseValidationError) ErrorName() string {
	return "SetActorIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SetActorIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetActorIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetActorIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetActorIdResponseValidationError{}

// Validate checks the field values on GetActiveLeadsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActiveLeadsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActiveLeadsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActiveLeadsRequestMultiError, or nil if none found.
func (m *GetActiveLeadsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActiveLeadsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActiveLeadsRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActiveLeadsRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActiveLeadsRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	if len(errors) > 0 {
		return GetActiveLeadsRequestMultiError(errors)
	}

	return nil
}

// GetActiveLeadsRequestMultiError is an error wrapping multiple validation
// errors returned by GetActiveLeadsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetActiveLeadsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActiveLeadsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActiveLeadsRequestMultiError) AllErrors() []error { return m }

// GetActiveLeadsRequestValidationError is the validation error returned by
// GetActiveLeadsRequest.Validate if the designated constraints aren't met.
type GetActiveLeadsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActiveLeadsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActiveLeadsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActiveLeadsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActiveLeadsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActiveLeadsRequestValidationError) ErrorName() string {
	return "GetActiveLeadsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActiveLeadsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActiveLeadsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActiveLeadsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActiveLeadsRequestValidationError{}

// Validate checks the field values on GetActiveLeadsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActiveLeadsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActiveLeadsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActiveLeadsResponseMultiError, or nil if none found.
func (m *GetActiveLeadsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActiveLeadsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActiveLeadsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActiveLeadsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActiveLeadsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]int32, len(m.GetProductTypeToActiveLeadMap()))
		i := 0
		for key := range m.GetProductTypeToActiveLeadMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetProductTypeToActiveLeadMap()[key]
			_ = val

			// no validation rules for ProductTypeToActiveLeadMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetActiveLeadsResponseValidationError{
							field:  fmt.Sprintf("ProductTypeToActiveLeadMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetActiveLeadsResponseValidationError{
							field:  fmt.Sprintf("ProductTypeToActiveLeadMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetActiveLeadsResponseValidationError{
						field:  fmt.Sprintf("ProductTypeToActiveLeadMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GetActiveLeadsResponseMultiError(errors)
	}

	return nil
}

// GetActiveLeadsResponseMultiError is an error wrapping multiple validation
// errors returned by GetActiveLeadsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetActiveLeadsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActiveLeadsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActiveLeadsResponseMultiError) AllErrors() []error { return m }

// GetActiveLeadsResponseValidationError is the validation error returned by
// GetActiveLeadsResponse.Validate if the designated constraints aren't met.
type GetActiveLeadsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActiveLeadsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActiveLeadsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActiveLeadsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActiveLeadsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActiveLeadsResponseValidationError) ErrorName() string {
	return "GetActiveLeadsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActiveLeadsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActiveLeadsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActiveLeadsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActiveLeadsResponseValidationError{}

// Validate checks the field values on CreateLeadRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateLeadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLeadRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLeadRequestMultiError, or nil if none found.
func (m *CreateLeadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLeadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientRequestId

	// no validation rules for ClientId

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetPersonalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "PersonalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "PersonalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPersonalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadRequestValidationError{
				field:  "PersonalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadRequestValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadRequestValidationError{
				field:  "AdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProductType

	if len(errors) > 0 {
		return CreateLeadRequestMultiError(errors)
	}

	return nil
}

// CreateLeadRequestMultiError is an error wrapping multiple validation errors
// returned by CreateLeadRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateLeadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLeadRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLeadRequestMultiError) AllErrors() []error { return m }

// CreateLeadRequestValidationError is the validation error returned by
// CreateLeadRequest.Validate if the designated constraints aren't met.
type CreateLeadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLeadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLeadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLeadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLeadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLeadRequestValidationError) ErrorName() string {
	return "CreateLeadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLeadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLeadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLeadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLeadRequestValidationError{}

// Validate checks the field values on CreateLeadResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateLeadResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateLeadResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateLeadResponseMultiError, or nil if none found.
func (m *CreateLeadResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateLeadResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateLeadResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateLeadResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateLeadResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Data.(type) {
	case *CreateLeadResponse_FiLoansLeadResponse:
		if v == nil {
			err := CreateLeadResponseValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFiLoansLeadResponse()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateLeadResponseValidationError{
						field:  "FiLoansLeadResponse",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateLeadResponseValidationError{
						field:  "FiLoansLeadResponse",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFiLoansLeadResponse()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateLeadResponseValidationError{
					field:  "FiLoansLeadResponse",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CreateLeadResponseMultiError(errors)
	}

	return nil
}

// CreateLeadResponseMultiError is an error wrapping multiple validation errors
// returned by CreateLeadResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateLeadResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateLeadResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateLeadResponseMultiError) AllErrors() []error { return m }

// CreateLeadResponseValidationError is the validation error returned by
// CreateLeadResponse.Validate if the designated constraints aren't met.
type CreateLeadResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateLeadResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateLeadResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateLeadResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateLeadResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateLeadResponseValidationError) ErrorName() string {
	return "CreateLeadResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateLeadResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateLeadResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateLeadResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateLeadResponseValidationError{}
