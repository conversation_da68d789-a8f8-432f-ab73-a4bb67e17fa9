//go:generate gen_sql -types=UserLeadStatus,ProductType

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/leads/enums.proto

package leads

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UserLeadStatus represents the current status of a user lead in the system.
type UserLeadStatus int32

const (
	// Initial status of the lead when it is created in our system.
	UserLeadStatus_USER_LEAD_STATUS_LEAD_CREATED UserLeadStatus = 0
	// User entity is created in the product service's system
	UserLeadStatus_USER_LEAD_STATUS_USER_CREATED UserLeadStatus = 1
	// If the lead does not reach any other terminal status within expiry window, it will be marked as expired.
	UserLeadStatus_USER_LEAD_STATUS_EXPIRED UserLeadStatus = 2
	// User got rejected from all possible product journey flows
	UserLeadStatus_USER_LEAD_STATUS_REJECTED UserLeadStatus = 3
	// Product onboarding is completed
	UserLeadStatus_USER_LEAD_STATUS_CONVERTED UserLeadStatus = 4
	// User is duplicate
	UserLeadStatus_USER_LEAD_STATUS_DUPLICATE UserLeadStatus = 5
)

// Enum value maps for UserLeadStatus.
var (
	UserLeadStatus_name = map[int32]string{
		0: "USER_LEAD_STATUS_LEAD_CREATED",
		1: "USER_LEAD_STATUS_USER_CREATED",
		2: "USER_LEAD_STATUS_EXPIRED",
		3: "USER_LEAD_STATUS_REJECTED",
		4: "USER_LEAD_STATUS_CONVERTED",
		5: "USER_LEAD_STATUS_DUPLICATE",
	}
	UserLeadStatus_value = map[string]int32{
		"USER_LEAD_STATUS_LEAD_CREATED": 0,
		"USER_LEAD_STATUS_USER_CREATED": 1,
		"USER_LEAD_STATUS_EXPIRED":      2,
		"USER_LEAD_STATUS_REJECTED":     3,
		"USER_LEAD_STATUS_CONVERTED":    4,
		"USER_LEAD_STATUS_DUPLICATE":    5,
	}
)

func (x UserLeadStatus) Enum() *UserLeadStatus {
	p := new(UserLeadStatus)
	*p = x
	return p
}

func (x UserLeadStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserLeadStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_leads_enums_proto_enumTypes[0].Descriptor()
}

func (UserLeadStatus) Type() protoreflect.EnumType {
	return &file_api_leads_enums_proto_enumTypes[0]
}

func (x UserLeadStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserLeadStatus.Descriptor instead.
func (UserLeadStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_leads_enums_proto_rawDescGZIP(), []int{0}
}

// ProductType represents the type of product associated with the lead.
type ProductType int32

const (
	// Default value, should not be used.
	ProductType_PRODUCT_TYPE_UNSPECIFIED ProductType = 0
	// FI Personal Loan product type.
	ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN ProductType = 1
)

// Enum value maps for ProductType.
var (
	ProductType_name = map[int32]string{
		0: "PRODUCT_TYPE_UNSPECIFIED",
		1: "PRODUCT_TYPE_FI_PERSONAL_LOAN",
	}
	ProductType_value = map[string]int32{
		"PRODUCT_TYPE_UNSPECIFIED":      0,
		"PRODUCT_TYPE_FI_PERSONAL_LOAN": 1,
	}
)

func (x ProductType) Enum() *ProductType {
	p := new(ProductType)
	*p = x
	return p
}

func (x ProductType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProductType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_leads_enums_proto_enumTypes[1].Descriptor()
}

func (ProductType) Type() protoreflect.EnumType {
	return &file_api_leads_enums_proto_enumTypes[1]
}

func (x ProductType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProductType.Descriptor instead.
func (ProductType) EnumDescriptor() ([]byte, []int) {
	return file_api_leads_enums_proto_rawDescGZIP(), []int{1}
}

type UserLeadFieldMask int32

const (
	UserLeadFieldMask_USER_LEAD_FIELD_MASK_UNSPECIFIED        UserLeadFieldMask = 0
	UserLeadFieldMask_USER_LEAD_FIELD_MASK_ACTOR_ID           UserLeadFieldMask = 1
	UserLeadFieldMask_USER_LEAD_FIELD_MASK_PERSONAL_DETAILS   UserLeadFieldMask = 2
	UserLeadFieldMask_USER_LEAD_FIELD_MASK_ADDITIONAL_DETAILS UserLeadFieldMask = 3
	UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS        UserLeadFieldMask = 4
	UserLeadFieldMask_USER_LEAD_FIELD_MASK_COMPLETED_AT       UserLeadFieldMask = 5
	UserLeadFieldMask_USER_LEAD_FIELD_MASK_EXPIRED_AT         UserLeadFieldMask = 6
	UserLeadFieldMask_USER_LEAD_FIELD_MASK_EMAIL              UserLeadFieldMask = 7
)

// Enum value maps for UserLeadFieldMask.
var (
	UserLeadFieldMask_name = map[int32]string{
		0: "USER_LEAD_FIELD_MASK_UNSPECIFIED",
		1: "USER_LEAD_FIELD_MASK_ACTOR_ID",
		2: "USER_LEAD_FIELD_MASK_PERSONAL_DETAILS",
		3: "USER_LEAD_FIELD_MASK_ADDITIONAL_DETAILS",
		4: "USER_LEAD_FIELD_MASK_LEAD_STATUS",
		5: "USER_LEAD_FIELD_MASK_COMPLETED_AT",
		6: "USER_LEAD_FIELD_MASK_EXPIRED_AT",
		7: "USER_LEAD_FIELD_MASK_EMAIL",
	}
	UserLeadFieldMask_value = map[string]int32{
		"USER_LEAD_FIELD_MASK_UNSPECIFIED":        0,
		"USER_LEAD_FIELD_MASK_ACTOR_ID":           1,
		"USER_LEAD_FIELD_MASK_PERSONAL_DETAILS":   2,
		"USER_LEAD_FIELD_MASK_ADDITIONAL_DETAILS": 3,
		"USER_LEAD_FIELD_MASK_LEAD_STATUS":        4,
		"USER_LEAD_FIELD_MASK_COMPLETED_AT":       5,
		"USER_LEAD_FIELD_MASK_EXPIRED_AT":         6,
		"USER_LEAD_FIELD_MASK_EMAIL":              7,
	}
)

func (x UserLeadFieldMask) Enum() *UserLeadFieldMask {
	p := new(UserLeadFieldMask)
	*p = x
	return p
}

func (x UserLeadFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserLeadFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_leads_enums_proto_enumTypes[2].Descriptor()
}

func (UserLeadFieldMask) Type() protoreflect.EnumType {
	return &file_api_leads_enums_proto_enumTypes[2]
}

func (x UserLeadFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserLeadFieldMask.Descriptor instead.
func (UserLeadFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_leads_enums_proto_rawDescGZIP(), []int{2}
}

// represents different types of evaluations to run for a loans lead
type FiLoansEvaluationType int32

const (
	FiLoansEvaluationType_FI_LOANS_EVALUATION_TYPE_UNSPECIFIED FiLoansEvaluationType = 0
	FiLoansEvaluationType_FI_LOANS_EVALUATION_TYPE_BASIC       FiLoansEvaluationType = 1
)

// Enum value maps for FiLoansEvaluationType.
var (
	FiLoansEvaluationType_name = map[int32]string{
		0: "FI_LOANS_EVALUATION_TYPE_UNSPECIFIED",
		1: "FI_LOANS_EVALUATION_TYPE_BASIC",
	}
	FiLoansEvaluationType_value = map[string]int32{
		"FI_LOANS_EVALUATION_TYPE_UNSPECIFIED": 0,
		"FI_LOANS_EVALUATION_TYPE_BASIC":       1,
	}
)

func (x FiLoansEvaluationType) Enum() *FiLoansEvaluationType {
	p := new(FiLoansEvaluationType)
	*p = x
	return p
}

func (x FiLoansEvaluationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FiLoansEvaluationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_leads_enums_proto_enumTypes[3].Descriptor()
}

func (FiLoansEvaluationType) Type() protoreflect.EnumType {
	return &file_api_leads_enums_proto_enumTypes[3]
}

func (x FiLoansEvaluationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FiLoansEvaluationType.Descriptor instead.
func (FiLoansEvaluationType) EnumDescriptor() ([]byte, []int) {
	return file_api_leads_enums_proto_rawDescGZIP(), []int{3}
}

type LenderType int32

const (
	LenderType_LENDER_TYPE_UNSPECIFIED LenderType = 0
	// e.g. Federal bank
	LenderType_LENDER_TYPE_BANK LenderType = 1
	// e.g. ABFL
	LenderType_LENDER_TYPE_LARGE_NBFC LenderType = 2
	// e.g. Lenden
	LenderType_LENDER_TYPE_SMALL_NBFC LenderType = 3
)

// Enum value maps for LenderType.
var (
	LenderType_name = map[int32]string{
		0: "LENDER_TYPE_UNSPECIFIED",
		1: "LENDER_TYPE_BANK",
		2: "LENDER_TYPE_LARGE_NBFC",
		3: "LENDER_TYPE_SMALL_NBFC",
	}
	LenderType_value = map[string]int32{
		"LENDER_TYPE_UNSPECIFIED": 0,
		"LENDER_TYPE_BANK":        1,
		"LENDER_TYPE_LARGE_NBFC":  2,
		"LENDER_TYPE_SMALL_NBFC":  3,
	}
)

func (x LenderType) Enum() *LenderType {
	p := new(LenderType)
	*p = x
	return p
}

func (x LenderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LenderType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_leads_enums_proto_enumTypes[4].Descriptor()
}

func (LenderType) Type() protoreflect.EnumType {
	return &file_api_leads_enums_proto_enumTypes[4]
}

func (x LenderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LenderType.Descriptor instead.
func (LenderType) EnumDescriptor() ([]byte, []int) {
	return file_api_leads_enums_proto_rawDescGZIP(), []int{4}
}

var File_api_leads_enums_proto protoreflect.FileDescriptor

var file_api_leads_enums_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2a, 0xd3,
	0x01, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x65, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x45, 0x41,
	0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x4c, 0x45, 0x41, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49,
	0x52, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x45,
	0x41, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54,
	0x45, 0x44, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x45, 0x41,
	0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x54,
	0x45, 0x44, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x45, 0x41,
	0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41,
	0x54, 0x45, 0x10, 0x05, 0x2a, 0x4e, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x46, 0x49, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x10, 0x01, 0x2a, 0xc6, 0x02, 0x0a, 0x11, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x65, 0x61,
	0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x24, 0x0a, 0x20, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x21, 0x0a, 0x1d, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49,
	0x44, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x45, 0x41, 0x44,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x45, 0x52, 0x53,
	0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x02, 0x12, 0x2b,
	0x0a, 0x27, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41,
	0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10,
	0x04, 0x12, 0x25, 0x0a, 0x21, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x05, 0x12, 0x23, 0x0a, 0x1f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x12, 0x1e, 0x0a,
	0x1a, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x07, 0x2a, 0x65, 0x0a,
	0x15, 0x46, 0x69, 0x4c, 0x6f, 0x61, 0x6e, 0x73, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x24, 0x46, 0x49, 0x5f, 0x4c, 0x4f, 0x41,
	0x4e, 0x53, 0x5f, 0x45, 0x56, 0x41, 0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x22, 0x0a, 0x1e, 0x46, 0x49, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x45, 0x56, 0x41,
	0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x53,
	0x49, 0x43, 0x10, 0x01, 0x2a, 0x77, 0x0a, 0x0a, 0x4c, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x14, 0x0a, 0x10, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42,
	0x41, 0x4e, 0x4b, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x4e, 0x42, 0x46, 0x43, 0x10,
	0x02, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x53, 0x4d, 0x41, 0x4c, 0x4c, 0x5f, 0x4e, 0x42, 0x46, 0x43, 0x10, 0x03, 0x42, 0x44, 0x0a,
	0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6c, 0x65, 0x61, 0x64,
	0x73, 0x5a, 0x20, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6c, 0x65,
	0x61, 0x64, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_leads_enums_proto_rawDescOnce sync.Once
	file_api_leads_enums_proto_rawDescData = file_api_leads_enums_proto_rawDesc
)

func file_api_leads_enums_proto_rawDescGZIP() []byte {
	file_api_leads_enums_proto_rawDescOnce.Do(func() {
		file_api_leads_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_leads_enums_proto_rawDescData)
	})
	return file_api_leads_enums_proto_rawDescData
}

var file_api_leads_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_api_leads_enums_proto_goTypes = []interface{}{
	(UserLeadStatus)(0),        // 0: leads.UserLeadStatus
	(ProductType)(0),           // 1: leads.ProductType
	(UserLeadFieldMask)(0),     // 2: leads.UserLeadFieldMask
	(FiLoansEvaluationType)(0), // 3: leads.FiLoansEvaluationType
	(LenderType)(0),            // 4: leads.LenderType
}
var file_api_leads_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_leads_enums_proto_init() }
func file_api_leads_enums_proto_init() {
	if File_api_leads_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_leads_enums_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_leads_enums_proto_goTypes,
		DependencyIndexes: file_api_leads_enums_proto_depIdxs,
		EnumInfos:         file_api_leads_enums_proto_enumTypes,
	}.Build()
	File_api_leads_enums_proto = out.File
	file_api_leads_enums_proto_rawDesc = nil
	file_api_leads_enums_proto_goTypes = nil
	file_api_leads_enums_proto_depIdxs = nil
}
