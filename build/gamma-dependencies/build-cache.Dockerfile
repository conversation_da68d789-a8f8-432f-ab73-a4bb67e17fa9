# Source for 632884248997.dkr.ecr.ap-south-1.amazonaws.com/gamma-dependencies:build-cache-latest
FROM 632884248997.dkr.ecr.ap-south-1.amazonaws.com/gamma-dependencies:latest AS builder

# set GOPROXY for fetching Go Modules to default value
ARG goproxy=https://proxy.golang.org,direct
ENV GOPROXY=$goproxy

# Structure of cmd/servers directory is as follows:
# cmd/servers/<env>/<server_name>/server/
# cmd/servers/<env>/<server_name>/server.gen.go -> this contains main function to build binary which just improts the cmd/servers/<env>/<server_name>/server/ package
# Structure of cmd/worker directory is as follows:
# cmd/worker/<worker_name>/worker/
# cmd/worker/<worker_name>/worker.go -> this contains main function to build binary which just improts the cmd/worker/<worker_name>/worker/ package
# so we can skip compiling packages with main files and avoid wasting lot of time spent by the go toolchain in linking and building the binary.

# Copy the source code for primary branch
COPY --chmod=777 ./primary/gamma /go/src/github.com/epifi/gamma/
WORKDIR /go/src/github.com/epifi/gamma/

RUN go install -v -trimpath -mod=readonly -gcflags="-e" -ldflags '-s' $(go list ./cmd/servers/qa/... ./cmd/worker/... | grep -E '(/server$|/worker$)') || true  && \
    rm -rf /go/src/github.com/epifi/gamma

# Copy the source code for the secondary branch
COPY --chmod=777 ./secondary/gamma /go/src/github.com/epifi/gamma/
WORKDIR /go/src/github.com/epifi/gamma/

RUN go install -trimpath -v -mod=readonly -gcflags="-e" -ldflags '-s' $(go list ./cmd/servers/qa/... ./cmd/worker/... | grep -E '(/server$|/worker$)') || true  && \
    rm -rf /go/src/github.com/epifi/gamma
