# Pupose of this dockerfile is to build a common base image for all the server images in teamspace with all the tools preinstalled.
# This Dockerfile is used to build 632884248997.dkr.ecr.ap-south-1.amazonaws.com/teamspace-server-base

# The second and final stage
FROM ubuntu:22.04

COPY --from=tarampampam/curl:7.78.0 /bin/curl /bin/curl

RUN apt-get -y update && apt-get -y upgrade

# certs are needed for https servers and tzdata is needed for timezone data in Golang application
# Ref- https://medium.com/@mhcbinder/using-local-time-in-a-golang-docker-container-built-from-scratch-2900af02fbaf
RUN apt-get install -y ca-certificates tzdata

# TODO(nitesh): add conditional statement to install dependencies only for the required targets
RUN apt-get install -y ffmpeg wget

RUN curl -JLO "https://dl.filippo.io/mkcert/v1.4.4?for=linux/amd64" && \
    chmod +x mkcert-v*-linux-amd64 && \
    cp mkcert-v*-linux-amd64 /usr/local/bin/mkcert && \
    mkdir -p /root/.pki/


# Set path and directory for 'Zinc' installation
ENV ZINC_PATH="/zinc"
RUN mkdir -p "${ZINC_PATH}/data"
EXPOSE 4080

# For servers which use 'Zinc', install wget and zinc
RUN echo "Installing zinc" &&\
	wget -O /tmp/zinc.tar.gz https://github.com/zinclabs/zinc/releases/download/v0.2.8/zinc_0.2.8_Linux_x86_64.tar.gz && \
	tar -xvf /tmp/zinc.tar.gz -C "${ZINC_PATH}"
