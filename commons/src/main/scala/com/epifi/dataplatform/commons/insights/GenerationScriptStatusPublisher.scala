package com.epifi.dataplatform.commons.insights

import com.epifi.dataplatform.commons.utils.PipelineLogger
import com.epifi.dataplatforms.commons.readwrite.aws.SqsPublisher
import play.api.libs.json._
import com.typesafe.config.Config

import java.time.LocalDateTime

class GenerationScriptStatusPublisher(val scriptName: String, val platformConfig: Config, jobConfig:Config) extends PipelineLogger {
  private val queueName = platformConfig.getConfig("insight_queues").getString("script_run_status_queue")
  private val publisher = new SqsPublisher(queueName)
  private val jobId = try {
    jobConfig.getString("jobID")
  }catch {
    case e:Exception => {
      "JOB_ID_UNSPECIFIED"
    }
  }

  def publishStartMessage(runId: String, insightId: String) : String = {
    val message = GenerationScriptStatus(runId, insightId, jobId, "GENERATION_PLATFORM_SPARK", 0, scriptName, ScriptRunStatus.RUN_STATUS_STARTED, LocalDateTime.now())
    try {
      val msgStr = Json.stringify(Json.toJson(message))
      publisher.publishMessage(msgStr)
    } catch {
      case ex: RuntimeException =>
        logError(Map(
          "message" -> "error while publishing script start event",
          "error" -> ex.getMessage
        ))
        ex.printStackTrace()
        throw ex
    }
  }

  def publishFinishMessage(runId: String, insightId: String, insightsGenerated: Int) : String = {
    val message = GenerationScriptStatus(runId, insightId, jobId, "GENERATION_PLATFORM_SPARK", insightsGenerated, scriptName, ScriptRunStatus.RUN_STATUS_COMPLETED, LocalDateTime.now())
    try {
      val msgStr = Json.stringify(Json.toJson(message))
      publisher.publishMessage(msgStr)
    } catch {
      case ex: RuntimeException =>
        logError(Map(
          "message" -> "error while publishing script completion event",
          "error" -> ex.getMessage
        ))
        ex.printStackTrace()
        throw ex
    }
  }

  def publishFailedMessage(runId: String, insightId: String) : String = {
    val message = GenerationScriptStatus(runId, insightId, jobId, "GENERATION_PLATFORM_SPARK", 0, scriptName, ScriptRunStatus.RUN_STATUS_FAILED, LocalDateTime.now())
    try {
      val msgStr = Json.stringify(Json.toJson(message))
      publisher.publishMessage(msgStr)
    } catch {
      case ex: RuntimeException =>
        logError(Map(
          "message" -> "error while publishing script failure event",
          "error" -> ex.getMessage
        ))
        ex.printStackTrace()
        throw ex
    }
  }
}
