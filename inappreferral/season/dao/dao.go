//go:generate dao_metrics_gen .
package dao

import (
	"context"

	"github.com/google/wire"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	seasonPb "github.com/epifi/gamma/api/inappreferral/season"
)

var SeasonDaoWireSet = wire.NewSet(NewSeasonDaoCrdb, wire.Bind(new(ISeasonDao), new(*SeasonDaoCrdb)))

type ISeasonDao interface {
	Create(ctx context.Context, seasonToCreate *seasonPb.Season) (*seasonPb.Season, error)
	GetByIds(ctx context.Context, ids []string) ([]*seasonPb.Season, error)
	GetSeasons(ctx context.Context, rewardOfferGroupId string, activeSince, activeTill, displaySince, displayTill *timestampPb.Timestamp) ([]*seasonPb.Season, error)
	Update(ctx context.Context, seasonId string, activeSince, activeTill, displaySince, displayTill *timestampPb.Timestamp, displayDetails *seasonPb.Season_DisplayDetails) (*seasonPb.Season, error)
}
